import babel from "@rollup/plugin-babel";
import peerDepsExternal from "rollup-plugin-peer-deps-external";
import del from "rollup-plugin-delete";
import image from "@rollup/plugin-image";
import postcss from "rollup-plugin-postcss";
import atImport from "postcss-import";
import url from "postcss-url";
import pkg from "./package.json" assert { type: "json" };
import terser from "@rollup/plugin-terser";
import tailwindcss from "tailwindcss";
import tailwindConfig from "./tailwind.config.js";
import json from "@rollup/plugin-json";

export default {
  input: pkg.source,
  output: [
    { file: pkg.main, format: "cjs" },
    { file: pkg.module, format: "esm" },
  ],
  plugins: [
    peerDepsExternal(),
    babel({
      exclude: "node_modules/**",
      babelHelpers: "bundled",
    }),
    postcss({
      modules: true,
      modules: {},
      plugins: [
        atImport(),
        url({ url: "inline" }),
        tailwindcss(tailwindConfig),
      ],
    }),
    image(),
    del({ targets: ["dist/*"] }),
    terser(),
    json(),
  ],
  external: Object.keys(pkg.peerDependencies || {}),
};
