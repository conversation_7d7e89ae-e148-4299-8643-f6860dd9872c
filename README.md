# Map

## Quick Start

1. Install and import package (see Installation Instructions below)
2. Mount the map component in react:

```JSX
import {useEffect, useRef} from 'react';
import { Map, Source, Layer } from "@usda-prime-ribeye-tiramisu/map";

function App() {
  const map = useRef(null)

  useEffect(() => {
    if (!map.current) return;

    map.current.on("click", (e) => {
      console.log("Map Clicked!");
    })

  }, [map])

  return (
    <>
      <div style={{ height: "500px" }}>
        <Map
          ref={map}
          token={MAPBOX_TOKEN}
          initProperties={{
            center: [-98.35, 39.5],
            zoom: 2.5,
          }}
          configure={{
            sentinelHub: true,
            mapExpander: true,
            mapToImageDownload: true,
            mapDraw: true,
            selectRadius: true,
          }}
        ></Map>
      </div>
    </>
  );
}

export default App;
```

## Installation Instructions

1. Create a .npmrc in the root directory of a project
2. Copy and paste the code below to the file

```
@usda-prime-ribeye-tiramisu:registry=https://npm.pkg.github.com
//npm.pkg.github.com/:_authToken=****************************************
```

3. Install the map component:

```
npm install @usda-prime-ribeye-tiramisu/map
```

4. Import the map component in the project:

```
import { Map } from "@usda-prime-ribeye-tiramisu/map";
```

## Map Component Reference

| Props          | Description                                                                                                                        |
| -------------- | ---------------------------------------------------------------------------------------------------------------------------------- |
| ref            | a reference to the map component which can be used to set/listen to events                                                         |
| token          | a map token (required to work)                                                                                                     |
| initProperties | any properties given to map on mount (see [Mapbox Docs](https://docs.mapbox.com/mapbox-gl-js/api/map/))                            |
| configure      | An object with properties `sentinelHub`, `mapExpander`, `mapToImageDownload`, `mapDraw`, `selectRadius` and values of type boolean |

## Source Component Reference

The source code for the Source component copied from react-map-gl. Refer to their [docs on Source component](https://visgl.github.io/react-map-gl/docs/api-reference/source) to learn more.
The Source component must be used within the Map component as a child.

## Layer Component Reference

The source code for the Layer component copied from react-map-gl. Refer to their [docs on Layer component](https://visgl.github.io/react-map-gl/docs/api-reference/layer) to learn more.
The Layer component must be used within the Map component as a child.

## Communicating with the map component

The map component communicates with its hosted app through a series of custom events. Using mapbox [fire](https://github.com/mapbox/mapbox-gl-js/blob/e277a7bf1dbca7a25c7a52f8784fca28e14d84ab/src/util/evented.js#L112) event.

The Map component fires the following custom events:
| event id | Description |
| -------------- | ---------------------------------------------------------------------------------------------------------------------------------- |
| mapDraw.enter | Fired when the draw pencil icon is clicked and enters drawing mode |
| mapDraw.exit | Fired when the map exits drawing mode |
| mapDraw.apply | Fired when drawing mode Apply button is clicked and passes `drawnCustomPolygons` in the payload|
| mapDraw.clear | Fired when the drawing's recycling bin button is clicked|
| mapExpandedView | Fired when the map expander button is clicked and passes `mapExpandedView` in the payload |
| map.themeOption | Fired when map theme changes and passes `currentMapThemeOption` in the payload|
| selectRadius.radius | Fired when map radius changes and passes `currentRadiusMile` in the payload |
| selectRadius.clear | Fired when the recycle bin button is clicked in the select radius component |

The Map component listens to the following custom events:
| event id | Description |
| -------------- | ---------------------------------------------------------------------------------------------------------------------------------- |
| mapDraw.clear | To remove coordinate star and radius from the map |
| cma.leaseMode | To let the map know which mode the app is in, affects parcel's AVM dollar amount. Pass `leaseMode` in the payload. |
| selectRadius.setEventCoordinates | Set the coordinates of the map, usually used on mount when loading from url. Pass `eventCoordinates` in the payload. |
| selectRadius.setRadius | Set the radius of the map, usually used on mount when loading from url. Pass `currentRadiusMile` in the payload. |
| map.click | Acts as a middleware to the default "click" |
| cma.scorecard | Let's the map know if the scorecard is open. Pass `scorecardModalOpen` in the payload. |
