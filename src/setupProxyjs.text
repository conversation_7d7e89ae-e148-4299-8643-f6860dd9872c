// const { createProxyMiddleware } = require("http-proxy-middleware");

// module.exports = function (app) {
//   app.use(
//     "/api/cma/exp",
//     createProxyMiddleware({
//       target: "http://ec2-3-235-170-15.compute-1.amazonaws.com:8080",
//       changeOrigin: true,
//       pathRewrite: { "^/api/cma/exp": "" },
//     })
//   );

//   app.use(
//     "/api/cma/prod",
//     createProxyMiddleware({
//       target: "http://ec2-3-237-18-99.compute-1.amazonaws.com:8080",
//       changeOrigin: true,
//       pathRewrite: { "^/api/cma/prod": "" },
//     })
//   );
// };
