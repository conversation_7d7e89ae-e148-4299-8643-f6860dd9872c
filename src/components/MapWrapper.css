@tailwind base;
@tailwind components;
@tailwind utilities;

#Map {
  box-sizing: border-box;
}

#Map {
  font-family: Arial, "helvetica", "sans-serif";
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}

.mapboxgl-popup {
  z-index: 800;
}

.mapboxgl-popup-content {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19) !important;
  border-radius: 10px !important;
  padding: 0 !important;
  overflow: hidden !important;
}

.mapboxgl-popup-tip {
  display: block !important;
}

#radiusSelectWrapper .ant-select-selector {
  border: none;
  color: var(--antd-active-blue);
  background-color: rgba(0, 0, 0, 0);
}

#Map #mapContainer .ant-message {
  position: relative !important;
}

.chainStoreMenu .dropdown-heading {
  height: 30px !important;
}
.chainStoreMenu .ant-modal-content .ant-modal-close {
  /* top: -5px;
  right: -5px; */
}

#chainLocationTable .ant-table-thead > tr > th {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 600;
  font-size: 14px;
  padding: 8px;
  background: #fafafa;
}
#chainLocationTable .ant-table-body tr > td {
  padding: 5px;
}
#chain-menu-segmented .ant-segmented-item-selected {
  background-color: #93c5fd;
}

#Map .ant-message {
  position: absolute !important;
}
