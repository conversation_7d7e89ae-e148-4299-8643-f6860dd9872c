import React, { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import {
  getWaterDistrictRasterLink,
  getWaterDistrictQueryData,
} from "../../../services/data";
import { geojsonTemplate } from "../../../constants";
import { initPopup } from "../MapUtility/general";
import { renderToString } from "react-dom/server";
import debounce from "lodash.debounce";
import { LoadingOutlined } from "@ant-design/icons";
import { Spin } from "antd";

const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

const sourceId = "water-district-source";

const minZoom = 6;

const layerStyle = {
  id: `${sourceId}LayerStyle`,
  type: "raster",
  source: sourceId,
  minzoom: minZoom,
  paint: {
    "raster-opacity": 1,
  },
};

const featureOutlineLayerStyle = {
  id: `${sourceId}-outline-FeatureLayerStyle`,
  type: "line",
  source: `${sourceId}-geojson`,
  minzoom: minZoom,
  paint: {
    "line-opacity": 1,
    "line-width": 3,
    "line-color": "red",
  },
  // filter: ["==", "$type", "Polygon"],
};

const featureLayerStyle = {
  id: `${sourceId}FeatureLayerStyle`,
  type: "fill",
  source: `${sourceId}-geojson`,
  minzoom: minZoom,
  paint: {
    "fill-opacity": 0,
  },
  // filter: ["==", "$type", "Polygon"],
};

const hoverFeatureOutlineLayerStyle = {
  id: `${sourceId}-hover-outline-FeatureLayerStyle`,
  type: "line",
  source: `${sourceId}-geojson`,
  minzoom: minZoom,
  paint: {
    "line-opacity": 1,
    "line-width": 5,
    "line-color": "green",
  },
  // filter: ["==", "$type", "Polygon"],
};

const convertQueryDataToGeoJSON = (queryData) => {
  const { features } = queryData;
  const geojson = structuredClone(geojsonTemplate);

  if (features.length > 0) {
    for (let i = 0; i < features.length; i++) {
      const newFeature = {
        type: "Feature",
        properties: features[i].attributes,
        geometry: {
          type: "Polygon",
          coordinates: features[i].geometry.rings,
        },
      };
      geojson.features.push(newFeature);
    }
  }
  return geojson;
};

let popup = initPopup({
  closeButton: true,
  closeOnClick: true,
  offset: 5,
});

const getTooltipHTML = (property) => {
  const ignoreKeys = [];

  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {Object.keys(property).map((key) => {
        if (ignoreKeys.includes(key)) return null;
        return (
          <div>
            <span style={{ fontWeight: "bold" }}>{key}: </span>
            {key === "website" ? (
              <a href={property[key]} target="_blank" title={property[key]}>
                {property[key]}
              </a>
            ) : (
              <span>{property[key]}</span>
            )}
          </div>
        );
      })}
      {property["DISTRICT_ID"] && (
        <a
          href={`https://www14.tceq.texas.gov/iwud/dist/index.cfm?fuseaction=DetailDistrict&DistrictNum=${property["DISTRICT_ID"]}&command=list`}
          target="_blank"
        >
          District Information
        </a>
      )}
    </div>
  );
};

const WaterDistrict = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const dispatch = useDispatch();

  const [rasterURL, setRasterURL] = useState(null);
  const [rasterCoordinate, setRasterCoordinate] = useState(null);
  const [featureGeoJSON, setFeatureGeoJSON] = useState(geojsonTemplate);
  const [hoverFeatureGeoJSON, setHoverlFeatureGeoJSON] = useState(null);

  const loaderRef = useRef(null);

  const currentMapLayerOptionsRef = useRef(currentMapLayerOptions);
  currentMapLayerOptionsRef.current = currentMapLayerOptions;

  useEffect(() => {
    if (!map) return;

    const moveEnd = () => {
      if (
        !currentMapLayerOptionsRef.current.includes("municipal water district")
      )
        return;

      const getWaterDistrictRaster = async () => {
        const width = map.getContainer().clientWidth;
        const height = map.getContainer().clientHeight;

        const bbox = map.getBounds().toArray().toString();

        const formData = new FormData();
        formData.append("dynamicLayers", JSON.stringify(dynamicLayers));
        formData.append("dpi", "96");
        formData.append("transparent", true);
        formData.append("format", "png32");
        formData.append("bbox", bbox);
        formData.append("bboxSR", "4326");
        formData.append("imageSR", "4326");
        formData.append("size", `${width},${height}`);
        formData.append("f", "json");

        const rasterInfo = await getWaterDistrictRasterLink({ body: formData });

        if (rasterInfo) {
          setRasterURL(rasterInfo.href);
          setRasterCoordinate([
            [rasterInfo.extent.xmin, rasterInfo.extent.ymax],
            [rasterInfo.extent.xmax, rasterInfo.extent.ymax],
            [rasterInfo.extent.xmax, rasterInfo.extent.ymin],
            [rasterInfo.extent.xmin, rasterInfo.extent.ymin],
          ]);
          const queryData = await getWaterDistrictQueryData({
            geometry: rasterInfo.extent,
          });
          if (queryData) {
            setFeatureGeoJSON(convertQueryDataToGeoJSON(queryData));
          }
        }
        if (loaderRef.current) loaderRef.current.style.display = "none";
      };

      if (loaderRef.current) loaderRef.current.style.display = "block";
      getWaterDistrictRaster();
    };

    const mouseClick = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [featureLayerStyle.id],
      });
      const feature = features.filter((f) =>
        f.source.includes(`${sourceId}-geojson`)
      );
      if (feature && feature.length > 0) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature[0].properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      } else {
        popup.remove();
      }
    };

    const mouseMove = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [featureLayerStyle.id],
      });
      const feature = features.filter((f) =>
        f.source.includes(`${sourceId}-geojson`)
      );
      if (feature && feature.length > 0) {
        map.getCanvas().style.cursor = "pointer";
        const { _x, _y, _z } = features[0];
        setHoverlFeatureGeoJSON(
          features[0]._vectorTileFeature.toGeoJSON(_x, _y, _z)
        );
      }
    };

    const mouseLeave = () => {
      map.getCanvas().style.cursor = "";
      setHoverlFeatureGeoJSON(null);
    };

    // if (currentMapLayerOptions.includes("municipal water district")) {
    moveEnd();
    // }

    map.on("moveend", debounce(moveEnd, 1000));
    map.on("click", mouseClick);
    map.on("mousemove", featureLayerStyle.id, mouseMove);
    map.on("mouseleave", featureLayerStyle.id, mouseLeave);
    // map.on("error", (e) => {
    //   console.log("ERROR", e);
    // });
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("municipal water district")) return null;
  return (
    <>
      {!currentMapLayerOptions.includes("municipal water district") ||
      !rasterURL ||
      !rasterCoordinate ? null : (
        <>
          <Source
            id={sourceId}
            type="image"
            url={rasterURL}
            coordinates={rasterCoordinate}
          >
            <Layer {...layerStyle} />
          </Source>
          <Source
            id={`${sourceId}-geojson`}
            type="geojson"
            data={featureGeoJSON}
          >
            <Layer {...featureLayerStyle} />
            <Layer {...featureOutlineLayerStyle} />
          </Source>
          {hoverFeatureGeoJSON && (
            <Source
              id={`${sourceId}-hover-geojson`}
              type="geojson"
              data={hoverFeatureGeoJSON}
            >
              <Layer {...hoverFeatureOutlineLayerStyle} />
            </Source>
          )}
        </>
      )}
      <div ref={loaderRef} style={{ display: "none" }}>
        <div
          style={{
            position: "absolute",
            top: "20px",
            left: "50%",
            transform: "translateX(-50%)",
            padding: "5px 10px",
            backgroundColor: "white",
            zIndex: 900,
            display: "flex",
            flexDirection: "row",
            gap: "10px",
            alignItems: "center",
            borderRadius: "5px",
            border: "1px solid #ccc",
            boxShadow:
              "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
          }}
        >
          <Spin indicator={antIcon} />
          <strong>Municipal water district layer is loading...</strong>
        </div>
      </div>
    </>
  );
};

export default WaterDistrict;

const dynamicLayers = [
  // {
  //   id: 6,
  //   name: "Municipal Utility District",
  //   source: { type: "mapLayer", mapLayerId: 6 },
  //   minScale: 15000000,
  //   maxScale: 0,
  // },
  {
    id: 19,
    name: "Counties",
    source: { type: "mapLayer", mapLayerId: 19 },
    minScale: 15000000,
    maxScale: 258006,
  },
  {
    id: 20,
    name: "TCEQ Service Regions",
    source: { type: "mapLayer", mapLayerId: 20 },
    drawingInfo: {
      renderer: {
        type: "uniqueValue",
        field1: "HQ",
        uniqueValueInfos: [
          {
            value: "Abilene",
            symbol: {
              color: [38, 115, 0, 62],
              outline: {
                color: [153, 153, 153, 64],
                width: 0.75,
                type: "esriSLS",
                style: "esriSLSSolid",
              },
              type: "esriSFS",
              style: "esriSFSSolid",
            },
            label: "Abilene",
          },
          {
            value: "Amarillo",
            symbol: {
              color: [20, 158, 206, 59],
              outline: {
                color: [153, 153, 153, 64],
                width: 0.75,
                type: "esriSLS",
                style: "esriSLSSolid",
              },
              type: "esriSFS",
              style: "esriSFSSolid",
            },
            label: "Amarillo",
          },
          {
            value: "Austin",
            symbol: {
              color: [56, 168, 0, 62],
              outline: {
                color: [153, 153, 153, 64],
                width: 0.75,
                type: "esriSLS",
                style: "esriSLSSolid",
              },
              type: "esriSFS",
              style: "esriSFSSolid",
            },
            label: "Austin",
          },
          {
            value: "Beaumon. t",
            symbol: {
              color: [255, 211, 127, 67],
              outline: {
                color: [153, 153, 153, 64],
                width: 0.75,
                type: "esriSLS",
                style: "esriSLSSolid",
              },
              type: "esriSFS",
              style: "esriSFSSolid",
            },
            label: "Beaumont",
          },
          {
            value: "Corpus Christi",
            symbol: {
              color: [255, 85, 0, 67],
              outline: {
                color: [153, 153, 153, 64],
                width: 0.75,
                type: "esriSLS",
                style: "esriSLSSolid",
              },
              type: "esriSFS",
              style: "esriSFSSolid",
            },
            label: "Corpus Christi",
          },
          {
            value: "Dallas/Fort Worth",
            symbol: {
              color: [255, 222, 62, 72],
              outline: {
                color: [153, 153, 153, 64],
                width: 0.75,
                type: "esriSLS",
                style: "esriSLSSolid",
              },
              type: "esriSFS",
              style: "esriSFSSolid",
            },
            label: "Dallas/Fort Worth",
          },
          {
            value: "El Paso",
            symbol: {
              color: [255, 0, 0, 59],
              outline: {
                color: [153, 153, 153, 64],
                width: 0.75,
                type: "esriSLS",
                style: "esriSLSSolid",
              },
              type: "esriSFS",
              style: "esriSFSSolid",
            },
            label: "El Paso",
          },
          {
            value: "Harlingen",
            symbol: {
              color: [76, 0, 115, 57],
              outline: {
                color: [153, 153, 153, 64],
                width: 0.75,
                type: "esriSLS",
                style: "esriSLSSolid",
              },
              type: "esriSFS",
              style: "esriSFSSolid",
            },
            label: "Harlingen",
          },
          {
            value: "Houston",
            symbol: {
              color: [0, 168, 132, 62],
              outline: {
                color: [153, 153, 153, 64],
                width: 0.75,
                type: "esriSLS",
                style: "esriSLSSolid",
              },
              type: "esriSFS",
              style: "esriSFSSolid",
            },
            label: "Houston",
          },
          {
            value: "Laredo",
            symbol: {
              color: [115, 0, 0, 67],
              outline: {
                color: [153, 153, 153, 64],
                width: 0.75,
                type: "esriSLS",
                style: "esriSLSSolid",
              },
              type: "esriSFS",
              style: "esriSFSSolid",
            },
            label: "Laredo",
          },
          {
            value: "Lubbock",
            symbol: {
              color: [233, 255, 190, 67],
              outline: {
                color: [153, 153, 153, 64],
                width: 0.75,
                type: "esriSLS",
                style: "esriSLSSolid",
              },
              type: "esriSFS",
              style: "esriSFSSolid",
            },
            label: "Lubbock",
          },
          {
            value: "Midland",
            symbol: {
              color: [127, 127, 127, 64],
              outline: {
                color: [153, 153, 153, 64],
                width: 0.75,
                type: "esriSLS",
                style: "esriSLSSolid",
              },
              type: "esriSFS",
              style: "esriSFSSolid",
            },
            label: "Midland",
          },
          {
            value: "San Angelo",
            symbol: {
              color: [253, 127, 111, 67],
              outline: {
                color: [153, 153, 153, 64],
                width: 0.75,
                type: "esriSLS",
                style: "esriSLSSolid",
              },
              type: "esriSFS",
              style: "esriSFSSolid",
            },
            label: "San Angelo",
          },
          {
            value: "San Antonio",
            symbol: {
              color: [26, 26, 26, 59],
              outline: {
                color: [153, 153, 153, 64],
                width: 0.75,
                type: "esriSLS",
                style: "esriSLSSolid",
              },
              type: "esriSFS",
              style: "esriSFSSolid",
            },
            label: "San Antonio",
          },
          {
            value: "Tyler",
            symbol: {
              color: [181, 71, 121, 59],
              outline: {
                color: [153, 153, 153, 64],
                width: 0.75,
                type: "esriSLS",
                style: "esriSLSSolid",
              },
              type: "esriSFS",
              style: "esriSFSSolid",
            },
            label: "Tyler",
          },
          {
            value: "Waco",
            symbol: {
              color: [0, 77, 168, 59],
              outline: {
                color: [153, 153, 153, 64],
                width: 0.75,
                type: "esriSLS",
                style: "esriSLSSolid",
              },
              type: "esriSFS",
              style: "esriSFSSolid",
            },
            label: "Waco",
          },
        ],
      },
      transparency: 0,
    },
    minScale: 0,
    maxScale: 196901,
  },
];
