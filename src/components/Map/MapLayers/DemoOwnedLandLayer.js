import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { renderToString } from "react-dom/server";
import Source from "./Source";
import Layer from "./Layer";
import data from "../../../data/blueriver_marketting_deals.json";
import { geojsonTemplate } from "../../../constants";
import greenMarker from "../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-green.png";
import redMarker from "../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-red.png";
import { initPopup } from "../MapUtility/general";
import { Button } from "antd";
import { getDemoLandData } from "../../../services/data";
import { convertToGeoJSON } from "../../../utils/geography";
const sourceId = "demo-land-owned";
const layerStyle = {
  id: `${sourceId}-style`,
  type: "symbol",
  layout: {
    "icon-image": ["match", ["get", "type"], "Owned", "redMarker", "greenMarker"],
    "icon-size": 1,
    "icon-allow-overlap": true,
  },
  paint: {
    "icon-color": "white",
    "icon-halo-color": "red",
    "icon-halo-width": 10,
  },
};

const popup = initPopup();

const formatValue = (key, value) => {
  if (key === "price") {
    return `$${value.toLocaleString()}`;
  } else if (key === "acres") {
    return `${value} ac`;
  }
  return value;
};

const hasSatellite = [
  "Windermere Park",
  "St Leo Commons",
  "920 Harvey Rd",
  "Patrick Mill",
  "Artisan Village",
  "Laurelview",
];

const popupHTML = (properties) => {
  const content = (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        padding: "10px",
      }}
    >
      {Object.keys(properties).map((key, idx) => (
        <div key={`${idx}-${key}`}>
          <span style={{ fontWeight: "bold" }}>{key}</span>: {formatValue(key, properties[key])}
        </div>
      ))}
      <div
        style={{
          marginTop: "10px",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        {hasSatellite.some((item) => properties.name.includes(item)) && (
          <button id="deals-popup-monthly-images" style={{ cursor: "pointer" }}>
            View Monthly Images
          </button>
        )}
      </div>
    </div>
  );
  return renderToString(content);
};

const DemoOwnedLandLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const [geojson, setGeojson] = useState(geojsonTemplate);

  const mouseOnPopup = useRef(false);

  useEffect(() => {
    if (!map) return;
    console.log("DemoUnderwrittenLandLayer currentMapLayerOptions", currentMapLayerOptions);
    const fetchDemoLand = async () => {
      const data = await getDemoLandData({
        type: "Owned",
      });

      setGeojson(
        convertToGeoJSON({
          data,
          geomAccessor: (item) => item.geom,
          propertiesAccessor: (item) => {
            const { geom, ...properties } = item;
            return properties;
          },
        })
      );
    };
    fetchDemoLand();
    const loadIconImage = () => {
      map.loadImage(greenMarker, (error, image) => {
        if (error) throw error;
        if (!map.hasImage("greenMarker")) {
          map.addImage("greenMarker", image);
        }
      });

      map.loadImage(redMarker, (error, image) => {
        if (error) throw error;
        if (!map.hasImage("redMarker")) {
          map.addImage("redMarker", image);
        }
      });
    };

    const mouseMove = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [layerStyle.id],
      });
      if (features.length > 0) {
        const feature = features[0];
        popup
          .setLngLat(feature.geometry.coordinates)
          .setHTML(popupHTML(feature.properties))
          .addTo(map);

        popup.getElement().addEventListener("mouseleave", () => {
          mouseOnPopup.current = false;
          map.getCanvas().style.cursor = "pointer";
          popup.remove();
        });

        popup.getElement().addEventListener("mouseenter", () => {
          mouseOnPopup.current = true;
        });

        document.getElementById("deals-popup-monthly-images")?.addEventListener(
          "click",
          () => {
            map.jumpTo({ center: feature.geometry.coordinates, zoom: 14 });
            map.once("idle", () => {
              map.fire("open-monthly-images");
            });
          },
          { once: true }
        );
      } else {
        popup.remove();
      }
    };

    const mouseLeave = () => {
      setTimeout(() => {
        if (mouseOnPopup.current) return;
        map.getCanvas().style.cursor = "";
        popup.remove();
      }, 250);
    };

    map.on("style.load", loadIconImage);
    map.on("mousemove", layerStyle.id, mouseMove);
    map.on("mouseleave", layerStyle.id, mouseLeave);
    return () => {
      map.off("style.load", loadIconImage);
      map.off("mousemove", layerStyle.id, mouseMove);
      map.off("mouseleave", layerStyle.id, mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("demo land owned")) return null;

  return (
    <Source id={sourceId} type="geojson" data={geojson}>
      <Layer {...layerStyle} />
    </Source>
  );
};

export default DemoOwnedLandLayer;
