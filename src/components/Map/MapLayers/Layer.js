import { useRef, useEffect, useState, useMemo } from "react";
import isEqual from "lodash.isequal";
import { useMap } from "../MapProvider";

// REFERENCE: https://github.com/visgl/react-map-gl/blob/master/src/components/layer.ts
let layerCounter = 0;

function updateLayer(map, id, props, prevProps) {
  if (props.id !== prevProps.id) {
    throw new Error("layer id changed");
  }
  if (props.type !== prevProps.type) {
    throw new Error("layer type changed");
  }

  if (props.type === "custom" || prevProps.type === "custom") {
    return;
  }

  const { layout = {}, paint = {}, filter, minzoom, maxzoom, beforeId } = props;

  if (beforeId !== prevProps.beforeId) {
    map.moveLayer(id, beforeId);
  }
  if (layout !== prevProps.layout) {
    const prevLayout = prevProps.layout || {};
    for (const key in layout) {
      if (!isEqual(layout[key], prevLayout[key])) {
        map.setLayoutProperty(id, key, layout[key]);
      }
    }
    for (const key in prevLayout) {
      if (!layout.hasOwnProperty(key)) {
        map.setLayoutProperty(id, key, undefined);
      }
    }
  }
  if (paint !== prevProps.paint) {
    const prevPaint = prevProps.paint || {};
    for (const key in paint) {
      if (!isEqual(paint[key], prevPaint[key])) {
        map.setPaintProperty(id, key, paint[key]);
      }
    }
    for (const key in prevPaint) {
      if (!paint.hasOwnProperty(key)) {
        map.setPaintProperty(id, key, undefined);
      }
    }
  }
  if (!isEqual(filter, prevProps.filter)) {
    map.setFilter(id, filter);
  }
  if (minzoom !== prevProps.minzoom || maxzoom !== prevProps.maxzoom) {
    map.setLayerZoomRange(id, minzoom, maxzoom);
  }
}

function createLayer(map, id, props) {
  // @ts-ignore
  if (
    map.style &&
    map.style._loaded &&
    (!("source" in props) || map.getSource(props.source))
  ) {
    const options = { ...props, id };
    delete options.beforeId;

    map.addLayer(options, props.beforeId);
  }
}

const Layer = (props) => {
  const { map } = useMap();
  const propsRef = useRef(props);
  const [, setStyleLoaded] = useState(0);

  const id = useMemo(() => props.id || `jsx-layer-${layerCounter++}`, []);

  useEffect(() => {
    if (map) {
      const forceUpdate = () => setStyleLoaded((version) => version + 1);
      map.on("styledata", forceUpdate);
      forceUpdate();

      return () => {
        map.off("styledata", forceUpdate);
        // @ts-ignore
        if (map.style && map.style._loaded && map.getLayer(id)) {
          map.removeLayer(id);
        }
      };
    }
    return undefined;
  }, [map]);

  const layer = map && map.style && map.getLayer(id);
  if (layer) {
    try {
      updateLayer(map, id, props, propsRef.current);
    } catch (error) {
      console.warn(error); // eslint-disable-line
    }
  } else {
    if (map) {
      createLayer(map, id, props);
    }
  }

  // Store last rendered props
  propsRef.current = props;

  return null;
};

export default Layer;
