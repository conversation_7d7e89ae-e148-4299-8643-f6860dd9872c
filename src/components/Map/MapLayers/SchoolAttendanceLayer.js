import { useEffect, useRef } from "react";
import { renderToString } from "react-dom/server";
import { useSelector, useDispatch } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { MAP_LAYER_NAME_BASE } from "../../../constants";
import { setAttendanceZoneColor } from "../MapUtility/colors";
import { hideVisibility, showVisibility } from "../MapUtility/layer";
import { initPopup } from "../MapUtility/general";
import { capitalize } from "../../../utils/strings";

const sourceId = MAP_LAYER_NAME_BASE.attendance;
const zoomLevelToAttendanceLayer = 9;
// const attendanceZoneSatelliteOpacity = 0.5;
const attendanceZoneDefaultOpacity = 0.5;

const pointStyle = {
  id: `${sourceId}PointLayer`,
  type: "circle",
  minzoom: zoomLevelToAttendanceLayer,
  paint: {
    "circle-radius": ["interpolate", ["linear"], ["zoom"], zoomLevelToAttendanceLayer, 1, 17, 8],
    "circle-color": "#0047AB",
    "circle-stroke-color": "#fff",
    "circle-stroke-width": 1,
  },
};

const fillStyle = {
  id: `${sourceId}FillLayer`,
  type: "fill",
  minzoom: zoomLevelToAttendanceLayer,
  paint: {
    "fill-opacity": attendanceZoneDefaultOpacity,
  },
};

let schoolAttendancePopup;

function SchoolAttendanceLayer() {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const selectedAttendanceCategory = useSelector((state) => state.Map.selectedAttendanceCategory);
  const currentAttendanceZoneGeoJSON = useSelector(
    (state) => state.Map.currentAttendanceZoneGeoJSON
  );
  const currentAttendancePointGeoJSON = useSelector(
    (state) => state.Map.currentAttendancePointGeoJSON
  );
  const dispatch = useDispatch();

  const currentMapLayerOptionsRef = useRef(currentMapLayerOptions);
  currentMapLayerOptionsRef.current = currentMapLayerOptions;

  const selectedAttendanceCategoryRef = useRef(selectedAttendanceCategory);
  selectedAttendanceCategoryRef.current = selectedAttendanceCategory;

  useEffect(() => {
    if (!map || !map.style || !map.style._loaded) return;

    if (!currentMapLayerOptions.includes("school zones")) return;
    map.fire("map.setThemeOption", {
      payload: { currentMapThemeOption: "Monochrome" },
    });
    if (!map.getLayer(`${sourceId}PointLayer`) || !map.getLayer(`${sourceId}FillLayer`)) return;

    if (currentMapLayerOptions.includes("school zones")) {
      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToAttendanceLayer) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getAttendanceZone",
          payload: {
            category: selectedAttendanceCategory,
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });
      }
      showVisibility(map, `${sourceId}PointLayer`);
      showVisibility(map, `${sourceId}FillLayer`);
    } else {
      if (
        map.getLayoutProperty(`${sourceId}PointLayer`, "visibility") === "visible" ||
        map.getLayoutProperty(`${sourceId}FillLayer`, "visibility") === "visible"
      ) {
        hideVisibility(map, `${sourceId}PointLayer`);
        hideVisibility(map, `${sourceId}FillLayer`);
      }
    }
    // eslint-disable-next-line
  }, [currentMapLayerOptions]);

  useEffect(() => {
    if (!map) return;

    schoolAttendancePopup = initPopup();

    const moveEnd = () => {
      if (!currentMapLayerOptionsRef.current.includes("school zones")) return;

      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToAttendanceLayer) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getAttendanceZone",
          payload: {
            category: selectedAttendanceCategoryRef.current,
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });
      }
    };

    const mouseMove = (e) => {
      const feature = map.queryRenderedFeatures(e.point, {
        layers: [`${sourceId}FillLayer`],
      });
      const coordinates = [e.lngLat.lng, e.lngLat.lat];

      if (feature && feature.length > 0) {
        const schoolName = feature[0].properties.obj_name;
        const rating = feature[0].properties.rating;

        // const htmlRender = renderToString(
        //   <span style={{ padding: "10px", fontWeight: "500" }}>
        //     {schoolName}
        //   </span>
        // );

        const htmlRender = renderToString(
          <div style={{ padding: "10px", fontWeight: "500" }}>
            <h4 style={{ fontSize: "14px", fontWeight: "600", margin: 0 }}>
              {capitalize(schoolName)}
            </h4>
            {rating && rating > 0 && <p style={{ margin: 0 }}>Rating: {rating}</p>}
          </div>
        );

        schoolAttendancePopup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      } else {
        schoolAttendancePopup.remove();
      }
    };

    const mouseLeave = () => {
      schoolAttendancePopup.remove();
    };

    map.on("moveend", moveEnd);
    map.on("mousemove", `${sourceId}FillLayer`, mouseMove);
    map.on("mouseleave", `${sourceId}FillLayer`, mouseLeave);

    return () => {
      map.off("moveend", moveEnd);
    };
    // eslint-disable-next-line
  }, [map]);

  if (map && currentMapLayerOptionsRef.current.includes("school zones")) {
    setAttendanceZoneColor(map, currentAttendanceZoneGeoJSON);
  }

  return (
    <>
      <Source id={`${sourceId}Point`} type="geojson" data={currentAttendancePointGeoJSON}>
        <Layer {...pointStyle} />
      </Source>
      <Source id={`${sourceId}Fill`} type="geojson" data={currentAttendanceZoneGeoJSON}>
        <Layer {...fillStyle} />
      </Source>
    </>
  );
}

export default SchoolAttendanceLayer;
