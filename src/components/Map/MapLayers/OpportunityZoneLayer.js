import { useEffect, useRef } from "react";
import { renderToString } from "react-dom/server";
import { useSelector, useDispatch } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import {
  MAP_LAYER_NAME_BASE,
  // zoomLevelToChangeBasemap,
} from "../../../constants";
import { hideVisibility, showVisibility } from "../MapUtility/layer";
import { initPopup } from "../MapUtility/general";

const sourceId = MAP_LAYER_NAME_BASE.opportunityZone;

const zoomLevelToLayer = 9;
// const opportunityZoneSatelliteOpacity = 0.6;
const opportunityZoneDefaultOpacity = 0.6;

const fillStyle = {
  id: `${sourceId}LayerFill`,
  type: "fill",
  minzoom: zoomLevelToLayer,
  paint: {
    "fill-opacity": opportunityZoneDefaultOpacity,
    "fill-color": [
      "match",
      ["get", "type"],
      "Low-Income Community",
      "#4daf4a",
      "Non-LIC Contiguous",
      "#a85802",
      "#000",
    ],
  },
};

const OutlineLayer = {
  id: `${sourceId}LayerOutline`,
  type: "line",
  minzoom: zoomLevelToLayer,
  paint: {
    "line-color": "#000",
    "line-width": 3,
  },
};

let opportunityZonePopup;

function OpportunityZoneLayer() {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const currentOpportunityZoneGeoJSON = useSelector(
    (state) => state.Map.currentOpportunityZoneGeoJSON
  );
  const heatmapType = useSelector((state) => state.Map.heatmapType);
  const dispatch = useDispatch();

  const currentMapLayerOptionsRef = useRef(currentMapLayerOptions);
  currentMapLayerOptionsRef.current = currentMapLayerOptions;

  useEffect(() => {
    if (!map || !map.style || !map.style._loaded) return;
    if (!map.getLayer(`${sourceId}LayerFill`)) return;

    if (currentMapLayerOptions.includes("opportunity zones")) {
      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToLayer) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getStrategyOpportunityZone",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });
      }
      showVisibility(map, `${sourceId}LayerFill`);
    } else {
      if (
        map.getLayoutProperty(`${sourceId}LayerFill`, "visibility") ===
        "visible"
      ) {
        hideVisibility(map, `${sourceId}LayerFill`);
      }
    }
  }, [currentMapLayerOptions]);

  useEffect(() => {
    if (!map) return;

    opportunityZonePopup = initPopup();

    const moveEnd = () => {
      if (!currentMapLayerOptionsRef.current.includes("opportunity zones"))
        return;

      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToLayer) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getStrategyOpportunityZone",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });
      }
    };

    const mouseMove = (e) => {
      const feature = map.queryRenderedFeatures(e.point, {
        layers: [`${sourceId}LayerFill`],
      });
      const coordinates = [e.lngLat.lng, e.lngLat.lat];

      if (feature && feature.length > 0) {
        const subtype = feature[0].properties.type;

        const htmlRender = renderToString(
          <span style={{ padding: "10px", fontWeight: "500" }}>{subtype}</span>
        );

        opportunityZonePopup
          .setLngLat(coordinates)
          .setHTML(htmlRender)
          .addTo(map);
      } else {
        opportunityZonePopup.remove();
      }
    };

    const mouseLeave = () => {
      opportunityZonePopup.remove();
    };

    map.on("moveend", moveEnd);
    map.on("mousemove", `${sourceId}LayerFill`, mouseMove);
    map.on("mouseleave", `${sourceId}LayerFill`, mouseLeave);

    return () => {
      map.off("moveend", moveEnd);
      map.off("mousemove", `${sourceId}LayerFill`, mouseMove);
      map.off("mouseleave", `${sourceId}LayerFill`, mouseLeave);
    };
  }, [map]);

  return (
    <>
      <Source id={sourceId} type="geojson" data={currentOpportunityZoneGeoJSON}>
        <Layer {...fillStyle} />
        {heatmapType &&
          (heatmapType.includes("demographics") ||
            heatmapType.includes("submarket")) &&
          currentMapLayerOptionsRef.current.includes("opportunity zones") && (
            <Layer {...OutlineLayer} />
          )}
      </Source>
    </>
  );
}

export default OpportunityZoneLayer;
