import { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { initPopup } from "../MapUtility/general";
import { renderToString } from "react-dom/server";
import { geojsonTemplate } from "../../../constants";
import { parcelHovered } from "./ParcelLayer";
import { airBnBHovered } from "./AirBnBLayer";
import { Button, Drawer, Spin, Switch } from "antd";
import { getParcelDetailData, tileURLRoot, getOwnerOtherParcels } from "../../../services/data";
import { isEmpty } from "lodash";
import { organizeParcelData } from "../../../utils/regrid";
import { ParcelBreakdownProvider, ParcelBreakDownContent } from "./RegridTab";
import { CloseOutlined } from "@ant-design/icons";
//turf
import * as turf from "@turf/turf";
import along from "@turf/along";
import length from "@turf/length";
import midpoint from "@turf/midpoint";
import bearing from "@turf/bearing";
import { point, lineString, featureCollection } from "@turf/helpers";
import simplify from "@turf/simplify";
import bbox from "@turf/bbox"; // Added for popup positioning
import area from "@turf/area"; // Added for area calculation
import centroid from "@turf/centroid"; // Added for popup positioning
import { createRoot } from "react-dom/client"; // Added for React rendering
import { Search } from "lucide-react";
import SearchBar from "./regridSections/Search";
const sourceId = "regrid";
// const sourceLayer = "6d4ea084a87498db6363be7aab005ab63a6b1f93";
const sourceLayer = "c9320a846de802ef7936b0b9968f0cda2f240355";

export const minZoom = 14;

// https://support.regrid.com/parcel-data/schema
// https://support.regrid.com/parcel-data/enhanced-ownership-schema
const fieldMapper = {
  parcelnumb: "Parcel ID",
  usecode: "Parcel Use Code",
  usedesc: "Parcel Use Description",
  struct: "Structure on Parcel",
  structno: "Number of Structures on Parcel",
  structstyle: "Structure Style",
  parvaltype: "Parcel Value Type",
  improvval: "Improvement Value",
  landval: "Land Value",
  owner: "Owner Name",
  ownfrst: "Owner First Name",
  ownlast: "Owner Last Name",
  mailadd: "Mailing Address",
  mail_city: "Mailing Address City",
  mail_zip: "Mailing Address ZIP Code",
  ll_last_refresh: "Last County Refresh Date",
  ll_uuid: "LL_UUID",
  eo_owner: "Owner",
  eo_deedowner: "Deed Owner",
  eo_last_refresh: "Last Owner Update",
  // from api:
  ogc_fid: "Object ID",
  geoid: "FIPS Code",
  sourceagent: "Source Agent",
  parcelnumb: "Parcel ID",
  parcelnumb_no_formatting: "Parcel ID without Formatting",
  alt_parcelnumb1: "First Alternative Parcel ID",
  alt_parcelnumb2: "Second Alternative Parcel ID",
  alt_parcelnumb3: "Third Alternative Parcel ID",
  usecode: "Parcel Use Code",
  usedesc: "Parcel Use Description",
  zoning: "Zoning Code",
  zoning_type: "Zoning Type",
  zoning_subtype: "Zoning Subtype",
  zoning_description: "Zoning Description",
  struct: "Structure on Parcel",
  multistruct: "Multiple Structures on Parcel",
  structno: "Number of Structures on Parcel",
  yearbuilt: "Structure Year Built",
  numstories: "Number of Stories",
  numunits: "Number of Units",
  structstyle: "Structure Style",
  parvaltype: "Parcel Value Type",
  improvval: "Improvement Value",
  landval: "Land Value",
  parval: "Total Parcel Value",
  agval: "Agricultural Value",
  homestead_exemption: "Homestead Exemption",
  saleprice: "Last Sale Price",
  saledate: "Last Sale Date",
  taxamt: "Annual Tax Bill",
  taxyear: "Tax Year",
  owntype: "Owner Type",
  owner: "Owner Name",
  ownfrst: "Owner First Name",
  ownlast: "Owner Last Name",
  owner2: "Second Owner Name",
  owner3: "Third Owner Name",
  owner4: "Fourth Owner Name",
  subsurfown: "Subsurface Owner",
  subowntype: "Subsurface Owner Type",
  mailadd: "Mailing Address",
  mail_address2: "Mailing Address Second Line",
  careof: "Mailing Address Care Of",
  mail_addno: "Mailing Address Street Number",
  mail_addpref: "Mailing Address Street Prefix",
  mail_addstr: "Mailing Address Street Name",
  mail_addsttyp: "Mailing Address Street Type",
  mail_addstsuf: "Mailing Address Street Suffix",
  mail_unit: "Mailing Address Unit Number",
  mail_city: "Mailing Address City",
  mail_state2: "Mailing Address State",
  mail_zip: "Mailing Address ZIP Code",
  mail_country: "Mailing Address Country",
  mail_urbanization: "Mailing Address Urbanizacion (Puerto Rico)",
  address: "Parcel Address",
  address2: "Parcel Address Second Line",
  saddno: "Parcel Address Number",
  saddpref: "Parcel Address Prefix",
  saddstr: "Parcel Address Street Name",
  saddsttyp: "Parcel Address Street Type",
  saddstsuf: "Parcel Address Street Suffix",
  sunit: "Parcel Address Unit",
  scity: "Parcel Address City",
  original_address: "Original Parcel Address",
  city: "US Census County Subdivision",
  county: "Parcel Address County",
  state2: "Parcel Address State",
  szip: "Parcel Address Zip Code",
  urbanization: "Parcel Urbanizacion",
  ll_address_count: "Regrid Calculated Total Address Count",
  location_name: "Location Name",
  address_source: "Primary Address Source",
  legaldesc: "Legal Description",
  plat: "Plat",
  book: "Book",
  page: "Page",
  block: "Block",
  lot: "Lot",
  neighborhood: "Neighborhood",
  subdivision: "Subdivision",
  lat: "Latitude",
  lon: "Longitude",
  qoz: "Federal Qualified Opportunity Zone",
  qoz_tract: "Qualified Opportunity Zone Tract Number",
  census_tract: "Census 2020 Tract",
  census_block: "Census 2020 Block",
  census_blockgroup: "Census 2020 Blockgroup",
  sourceref: "Source Document Reference",
  sourcedate: "Source Document Date",
  ll_last_refresh: "Last County Refresh Date",
  sourceurl: "Source URL",
  recrdareatx: "Recorded Area (text)",
  recrdareano: "Total Square Footage of Structures",
  gisacre: "County-Provided Acres",
  sqft: "County-Provided Parcel Square Feet",
  ll_gisacre: "Regrid Calculated Parcel Acres",
  ll_gissqft: "Regrid Calculated Parcel Square Feet",
  ll_bldg_footprint_sqft: "Regrid Calculated Building Footprint Square Feet",
  ll_bldg_count: "Regrid Calculated Building Count",
  cdl_raw: "Cropland Data Layer Raw Values",
  cdl_majority_category: "Cropland Data Layer Majority Category",
  cdl_majority_percent: "Cropland Data Layer Majority Percent",
  cdl_date: "Cropland Data Layer Date",
  plss_township: "PLSS Township",
  plss_section: "PLSS Section",
  plss_range: "PLSS Range",
  reviseddate: "Date of Last Revision",
  path: "Parcel Path",
  ll_stable_id: "Stable ID Status",
  ll_uuid: "Regrid UUID",
  ll_stack_uuid: "Parcel Stack UUID",
  ll_row_parcel: "Regrid Right-of-Way Parcel Flag",
  ll_updated_at: "Last Modified",
  dpv_status: "USPS Delivery Point Validation",
  dpv_codes: "Delivery Point Validation Codes",
  dpv_notes: "Delivery Point Validation Notes",
  dpv_type: "Delivery Point Match Type",
  cass_errorno: "CASS Error Codes",
  rdi: "Residential Delivery Indicator",
  usps_vacancy: "USPS Vacancy Indicator",
  usps_vacancy_date: "USPS Vacancy Indicator Date",
  padus_public_access: "PAD-US Public Access Designation",
  lbcs_activity: "Land Use Code: Activity",
  lbcs_activity_desc: "Land Use Code Description: Activity",
  lbcs_function: "Land Use Code: Function",
  lbcs_function_desc: "Land Use Code Description: Function",
  lbcs_structure: "Land Use Code: Structure",
  lbcs_structure_desc: "Land Use Code Description: Structure",
  lbcs_site: "Land Use Code: Site",
  lbcs_site_desc: "Land Use Code Description: Site",
  lbcs_ownership: "Land Use Code: Ownership",
  lbcs_ownership_desc: "Land Use Code Description: Ownership",
};

const getFormattedLabel = (label) => {
  const result = fieldMapper[label];
  if (result) return result;
  return label;
};

const hoveredParcelLineStyle = {
  id: "hoveredParcelStyle",
  type: "line",
  minzoom: minZoom,
  // maxzoom: 20,
  layout: {
    visibility: "visible",
  },
  paint: {
    "line-width": 4,
    "line-color": "red",
  },
};

const lineStyle = {
  id: "parcelsStyle",
  type: "line",
  minzoom: minZoom,
  // maxzoom: 20,
  "source-layer": sourceLayer,
  layout: {
    visibility: "visible",
  },
  paint: {
    "line-width": ["interpolate", ["linear"], ["zoom"], 0, 1, 12, 2, 17, 3],
    "line-color": "white",
  },
  beforeId: "circleFill",
};

const fillStyle = {
  id: "parcelsFillStyle",
  type: "fill",
  minzoom: minZoom,
  // maxzoom: 20,
  "source-layer": sourceLayer,
  paint: {
    "fill-opacity": 0,
  },
};

const labelStyle = {
  id: "parcelsLabelStyle",
  type: "symbol",
  minzoom: minZoom,
  // maxzoom: 20,
  source: sourceId,
  "source-layer": sourceLayer,
  layout: {
    "text-field": "{zoning}",
    "text-size": 18,
    "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"],
    // "text-allow-overlap": true,
    // "symbol-spacing": 500,
  },
  paint: {
    "text-color": "purple",
    "text-halo-color": "white",
    "text-halo-width": 4,
  },
};

const fillStyleZoning = {
  id: "parcelsFillStyleZoning",
  type: "fill",
  minzoom: minZoom,
  source: sourceId,
  "source-layer": sourceLayer,
  paint: {
    "fill-color": [
      "case",
      ["==", ["get", "zoning_subtype"], "Single Family"],
      "#a6cee3",
      ["==", ["get", "zoning_subtype"], "Two Family"],
      "#1f78b4",
      ["==", ["get", "zoning_subtype"], "Multi Family"],
      "#b2df8a",
      ["==", ["get", "zoning_subtype"], "Mobile Home Park"],
      "#33a02c",
      ["==", ["get", "zoning_subtype"], "General Commercial"],
      "#fb9a99",
      ["==", ["get", "zoning_subtype"], "Core Commercial"],
      "#e31a1c",
      ["==", ["get", "zoning_subtype"], "Retail Commercial"],
      "#fdbf6f",
      ["==", ["get", "zoning_subtype"], "Neighborhood Commercial"],
      "#ff7f00",
      ["==", ["get", "zoning_subtype"], "Office"],
      "#cab2d6",
      ["==", ["get", "zoning_subtype"], "Special Commercial"],
      "#6a3d9a",
      ["==", ["get", "zoning_subtype"], "Mixed Use"],
      "#ffff99",
      ["==", ["get", "zoning_subtype"], "Industrial"],
      "#b15928",
      ["==", ["get", "zoning_subtype"], "Light Industrial"],
      "#dfc27d",
      ["==", ["get", "zoning_subtype"], "Special"],
      "#66bd63",
      ["==", ["get", "zoning_subtype"], "Planned"],
      "#80cdc1",
      ["==", ["get", "zoning_subtype"], "Overlay"],
      "#878787",
      "transparent", // Default case if none of the above conditions are met
    ],
    "fill-opacity": 0.6,
  },
  beforeId: "parcelsLabelStyle",
};

const dimensionLabelStyle = {
  id: "dimensionLabels",
  type: "symbol",
  minzoom: minZoom,
  layout: {
    "text-field": ["get", "length"],
    "text-size": 14,
    "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"],
    "text-anchor": "center",
    "text-offset": [0, -1], // Offset above the line for clarity
    "text-rotate": 0, // Force horizontal labels
    "text-allow-overlap": false, // Prevent overlap
    "text-ignore-placement": false,
  },
  paint: {
    "text-color": "#000000",
    "text-halo-color": "#FFFFFF",
    "text-halo-width": 2,
  },
};

const hoverPopup = initPopup({
  closeButton: false,
  closeOnClick: false,
  anchor: "top",
});

const areaPopup = initPopup({
  closeButton: true,
  closeOnClick: true,
  anchor: "top",
  className: "area-popup",
  offset: 50, // Offset to avoid overlap
});

const popup = initPopup({
  closeButton: false,
  closeOnClick: false,
  anchor: "top",
});

const getTooltipHTML = (properties) => {
  const keysToDisplay = ["owner", "usedesc", "mailadd", "zoning", "zoning_type", "zoning_subtype"];

  let emptyLand = false;
  if (
    ("struct" in properties && properties.struct === false) ||
    ("structstyle" in properties && properties.structstyle === "LAND ONLY")
  ) {
    emptyLand = true;
  }

  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
        gap: "5px",
        // width: "500px",
      }}
    >
      {emptyLand && (
        <div
          style={{
            textAlign: "center",
            display: "flex",
            flexDirection: "column",
            lineHeight: "1.25",
          }}
        >
          <strong>Empty Land</strong>
        </div>
      )}
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          lineHeight: "1.3",
        }}
      >
        {keysToDisplay.map((key) => {
          if (!properties[key]) return null;
          return (
            <div key={key}>
              {key === "mailadd" ? (
                <span>
                  {fieldMapper[key]}:{" "}
                  <strong>{`${properties[key]}, ${properties["mail_city"]} ${properties["mail_zip"]}`}</strong>
                </span>
              ) : (
                <span>
                  {fieldMapper[key]}: <strong>{properties[key]}</strong>
                </span>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

const AreaPopupContent = ({ geojson, perimeterFeet }) => {
  const areaMeters = turf.area(geojson);
  const areaSqFt = areaMeters * 10.7639;
  const areaAcres = areaSqFt / 43560;

  return (
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
        gap: "5px",
        background: "#fff",
        borderRadius: "4px",
        boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
      }}
    >
      <div>
        Area (Square Feet): <strong>{Math.round(areaSqFt).toLocaleString()} sqft</strong>
      </div>
      <div>
        Area (Acres): <strong>{areaAcres.toFixed(2)} acres</strong>
      </div>
      <div>
        Perimeter: <strong>{Math.round(perimeterFeet)} ft</strong>
      </div>
    </div>
  );
};

export let regridIsHovered;

const getTileURL = (serverType, token) =>
  `${tileURLRoot(
    "sbs",
    serverType
  )}/api/v1/parcel/boundaries/tile/{z}/{x}/{y}.mvt?access_token=${token}`;

function Regrid() {
  const dispatch = useDispatch();
  const serverType = useSelector((state) => state.Configure.serverType);
  const user = useSelector((state) => state.Configure.user);
  const map = useSelector((state) => state.Map.map);
  const parcelMode = useSelector((state) => state.Map.parcelMode);
  const drawingMode = useSelector((state) => state.Map.drawingMode);
  const parcelOutputMode = useSelector((state) => state.Map.parcelOutputMode);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const mapExpandedView = useSelector((state) => state.Map.mapExpandedView);
  const [hoverParcel, setHoverParcel] = useState(geojsonTemplate);
  const [parcelFetchLoading, setParcelFetchLoading] = useState(false);

  const [drawerOpen, setDrawerOpen] = useState(false);
  const [parcelInfo, setParcelInfo] = useState(null);
  const [selectedProperty, setSelectedProperty] = useState(null);
  const [selectedParcel, setSelectedParcel] = useState(geojsonTemplate);
  const [dimensionLabels, setDimensionLabels] = useState(geojsonTemplate);
  const [perimeterFeet, setPerimeterFeet] = useState(0);

  const selectedParcelStyle = {
    id: "selectedParcelStyle",
    type: "line",
    minzoom: minZoom,
    layout: {
      visibility: "visible",
    },
    paint: {
      "line-width": 4,
      "line-color": "#FFCC00", // Bright yellow color, you can change this
      "line-opacity": 0.9,
    },
  };

  // Add fill style for selected parcel (optional)
  const selectedParcelFillStyle = {
    id: "selectedParcelFillStyle",
    type: "fill",
    minzoom: minZoom,
    paint: {
      "fill-color": "#FFCC00",
      "fill-opacity": 0.3,
    },
  };

  regridIsHovered = useRef(false);
  const popupRootRef = useRef(null);
  const popupNodeRef = useRef(null); // Store popup node for re-rendering
  const [token, setToken] = useState(null);
  const calculateBoundaryDimensions = (geojson) => {
    if (geojson.geometry.type !== "Polygon")
      return { labels: featureCollection([]), perimeterFeet: 0 };

    const simplifiedGeojson = simplify(geojson, { tolerance: 0.00001, highQuality: true });
    const coordinates = simplifiedGeojson.geometry.coordinates[0];
    const labelFeatures = [];
    let totalPerimeterFeet = 0;
    let i = 0;

    while (i < coordinates.length - 1) {
      const start = coordinates[i];
      let end = coordinates[i + 1];
      let segment = lineString([start, end]);
      let segmentLengthFeet = length(segment, { units: "feet" });
      let segmentBearing = bearing(point(start), point(end));

      let curveCoords = [start, end];
      let totalLengthFeet = segmentLengthFeet;
      let bearings = [segmentBearing];
      let j = i + 1;

      while (j < coordinates.length - 1) {
        const nextStart = coordinates[j];
        const nextEnd = coordinates[j + 1];
        const nextSegment = lineString([nextStart, nextEnd]);
        const nextLengthFeet = length(nextSegment, { units: "feet" });
        const nextBearing = bearing(point(nextStart), point(nextEnd));

        if (nextLengthFeet < 10 && Math.abs(nextBearing - bearings[bearings.length - 1]) < 30) {
          curveCoords.push(nextEnd);
          totalLengthFeet += nextLengthFeet;
          bearings.push(nextBearing);
          j++;
        } else {
          break;
        }
      }

      totalPerimeterFeet += totalLengthFeet;

      if (curveCoords.length > 2) {
        const curve = lineString(curveCoords);
        const curveMidpoint = along(curve, totalLengthFeet / 2, { units: "feet" });

        labelFeatures.push({
          type: "Feature",
          geometry: curveMidpoint.geometry,
          properties: {
            length: `${Math.round(totalLengthFeet)} ft`,
            rotation: 0,
          },
        });

        i = j;
      } else {
        const midPoint = midpoint(point(start), point(end));

        labelFeatures.push({
          type: "Feature",
          geometry: midPoint.geometry,
          properties: {
            length: `${Math.round(segmentLengthFeet)} ft`,
            rotation: 0,
          },
        });

        i++;
      }
    }

    return { labels: featureCollection(labelFeatures), perimeterFeet: totalPerimeterFeet };
  };

  useEffect(() => {
    if (!map) return;
    console.log("drawingMode", { drawingMode, parcelOutputMode });

    const mouseMove = (e) => {
      // hide hover popup for ILE
      if (user && user?.userGroup && user?.userGroup?.includes("ILE")) {
        return;
      }

      const features = map.queryRenderedFeatures(e.point, {
        layers: [fillStyle.id],
      });

      if (features.length > 0 && !(drawingMode || parcelOutputMode)) {
        const { _x, _y, _z } = features[0];
        setHoverParcel(features[0]._vectorTileFeature.toGeoJSON(_x, _y, _z));

        const property = features[0].properties;

        if (!parcelHovered.current && !airBnBHovered.current) {
          const htmlRender = getTooltipHTML(property);
          hoverPopup.setLngLat(e.lngLat).setHTML(htmlRender).addTo(map);
          regridIsHovered.current = true;
        } else {
          hoverPopup.remove();
          regridIsHovered.current = false;
        }
      } else {
        hoverPopup.remove();
        regridIsHovered.current = false;
        setHoverParcel(geojsonTemplate);
      }
    };

    const mouseLeave = () => {
      hoverPopup.remove();
      regridIsHovered.current = false;
      setHoverParcel(geojsonTemplate);
    };

    const click = (e) => {
      if (!parcelMode) return;
      if (e.features.length === 0) return;

      console.log("click", e.features[0]);

      const { _x, _y, _z } = e.features[0];
      const clickedParcelGeoJSON = e.features[0]._vectorTileFeature.toGeoJSON(_x, _y, _z);

      setSelectedParcel(clickedParcelGeoJSON);
      const { labels, perimeterFeet } = calculateBoundaryDimensions(clickedParcelGeoJSON);
      setDimensionLabels(labels);
      setPerimeterFeet(perimeterFeet);

      const parcelCentroid = centroid(clickedParcelGeoJSON);
      const parcelBbox = bbox(clickedParcelGeoJSON);
      const centroidLngLat = parcelCentroid.geometry.coordinates;
      const offsetLng = (parcelBbox[2] - parcelBbox[0]) * 0.1;
      const popupLngLat = [centroidLngLat[0] + offsetLng, centroidLngLat[1]];

      const popupNode = document.createElement("div");
      popupNodeRef.current = popupNode;
      const root = createRoot(popupNode);
      popupRootRef.current = root;
      root.render(
        <AreaPopupContent geojson={clickedParcelGeoJSON} perimeterFeet={perimeterFeet} />
      );

      areaPopup.setLngLat(popupLngLat).setDOMContent(popupNode).addTo(map);

      dispatch({
        type: "Map/saveState",
        payload: {
          mapExpandedView: true,
        },
      });
      map.fire("mapExpandedView", {
        payload: { mapExpandedView: !mapExpandedView },
      });
      setSelectedProperty(e.features[0].properties);
      openOwnerModal(e.features[0].properties);
    };

    if (!parcelMode) {
      onDrawerClose();
    }

    const checkForLatestToken = async () => {
      const newToken = await window.spatiallaser.getUserToken("access");
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    map.on("moveend", () => {
      const data = map.querySourceFeatures(sourceId, {
        sourceLayer: sourceLayer,
      });
      console.log("data", data);
    });

    checkForLatestToken();
    map.on("movestart", checkForLatestToken);
    map.on("mousemove", fillStyle.id, mouseMove);
    map.on("mouseleave", fillStyle.id, mouseLeave);
    map.on("click", fillStyle.id, click);

    return () => {
      map.off("movestart", checkForLatestToken);
      map.off("mousemove", fillStyle.id, mouseMove);
      map.off("mouseleave", fillStyle.id, mouseLeave);
      map.off("click", fillStyle.id, click);
      areaPopup.remove();
      if (popupRootRef.current) {
        popupRootRef.current.unmount();
        popupRootRef.current = null;
      }
      popupNodeRef.current = null;
    };
  }, [map, parcelMode, drawingMode, parcelOutputMode]);

  const fetchParcelData = async (property) => {
    setParcelFetchLoading(true);
    try {
      const { ll_uuid } = property;
      const response = ll_uuid ? await getParcelDetailData({ ll_uuid: ll_uuid }) : null;

      if (response && !isEmpty(response)) {
        // Show initial parcel data immediately
        setParcelInfo({
          parcel: organizeParcelData(response),
          propertyTax: null,
          enhancedOwnership: null,
          ownersOtherParcels: null,
        });
        setParcelFetchLoading(false);

        // Then fetch tax data and owner parcels in background
        Promise.all([
          // getPropertyTaxData({
          //   lat: response?.fields?.lat,
          //   lng: response?.fields?.lon,
          // }),
          getOwnerOtherParcels({
            lat: response?.fields?.lat,
            lng: response?.fields?.lon,
          }),
          map.flyTo({
            center: [response?.fields?.lon, response?.fields?.lat],
            offset: [map.getContainer().clientWidth / 4, 0], // Positive x offset shifts viewport left, placing the target on right
            essential: true,
          }),
        ]).then(([taxResponse, otherParcelsResponse]) => {
          setParcelInfo((prev) => ({
            ...prev,
            // propertyTax: taxResponse[0]?.tax_rate_percent ?? null,
            ownersOtherParcels: otherParcelsResponse,
          }));
        });
      } else {
        setParcelInfo(null);
        setParcelFetchLoading(false);
      }
    } catch (err) {
      console.error(err);
      setParcelInfo(null);
      setParcelFetchLoading(false);
    }
  };

  const openOwnerModal = (property) => {
    if (!property || !parcelMode) return;

    setDrawerOpen(true);
    setParcelFetchLoading(true);
    fetchParcelData(property);
  };
  const onDrawerClose = () => {
    setParcelInfo(null);
    setDrawerOpen(false);
    setSelectedProperty(null);
    setSelectedParcel(geojsonTemplate);
    setDimensionLabels(geojsonTemplate);
    setPerimeterFeet(0);
    areaPopup.remove();
    if (popupRootRef.current) {
      popupRootRef.current.unmount();
      popupRootRef.current = null;
    }
    popupNodeRef.current = null;
  };

  const onExportToCSV = () => {
    if (!parcelInfo || !parcelInfo.parcel || parcelInfo.parcel.length === 0) {
      alert("No parcel data available to export to csv.");
      return;
    }

    const processNestedObjects = (obj, parentKey = "") => {
      let entries = [];
      for (const [key, value] of Object.entries(obj)) {
        if (value && typeof value === "object" && !Array.isArray(value)) {
          entries = [...entries, ...processNestedObjects(value, key)];
        } else {
          entries.push([key, value]);
        }
      }
      return entries;
    };

    const { parcel } = parcelInfo;
    let content = "";

    const entries = processNestedObjects(parcel);
    entries.forEach(([key, value]) => {
      let formattedValue = value;
      if (typeof value === "string") {
        formattedValue = `"${value.replace(/"/g, '""')}"`;
      }
      content += `${key},${formattedValue}\n`;
    });

    const blob = new Blob([content], { type: "text/csv;charset=utf-8;" });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `Parcel_Info_${new Date().toISOString()}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (!token || !map) return null;

  return (
    <>
      <Source id={sourceId} type="vector" tiles={[getTileURL(serverType, token)]}>
        {currentMapLayerOptions.includes("zoning types") && (
          <>
            <Layer {...labelStyle} />
            <Layer {...fillStyleZoning} />
          </>
        )}
        {/* {!currentMapLayerOptions.includes("zoning types") && ( */}
        <Layer {...fillStyle} />
        {/* )} */}
        <Layer {...lineStyle} />
      </Source>
      {parcelMode && (
        <Source id="hoveredParcel" type="geojson" data={hoverParcel}>
          <Layer {...hoveredParcelLineStyle} />
        </Source>
      )}
      {parcelMode && (
        <Source id="selectedParcel" type="geojson" data={selectedParcel}>
          <Layer {...selectedParcelFillStyle} />
          <Layer {...selectedParcelStyle} />
        </Source>
      )}
      {parcelMode && (
        <Source id="dimensionLabels" type="geojson" data={dimensionLabels}>
          <Layer {...dimensionLabelStyle} />
        </Source>
      )}

      {parcelMode && drawerOpen && (
        <Drawer
          title={
            <>
              <div className="relative flex flex-row justify-center items-center pt-3">
                <h2 className="text-black text-opacity-88 text-center text-lg font-bold leading-[28.29px]">
                  Parcel Info
                </h2>
                <Button
                  type="text"
                  id="close drawer"
                  onClick={() => setDrawerOpen(false)}
                  className="absolute right-0 top-0"
                >
                  <CloseOutlined />
                </Button>
              </div>
            </>
          }
          placement="left"
          closable={false}
          onClose={onDrawerClose}
          open={drawerOpen}
          getContainer={false}
          maskClosable={false}
          mask={false}
          // size={"large"}
          width={"415px"}
          // bodyStyle={{ position: "relative" }}
          styles={{ body: { position: "relative", padding: 0 } }}
        >
          {parcelFetchLoading && (
            <div className="flex justify-center items-center h-[300px]">
              <Spin tip="loading" size="large" />
            </div>
          )}

          {!parcelFetchLoading && !parcelInfo && (
            <div className="flex flex-col gap-[5px] justify-center items-center h-[300px]">
              <span>No data available.</span>
              <Button
                onClick={() => {
                  if (selectedProperty != null) fetchParcelData(selectedProperty);
                }}
              >
                Try Again?
              </Button>
            </div>
          )}

          <ParcelBreakdownProvider
            parcelFetchLoading={parcelFetchLoading}
            parcelInfo={parcelInfo}
            setParcelInfo={setParcelInfo}
            onDrawerClose={onDrawerClose}
          >
            <SearchBar />
            <div className="flex justify-center py-3">
              <Button id="parcel-breakdown-export-to-csv" onClick={onExportToCSV}>
                Export to CSV
              </Button>
            </div>
            <ParcelBreakDownContent />
          </ParcelBreakdownProvider>
        </Drawer>
      )}
    </>
  );
}

export default Regrid;
