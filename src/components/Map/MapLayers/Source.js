import React, { useRef, useEffect, useState, useMemo } from "react";
import isEqual from "lodash.isequal";
import { useMap } from "../MapProvider";

// REFERENCE: https://github.com/visgl/react-map-gl/blob/master/src/components/source.ts

let sourceCounter = 0;

function createSource(map, id, props) {
  if (map.style && map.style._loaded) {
    const options = { ...props };
    delete options.id;
    delete options.children;

    map.addSource(id, options);
    return map.getSource(id);
  }
  return null;
}

function updateSource(source, props, prevProps) {
  if (props.id !== prevProps.id) {
    throw new Error("source id changed");
  }
  if (props.type !== prevProps.type) {
    throw new Error("source type changed");
  }

  let changedKey = "";
  let changedKeyCount = 0;

  for (const key in props) {
    if (
      key !== "children" &&
      key !== "id" &&
      !isEqual(prevProps[key], props[key])
    ) {
      changedKey = key;
      changedKeyCount++;
    }
  }

  if (!changedKeyCount) {
    return;
  }

  const type = props.type;

  if (type === "geojson") {
    source.setData(props.data);
  } else if (type === "image") {
    source.updateImage({ url: props.url, coordinates: props.coordinates });
  } else if (
    (type === "canvas" || type === "video") &&
    changedKeyCount === 1 &&
    changedKey === "coordinates"
  ) {
    source.setCoordinates(props.coordinates);
  } else if (type === "vector" && "setUrl" in source) {
    switch (changedKey) {
      case "url":
        source.setUrl(props.url);
        break;
      case "tiles":
        source.setTiles(props.tiles);
        break;
      default:
    }
  } else {
    // eslint-disable-next-line
    console.warn(`Unable to update <Source> prop: ${changedKey}`);
  }
}

const Source = (props) => {
  const { map } = useMap();
  const propsRef = useRef(props);
  const [, setStyleLoaded] = useState(0);

  const id = useMemo(() => props.id || `jsx-source-${sourceCounter++}`, []);

  useEffect(() => {
    if (map) {
      const forceUpdate = () =>
        setTimeout(() => setStyleLoaded((version) => version + 1), 0);
      map.on("styledata", forceUpdate);
      forceUpdate();

      return () => {
        map.off("styledata", forceUpdate);
        if (map.style && map.style._loaded && map.getSource(id)) {
          const allLayers = map.getStyle().layers;
          if (allLayers) {
            for (const layer of allLayers) {
              if (layer.source === id) {
                map.removeLayer(layer.id);
              }
            }
          }
          map.removeSource(id);
        }
      };
    }
    return undefined;
  }, [map]);

  let source = map && map.style && map.getSource(id);
  if (source) {
    updateSource(source, props, propsRef.current);
  } else {
    if (map) {
      source = createSource(map, id, props);
    }
  }

  propsRef.current = props;

  // Map source id to layer source in child Layers
  return (
    (source &&
      React.Children.map(
        props.children,
        (child) =>
          child &&
          React.cloneElement(child, {
            source: id,
          })
      )) ||
    null
  );
};

export default Source;
