import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useSelector } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { MAP_LAYER_NAME_BASE } from "../../../constants";
import { rasterize, initPopup } from "../MapUtility/general";
import asset1 from "../../../assets/images/mapbox/patterns/asset-1.svg";
import asset2 from "../../../assets/images/mapbox/patterns/asset-2.svg";
import asset4 from "../../../assets/images/mapbox/patterns/asset-4.svg";
import asset5 from "../../../assets/images/mapbox/patterns/asset-5.svg";
import asset6 from "../../../assets/images/mapbox/patterns/asset-6.svg";
import { tileURLRoot } from "../../../services/data";

const sourceId = MAP_LAYER_NAME_BASE.floodZone;
const sourceLayer = "floodzone";
const sourceLayerLOMR = "floodzone-lomr";

const zoomLevelToLayer = 7;
const floodZoneDefaultOpacity = 0.5;
// const floodZoneSatelliteOpacity = 0.7;

let fillPattern = ["match", ["get", "fld_zone"]];
fillPattern.push("A", "high risk areas");
fillPattern.push("AE", "high risk areas");
fillPattern.push("AH", "high risk areas");
fillPattern.push("AO", "high risk areas");
fillPattern.push("A99", "high risk areas");
fillPattern.push("X", "moderate to low risk areas");
fillPattern.push("V", "high risk coastal areas");
fillPattern.push("VE", "high risk coastal areas");
fillPattern.push("D", "undetermined risk areas");
fillPattern.push("OPEN WATER", "other");
fillPattern.push("AREA NOT INCLUDED", "other");
fillPattern.push("#000");

export const EFFECTIVE_COLOR_LOMR = "#91cf60";
export const INCORPORATED_COLOR_LOMR = "#af8dc3";
export const SUPERSEDED_COLOR_LOMR = "#fc8d59";
export const OTHER_COLOR_LOMR = "#999999";
// export const OTHER_COLOR_LOMR = "red";

const fillStyle = {
  id: `${sourceId}LayerFill`,
  type: "fill",
  minzoom: zoomLevelToLayer,
  "source-layer": sourceLayer,
  paint: {
    "fill-opacity": floodZoneDefaultOpacity,
    "fill-pattern": fillPattern,
  },
};

const outlineStyle = {
  id: `${sourceId}LayerOutline`,
  type: "line",
  minzoom: zoomLevelToLayer,
  "source-layer": sourceLayer,
  paint: {
    "line-color": "#0D4F8B",
    "line-opacity": 0.5,
    "line-width": 2,
  },
};

const fillStyleLOMR = {
  id: `${sourceId}LayerFillLOMR`,
  type: "fill",
  minzoom: zoomLevelToLayer,
  "source-layer": sourceLayerLOMR,
  paint: {
    "fill-opacity": 0.5,
    "fill-color": [
      "match",
      ["get", "status"],
      "Effective",
      EFFECTIVE_COLOR_LOMR,
      "Incorporated",
      INCORPORATED_COLOR_LOMR,
      "Superseded",
      SUPERSEDED_COLOR_LOMR,
      OTHER_COLOR_LOMR,
    ],
  },
};

const outlineStyleLOMR = {
  id: `${sourceId}LayerOutlineLOMR`,
  type: "line",
  minzoom: zoomLevelToLayer,
  "source-layer": sourceLayerLOMR,
  paint: {
    "line-color": [
      "match",
      ["get", "status"],
      "Effective",
      EFFECTIVE_COLOR_LOMR,
      "Incorporated",
      INCORPORATED_COLOR_LOMR,
      "Superseded",
      SUPERSEDED_COLOR_LOMR,
      OTHER_COLOR_LOMR,
    ],
    "line-opacity": 0.8,
    "line-width": 5,
  },
};

const labelStyleLOMR = {
  id: `${sourceId}LayerLabelLOMR`,
  type: "symbol",
  minzoom: zoomLevelToLayer,
  "source-layer": sourceLayerLOMR,
  layout: {
    "text-field": ["get", "eff_date"],
    "text-variable-anchor": ["center"],
    "text-size": ["interpolate", ["linear"], ["zoom"], 4, 7, 14, 25],
    "text-font": ["Source Sans Pro Bold", "Open Sans Bold"],
  },
  paint: {
    "text-color": "#000",
    "text-opacity": 1,
    "text-halo-width": 2,
    "text-halo-color": "#fff",
  },
  filter: ["in", "$type", "Point"],
};

const getCategoryName = (zone) => {
  if (["A", "AE", "AH", "AO", "A99"].includes(zone)) {
    return "High Risk";
  } else if (["X"].includes(zone)) {
    return "Moderate to Low Risk";
  } else if (["V", "VE"].includes(zone)) {
    return "High Risk - Coastal";
  } else if (["D"].includes(zone)) {
    return "Undetermined Risk";
  } else if (["OPEN WATER", "AREA NOT INCLUDED"].includes(zone)) {
    return "Others";
  }
};

let floodZonePopup;
let floodZoneLOMRPopup;

const getTileURL = (serverType, token) =>
  `${tileURLRoot(
    "cma",
    serverType
  )}/flood-zone/tile/{z}/{x}/{y}.mvt?token=${token}`;

const getTileLOMRURL = (serverType, token) =>
  `${tileURLRoot(
    "cma",
    serverType
  )}/flood-zone-lomr/tile/{z}/{x}/{y}.mvt?token=${token}`;

function FloodZoneLayer() {
  const serverType = useSelector((state) => state.Configure.serverType);
  const map = useSelector((state) => state.Map.map);

  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const enableFloodZoneLOMRLayer = useSelector(
    (state) => state.Map.enableFloodZoneLOMRLayer
  );

  const [token, setToken] = useState(null);

  useEffect(() => {
    if (!map) return;

    const loadImage = () => {
      rasterize(
        "https://sl-img-client.s3.amazonaws.com/map/legend-flood-pattern-1.svg"
      ).then((res) => {
        map.loadImage(res, (err, img) => {
          if (map.hasImage("undetermined risk areas")) return;
          map.addImage("undetermined risk areas", img);
        });
      });
      rasterize(
        "https://sl-img-client.s3.amazonaws.com/map/legend-flood-pattern-2.svg"
      ).then((res) => {
        map.loadImage(res, (err, img) => {
          if (map.hasImage("moderate to low risk areas")) return;
          map.addImage("moderate to low risk areas", img);
        });
      });
      rasterize(
        "https://sl-img-client.s3.amazonaws.com/map/legend-flood-pattern-4.svg"
      ).then((res) => {
        map.loadImage(res, (err, img) => {
          if (map.hasImage("other")) return;
          map.addImage("other", img);
        });
      });
      rasterize(
        "https://sl-img-client.s3.amazonaws.com/map/legend-flood-pattern-5.svg"
      ).then((res) => {
        map.loadImage(res, (err, img) => {
          if (map.hasImage("high risk coastal areas")) return;
          map.addImage("high risk coastal areas", img);
        });
      });
      rasterize(
        "https://sl-img-client.s3.amazonaws.com/map/legend-flood-pattern-6.svg"
      ).then((res) => {
        map.loadImage(res, (err, img) => {
          if (map.hasImage("high risk areas")) return;
          map.addImage("high risk areas", img);
        });
      });
    };

    floodZonePopup = initPopup();
    floodZoneLOMRPopup = initPopup();

    const mouseMove = (e) => {
      if (floodZoneLOMRPopup.isOpen()) {
        floodZoneLOMRPopup.remove();
      }

      const feature = map.queryRenderedFeatures(e.point, {
        layers: [`${sourceId}LayerFill`],
      });
      const coordinates = [e.lngLat.lng, e.lngLat.lat];

      if (feature && feature.length > 0) {
        const objectid = feature[0].properties.objectid;
        const fld_zone = feature[0].properties.fld_zone;
        const zone_subty = feature[0].properties.zone_subty;

        const htmlRender = renderToString(
          <div style={{ padding: "10px", fontWeight: "500" }}>
            <h4 style={{ fontSize: "14px", fontWeight: "600", margin: 0 }}>
              {getCategoryName(fld_zone)}
            </h4>
            <p style={{ margin: 0 }}>Zone Type: {fld_zone}</p>
            {zone_subty && <p style={{ margin: 0 }}>{zone_subty}</p>}
          </div>
        );

        floodZonePopup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      } else {
        floodZonePopup.remove();
      }
    };

    const mouseLeave = () => {
      floodZonePopup.remove();
    };

    const mouseMoveLOMR = (e) => {
      if (floodZonePopup.isOpen()) {
        return;
      }
      const feature = map.queryRenderedFeatures(e.point, {
        layers: [`${sourceId}LayerFillLOMR`],
      });
      const coordinates = [e.lngLat.lng, e.lngLat.lat];

      if (feature && feature.length > 0) {
        const objectid = feature[0].properties.objectid;
        const eff_date = feature[0].properties.eff_date;
        const status = feature[0].properties.status;

        const htmlRender = renderToString(
          <div style={{ padding: "10px", fontWeight: "500" }}>
            <h4 style={{ fontSize: "14px", fontWeight: "600", margin: 0 }}>
              Letter of Map Revision
            </h4>
            <p style={{ margin: 0 }}>Status: {status}</p>
            <p style={{ margin: 0 }}>
              Date: {eff_date ? eff_date.split(" ")[0] : "N/A"}
            </p>
          </div>
        );

        floodZoneLOMRPopup
          .setLngLat(coordinates)
          .setHTML(htmlRender)
          .addTo(map);
      } else {
        floodZoneLOMRPopup.remove();
      }
    };

    const mouseLeaveLOMR = () => {
      floodZoneLOMRPopup.remove();
    };

    const checkForLatestToken = async () => {
      const newToken = await window.spatiallaser.getUserToken("access");
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    checkForLatestToken();
    map.on("style.load", loadImage);
    map.on("movestart", checkForLatestToken);
    map.on("mousemove", `${sourceId}LayerFill`, mouseMove);
    map.on("mouseleave", `${sourceId}LayerFill`, mouseLeave);
    map.on("mousemove", `${sourceId}LayerFillLOMR`, mouseMoveLOMR);
    map.on("mouseleave", `${sourceId}LayerFillLOMR`, mouseLeaveLOMR);

    map.on("error", (e) => console.log(e));
    return () => {
      map.off("movestart", checkForLatestToken);
      map.off("mousemove", `${sourceId}LayerFill`, mouseMove);
      map.off("mouseleave", `${sourceId}LayerFill`, mouseLeave);
      map.off("mousemove", `${sourceId}LayerFillLOMR`, mouseMoveLOMR);
      map.off("mouseleave", `${sourceId}LayerFillLOMR`, mouseLeaveLOMR);
    };
  }, [map, currentMapLayerOptions, token]);

  if (!token || !currentMapLayerOptions.includes("flood zone")) return null;

  return (
    <>
      {/* RASTER TILE */}
      {/* <Source
        id={sourceId}
        type="raster"
        tileSize={256}
        tiles={["http://localhost:8080/flood-zone/pngtest/{z}/{x}/{y}.png"]}
      >
        <Layer
          {...{
            id: `${sourceId}LayerFill`,
            type: "raster",
            // minzoom: zoomLevelToLayer,
            paint: {
              "raster-opacity": 0.8,
            },
          }}
        />
      </Source> */}
      <Source
        id={sourceId}
        type="vector"
        tiles={[getTileURL(serverType, token)]}
      >
        <Layer {...fillStyle} />
        <Layer {...outlineStyle} />
      </Source>
      {enableFloodZoneLOMRLayer && (
        <Source
          id={`${sourceId}LOMR`}
          type="vector"
          tiles={[getTileLOMRURL(serverType, token)]}
        >
          <Layer {...fillStyleLOMR} />
          <Layer {...outlineStyleLOMR} />
          <Layer {...labelStyleLOMR} />
        </Source>
      )}
    </>
  );
}

export default FloodZoneLayer;
