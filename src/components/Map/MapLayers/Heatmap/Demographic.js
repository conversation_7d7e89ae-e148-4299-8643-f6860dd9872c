import { useState, useEffect, useCallback, useRef } from "react";
import { renderToString } from "react-dom/server";
import { useSelector, useDispatch } from "react-redux";
import Source from "../Source";
import Layer from "../Layer";
import { initPopup } from "../../MapUtility/general";
import { MAP_LAYER_NAME_BASE, geojsonTemplate } from "../../../../constants";
import {
  getTitleForStudyType,
  formatStudyValue,
  getColorScoreForStudyType,
  heatmapDemographicsStudyTypes,
  heatmapSubmarketStudyTypes,
} from "../../MapControls/HeatmapMenu/utils";
import {
  getMLSListingSummaryPointWithinLayerData,
  getHeatmapDomainData,
} from "../../../../services/data";
import {
  getSubmarketPaintLayout,
  getDemographicPaintLayout,
  getCountryDemographicColorArray,
  getCountryDemographicScale,
  getCountrySubmarketColorArray,
  getCountrySubmarketScale,
  generateClassRange,
} from "../../../../utils/heatmapRanges";
import * as d3 from "d3";
import { setPaintPropertySafely } from "../../MapUtility/general";

const sourceId = MAP_LAYER_NAME_BASE.heatmapDemographic;
const sourceLayer = "BG-1nqtgi";
const minZoom = 9;

let popup = initPopup();
let hoveredID = null;
let sourceLoaded = false;

const fillStyle = {
  id: `${sourceId}Style`,
  type: "fill",
  "source-layer": sourceLayer,
  minzoom: minZoom,
  paint: {
    "fill-opacity": 0,
  },
  beforeId: "circleFill",
};

function Demographic() {
  const map = useSelector((state) => state.Map.map);
  const heatmapType = useSelector((state) => state.Map.heatmapType);
  const heatmapDemographicsStudyType = useSelector(
    (state) => state.Map.heatmapDemographicsStudyType
  );
  const heatmapDemographicsData = useSelector(
    (state) => state.Map.heatmapDemographicsData
  );
  const heatmapBGFilters = useSelector((state) => state.Map.heatmapBGFilters);
  const combineAllDemographicsFilters = useSelector(
    (state) => state.Map.combineAllDemographicsFilters
  );
  const heatmapRangeColorType = useSelector(
    (state) => state.Map.heatmapRangeColorType
  );
  const currentDemographicColorScale = useSelector(
    (state) => state.Map.currentDemographicColorScale
  );
  const heatmapBlockGroupMap = useSelector(
    (state) => state.Map.heatmapBlockGroupMap
  );
  const heatmapColorScale = useSelector((state) => state.Map.heatmapColorScale);
  const heatmapOpacity = useSelector((state) => state.Map.heatmapOpacity);

  const dispatch = useDispatch();

  const [demographicDataMap, setDemographicDataMap] = useState(new Map());
  const [currentMetro, setCurrentMetro] = useState(null);

  useEffect(() => {
    if (!map) return;

    popup.remove();
    if (heatmapType !== "demographics" && heatmapDemographicsData.length > 0) {
      dispatch({
        type: "Map/saveState",
        payload: { heatmapDemographicsData: [] },
      });
    }

    const passedAnyFilter = (data) => {
      if (combineAllDemographicsFilters) {
        const filterResults = Object.keys(heatmapBGFilters).map((key) => {
          const filter = heatmapBGFilters[key];

          return filter.active
            ? data[key] >= filter.min && data[key] <= filter.max
            : null;
        });

        // true = pass, false = fail, null = not active
        if (!filterResults.includes(false)) {
          return true;
        } else {
          return false;
        }
      } else {
        const filter = heatmapBGFilters[heatmapDemographicsStudyType];
        if (filter && filter.active) {
          if (
            data[heatmapDemographicsStudyType] >= filter.min &&
            data[heatmapDemographicsStudyType] <= filter.max
          ) {
            return true;
          } else {
            return false;
          }
        }
        return true;
      }
    };

    const getMetro = async () => {
      const mapCenter = map.getCenter();
      const response = await getMLSListingSummaryPointWithinLayerData({
        lat: mapCenter.lat,
        lng: mapCenter.lng,
      });
      if (response && response.length > 0) {
        const metro = response.filter((item) => item.type === "metro");
        if (metro && metro.length > 0) {
          if (
            currentMetro &&
            currentMetro.key === metro[0].key &&
            currentMetro.name === metro[0].name
          ) {
            return;
          }
          setCurrentMetro(metro[0]);
        }
      }
    };

    const sourceDataLoading = (e) => {
      if (!heatmapType || heatmapType != "demographics") return;

      if (e.sourceId === sourceId) {
        sourceLoaded = false;
      }
    };

    const sourceData = (e) => {
      if (!heatmapType || heatmapType != "demographics") return;

      if (e.isSourceLoaded && e.sourceId === sourceId && !sourceLoaded) {
        const features = map.querySourceFeatures(sourceId, {
          sourceLayer: sourceLayer,
        });
        if (features.length > 0) {
          const ids = features.map((feature) => feature.properties.Name);
          const mapCenter = map.getCenter();

          dispatch({
            type: "Map/getHeatmapDemographics",
            payload: {
              body: JSON.stringify(ids),
              lat: mapCenter.lat,
              lng: mapCenter.lng,
            },
          });
          getMetro();
        } else {
          dispatch({
            type: "Map/saveState",
            payload: { heatmapDemographicsData: [] },
          });
        }
        sourceLoaded = true;
      }
    };

    const mouseMove = (e) => {
      try {
        if (!heatmapType || heatmapType != "demographics") return;

        if (e.features.length > 0) {
          if (hoveredID !== null) {
            map.setFeatureState(
              { id: hoveredID, source: sourceId, sourceLayer: sourceLayer },
              { hover: false }
            );
          }
          // console.log(e.features[0]);
          hoveredID = e.features[0].id;
          map.setFeatureState(
            { id: hoveredID, source: sourceId, sourceLayer: sourceLayer },
            { hover: true }
          );

          let demographicValue = "";
          let demographicFilterPassed = true;
          let demoColorCode = null;

          if (heatmapType === "demographics") {
            const data = heatmapBlockGroupMap[e.features[0].properties.Name];
            if (data) {
              demographicValue =
                data.getAllData()[heatmapDemographicsStudyType];
              demographicFilterPassed = passedAnyFilter({
                ...data.getAllData(),
                combinedFilterScore: data.combinedFilterScore,
              });
              const demoColorScale = currentDemographicColorScale;
              demoColorCode = demoColorScale
                ? demoColorScale
                    .range()
                    .indexOf(demoColorScale(demographicValue)) + 1
                : null;
            }
          }

          let combinedScore = 0;

          if (combineAllDemographicsFilters) {
            // find active filters
            const activeFilters = Object.keys(heatmapBGFilters).filter(
              (key) =>
                key != "combinedFilterScore" &&
                heatmapBGFilters[key].active &&
                heatmapDemographicsStudyTypes.includes(key)
            );
            const data = heatmapBlockGroupMap[e.features[0].properties.Name];
            if (data) {
              let scoreSum = 0;

              for (let i = 0; i < activeFilters.length; i++) {
                const value = data.getAllData()[activeFilters[i]];
                const scale = heatmapColorScale[activeFilters[i]];
                const score = scale
                  ? scale.range().indexOf(scale(value)) + 1
                  : 0;
                scoreSum += score;
              }

              combinedScore = scoreSum / activeFilters.length;
              combinedScore =
                combinedScore % 1 === 0
                  ? combinedScore
                  : combinedScore.toFixed(1);
            }
          }

          if (demographicFilterPassed) {
            const coordinates = [e.lngLat.lng, e.lngLat.lat];
            const htmlRender = renderToString(
              <div style={{ padding: "10px" }}>
                <div>
                  <span>
                    Id: <strong>{e.features[0].properties.Name}</strong>
                  </span>
                </div>
                {heatmapDemographicsStudyType != "combinedFilterScore" && (
                  <div>
                    <strong>
                      {getTitleForStudyType(heatmapDemographicsStudyType)}:{" "}
                    </strong>
                    <strong>
                      {formatStudyValue(
                        heatmapDemographicsStudyType,
                        demographicValue
                      )}
                    </strong>
                  </div>
                )}
                {heatmapDemographicsStudyTypes
                  .filter(
                    (type) =>
                      type != heatmapDemographicsStudyType &&
                      type != "combinedFilterScore"
                  )
                  .map((type) => {
                    const data = demographicDataMap.get(
                      e.features[0].properties.Name
                    );
                    if (data) {
                      return (
                        <div key={type}>
                          <span>{getTitleForStudyType(type)}: </span>
                          <strong>{formatStudyValue(type, data[type])}</strong>
                        </div>
                      );
                    }
                  })}
                <div>
                  <span>Color Score: </span>
                  <strong>{demoColorCode}</strong>
                </div>
                {combineAllDemographicsFilters && (
                  <div>
                    <span>Combined score: </span>
                    <strong>{combinedScore}</strong>
                  </div>
                )}
              </div>
            );

            popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
          }
        }
      } catch (err) {
        console.log(err);
      }
    };

    const mouseLeave = (e) => {
      if (hoveredID !== null) {
        map.setFeatureState(
          { id: hoveredID, source: sourceId, sourceLayer: sourceLayer },
          { hover: false }
        );
      }
      hoveredID = null;
      popup.remove();
    };

    map.on("sourcedataloading", sourceDataLoading);
    map.on("sourcedata", sourceData);
    map.on("mousemove", fillStyle.id, mouseMove);
    map.on("mouseleave", fillStyle.id, mouseLeave);
    return () => {
      map.off("sourcedataloading", sourceDataLoading);
      map.off("sourcedata", sourceData);
      map.off("mousemove", fillStyle.id, mouseMove);
      map.off("mouseleave", fillStyle.id, mouseLeave);
    };
  }, [
    map,
    heatmapType,
    heatmapBGFilters,
    heatmapDemographicsStudyType,
    demographicDataMap,
    currentDemographicColorScale,
    heatmapBlockGroupMap,
    combineAllDemographicsFilters,
  ]);

  useEffect(() => {
    if (!heatmapType || heatmapType != "demographics") return;
    if (heatmapDemographicsStudyType === "combinedFilterScore") return;

    const getHeatmapDomainValues = async () => {
      const mapCenter = map.getCenter();
      let response = [];

      if (heatmapDemographicsStudyType === "rent_vs_own") {
        // median_home_value
        // median_rent

        const home_sale = await getHeatmapDomainData({
          type: "median_home_value",
          lat: mapCenter.lat,
          lng: mapCenter.lng,
        });
        const home_rent = await getHeatmapDomainData({
          type: "median_rent",
          lat: mapCenter.lat,
          lng: mapCenter.lng,
        });

        for (let i = 0; i < home_sale.length; i++) {
          for (let j = 0; j < home_rent.length; j++) {
            if (home_sale[i].block_group_id === home_rent[j].block_group_id) {
              const mortgage =
                home_sale[i].median_home_value * 0.965 * 0.006157172 + 600;
              const rent_vs_own = mortgage - home_rent[i].median_rent;
              if (rent_vs_own) {
                response.push(rent_vs_own);
              }
            }
          }
        }
      } else if (
        heatmapDemographicsStudyType === "median_home_value" ||
        heatmapDemographicsStudyType === "median_rent"
      ) {
        const home_rent_price = await getHeatmapDomainData({
          type: heatmapDemographicsStudyType,
          lat: mapCenter.lat,
          lng: mapCenter.lng,
        });

        response = home_rent_price.map((d) => d[heatmapDemographicsStudyType]);
      } else {
        response = await getHeatmapDomainData({
          type: heatmapDemographicsStudyType,
          lat: mapCenter.lat,
          lng: mapCenter.lng,
        });
      }

      dispatch({
        type: "Map/saveState",
        payload: {
          heatmapRangeColorType: {
            ...heatmapRangeColorType,
            [heatmapDemographicsStudyType]: {
              ...heatmapRangeColorType[heatmapDemographicsStudyType],
              metroDomain: response,
            },
          },
        },
      });
    };

    getHeatmapDomainValues();
  }, [heatmapType, heatmapDemographicsStudyType, currentMetro]);

  useEffect(() => {
    if (heatmapDemographicsData.length > 0) {
      const rangeColorType =
        heatmapRangeColorType[heatmapDemographicsStudyType];

      if (
        heatmapDemographicsStudyType != "combinedFilterScore" &&
        rangeColorType.rangeType === "metro" &&
        (!heatmapRangeColorType[heatmapDemographicsStudyType].metroDomain ||
          heatmapRangeColorType[heatmapDemographicsStudyType].metroDomain
            .length === 0)
      ) {
        return;
      }
      const demoMap = new Map();
      const expression = ["match", ["get", "Name"]];

      let bluesequential = d3.scaleSequential(d3.interpolateBlues);

      const colorArray =
        rangeColorType.colorType === "default"
          ? getCountryDemographicColorArray(heatmapDemographicsStudyType)
          : generateClassRange(10).map((q) => bluesequential(q));

      // const domain = heatmapDemographicsData.map(
      //   (d) => d[heatmapDemographicsStudyType]
      // );

      const colorScale =
        rangeColorType.rangeType === "country"
          ? getCountryDemographicScale(heatmapDemographicsStudyType, colorArray)
          : d3
              .scaleQuantile()
              // .domain([minStudyVal, maxStudyVal])
              .domain(
                heatmapRangeColorType[heatmapDemographicsStudyType].metroDomain
              )
              .range(colorArray);

      // console.log('heatmapDemographicsStudyType: ', heatmapDemographicsStudyType);
      // console.log('colorScale: 4.5 - ', colorScale(4.5));
      // console.log('colorScale: 5.5 - ', colorScale(5.5));

      const passedAnyFilter = (data) => {
        if (combineAllDemographicsFilters) {
          const filterResults = Object.keys(heatmapBGFilters).map((key) => {
            const filter = heatmapBGFilters[key];

            return filter.active
              ? data[key] >= filter.min && data[key] <= filter.max
              : null;
          });

          // true = pass, false = fail, null = not active
          if (!filterResults.includes(false)) {
            return true;
          } else {
            return false;
          }
        } else {
          const filter = heatmapBGFilters[heatmapDemographicsStudyType];
          if (filter && filter.active) {
            if (
              data[heatmapDemographicsStudyType] >= filter.min &&
              data[heatmapDemographicsStudyType] <= filter.max
            ) {
              return true;
            } else {
              return false;
            }
          }
          return true;
        }
      };

      for (let i = 0; i < heatmapDemographicsData.length; i++) {
        // const data = heatmapDemographicsData[i];
        const blockgroup = heatmapBlockGroupMap[heatmapDemographicsData[i].id];
        if (blockgroup) {
          const data = {
            ...blockgroup.getAllData(),
            combinedFilterScore: blockgroup.combinedFilterScore,
          };
          // mapbox style match get does not work when duplicates exist
          if (expression.includes(data.id)) continue;

          expression.push(data.id);
          const color = colorScale(data[heatmapDemographicsStudyType]);

          if (passedAnyFilter(data)) {
            expression.push(color != null ? color : "transparent");
          } else {
            expression.push("transparent");
          }
          const { id, ...items } = data;
          demoMap.set(id, items);
        }
      }

      setDemographicDataMap(demoMap);

      dispatch({
        type: "Map/saveState",
        payload: {
          currentDemographicColorScale: colorScale,
          heatmapColorScale: {
            ...heatmapColorScale,
            [heatmapDemographicsStudyType]: colorScale,
          },
        },
      });

      // console.log("rangeType: ", rangeColorType.rangeType);
      // console.log("colorScale.quantiles(): ", colorScale.quantiles());
      // console.log("colorScale.quantiles(): ", colorScale.range());
      // console.log("colorScale(93000): ", colorScale(93000));

      expression.push("rgba(0,0,0,0)");
      if (expression.length > 3) {
        if (map.getLayer(fillStyle.id)) {
          setPaintPropertySafely(map, fillStyle.id, "fill-color", expression);
          setPaintPropertySafely(map, fillStyle.id, "fill-opacity", [
            "case",
            ["boolean", ["feature-state", "hover"], false],
            0.9,
            heatmapOpacity,
          ]);
        }
      }
    }
  }, [
    heatmapDemographicsData,
    heatmapDemographicsStudyType,
    heatmapBGFilters,
    combineAllDemographicsFilters,
    heatmapRangeColorType,
    heatmapBlockGroupMap,
  ]);

  useEffect(() => {
    if (!heatmapType || heatmapType != "demographics") return;

    if (map.getLayer(fillStyle.id)) {
      setPaintPropertySafely(map, fillStyle.id, "fill-opacity", [
        "case",
        ["boolean", ["feature-state", "hover"], false],
        0.9,
        heatmapOpacity,
      ]);
    }
  }, [heatmapOpacity]);

  if (heatmapType && heatmapType === "demographics") {
    return (
      <Source id={sourceId} type="vector" url="mapbox://sxbxchen.2zcnf2qd">
        <Layer {...fillStyle} />
      </Source>
    );
  }
  return null;
}

export default Demographic;
