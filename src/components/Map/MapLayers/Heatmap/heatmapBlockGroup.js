import {
  heatmapSubmarketStudyTypes,
  heatmapDemographicsStudyTypes,
} from "../../MapControls/HeatmapMenu/utils";

class HeatmapBlockGroup {
  id = null;
  demographicData = {};
  submarketData = {};
  combinedFilterScore = 0;

  validDemographicDataTypes = heatmapDemographicsStudyTypes;
  validSubmarketDataTypes = heatmapSubmarketStudyTypes;

  constructor(id) {
    this.id = id;
  }

  setDemographicData(data) {
    for (const key in data) {
      if (this.validDemographicDataTypes.includes(key)) {
        this.demographicData[key] = data[key];
      } else {
        // console.warn(`Invalid demographic data type: ${key}`);
      }
    }
  }

  setSubmarketData(data) {
    for (const key in data) {
      if (this.validSubmarketDataTypes.includes(key)) {
        this.submarketData[key] = data[key];
      } else {
        // console.warn(`Invalid submarket data type: ${key}`);
      }
    }
  }

  setCombinedFilterScore(score) {
    this.combinedFilterScore = score;
  }

  getDemographicData() {
    return this.demographicData;
  }

  getSubmarketData() {
    return this.submarketData;
  }

  getAllData() {
    return {
      id: this.id,
      ...this.demographicData,
      ...this.submarketData,
    };
  }
}

export default HeatmapBlockGroup;
