import { useState, useEffect, useCallback, useRef } from "react";
import { renderToString } from "react-dom/server";
import { useSelector, useDispatch } from "react-redux";
import Source from "../Source";
import Layer from "../Layer";
import { initPopup, setPaintPropertySafely } from "../../MapUtility/general";
import { MAP_LAYER_NAME_BASE, geojsonTemplate } from "../../../../constants";
import {
  getTitleForStudyType,
  formatStudyValue,
  getColorScoreForStudyType,
  heatmapDemographicsStudyTypes,
  heatmapSubmarketStudyTypes,
} from "../../MapControls/HeatmapMenu/utils";
import {
  getMLSListingSummaryPointWithinLayerData,
  getSubmarketDomainData,
  getSubmarketBGDomainData,
} from "../../../../services/data";
import {
  getSubmarketPaintLayout,
  getDemographicPaintLayout,
  getCountryDemographicColorArray,
  getCountryDemographicScale,
  getCountrySubmarketColorArray,
  getCountrySubmarketScale,
  generateClassRange,
} from "../../../../utils/heatmapRanges";
import * as d3 from "d3";

const sourceId = MAP_LAYER_NAME_BASE.heatmapSubmarket;
const sourceLayer = "BG-1nqtgi";
const minZoom = 9;
const blockGroupURL = "mapbox://sxbxchen.2zcnf2qd";

let popup = initPopup();
let hoveredID = null;
let sourceLoaded = false;

const fillStyle = {
  id: `${sourceId}Style`,
  type: "fill",
  minzoom: minZoom,
  paint: {
    "fill-opacity": 0,
  },
};

const fillStyleBG = {
  id: `${sourceId}Style`,
  type: "fill",
  "source-layer": sourceLayer,
  minzoom: minZoom,
  paint: {
    "fill-opacity": 0,
  },
  beforeId: "circleFill",
};

function Submarket() {
  const map = useSelector((state) => state.Map.map);
  const heatmapType = useSelector((state) => state.Map.heatmapType);
  const heatmapSubmarketStudyType = useSelector(
    (state) => state.Map.heatmapSubmarketStudyType
  );
  const heatmapSubmarketData = useSelector(
    (state) => state.Map.heatmapSubmarketData
  );
  const combineAllDemographicsFilters = useSelector(
    (state) => state.Map.combineAllDemographicsFilters
  );
  const heatmapRangeColorType = useSelector(
    (state) => state.Map.heatmapRangeColorType
  );

  const currentSubmarketColorScale = useSelector(
    (state) => state.Map.currentSubmarketColorScale
  );
  const submarketBGEnabled = useSelector(
    (state) => state.Map.submarketBGEnabled
  );
  const heatmapBGFilters = useSelector((state) => state.Map.heatmapBGFilters);
  const heatmapBlockGroupMap = useSelector(
    (state) => state.Map.heatmapBlockGroupMap
  );
  const heatmapColorScale = useSelector((state) => state.Map.heatmapColorScale);
  const heatmapOpacity = useSelector((state) => state.Map.heatmapOpacity);

  // console.log("heatmapSubmarketData:", heatmapSubmarketData);
  // console.log("submarketBGEnabled", submarketBGEnabled);

  // console.log("heatmapBGFilters: ", heatmapBGFilters);

  const dispatch = useDispatch();

  const [submarketGeojson, setSubmarketGeojson] = useState(geojsonTemplate);
  const [currentMetro, setCurrentMetro] = useState(null);
  const [submarketDataMap, setSubmarketDataMap] = useState(new Map());

  useEffect(() => {
    if (!map) return;

    popup.remove();
    if (!heatmapType || heatmapType != "submarket") {
      if (heatmapSubmarketData.length > 0) {
        dispatch({
          type: "Map/saveState",
          payload: { heatmapSubmarketData: [] },
        });
      }
      if (submarketGeojson.features.length > 0) {
        setSubmarketGeojson(geojsonTemplate);
      }
      setCurrentMetro(null);
    }

    const passedAnyFilter = (data) => {
      if (combineAllDemographicsFilters) {
        const filterResults = Object.keys(heatmapBGFilters).map((key) => {
          const filter = heatmapBGFilters[key];

          return filter.active
            ? data[key] >= filter.min && data[key] <= filter.max
            : null;
        });

        // true = pass, false = fail, null = not active
        if (!filterResults.includes(false)) {
          return true;
        } else {
          return false;
        }
      } else {
        const filter = heatmapBGFilters[heatmapSubmarketStudyType];
        if (filter && filter.active) {
          if (
            data[heatmapSubmarketStudyType] >= filter.min &&
            data[heatmapSubmarketStudyType] <= filter.max
          ) {
            return true;
          } else {
            return false;
          }
        }
        return true;
      }
    };

    const getMetro = async () => {
      const mapCenter = map.getCenter();
      const response = await getMLSListingSummaryPointWithinLayerData({
        lat: mapCenter.lat,
        lng: mapCenter.lng,
      });
      if (response && response.length > 0) {
        const metro = response.filter((item) => item.type === "metro");
        if (metro && metro.length > 0) {
          if (
            currentMetro &&
            currentMetro.key === metro[0].key &&
            currentMetro.name === metro[0].name
          ) {
            return;
          }
          setCurrentMetro(metro[0]);
        }
      }
    };

    const sourceDataLoading = (e) => {
      if (!heatmapType || heatmapType != "submarket") return;
      if (!submarketBGEnabled) return;

      if (e.sourceId === `${sourceId}BG`) {
        sourceLoaded = false;
      }
    };

    const sourceData = (e) => {
      // console.log("heatmapType: ", heatmapType);
      // console.log("submarketBGEnabled: ", submarketBGEnabled);
      if (!heatmapType || heatmapType != "submarket") return;
      if (!submarketBGEnabled) return;

      if (e.isSourceLoaded && e.sourceId === `${sourceId}BG` && !sourceLoaded) {
        const features = map.querySourceFeatures(`${sourceId}BG`, {
          sourceLayer: sourceLayer,
        });

        if (features.length > 0) {
          const ids = features.map((feature) => feature.properties.Name);
          // console.log("ids:", ids);
          dispatch({
            type: "Map/getHeatmapSubmarket",
            payload: {
              type: "bg",
              body: JSON.stringify(ids),
            },
          });
          getMetro();
        } else {
          dispatch({
            type: "Map/saveState",
            payload: { heatmapSubmarketData: [] },
          });
        }
        sourceLoaded = true;
      }
    };

    const moveEnd = () => {
      if (!heatmapType || heatmapType != "submarket") return;
      if (map.getZoom() < minZoom) return;
      if (submarketBGEnabled) return;

      const bounds = map.getBounds().toArray();
      dispatch({
        type: "Map/getHeatmapSubmarket",
        payload: {
          type: "default",
          lng1: bounds[0][1],
          lat1: bounds[0][0],
          lng2: bounds[1][1],
          lat2: bounds[1][0],
        },
      });
      getMetro();
    };

    const mouseMove = (e) => {
      if (!heatmapType || heatmapType != "submarket") return;

      // console.log(e.features[0]);
      // if (submarketBGEnabled) return;

      if (e.features.length > 0) {
        if (hoveredID !== null) {
          map.setFeatureState(
            submarketBGEnabled
              ? {
                  id: hoveredID,
                  source: `${sourceId}BG`,
                  sourceLayer: sourceLayer,
                }
              : { id: hoveredID, source: sourceId },
            { hover: false }
          );
        }

        hoveredID = e.features[0].id;
        map.setFeatureState(
          submarketBGEnabled
            ? {
                id: hoveredID,
                source: `${sourceId}BG`,
                sourceLayer: sourceLayer,
              }
            : { id: hoveredID, source: sourceId },
          { hover: true }
        );

        let submarketColorCode = null;
        let submarketFilterPassed = true;
        let data;
        data = submarketBGEnabled
          ? heatmapBlockGroupMap[e.features[0].properties.Name]
          : e.features[0].properties;

        if (data) {
          const submarketColorScale = currentSubmarketColorScale;
          if (submarketBGEnabled) {
            submarketFilterPassed = passedAnyFilter(data.getAllData());
            submarketColorCode = submarketColorScale
              ? submarketColorScale
                  .range()
                  .indexOf(
                    submarketColorScale(
                      data.getAllData()[heatmapSubmarketStudyType]
                    )
                  ) + 1
              : "N/A";
          } else {
            submarketColorCode = submarketColorScale
              ? submarketColorScale
                  .range()
                  .indexOf(
                    submarketColorScale(data[heatmapSubmarketStudyType])
                  ) + 1
              : "N/A";
          }
        } else {
          popup.remove();
          return;
        }

        if (
          submarketColorCode !== null &&
          ["vacancy_rate", "market_effective_rent_sf"].includes(
            heatmapSubmarketStudyType
          )
        ) {
          submarketColorCode = 10 - submarketColorCode + 1;
        }

        let combinedScore = 0;
        if (submarketBGEnabled && combineAllDemographicsFilters) {
          // find active filters
          const activeFilters = Object.keys(heatmapBGFilters).filter(
            (key) =>
              heatmapBGFilters[key].active &&
              heatmapSubmarketStudyTypes.includes(key)
          );
          const data = heatmapBlockGroupMap[e.features[0].properties.Name];
          if (data) {
            let scoreSum = 0;

            for (let i = 0; i < activeFilters.length; i++) {
              const value = data.getAllData()[activeFilters[i]];
              const scale = heatmapColorScale[activeFilters[i]];
              const score = scale ? scale.range().indexOf(scale(value)) + 1 : 0;

              if (
                ["vacancy_rate", "market_effective_rent_sf"].includes(
                  activeFilters[i]
                )
              ) {
                scoreSum += 10 - score + 1;
              } else {
                scoreSum += score;
              }
            }

            combinedScore = scoreSum / activeFilters.length;
            combinedScore =
              combinedScore % 1 === 0
                ? combinedScore
                : combinedScore.toFixed(1);
          }
        }

        if (submarketFilterPassed) {
          const coordinates = [e.lngLat.lng, e.lngLat.lat];
          const htmlRender = renderToString(
            <div style={{ padding: "10px" }}>
              {e.features[0].properties.geogname && (
                <div>
                  <span>
                    Location:{" "}
                    <strong>{e.features[0].properties.geogname}</strong>
                  </span>
                </div>
              )}
              {heatmapSubmarketStudyType != "combinedFilterScore" && (
                <div>
                  <strong>
                    {getTitleForStudyType(heatmapSubmarketStudyType)}:{" "}
                  </strong>
                  <strong>
                    {formatStudyValue(
                      heatmapSubmarketStudyType,
                      submarketBGEnabled
                        ? data.getAllData()[heatmapSubmarketStudyType]
                        : e.features[0].properties[heatmapSubmarketStudyType]
                    )}
                  </strong>
                </div>
              )}

              {heatmapSubmarketStudyTypes
                .filter((type) => type != heatmapSubmarketStudyType)
                .map((type) => {
                  const data = submarketBGEnabled
                    ? submarketDataMap.get(e.features[0].properties.Name)
                    : e.features[0].properties;
                  if (data) {
                    return (
                      <div key={type}>
                        <span>{getTitleForStudyType(type)}: </span>
                        <strong>
                          {formatStudyValue(
                            type,
                            data[type],
                            submarketBGEnabled ? true : false
                          )}
                        </strong>
                      </div>
                    );
                  }
                })}
              <div>
                <span>Color Score: </span>
                <strong>{submarketColorCode}</strong>
              </div>
              {submarketBGEnabled && combineAllDemographicsFilters && (
                <div>
                  <span>Combined score: </span>
                  <strong>{combinedScore}</strong>
                </div>
              )}
            </div>
          );
          if (heatmapType === "submarket") {
            popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
          }
        }
      }
    };

    const mouseLeave = (e) => {
      // if (submarketBGEnabled) return;
      if (hoveredID !== null) {
        map.setFeatureState(
          submarketBGEnabled
            ? {
                id: hoveredID,
                source: `${sourceId}BG`,
                sourceLayer: sourceLayer,
              }
            : { id: hoveredID, source: sourceId },
          { hover: false }
        );
      }
      hoveredID = null;
      popup.remove();
    };

    if (heatmapSubmarketData.length === 0) {
      moveEnd();
    }
    map.on("sourcedataloading", sourceDataLoading);
    map.on("sourcedata", sourceData);
    map.on("moveend", moveEnd);
    map.on(
      "mousemove",
      submarketBGEnabled ? fillStyleBG.id : fillStyle.id,
      mouseMove
    );
    map.on(
      "mouseleave",
      submarketBGEnabled ? fillStyleBG.id : fillStyle.id,
      mouseLeave
    );
    return () => {
      map.off("sourcedataloading", sourceDataLoading);
      map.off("sourcedata", sourceData);
      map.off("moveend", moveEnd);
      map.off(
        "mousemove",
        submarketBGEnabled ? fillStyleBG.id : fillStyle.id,
        mouseMove
      );
      map.off(
        "mouseleave",
        submarketBGEnabled ? fillStyleBG.id : fillStyle.id,
        mouseLeave
      );
    };
  }, [
    map,
    heatmapType,
    currentSubmarketColorScale,
    heatmapSubmarketStudyType,
    heatmapSubmarketData,
    submarketBGEnabled,
    submarketDataMap,
    combineAllDemographicsFilters,
    heatmapBGFilters,
    heatmapBlockGroupMap,
  ]);

  useEffect(() => {
    if (!heatmapType || heatmapType != "submarket" || !currentMetro) return;
    if (heatmapSubmarketStudyType === "combinedFilterScore") return;

    const getSubmarketDomain = async () => {
      const mapCenter = map.getCenter();

      let response;
      if (!submarketBGEnabled) {
        response = await getSubmarketDomainData({
          type: heatmapSubmarketStudyType,
          lat: mapCenter.lat,
          lng: mapCenter.lng,
        });
      } else {
        if (heatmapSubmarketStudyType !== "rental_growth_5_years") {
          response = await getSubmarketBGDomainData({
            type: heatmapSubmarketStudyType,
            lat: mapCenter.lat,
            lng: mapCenter.lng,
          });
        }
      }

      dispatch({
        type: "Map/saveState",
        payload: {
          heatmapRangeColorType: {
            ...heatmapRangeColorType,
            [heatmapSubmarketStudyType]: {
              ...heatmapRangeColorType[heatmapSubmarketStudyType],
              metroDomain: response || [],
            },
          },
        },
      });
    };

    getSubmarketDomain();
  }, [currentMetro, heatmapSubmarketStudyType, heatmapBlockGroupMap]);

  useEffect(() => {
    if (heatmapSubmarketData.length > 0) {
      const rangeColorType = heatmapRangeColorType[heatmapSubmarketStudyType];

      if (
        heatmapSubmarketStudyType != "combinedFilterScore" &&
        rangeColorType.rangeType === "metro" &&
        (!heatmapRangeColorType[heatmapSubmarketStudyType].metroDomain ||
          heatmapRangeColorType[heatmapSubmarketStudyType].metroDomain
            .length === 0)
      ) {
        return;
      }

      if (!submarketBGEnabled) {
        const geojson = structuredClone(geojsonTemplate);
        for (let i = 0; i < heatmapSubmarketData.length; i++) {
          const { geom, ...properties } = heatmapSubmarketData[i];
          geojson.features.push({
            id: i,
            type: "Feature",
            properties: properties,
            geometry: geom,
          });
        }
        setSubmarketGeojson(geojson);

        let bluesequential = d3.scaleSequential(d3.interpolateBlues);

        const colorArray =
          rangeColorType.colorType === "default"
            ? getCountrySubmarketColorArray(heatmapSubmarketStudyType)
            : generateClassRange(10).map((q) => bluesequential(q));

        const colorScale =
          rangeColorType.rangeType === "country"
            ? getCountrySubmarketScale(heatmapSubmarketStudyType, colorArray)
            : d3
                .scaleQuantile()
                // .domain([minStudyVal, maxStudyVal])
                .domain(
                  heatmapRangeColorType[heatmapSubmarketStudyType].metroDomain
                )
                .range(colorArray);

        let colorRanges = [];
        if (rangeColorType.rangeType === "country") {
          for (let i = 0; i < colorScale.quantiles().length; i++) {
            colorRanges.push(colorScale.quantiles()[i], colorScale.range()[i]);
          }
        } else {
          for (let i = 0; i < colorScale.quantiles().length; i++) {
            colorRanges.push(
              colorScale.quantiles()[i],
              colorScale.range()[i + 1]
            );
          }
        }

        const paintStyle = {
          "fill-color": [
            "interpolate",
            ["linear"],
            ["get", heatmapSubmarketStudyType],
            ...colorRanges,
          ],
          "fill-opacity": [
            "case",
            ["boolean", ["feature-state", "hover"], false],
            0.9,
            0.6,
          ],
        };
        fillStyle.paint = paintStyle;

        dispatch({
          type: "Map/saveState",
          payload: { currentSubmarketColorScale: colorScale },
        });
      } else {
        let bluesequential = d3.scaleSequential(d3.interpolateBlues);

        const colorArray =
          rangeColorType.colorType === "default"
            ? getCountrySubmarketColorArray(heatmapSubmarketStudyType)
            : generateClassRange(10).map((q) => bluesequential(q));

        const colorScale =
          rangeColorType.rangeType === "country"
            ? getCountrySubmarketScale(heatmapSubmarketStudyType, colorArray)
            : d3
                .scaleQuantile()
                // .domain([minStudyVal, maxStudyVal])
                .domain(
                  heatmapRangeColorType[heatmapSubmarketStudyType].metroDomain
                )
                .range(colorArray);

        const passedAnyFilter = (data) => {
          if (combineAllDemographicsFilters) {
            const filterResults = Object.keys(heatmapBGFilters).map((key) => {
              const filter = heatmapBGFilters[key];

              return filter.active
                ? data[key] >= filter.min && data[key] <= filter.max
                : null;
            });

            // true = pass, false = fail, null = not active
            if (!filterResults.includes(false)) {
              return true;
            } else {
              return false;
            }
          } else {
            const filter = heatmapBGFilters[heatmapSubmarketStudyType];
            if (filter && filter.active) {
              if (
                data[heatmapSubmarketStudyType] >= filter.min &&
                data[heatmapSubmarketStudyType] <= filter.max
              ) {
                return true;
              } else {
                return false;
              }
            }
            return true;
          }
        };

        const submarketMap = new Map();
        const expression = ["match", ["get", "Name"]];
        for (let i = 0; i < heatmapSubmarketData.length; i++) {
          const blockgroup = heatmapBlockGroupMap[heatmapSubmarketData[i].id];
          if (blockgroup) {
            const data = {
              ...blockgroup.getAllData(),
              combinedFilterScore: blockgroup.combinedFilterScore,
            };
            // mapbox style match get does not work when duplicates exist
            if (expression.includes(data.id)) continue;

            expression.push(data.id);
            const color = colorScale(data[heatmapSubmarketStudyType]);
            if (passedAnyFilter(data)) {
              expression.push(color != null ? color : "transparent");
            } else {
              expression.push("transparent");
            }
            const { id, ...items } = data;
            submarketMap.set(id, items);
          }
        }
        setSubmarketDataMap(submarketMap);
        dispatch({
          type: "Map/saveState",
          payload: {
            currentSubmarketColorScale: colorScale,
            heatmapColorScale: {
              ...heatmapColorScale,
              [heatmapSubmarketStudyType]: colorScale,
            },
          },
        });

        expression.push("rgba(0,0,0,0)");
        // expression.push("black");
        if (expression.length > 3) {
          if (map.getLayer(fillStyleBG.id)) {
            setPaintPropertySafely(
              map,
              fillStyleBG.id,
              "fill-color",
              expression
            );
            setPaintPropertySafely(map, fillStyleBG.id, "fill-opacity", [
              "case",
              ["boolean", ["feature-state", "hover"], false],
              0.9,
              heatmapOpacity,
            ]);
          }
        }
      }
    }
  }, [
    heatmapSubmarketData,
    heatmapSubmarketStudyType,
    heatmapRangeColorType,
    submarketBGEnabled,
    combineAllDemographicsFilters,
    heatmapBGFilters,
    heatmapBlockGroupMap,
  ]);

  useEffect(() => {
    if (!heatmapType || heatmapType != "submarket" || !submarketBGEnabled)
      return;

    if (map.getLayer(fillStyleBG.id)) {
      setPaintPropertySafely(map, fillStyleBG.id, "fill-opacity", [
        "case",
        ["boolean", ["feature-state", "hover"], false],
        0.9,
        heatmapOpacity,
      ]);
    }
  }, [heatmapOpacity]);

  if (heatmapType && heatmapType === "submarket") {
    return (
      <>
        {submarketBGEnabled && (
          <Source
            id={`${sourceId}BG`}
            type="vector"
            url="mapbox://sxbxchen.2zcnf2qd"
          >
            <Layer {...fillStyleBG} />
          </Source>
        )}
        {!submarketBGEnabled && (
          <Source id={sourceId} type="geojson" data={submarketGeojson}>
            <Layer {...fillStyle} />
          </Source>
        )}
        {/* <Source id={sourceId} type="vector" url="mapbox://sxbxchen.2zcnf2qd">
          <Layer {...fillStyle} />
        </Source> */}
      </>
    );
  }
  return null;
}

export default Submarket;
