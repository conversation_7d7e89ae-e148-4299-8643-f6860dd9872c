import { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import Demographic from "./Demographic";
import Submarket from "./Submarket";
import HeatmapBlockGroup from "./heatmapBlockGroup";
import { DEFAULT_HEATMAP_TYPES } from "../../../../constants";
import {
  heatmapDemographicsStudyTypes,
  heatmapSubmarketStudyTypes,
} from "../../MapControls/HeatmapMenu/utils";

function HeatmapLayer() {
  const map = useSelector((state) => state.Map.map);
  const heatmapType = useSelector((state) => state.Map.heatmapType);
  const submarketBGEnabled = useSelector(
    (state) => state.Map.submarketBGEnabled
  );
  const heatmapSubmarketData = useSelector(
    (state) => state.Map.heatmapSubmarketData
  );
  const heatmapDemographicsData = useSelector(
    (state) => state.Map.heatmapDemographicsData
  );
  const heatmapBlockGroupMap = useSelector(
    (state) => state.Map.heatmapBlockGroupMap
  );
  const combineAllDemographicsFilters = useSelector(
    (state) => state.Map.combineAllDemographicsFilters
  );
  const heatmapBGFilters = useSelector((state) => state.Map.heatmapBGFilters);
  const heatmapColorScale = useSelector((state) => state.Map.heatmapColorScale);
  const heatmapRangeColorType = useSelector(
    (state) => state.Map.heatmapRangeColorType
  );
  const heatmapDemographicsStudyType = useSelector(state => state.Map.heatmapDemographicsStudyType)
  const heatmapSubmarketStudyType = useSelector(state => state.Map.heatmapSubmarketStudyType)

  const dispatch = useDispatch();

  useEffect(() => {
    if (!map) return;

    const updateHeatmapType = ({ payload }) => {
      const type = payload.heatmapType;
      dispatch({ type: "Map/saveState", payload: { heatmapType: type } });
    };

    map.on("heatmap.updateType", updateHeatmapType);
    return () => {
      map.off("heatmap.updateType", updateHeatmapType);
    };
  }, [map]);

  useEffect(() => {
    // When closing the heatmap, reset the heatmap state
    if (!heatmapType) {
      dispatch({
        type: "Map/saveState",
        payload: {
          heatmapBlockGroupMap: {},
          submarketBGEnabled: false,
          heatmapSubmarketStudyType: "vacancy_rate",
          heatmapDemographicsStudyType: "median_hh_income",
          heatmapBGFilters: {},
          combineAllDemographicsFilters: false,
          heatmapRangeColorType: DEFAULT_HEATMAP_TYPES,
          heatmapColorScale: {},
          currentDemographicColorScale: null,
          currentSubmarketColorScale: null,
        },
      });
    }
  }, [heatmapType]);

  useEffect(() => {
    if (!heatmapType) return;
    if (heatmapType === "submarket" && !submarketBGEnabled) return;

    const blockGroupMap = { ...heatmapBlockGroupMap };

    for (let i = 0; i < heatmapSubmarketData.length; i++) {
      const data = heatmapSubmarketData[i];
      let blockgroup = blockGroupMap[data.id];
      if (blockgroup) {
        blockgroup.setSubmarketData(data);
      } else {
        blockgroup = new HeatmapBlockGroup(data.id);
        blockgroup.setSubmarketData(data);
      }
      blockGroupMap[data.id] = blockgroup;
    }

    for (let i = 0; i < heatmapDemographicsData.length; i++) {
      const data = heatmapDemographicsData[i];
      let blockgroup = blockGroupMap[data.id];
      if (blockgroup) {
        blockgroup.setDemographicData(data);
      } else {
        blockgroup = new HeatmapBlockGroup(data.id);
        blockgroup.setDemographicData(data);
      }
      blockGroupMap[data.id] = blockgroup;
    }

    if (heatmapSubmarketData.length > 0 || heatmapDemographicsData.length > 0) {
      dispatch({
        type: "Map/saveState",
        payload: {
          heatmapBlockGroupMap: blockGroupMap,
        },
      });
    }
  }, [heatmapSubmarketData, heatmapDemographicsData]);

  useEffect(() => {
    if (!heatmapType) return;
    if (heatmapType === "submarket" && !submarketBGEnabled) return;

    if (combineAllDemographicsFilters) {
      const blockGroupMap = { ...heatmapBlockGroupMap };

      if (heatmapType === "demographics") {
        const activeFilters = Object.keys(heatmapBGFilters)
          .filter(
            (key) =>
              heatmapBGFilters[key].active &&
              heatmapDemographicsStudyTypes.includes(key)
          )
          .filter((key) => key !== "combinedFilterScore");
        const features = map.querySourceFeatures("heatmapDemographic", {
          sourceLayer: "BG-1nqtgi",
        });
        // Ids in mapview
        const ids = features.map((feature) => feature.properties.Name);
        const quantileDomain = [];

        for (let i = 0; i < ids.length; i++) {
          const data = blockGroupMap[ids[i]];
          let scoreSum = 0;
          for (let j = 0; j < activeFilters.length; j++) {
            const value = data.getAllData()[activeFilters[j]];
            if (value === null) continue;

            const scale = heatmapColorScale[activeFilters[j]];
            const score = scale ? scale.range().indexOf(scale(value)) + 1 : 0;
            if (score != 0) {
            scoreSum += score;
            quantileDomain.push(score);
            } 
          }
          const combinedScore = scoreSum / activeFilters.length;
          if (combinedScore != 0) {
            data.setCombinedFilterScore(combinedScore);
          }
        }

        dispatch({
          type: "Map/saveState",

          payload: {
            heatmapRangeColorType: {
              ...heatmapRangeColorType,
              combinedFilterScore: {
                ...heatmapRangeColorType["combinedFilterScore"],
                metroDomain: quantileDomain,
              },
            },
          },
        });
      } else if (heatmapType === "submarket") {
        // find active filters
        const activeFilters = Object.keys(heatmapBGFilters)
          .filter(
            (key) =>
              heatmapBGFilters[key].active &&
              heatmapSubmarketStudyTypes.includes(key)
          )
          .filter((key) => key !== "combinedFilterScore");
        const features = map.querySourceFeatures("heatmapSubmarketBG", {
          sourceLayer: "BG-1nqtgi",
        });
        // Ids in mapview
        const ids = features.map((feature) => feature.properties.Name);
        const quantileDomain = [];

        for (let i = 0; i < ids.length; i++) {
          const data = blockGroupMap[ids[i]];
          if (!data) continue;
          let scoreSum = 0;
          for (let j = 0; j < activeFilters.length; j++) {
            const value = data.getAllData()[activeFilters[j]];
            if (value === null) continue;
            const scale = heatmapColorScale[activeFilters[j]];
            const score = scale ? scale.range().indexOf(scale(value)) + 1 : 0;

            if (
              ["vacancy_rate", "market_effective_rent_sf"].includes(
                activeFilters[j]
              )
            ) {
              scoreSum += 10 - score + 1;
              quantileDomain.push(10 - score + 1);
            } else {
              scoreSum += score;
              quantileDomain.push(score);
            }
          }
          const combinedScore = scoreSum / activeFilters.length;
          data.setCombinedFilterScore(combinedScore);
        }
        dispatch({
          type: "Map/saveState",

          payload: {
            heatmapRangeColorType: {
              ...heatmapRangeColorType,
              combinedFilterScore: {
                ...heatmapRangeColorType["combinedFilterScore"],
                metroDomain: quantileDomain,
              },
            },
          },
        });
      }

      dispatch({
        type: "Map/saveState",
        payload: {
          heatmapBlockGroupMap: {
            ...heatmapBlockGroupMap,
            ...blockGroupMap,
          },
        },
      });
    } else {
      dispatch({
        type: "Map/saveState",
        payload: {
          heatmapBGFilters: {
            ...heatmapBGFilters,
            combinedFilterScore: {
              ...heatmapBGFilters["combinedFilterScore"],
              active: false,
            },
          },
        },
      });
    }
  }, [combineAllDemographicsFilters, heatmapDemographicsStudyType, heatmapSubmarketStudyType]);

  // console.log("heatmapBlockGroupMap: ", heatmapBlockGroupMap);

  return (
    <>
      <Demographic />
      <Submarket />
    </>
  );
}

export default HeatmapLayer;
