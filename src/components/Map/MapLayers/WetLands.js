import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { zoomLevelToChangeBasemap } from "../Map";
import { LoadingOutlined } from "@ant-design/icons";
import { Spin } from "antd";

const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

const sourceId = "wetlands";

const minZoom = 9;
const zoomForMapServer = 14;

const layerStyle = {
  id: `${sourceId}LayerStyle`,
  type: "raster",
  source: sourceId,
  minzoom: minZoom,
  paint: {
    "raster-opacity": 0.5,
  },
};

const getWetLandsMapServerURL = (mapBounds, width, height) => {
  const url = new URL(
    // `https://fwsprimary.wim.usgs.gov/server/rest/services/Wetlands/MapServer/export`
    // `https://fwsprimary.wim.usgs.gov/server/rest/services/Test/Wetlands_gdb_split/MapServer/export`
    `https://fwspublicservices.wim.usgs.gov/wetlandsmapservice/rest/services/Wetlands/MapServer/export`
  );
  url.searchParams.append("dpi", "96");
  url.searchParams.append("transparent", "true");
  url.searchParams.append("format", "png8");
  url.searchParams.append(
    "bbox",
    `${mapBounds._sw.lng},${mapBounds._sw.lat},${mapBounds._ne.lng},${mapBounds._ne.lat}`
  );
  url.searchParams.append("bboxSR", "4326");
  url.searchParams.append("imageSR", "3857");
  url.searchParams.append("size", `${width},${height}`);
  url.searchParams.append("f", "image");
  return url;
};
const getWetLandsImageServerURL = (mapBounds, width, height) => {
  const url = new URL(
    // `https://fwsprimary.wim.usgs.gov/server/rest/services/Wetlands_Raster/ImageServer/exportImage`
    `https://fwspublicservices.wim.usgs.gov/wetlandsmapservice/rest/services/WetlandsRaster/ImageServer/exportImage`
  );

  url.searchParams.append("f", "image");
  url.searchParams.append(
    "bbox",
    `${mapBounds._sw.lng},${mapBounds._sw.lat},${mapBounds._ne.lng},${mapBounds._ne.lat}`
  );
  url.searchParams.append("imageSR", "3857");
  url.searchParams.append("bboxSR", "4326");
  url.searchParams.append("size", `${width},${height}`);
  return url;
};
const getImageCoordinates = (mapBounds) => {
  // Don't understand, got here through trial and error
  // Coordinates is bbox rotated and flipped
  const coordinates = [
    [mapBounds._sw.lng, mapBounds._ne.lat], // actual: bottom right, transform: top left
    [mapBounds._ne.lng, mapBounds._ne.lat], // actual: top right, transform: top right
    [mapBounds._ne.lng, mapBounds._sw.lat], // actual: top left, transform: bottom right
    [mapBounds._sw.lng, mapBounds._sw.lat], // actual: bottom left, transform: bottom left
  ];
  return coordinates;
};

function throttle(func, timeFrame) {
  var lastTime = 0;
  return function () {
    var now = Date.now();
    if (now - lastTime >= timeFrame) {
      func();
      lastTime = now;
    }
  };
}

function debounce(func, timeout = 1000) {
  let timer;
  return (...args) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(this, args);
    }, timeout);
  };
}

let loadingURL = null;

function WetLands() {
  const map = useSelector((state) => state.Map.map);
  const currentMapThemeOption = useSelector(
    (state) => state.Map.currentMapThemeOption
  );
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );

  const [initURL, setInitURL] = useState(null);
  const [initCoordinates, setInitCoordinates] = useState(null);
  const [layerTooltip, setLayerTooltip] = useState({
    display: true,
    currentZoom: null,
  });

  const loaderRef = useRef(null);
  // const currentMapThemeOptionf(currentMapThemeOption);
  // currentMapThemeOption = currentMapThemeOption;

  useEffect(() => {
    if (!map) return;

    const onceLoad = () => {
      const mapZoom = map.getZoom();
      const mapBounds = map.getBounds();
      const width = map.getContainer().clientWidth;
      const height = map.getContainer().clientHeight;

      // console.log("Wetlands onceLoad");
      let url;
      if (mapZoom < 14) {
        url = getWetLandsImageServerURL(mapBounds, width, height);
        setLayerTooltip({ display: true, currentZoom: mapZoom });
      } else {
        url = getWetLandsMapServerURL(mapBounds, width, height);
        setLayerTooltip({ display: false, currentZoom: mapZoom });
      }
      const coordinates = getImageCoordinates(mapBounds);

      setInitURL(url.toString());
      setInitCoordinates(coordinates);
    };

    const moveEnd = async () => {
      // console.log("Wetlands movened");
      const mapZoom = map.getZoom();
      const mapBounds = map.getBounds();
      const width = map.getContainer().clientWidth;
      const height = map.getContainer().clientHeight;

      let url;
      if (mapZoom < zoomForMapServer) {
        url = getWetLandsImageServerURL(mapBounds, width, height);
        setLayerTooltip({ display: true, currentZoom: mapZoom });
      } else {
        url = getWetLandsMapServerURL(mapBounds, width, height);
        setLayerTooltip({ display: false, currentZoom: mapZoom });
      }

      if (mapZoom < minZoom) return;

      loadingURL = url.toString();
      if (loaderRef.current) {
        loaderRef.current.style.display = "block";
      }

      const coordinates = getImageCoordinates(mapBounds);
      const source = map.getSource(sourceId);
      if (source) {
        if (currentMapThemeOption != "Automatic") {
          source.updateImage({ url: url.toString(), coordinates: coordinates });
        } else {
          if (mapZoom < zoomLevelToChangeBasemap) {
            if (map.getStyle().name === "Streets") {
              map.once("idle", () => {
                const source = map.getSource(sourceId);
                source.updateImage({
                  url: url.toString(),
                  coordinates: coordinates,
                });
              });
            } else {
              map.once("idle", () => {
                const source = map.getSource(sourceId);
                source.updateImage({
                  url: url.toString(),
                  coordinates: coordinates,
                });
              });
            }
          } else if (mapZoom >= zoomLevelToChangeBasemap) {
            if (map.getStyle().name === "Satellite Streets") {
              map.once("idle", () => {
                const source = map.getSource(sourceId);
                source.updateImage({
                  url: url.toString(),
                  coordinates: coordinates,
                });
              });
            } else {
              map.once("idle", () => {
                const source = map.getSource(sourceId);
                source.updateImage({
                  url: url.toString(),
                  coordinates: coordinates,
                });
              });
            }
          }
        }
      }
    };

    // const sourceDataLoading = (e) => {
    //   if (e.sourceId === sourceId) {
    //     if (e.source.url != loadingURL) {
    //       loadingURL = e.source.url;
    //       if (loaderRef.current) {
    //         loaderRef.current.style.display = "block";
    //       }
    //     }
    //   }
    // };

    const sourceData = (e) => {
      if (e.sourceId === sourceId) {
        if (e.source.url === loadingURL) {
          if (loaderRef.current) {
            loaderRef.current.style.display = "none";
          }
        }
      }
    };

    // map.on("zoomend", zoomEnd);

    if (currentMapLayerOptions.includes("wetlands")) {
      moveEnd();
    }

    map.once("load", onceLoad);
    // }
    map.on("moveend", moveEnd);
    // map.on("sourcedataloading", sourceDataLoading);
    map.on("sourcedata", sourceData);
    map.on("error", (e) => {
      if (!e.source || !e.source.url) return;
      if (e.source.url.includes("MapServer")) {
        // if map server fails, switch to image server which is more stable
        const mapBounds = map.getBounds();
        const width = map.getContainer().clientWidth;
        const height = map.getContainer().clientHeight;
        const url = getWetLandsImageServerURL(mapBounds, width, height);
        loadingURL = url.toString();
        const coordinates = getImageCoordinates(mapBounds);
        const source = map.getSource(sourceId);
        source.updateImage({ url: url.toString(), coordinates: coordinates });
        setLayerTooltip({
          display: true,
          message: "The detailed wetlands layer is currently not available",
        });
      }
    });
    return () => {
      map.off("moveend", moveEnd);
      // map.off("sourcedataloading", sourceDataLoading);
      map.off("sourcedata", sourceData);
    };
  }, [map, currentMapThemeOption, currentMapLayerOptions]);

  return (
    <>
      {currentMapLayerOptions.includes("wetlands") &&
      initURL &&
      initCoordinates ? (
        <>
          {layerTooltip.display &&
            ((layerTooltip.currentZoom &&
              layerTooltip.currentZoom >= minZoom) ||
              layerTooltip.message) && (
              <div
                style={{
                  position: "absolute",
                  top: "70px",
                  left: "50%",
                  transform: "translateX(-50%)",
                  padding: "5px 10px",
                  backgroundColor: "white",
                  zIndex: 910,
                  display: "flex",
                  flexDirection: "row",
                  gap: "10px",
                  alignItems: "center",
                  borderRadius: "5px",
                  border: "1px solid #ccc",
                  boxShadow:
                    "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
                }}
              >
                {/* <Spin indicator={antIcon} /> */}
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                  }}
                >
                  {layerTooltip.message ? (
                    <span>{layerTooltip.message}</span>
                  ) : (
                    <>
                      <strong>
                        Zoom to level {zoomForMapServer.toFixed(0)} for detailed
                        wetlands
                      </strong>
                      <small>
                        Current zoom: {layerTooltip.currentZoom.toFixed(2)}
                      </small>
                    </>
                  )}
                </div>
              </div>
            )}
          <div ref={loaderRef} style={{ display: "none" }}>
            <div
              style={{
                position: "absolute",
                top: "20px",
                left: "50%",
                transform: "translateX(-50%)",
                padding: "5px 10px",
                backgroundColor: "white",
                zIndex: 900,
                display: "flex",
                flexDirection: "row",
                gap: "10px",
                alignItems: "center",
                borderRadius: "5px",
                border: "1px solid #ccc",
                boxShadow:
                  "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
              }}
            >
              <Spin indicator={antIcon} />
              <strong>Wetlands layer is loading...</strong>
            </div>
          </div>

          <Source
            id={sourceId}
            type="image"
            url={initURL}
            coordinates={initCoordinates}
          >
            <Layer {...layerStyle} />
          </Source>
        </>
      ) : null}
    </>
  );
}

export default WetLands;
// ne: Object { lng: -95.59744199752319, lat: 35.32654330148338 }
// ​
// _sw: Object { lng: -99.13286017395424, lat: 30.43686175553313 }
// https://fwspublicservices.wim.usgs.gov/wetlandsmapservice/rest/services/WetlandsRaster/ImageServer/exportImage?f=image&bbox=-43.26408,66.04795,-156.35951,5.72335&bboxSR=4326&imageSR=4326&size=1159,1035&format=jpgpng&mosaicRule={"ascending":true,"mosaicMethod":"esriMosaicCenter","mosaicOperation":"MT_FIRST"}
