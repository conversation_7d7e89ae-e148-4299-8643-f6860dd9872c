import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useDispatch, useSelector } from "react-redux";
import { getTransitLine } from "../../../services/data";
import Source from "./Source";
import Layer from "./Layer";
import { geojsonTemplate } from "../../../constants";
import { initPopup } from "../MapUtility/general";
import { Typography, Switch } from "antd";
const { Title } = Typography;
const sourceId = "arcgis-transit-line";
const minZoom = 8;

const lineStyle = {
  id: `${sourceId}LineStyle`,
  type: "line",
  minzoom: minZoom,
  layout: {
    "line-join": "round",
    "line-cap": "round",
  },
  paint: {
    "line-width": [
      "interpolate",
      ["linear"],
      ["zoom"],
      8,
      2,
      10,
      3,
      12,
      4,
      14,
      5,
    ],
    "line-color": ["get", "route_color_formatted"],
    "line-opacity": [
      "case",
      ["all",
        ["match",
          ["downcase", ["get", "route_type_text"]],
          ["bus"],
          true,
          false
        ],
        ["!", ["get", "showBus"]]
      ],
      0,  // Hide bus routes when showBus is false
      0.8 // Show all other routes
    ]
  },
};

const popup = initPopup();

const getPopupHTML = (properties) => {
  const { route_short_name, route_long_name, route_type_text } = properties;

  return renderToString(
    <div style={{ padding: "5px 10px" }}>
      <strong>
        Route {route_short_name}: {route_long_name}
      </strong>
      <div style={{ display: "flex", flexDirection: "column" }}>
        <span>Type: {route_type_text}</span>
      </div>
    </div>
  );
};

const TransitLineLayer = () => {
  const dispatch = useDispatch();
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const geoJSON = useSelector((state) => state.Map.transitLineGeoJSON);
  const transitLineFilter = useSelector((state) => state.Map.transitLineFilter);

  useEffect(() => {
    if (!map) return;

    const getTransitLineData = async () => {
      if (!currentMapLayerOptions.includes("arcgis transit line")) return;

      const bounds = map.getBounds().toArray();
      const data = await getTransitLine({
        lng1: bounds[0][0],
        lat1: bounds[0][1],
        lng2: bounds[1][0],
        lat2: bounds[1][1],
      });

      if (data && data.features) {
        const features = data.features.map((feature) => ({
          type: "Feature",
          geometry: {
            type: "LineString",
            coordinates: feature.geometry.paths[0],
          },
          properties: {
            ...feature.attributes,
            route_color_formatted: feature.attributes.route_color_formatted
              ? feature.attributes.route_color_formatted.replace(/[()]/g, "").split(",").map(Number)
              : [0, 0, 0],
            showBus: transitLineFilter.showBus
          },
        }));
        
        dispatch({
          type: "Map/saveState",
          payload: {
            transitLineGeoJSON: {
              type: "FeatureCollection",
              features: features,
            },
          },
        });
      } else {
        dispatch({
          type: "Map/saveState",
          payload: {
            transitLineGeoJSON: geojsonTemplate
          },
        });
      }
    };

    const mouseMove = (e) => {
      const features = e.features;
      if (features.length > 0) {
        const feature = features[0];
        const isBus = feature.properties.route_type_text.toLowerCase() === 'bus';
        
        // Only show tooltip if:
        // 1. It's not a bus route, OR
        // 2. It's a bus route AND showBus is true
        if (!isBus || (isBus && transitLineFilter.showBus)) {
          map.getCanvas().style.cursor = "pointer";
          popup.setLngLat(e.lngLat).setHTML(getPopupHTML(feature.properties)).addTo(map);
        } else {
          map.getCanvas().style.cursor = "";
          popup.remove();
        }
      } else {
        map.getCanvas().style.cursor = "";
        popup.remove();
      }
    };

    const mouseLeave = () => {
      map.getCanvas().style.cursor = "";
      popup.remove();
    };

    getTransitLineData();
    map.on("moveend", getTransitLineData);
    map.on("mousemove", lineStyle.id, mouseMove);
    map.on("mouseleave", lineStyle.id, mouseLeave);

    return () => {
      map.off("moveend", getTransitLineData);
      map.off("mousemove", lineStyle.id, mouseMove);
      map.off("mouseleave", lineStyle.id, mouseLeave);
    };
  }, [map, currentMapLayerOptions, transitLineFilter]);

  if (!currentMapLayerOptions.includes("arcgis transit line")) return null;

  return (
    <Source id={sourceId} type="geojson" data={geoJSON}>
      <Layer {...lineStyle} />
    </Source>
  );
};

export default TransitLineLayer;

export const TransitLineLegend = () => {
  const dispatch = useDispatch();
  const transitLineFilter = useSelector((state) => state.Map.transitLineFilter);

  const handleBusToggle = (checked) => {
    dispatch({
      type: "Map/saveState",
      payload: {
        transitLineFilter: {
          ...transitLineFilter,
          showBus: checked
        }
      }
    });
  };

  return (
    <div className="bg-white flex flex-col px-4 pb-6 w-[400px]">
      <Title level={5} className="text-center pt-2">
        Transit Line
      </Title>
      <div className="flex items-center justify-between mt-2">
        <span>Show Bus Routes</span>
        <Switch 
          checked={transitLineFilter?.showBus}
          onChange={handleBusToggle}
        />
      </div>
    </div>
  );
};