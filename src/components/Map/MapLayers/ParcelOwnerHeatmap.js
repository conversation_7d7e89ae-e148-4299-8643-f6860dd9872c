import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { initPopup } from "../MapUtility/general";
import { renderToString } from "react-dom/server";
import { zoomLevelToChangeBasemap } from "../Map";
import { setPaintPropertySafely } from "../MapUtility/general";
import { tileURLRoot } from "../../../services/data";

const sourceId = "parcelOwnerHeatmap";
const minZoom = 9;

const circleStyle = {
  id: `${sourceId}CircleLayer`,
  type: "circle",
  "source-layer": "institutional_owners",
  sourceLayer: "data",
  paint: {
    "circle-radius": ["interpolate", ["linear"], ["zoom"], 9, 2, 12, 6, 22, 16],
    "circle-color": [
      "match",
      ["get", "institution"],
      "AH4R",
      "#f5222d",
      "Cerberus",
      "#1890ff",
      "Invitation",
      "#52c41a",
      "Progress",
      "#08979c",
      "Tricon",
      "#5047b9",
      "Amherst",
      "#3d9146",
      "transparent",
    ],
  },
};

const popup = initPopup();

const createChainPopup = (map, feature, coordinates) => {
  if (feature && feature.length > 0) {
    const { institution } = feature[0].properties;

    const htmlRender = renderToString(
      <div style={{ padding: "10px", fontWeight: "500" }}>
        <p style={{ margin: 0 }}>
          Institution:{" "}
          <strong>{institution.includes("AH4R") ? "AMH" : institution}</strong>
        </p>
      </div>
    );

    popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
  } else {
    popup.remove();
  }
};

const getTileURL = (serverType, token) =>
  `${tileURLRoot(
    "cma",
    serverType
  )}/parcel/owners-map/{z}/{x}/{y}.mvt?token=${token}`;

const useAccessToken = ({ map }) => {
  const [token, setToken] = useState(null);

  useEffect(() => {
    if (!map) return;

    const checkForLatestToken = async () => {
      popup.isOpen() && popup.remove();
      const newToken = await window.spatiallaser.getUserToken("access");
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    checkForLatestToken();
    map.on("movestart", checkForLatestToken);
  }, [map]);

  return token;
};

const ParcelOwnerHeatmap = () => {
  const map = useSelector((state) => state.Map.map);
  const token = useAccessToken({ map });
  const serverType = useSelector((state) => state.Configure.serverType);

  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const currentMapThemeOption = useSelector(
    (state) => state.Map.currentMapThemeOption
  );

  const parcelHeatmapOptions = useSelector(
    (state) => state.Map.parcelHeatmapOptions
  );

  useEffect(() => {
    if (!map) return;

    const mouseEnter = (e) => {
      const feature = map.queryRenderedFeatures(e.point, {
        layers: [circleStyle.id],
      });

      const coordinates = [e.lngLat.lng, e.lngLat.lat];
      createChainPopup(map, feature, coordinates);
    };
    const mouseLeave = () => {
      popup.remove();
    };

    map.on("mouseenter", circleStyle.id, mouseEnter);
    map.on("mouseleave", circleStyle.id, mouseLeave);
    // prettier-ignore
    return () => {

      map.off("mouseenter", circleStyle.id, mouseEnter);
      map.off("mouseleave", circleStyle.id, mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  useEffect(() => {
    if (!map) return;

    // prettier-ignore
    if (currentMapLayerOptions.includes("institutional owners")) {
      const ownerFilter = ["in", "institution"];

      if (parcelHeatmapOptions.ah4r) ownerFilter.push("AH4R");
      if (parcelHeatmapOptions.cerberus) ownerFilter.push("Cerberus");
      if (parcelHeatmapOptions.invitationHomes) ownerFilter.push("Invitation");
      if (parcelHeatmapOptions.progressResidential) ownerFilter.push("Progress");
      if (parcelHeatmapOptions.tricon) ownerFilter.push("Tricon");
      if (parcelHeatmapOptions.amherst) ownerFilter.push("Amherst");

      map.setFilter(`${sourceId}CircleLayer`, ownerFilter);
    }
  }, [parcelHeatmapOptions, currentMapLayerOptions]);

  useEffect(() => {
    if (!map) return;
    if (!currentMapLayerOptions.includes("institutional owners")) return;

    const updatePaintProperties = (color, width) => {
      setPaintPropertySafely(map, circleStyle.id, "circle-stroke-color", color);
      setPaintPropertySafely(map, circleStyle.id, "circle-stroke-width", width);
    };

    const checkIfShouldChange = (currentColor) => {
      const strokeColor = map.getPaintProperty(
        circleStyle.id,
        "circle-stroke-color"
      );
      return currentColor !== strokeColor;
    };

    if (currentMapThemeOption.includes("Satellite")) {
      if (checkIfShouldChange("#fff")) {
        updatePaintProperties("#fff", 1);
      }
    } else if (!currentMapThemeOption.includes("Autmatic")) {
      if (checkIfShouldChange("transparent")) {
        updatePaintProperties("transparent", 0);
      }
    }

    const moveEnd = () => {
      if (!currentMapThemeOption.includes("Automatic")) return;

      const zoom = map.getZoom();
      if (zoom >= zoomLevelToChangeBasemap) {
        if (!checkIfShouldChange("#fff")) return;
        updatePaintProperties("#fff", 1);
      } else {
        if (!checkIfShouldChange("transparent")) return;
        updatePaintProperties("transparent", 0);
      }
    };

    map.on("moveend", moveEnd);
    return () => {
      map.off("moveend", moveEnd);
    };
  }, [currentMapThemeOption, currentMapLayerOptions]);

  if (!token) return null;
  if (currentMapLayerOptions.includes("institutional owners")) {
    return (
      <>
        <Source
          id={sourceId}
          type="vector"
          tiles={[getTileURL(serverType, token)]}
        >
          <Layer {...circleStyle} />
        </Source>
      </>
    );
  }
  return null;
};

export default ParcelOwnerHeatmap;
