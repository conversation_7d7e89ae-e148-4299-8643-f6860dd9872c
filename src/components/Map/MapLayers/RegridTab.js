import { <PERSON><PERSON><PERSON>, Divider, But<PERSON>, Segmented, Spin, Card } from "antd";
import React, {
  useEffect,
  useState,
  createContext,
  useContext,
  useMemo,
  useCallback,
  useRef,
} from "react";
import { useQuery } from "react-query";
import {
  getZoningDetailsData,
  getParcelAVMData,
  getTaxProperDetailsData,
  getPropertyTaxData,
  getOwnerOtherParcels,
  getParcelDetailData,
  postSkipTrace,
} from "../../../services/data";
import { AimOutlined } from "@ant-design/icons";
import { useSelector } from "react-redux";
import mapboxgl from "mapbox-gl";
import { organizeParcelData } from "../../../utils/regrid";
import isEmpty from "lodash.isempty";
import { ErrorBoundary } from "react-error-boundary";
import SiteAddressRenderer from "./regridSections/SiteAddressRenderer";
import { restructurePropertyData } from "./regridSections/utils";
import LandUseRenderer, { LandUseDetails } from "./regridSections/LandUseRenderer";
import OwnerRenderer, { OwnerDetails } from "./regridSections/OwnerRenderer";
import ZoningRenderer from "./regridSections/ZoningRenderer";
import StructureDetailsRenderer, {
  StructureDetails,
} from "./regridSections/StructureDetailsRenderer";
import GeographicInfoRenderer, {
  GeographicInfoDetails,
} from "./regridSections/GeographicInfoRenderer";
import AdditionalItemsRenderer, {
  AdditionalItemsDetails,
} from "./regridSections/AdditionalItemsRenderer";
import { ChevronLeft, ChevronRight, Search } from "lucide-react";
import SearchBar, { SearchResults } from "./regridSections/Search";
import TaxRenderer from "./regridSections/TaxRenderer";

const Context = createContext(undefined);

export const ParcelBreakdownProvider = ({
  children,
  parcelFetchLoading,
  parcelInfo,
  setParcelInfo,
  onDrawerClose,
}) => {
  const [currentView, setCurrentView] = useState("MAIN"); // MAIN | ZONEOMICS | TAXPROPER
  const [zoneomicsData, setZoneomicsData] = useState(null);
  const [searchingParams, setSearchingParams] = useState(null);

  useEffect(() => {
    return () => {
      setCurrentView("MAIN");
      setZoneomicsData(null);
    };
  }, []);
  useEffect(() => {
    const csvBtn = document.querySelector("#parcel-breakdown-export-to-csv");
    const searchContainer = document.querySelector("#search-container");
    const searchTips = document.querySelector("#search-tips");
  
    // Log warnings if elements are missing
    if (!csvBtn) console.warn("Element #parcel-breakdown-export-to-csv not found in DOM");
    if (!searchContainer) console.warn("Element #search-container not found in DOM");
    if (!searchTips) console.warn("Element #search-tips not found in DOM");
  
    // Handle CSV button visibility
    if (csvBtn && ["ZONEOMICS", "TAXPROPER"].includes(currentView) && csvBtn.style.visibility !== "hidden") {
      csvBtn.style.visibility = "hidden";
    } else if (csvBtn && currentView === "MAIN" && csvBtn.style.visibility === "hidden") {
      csvBtn.style.visibility = "visible";
    }
  
    // Handle search container and search tips display
    if (["MAIN", "SEARCH"].includes(currentView)) {
      if (searchContainer && searchContainer.style.display !== "block") {
        searchContainer.style.display = "block";
      }
      if (searchTips && searchTips.style.display !== "block") {
        searchTips.style.display = "block";
      }
    } else {
      if (searchContainer && searchContainer.style.display !== "none") {
        searchContainer.style.display = "none";
      }
      if (searchTips && searchTips.style.display !== "none") {
        searchTips.style.display = "none";
      }
    }
  }, [currentView]);

  const context = useMemo(
    () => ({
      currentView,
      setCurrentView,
      zoneomicsData,
      setZoneomicsData,
      parcelFetchLoading,
      parcelInfo,
      setParcelInfo,
      onDrawerClose,
      searchingParams,
      setSearchingParams,
    }),
    [currentView, zoneomicsData, parcelFetchLoading, parcelInfo, searchingParams]
  );

  return <Context.Provider value={context}>{children}</Context.Provider>;
};

export const useParcelBreakdown = () => {
  const context = useContext(Context);
  if (!context) {
    throw new Error("useParcelBreakdown must be used within a ParcelBreakdownProvider");
  }
  return context;
};

export const ParcelBreakDownContent = () => {
  const { currentView, parcelFetchLoading, parcelInfo, setCurrentView } = useParcelBreakdown();
  console.log("ownerContactInformation parcelInfo", parcelInfo);
  if (!parcelFetchLoading && parcelInfo) {
    let coordinates = [];
    let address = null;
    if (
      parcelInfo.parcel &&
      parcelInfo.parcel.geographicInformation &&
      parcelInfo.parcel.geographicInformation.centroidCoordinates &&
      parcelInfo.parcel.geographicInformation.centroidCoordinates.length > 0
    ) {
      try {
        coordinates = parcelInfo.parcel.geographicInformation.centroidCoordinates
          .trim()
          .split(",")
          .map((coord) => parseFloat(coord));
      } catch (error) {
        console.error(error);
      }
    }

    if (parcelInfo.parcel && parcelInfo.parcel.parcelAddress) {
      address = `${parcelInfo.parcel.parcelAddress?.siteAddress || ""}, ${
        parcelInfo.parcel.parcelAddress?.siteCity || ""
      }, ${parcelInfo.parcel.parcelAddress?.siteState || ""} ${
        parcelInfo.parcel.parcelAddress?.siteZip || ""
      }`;
      if (address.trim() === ", , ") {
        address = null;
      } else {
        address = address.trim() + ", USA";
      }
    }
    if (currentView === "MAIN") {
      return (
        <div className="flex flex-col gap-[10px] px-[24px]">
          <RegridTab
            parcel={parcelInfo.parcel}
            tax={parcelInfo.propertyTax}
            contactInfo={parcelInfo.ownerContactInformation}
          />
        </div>
      );
    } else if (currentView === "ZONEOMICS") {
      return <Zoneomics coordinates={coordinates} />;
    } else if (currentView === "TAXPROPER") {
      return (
        <ErrorBoundary
          fallback={
            <div>
              <div className="mb-1">
                <Button type="link" className="p-0" onClick={() => setCurrentView("MAIN")}>
                  Back
                </Button>
              </div>
              <div className="px-4">
                <div>Something went wrong.</div>
              </div>
            </div>
          }
        >
          <TaxProper address={address} coordinates={coordinates} />
        </ErrorBoundary>
      );
    } else if (currentView === "OWNERPARCELS") {
      return <OwnerParcels />;
    }
    // NEW
    else if (currentView === "LANDUSE") {
      return <LandUseDetails />;
    } else if (currentView === "OWNER") {
      return <OwnerDetails />;
    } else if (currentView === "STRUCTUREDETAILS") {
      return <StructureDetails />;
    } else if (currentView === "GEOGRAPHICINFO") {
      return <GeographicInfoDetails />;
    } else if (currentView === "ADDITIONALITEMS") {
      return <AdditionalItemsDetails />;
    } else if (currentView === "SEARCH") {
      return <SearchResults />;
    }
  }
  return null;
};

function parseCamelCaseToString(input) {
  if (input === "fipsCode") return "FIPS Code";
  if (input === "propertySalesAndValue") return "Property Sales & Values";
  if (input === "zoningLandUseVacancy") return "Zoning, Land Use & Vacancy";
  if (input === "gisAcre") return "GIS Acres";
  let result = input.replace(/_/g, " ");
  result = result.replace(/([A-Z])/g, " $1");
  result = result
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");

  return result;
}

function formatCurrency(number) {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(number);
}
const RegridTab = ({ parcel, tax, contactInfo }) => {
  const { setCurrentView, onDrawerClose } = useParcelBreakdown();
  const [parcelToRender, setParcelToRender] = useState();
  const map = useSelector((state) => state.Map.map);
  const buttonRef = useRef(null); // Create a ref for the button

  // Handle button click
  useEffect(() => {
    if (!map) return;

    map.on("owner-other-parcel-land-search", () => {
      console.log("owner-other-parcel-land-search triggered");
    });

    return () => {
      map.off("owner-other-parcel-land-search");
    };
  }, [map]);

  console.log("land-search", parcel);
  const handleClick = () => {
    if (!map) return;
    console.log("Button clicked");
    map.fire("owner-other-parcel-land-search", {
      parcel: parcel,
    });
    onDrawerClose();
  };

  useEffect(() => {
    console.log("parcel org", parcel);
    const restructured = restructurePropertyData(parcel);
    console.log("restructured", restructured);
    setParcelToRender(restructured);
  }, [parcel, tax, contactInfo]);

  if (parcelToRender) {
    return (
      // <div>
      //   {parcelToRender &&
      //     Object.entries(parcelToRender).map(([key, value], idx) => (
      //       <div key={idx} style={{ position: "relative" }}>
      //         <Divider plain>
      //           <p
      //             style={{
      //               fontWeight: "bold",
      //               fontSize: "1.125rem",
      //               paddingTop: "0.5rem",
      //             }}
      //           >
      //             {parseCamelCaseToString(key)}
      //           </p>
      //           {key === "ownerInformation" && (
      //             <div className="flex gap-4 justify-center">
      //               <Button
      //                 type="default"
      //                 size="small"
      //                 onClick={() => setCurrentView("CONTACT")}
      //               >
      //                 Contact Information
      //               </Button>
      //               <Button
      //                 id="owner-other-parcel-land-search"
      //                 type="default"
      //                 size="small"
      //                 onClick={handleClick}
      //               >
      //                 Owner's Other Parcels
      //               </Button>
      //             </div>
      //           )}
      //           {key === "propertySalesAndValue" && (
      //             <div>
      //               <Button
      //                 type="default"
      //                 size="small"
      //                 onClick={() => setCurrentView("TAXPROPER")}
      //               >
      //                 More Details
      //               </Button>
      //             </div>
      //           )}
      //           {key === "zoningLandUseVacancy" && (
      //             <div>
      //               <Button
      //                 type="default"
      //                 size="small"
      //                 onClick={() => setCurrentView("ZONEOMICS")}
      //               >
      //                 More Details
      //               </Button>
      //             </div>
      //           )}
      //         </Divider>
      //         {/* <Collapse ghost defaultActiveKey={[...Array(11).keys()]}> */}
      //         <div>
      //           {value &&
      //             Object.entries(value).map(([key, val], idx) => {
      //               if (typeof val !== "object") {
      //                 return (
      //                   <div
      //                     key={`${idx}-${key}`}
      //                     style={{
      //                       display: "grid",
      //                       gridTemplateColumns: "1fr 1fr",
      //                     }}
      //                   >
      //                     {key === "zoningCodeLink"
      //                       ? renderLink(key, val)
      //                       : renderValue(key, val)}
      //                   </div>
      //                 );
      //               } else {
      //                 return (
      //                   <div
      //                     key={`${idx}-${key}`}
      //                     style={{
      //                       display: "grid",
      //                       gridTemplateColumns: "1fr 1fr",
      //                     }}
      //                   >
      //                     {renderObject(key, val)}
      //                   </div>
      //                 );
      //               }
      //             })}
      //         </div>
      //       </div>
      //     ))}
      // </div>
      <div className="space-y-3">
 
        <SiteAddressRenderer parcelToRender={parcelToRender} />
        <LandUseRenderer parcelToRender={parcelToRender} />
        <OwnerRenderer parcelToRender={parcelToRender} />
        <ZoningRenderer parcelToRender={parcelToRender} />
        <TaxRenderer parcelToRender={parcelToRender} />
        <StructureDetailsRenderer parcelToRender={parcelToRender} />
        <GeographicInfoRenderer parcelToRender={parcelToRender} />
        <AdditionalItemsRenderer parcelToRender={parcelToRender} />
      </div>
    );
  } else {
    return <></>;
  }
};

const Zoneomics = ({ coordinates }) => {
  const { zoneomicsData, setZoneomicsData } = useParcelBreakdown();
  const { data, isError, isLoading, refetch } = useQuery(
    ["parcel-breakdown-zoning-details", coordinates[0], coordinates[1]],
    async () =>
      await getZoningDetailsData({
        lat: coordinates[0],
        lng: coordinates[1],
      }),
    {
      enabled: coordinates && coordinates.length > 0,
      staleTime: 1000 * 60 * 60 * 24 * 3, // 3 days unless browser closes, uses react-query-persist-client to persist
    }
  );

  useEffect(() => {
    if (data) {
      setZoneomicsData(data);
    }
  }, [data]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-[300px] p-[24px]">
        <Spin tip="loading" size="large" />
      </div>
    );
  } else if (isError) {
    return (
      <div className="flex flex-col gap-2 p-[24px]">
        <span>Failed to fetch data.</span>
        <Button type="text" onClick={() => refetch()}>
          Try again?
        </Button>
      </div>
    );
  } else {
    if (!isError && !isLoading && (data != null || zoneomicsData != null)) {
      return <ZoneomicsContent zoneData={zoneomicsData} />;
    } else {
      return <div>No data available.</div>;
    }
  }
};

const ZoneomicsContent = ({ zoneData }) => {
  const { setCurrentView } = useParcelBreakdown();
  const [activeTab, setActiveTab] = useState("zone_details");
  const [controlItems, setControlItems] = useState([]);

  useEffect(() => {
    if (!zoneData || !zoneData.data || !zoneData.data.controls) return;
    const controls = zoneData.data.controls;
    const controlItems = Object.entries(controls).reduce((acc, [key, value]) => {
      const standard = value.standard;
      const nonStandard = value["non-standard"];
      const standardItems = !standard
        ? []
        : Object.entries(standard).reduce((acc, [key, value]) => {
            acc.push({ key, value });
            return acc;
          }, []);
      const nonStandardItems = !nonStandard
        ? []
        : Object.entries(nonStandard).reduce((acc, [key, value]) => {
            acc.push({ key, value });
            return acc;
          }, []);
      acc.push({
        key: key,
        label: (
          <span className="font-semibold text-base">{key.split("_").join(" ").toUpperCase()}</span>
        ),

        children: (
          <div className="flex flex-col pt-1 px-5 pb-5 gap-2">
            {standardItems.length > 0 || nonStandardItems.length > 0 ? (
              <>
                {standardItems.map((item, idx) => {
                  return (
                    <div key={idx} className="flex flex-col gap-1">
                      <span className="font-semibold text-sm">
                        {item.key.split("_").join(" ").toUpperCase()}
                      </span>
                      <span>{item.value ? item.value : "N/A"}</span>
                    </div>
                  );
                })}
                {nonStandardItems.map((item, idx) => {
                  return (
                    <div key={idx} className="flex flex-col gap-1">
                      <span className="font-semibold text-sm">
                        {item.key.split("_").join(" ").toUpperCase()}
                      </span>
                      <span>{item.value ? item.value : "N/A"}</span>
                    </div>
                  );
                })}
              </>
            ) : (
              <span>No data available.</span>
            )}
          </div>
        ),
      });
      return acc;
    }, []);

    setControlItems(controlItems);
  }, [zoneData]);

  const zoneRender = useCallback((key, value) => {
    if (key === "link")
      return (
        <a href={value} target="_blank">
          Click to Open
        </a>
      );
    return <span>{value ? value : "N/A"}</span>;
  }, []);

  const checkIfAnyPermitted = useCallback(() => {
    if (!zoneData || !zoneData.data) return false;
    if (zoneData.data.permitted_land_uses) {
      return [
        "single_family_permitted",
        "two_family_permitted",
        "multi_family_permitted",
        "commercial_uses_permitted",
        "industrial_uses_permitted",
        "adu_local_permitted",
        "adu_state_permitted",
        "short_term_rentals_permitted",
      ].some((key) => zoneData.data.permitted_land_uses[key] === true);
    }
    return false;
  }, [zoneData]);

  if (!zoneData || !zoneData.data) return null;
  return (
    <>
      <div
        className="sticky top-0 z-10 px-[24px] pb-[24px] bg-white"
        style={{ borderBottom: "1px solid #ddd" }}
      >
        <div className="mb-1">
          <Button type="link" className="p-0" onClick={() => setCurrentView("MAIN")}>
            Back
          </Button>
        </div>

        <Segmented
          value={activeTab}
          onChange={(value) => setActiveTab(value)}
          block
          options={[
            { label: "Zone Details", value: "zone_details" },
            { label: "Permitted Uses", value: "permitted_land_uses" },
            { label: "Controls", value: "controls" },
          ]}
        />
      </div>
      <div className="flex flex-col gap-8 pt-4 px-[24px] pb-[24px]" style={{ color: "#333" }}>
        {activeTab === "zone_details" && (
          <>
            {zoneData && zoneData.data && zoneData.data.zone_details ? (
              <div className="flex flex-col gap-6">
                {Object.entries(zoneData.data.zone_details).map(([key, value], idx) => (
                  <div key={idx} className="flex flex-col gap-2">
                    <span className="font-semibold text-base">
                      {key.split("_").join(" ").toUpperCase()}
                    </span>
                    {zoneRender(key, value)}
                  </div>
                ))}
              </div>
            ) : (
              <div>No zone data available.</div>
            )}
          </>
        )}
        {activeTab === "permitted_land_uses" && (
          <>
            {zoneData.data.permitted_land_uses ? (
              <div className="flex flex-col gap-6">
                <div className="flex flex-col gap-2">
                  <span className="font-semibold text-base uppercase">{`What's permitted?`}</span>
                  {checkIfAnyPermitted() ? (
                    <ul className="pl-4 mt-0">
                      {[
                        "single_family_permitted",
                        "two_family_permitted",
                        "multi_family_permitted",
                        "commercial_uses_permitted",
                        "industrial_uses_permitted",
                        "adu_local_permitted",
                        "adu_state_permitted",
                        "short_term_rentals_permitted",
                      ].reduce((acc, key) => {
                        if (zoneData.data.permitted_land_uses[key] === true) {
                          acc.push(
                            <li key={key} className="mb-1">
                              {key
                                .split("_")
                                .filter((str) => str != "permitted")
                                .join(" ")}
                            </li>
                          );
                        }
                        return acc;
                      }, [])}
                    </ul>
                  ) : (
                    <span>No permitted uses available.</span>
                  )}
                </div>
                {zoneData.data.permitted_land_uses.as_of_right && (
                  <div className="flex flex-col gap-2">
                    <span className="font-semibold text-base uppercase">{`As of right`}</span>
                    {zoneData.data.permitted_land_uses.as_of_right.length > 0 ? (
                      <ul className="pl-4 mt-0">
                        {zoneData.data.permitted_land_uses.as_of_right.map((use, idx) => (
                          <li key={idx} className="mb-1">
                            {use}
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <span>No as of right available.</span>
                    )}
                  </div>
                )}
                {zoneData.data.permitted_land_uses.conditional_uses && (
                  <div className="flex flex-col gap-2">
                    <span className="font-semibold text-base uppercase">{`Conditional uses`}</span>
                    {zoneData.data.permitted_land_uses.conditional_uses.length > 0 ? (
                      <ul className="pl-4 mt-0">
                        {zoneData.data.permitted_land_uses.conditional_uses.map((use, idx) => (
                          <li key={idx} className="mb-1">
                            {use}
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <span>No conditional uses available.</span>
                    )}
                  </div>
                )}
                {zoneData.data.permitted_land_uses.prohibited && (
                  <div className="flex flex-col gap-2">
                    <span className="font-semibold text-base uppercase">{`Prohibited`}</span>
                    {zoneData.data.permitted_land_uses.prohibited.length > 0 ? (
                      <ul className="pl-4 mt-0">
                        {zoneData.data.permitted_land_uses.prohibited.map((use, idx) => (
                          <li key={idx} className="mb-1">
                            {use}
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <span>No prohibited uses available.</span>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <div>No land use data available.</div>
            )}
          </>
        )}
        {activeTab === "controls" && (
          <>
            {zoneData.data.controls ? (
              <div className="flex flex-col gap-6">
                <Collapse
                  items={controlItems}
                  defaultActiveKey={[...Object.keys(zoneData.data.controls)]}
                  bordered={false}
                  ghost
                />
              </div>
            ) : (
              <div>No controls data available.</div>
            )}
          </>
        )}
      </div>
    </>
  );
};

const TaxProper = ({ address, coordinates }) => {
  const { setCurrentView } = useParcelBreakdown();
  const { data: avm, isLoading: avmLoading } = useQuery(
    ["parcel-breakdown-avm", coordinates[0], coordinates[1]],
    async () =>
      await getParcelAVMData({
        lat: coordinates[0],
        lng: coordinates[1],
      }),
    {
      enabled: coordinates && coordinates.length > 0,
    }
  );
  const { data, isError, isLoading, refetch } = useQuery(
    [
      "parcel-breakdown-taxproper",
      coordinates[0],
      coordinates[1],
      // avm?.salesavm,
      address,
    ],
    async () => {
      const res = await getTaxProperDetailsData({
        body: {
          ...(address
            ? { address, latitude: coordinates[0], longitude: coordinates[1] }
            : {
                latitude: coordinates[0],
                longitude: coordinates[1],
              }),
          purchase_price: avm?.salesavm || 500000,
        },
      });

      const data = res.json();
      return data;
    },
    {
      enabled:
        ((coordinates && coordinates.length > 0) || address) && !avmLoading && avm
          ? // && avm.salesavm
            true
          : false,
      staleTime: 1000 * 60 * 60 * 24 * 3, // 3 days unless browser closes, uses react-query-persist-client to persist
    }
  );

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-[300px] p-[24px]">
        <Spin tip="loading" size="large" />
      </div>
    );
  } else if (isError) {
    return (
      <div className="flex flex-col gap-2 p-[24px]">
        <span>Failed to fetch data.</span>
        <Button type="text" onClick={() => refetch()}>
          Try again?
        </Button>
      </div>
    );
  } else {
    if (!isError && !isLoading && data && !data.error) {
      return <TaxProperContent taxData={data} />;
    } else {
      console.log("taxData", data);
      return (
        <>
          <div className="mb-1">
            <Button type="link" className="p-0" onClick={() => setCurrentView("MAIN")}>
              Back
            </Button>
          </div>
          <div className="px-4">
            {data && data.error && data.error.message ? (
              <div>{data.error.message}</div>
            ) : (
              <div>No data available.</div>
            )}
          </div>
        </>
      );
    }
  }
};

const TaxProperContent = ({ taxData }) => {
  const { setCurrentView } = useParcelBreakdown();
  const [activeTab, setActiveTab] = useState("general");

  console.log("taxData", taxData);

  const parseSnakeCaseToString = (input) => {
    return input
      .replace(/_/g, " ")
      .split(" ")
      .map((s) => s.charAt(0).toUpperCase() + s.substring(1))
      .join(" ");
  };

  const renderValue = (key, value) => (
    <div key={key} style={{ display: "grid", gridTemplateColumns: "1.5fr 1.5fr" }}>
      <p>{parseSnakeCaseToString(key)}</p>
      <p className="font-bold">{value !== null && value !== undefined ? value : "-"}</p>
    </div>
  );

  const segmentedOptions = React.useMemo(() => {
    const options = [
      { label: "General", value: "general" },
      { label: "Projections", value: "projections" },
      { label: "Rates", value: "rate_breakdown" },
    ];

    if (taxData.source_forecasts) {
      options.push({
        label: "Nearby",
        value: "source_forecasts",
      });
    }
    return options;
  }, [taxData]);
  return (
    <>
      <div
        className="sticky top-0 z-10 px-[24px] bg-white"
        style={{ borderBottom: "1px solid #ddd" }}
      >
        <div className="flex cursor-pointer select-none" onClick={() => setCurrentView("MAIN")}>
          <div className="px-2 items-center pt-[2px]">
            <ChevronLeft />
          </div>
          {/* Title */}
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              textAlign: "left",
              fontSize: "18px",
              fontStyle: "normal",
              fontWeight: 700,
              // lineHeight: "28.29px",
            }}
            className="pb-3 items-center-safe"
          >
            Taxes
          </div>

          {/* Horizontal Divider */}
          <div
            style={{
              height: "1px",
              flexShrink: 0,
              backgroundColor: "rgba(0, 0, 0, 0.12)",
              marginBottom: "16px",
            }}
          />
        </div>
        <Segmented
          value={activeTab}
          onChange={(value) => setActiveTab(value)}
          block
          options={segmentedOptions}
        />
      </div>

      <div className="flex flex-col gap-8 pt-4 px-[24px] pb-[24px]" style={{ color: "#333" }}>
        {activeTab === "general" && (
          <>
            <div>
              {renderValue("base_rate", taxData.base_rate?.toFixed(6))}
              {renderValue("tax_cap", taxData.tax_cap?.toFixed(2))}
              {/* assessment_cap is a json object, try phoennix area */}
              {/* {renderValue("assessment_cap", taxData.assessment_cap?.toFixed(2))} */}
            </div>
            <div>
              {Object.entries(taxData.forecast_metadata)
                .filter(
                  ([k, v]) =>
                    !["messages", "tax_districts", "request_id", "purchase_price"].includes(k)
                )
                .map(([key, value]) => {
                  if (["rates_last_check", "rates_next_check"].includes(key)) {
                    return <div key={key}>{renderValue(key, value && value.split("T")[0])}</div>;
                  }
                  if (
                    [
                      "total_millage_rate",
                      "total_flat_charge",
                      "median_millage_rate",
                      "median_flat_charge",
                      "mean_millage_rate",
                      "median_flat_charge",
                    ].includes(key)
                  ) {
                    return <div key={key}>{renderValue(key, value && value?.toFixed(6))}</div>;
                  }
                  if (["purchase_price", "stabilized_tax_amount"].includes(key)) {
                    // filtered out, maybe temp
                    return (
                      <div key={key}>
                        {renderValue(key, value && `$ ${Math.round(value).toLocaleString()}`)}
                      </div>
                    );
                  }
                  return <div key={key}>{renderValue(key, value)}</div>;
                })}
            </div>
          </>
        )}
        {activeTab === "projections" && (
          <>
            {taxData.tax_projections.map((projection, idx) => (
              <div key={idx}>
                <h3 className="font-bold text-base">Year {projection.year}</h3>

                {projection?.market_value &&
                  renderValue(
                    "market_value",
                    projection?.market_value !== null
                      ? `$ ${Math.round(projection?.market_value).toLocaleString()}`
                      : "-"
                  )}
                {projection?.tax_amount &&
                  renderValue(
                    "tax_amount",
                    `$ ${Math.round(projection?.tax_amount).toLocaleString()}`
                  )}
                {projection?.tax_amount && renderValue("tax_cap", projection?.tax_cap)}

                {projection?.assessment_method &&
                  renderValue(
                    "assessment_method",
                    `${projection?.assessment_method.split("_").join(" ")}`
                  )}

                {renderValue(
                  "is_assessment_capped",
                  `${projection.is_assessment_capped ? "Yes" : "No"}`
                )}

                {projection?.assessment &&
                  renderValue(
                    "assessment",
                    `$ ${Math.round(projection?.assessment).toLocaleString()}`
                  )}
                {projection?.gross_assessment &&
                  renderValue(
                    "gross_assessment",
                    `$ ${Math.round(projection?.gross_assessment).toLocaleString()}`
                  )}
                {projection?.net_assessment &&
                  renderValue(
                    "net_assessment",
                    `$ ${Math.round(projection?.net_assessment).toLocaleString()}`
                  )}
              </div>
            ))}
          </>
        )}
        {activeTab === "rate_breakdown" && (
          <>
            {taxData.rate_breakdown.map((rate, idx) => (
              <div key={idx}>
                <h3 className="font-bold text-base">{rate.taxing_authority}</h3>
                {renderValue("rate", rate.rate?.toFixed(6))}
                {renderValue(
                  "amount",
                  rate.amount && `$ ${Math.round(rate.amount).toLocaleString()}`
                )}
                {renderValue(
                  "flat_charge",
                  rate.flat_charge !== null
                    ? `$ ${Math.round(rate.flat_charge).toLocaleString()}`
                    : "-"
                )}
              </div>
            ))}
          </>
        )}
        {/* {activeTab === "forecast_metadata" && (
          <>
            {Object.entries(taxData.forecast_metadata).map(([key, value]) => (
              <div key={key}>{renderValue(key, value)}</div>
            ))}
          </>
        )} */}
        {taxData.source_forecasts && activeTab === "source_forecasts" && (
          <>
            {taxData.source_forecasts &&
              taxData.source_forecasts.map((source, idx) => (
                <div key={idx}>
                  {/* <h3 className="font-bold text-base">Forecast #{idx + 1}</h3> */}
                  {source?.location && (
                    <>
                      <h3 className="font-bold text-base">
                        {source?.location?.resolved_address || "-"}
                      </h3>
                      <div
                        className="mb-2"
                        style={{
                          display: "grid",
                          gridTemplateColumns: "1fr 1fr",
                        }}
                      >
                        <span className="font-bold">Coordinates</span>
                        <span>
                          {source?.location?.latitude || "-"}, {source?.location?.longitude || "-"}
                        </span>
                      </div>
                    </>
                  )}
                  <div className="flex flex-col gap-2">
                    {source.tax_projections.map((estimate, estimateIdx) => (
                      <div key={estimateIdx}>
                        <h3 className="font-bold text-base">Year {estimate.year}</h3>
                        {renderValue(
                          "market_value",
                          estimate.market_value &&
                            `$ ${Math.round(estimate.market_value).toLocaleString()}`
                        )}
                        {renderValue(
                          "gross_assessment",
                          estimate.gross_assessment &&
                            `$ ${Math.round(estimate.gross_assessment).toLocaleString()}`
                        )}
                        {renderValue(
                          "net_assessment",
                          estimate.net_assessment &&
                            `$ ${Math.round(estimate.net_assessment).toLocaleString()}`
                        )}
                        {renderValue(
                          "gross_taxes",
                          estimate.gross_taxes &&
                            `$ ${Math.round(estimate.gross_taxes).toLocaleString()}`
                        )}
                        {renderValue(
                          "net_taxes",
                          estimate.net_taxes &&
                            `$ ${Math.round(estimate.net_taxes).toLocaleString()}`
                        )}
                        {renderValue(
                          "tax_amount",
                          estimate.tax_amount &&
                            `$ ${Math.round(estimate.tax_amount).toLocaleString()}`
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
          </>
        )}
      </div>
    </>
  );
};


const OwnerParcels = () => {
  const { setCurrentView, parcelInfo } = useParcelBreakdown();
  const map = useSelector((state) => state.Map.map);
  const [markers, setMarkers] = useState([]);

  const coordinates =
    parcelInfo?.parcel?.geographicInformation?.centroidCoordinates
      ?.split(",")
      .map((coord) => parseFloat(coord.trim())) || [];
  const ownerName = parcelInfo?.parcel?.ownerInformation?.ownerName || "Unknown Owner";
  const ownerParcels = parcelInfo?.ownersOtherParcels;

  useEffect(() => {
    if (!ownerParcels?.features) return;

    markers.forEach((marker) => marker.remove());
    const bounds = new mapboxgl.LngLatBounds();
    const newMarkers = [];

    ownerParcels.features.forEach((feature) => {
      const [lng, lat] = feature.geometry.coordinates;
      bounds.extend([lng, lat]);

      const el = document.createElement("div");
      el.className = "parcel-marker";
      el.style.width = "20px";
      el.style.height = "20px";
      el.style.backgroundColor = "#4CAF50";
      el.style.borderRadius = "50%";
      el.style.border = "2px solid white";

      const marker = new mapboxgl.Marker(el).setLngLat([lng, lat]).addTo(map);
      newMarkers.push(marker);
    });

    setMarkers(newMarkers);

    if (ownerParcels.features.length === 1) {
      const [lng, lat] = ownerParcels.features[0].geometry.coordinates;
      map.flyTo({ center: [lng, lat], zoom: 12, duration: 1000 });
    } else {
      map.fitBounds(bounds, { padding: 50, duration: 1000 });
    }

    return () => markers.forEach((marker) => marker.remove());
  }, [ownerParcels, map]);

  const handleLocate = (coordinates) => {
    map.flyTo({ center: coordinates, zoom: 16, duration: 1000 });
  };

  if (!ownerParcels) {
    return (
      <div className="flex flex-col gap-[5px] justify-center items-center h-[300px]">
        <span>No data available.</span>
      </div>
    );
  }

  return (
    <OwnerParcelsContent parcelsData={ownerParcels} ownerName={ownerName} onLocate={handleLocate} />
  );
};

const OwnerParcelsContent = ({ parcelsData, ownerName, onLocate }) => {
  const { setCurrentView, setParcelInfo } = useParcelBreakdown();

  const handleViewDetails = async (property) => {
    try {
      const { ll_uuid } = property;
      const response = ll_uuid ? await getParcelDetailData({ ll_uuid: ll_uuid }) : null;

      if (response && !isEmpty(response)) {
        const taxResponse = await getPropertyTaxData({
          lat: response?.fields?.lat,
          lng: response?.fields?.lon,
        });
        const otherParcelsResponse = await getOwnerOtherParcels({
          lat: response?.fields?.lat,
          lng: response?.fields?.lon,
        });

        setParcelInfo({
          parcel: organizeParcelData(response),
          propertyTax: taxResponse[0]?.tax_rate_percent ?? null,
          enhancedOwnership: null,
          ownersOtherParcels: otherParcelsResponse,
        });
        setCurrentView("MAIN");
      }
    } catch (err) {
      console.error(err);
      setParcelInfo(null);
    }
  };

  return (
    <div className="flex flex-col">
      <div className="sticky top-0 z-10 px-6 py-4 bg-white border-b">
        <div className="flex items-center gap-2 mb-4">
          <Button type="link" className="p-0" onClick={() => setCurrentView("MAIN")}>
            <span className="text-green-600">← Back</span>
          </Button>
        </div>
        <h2 className="text-lg font-semibold">Parcels Owned by {ownerName}</h2>
      </div>

      <div className="flex flex-col gap-4 p-6">
        {parcelsData.features?.map((feature, index) => (
          <Card
            key={index}
            title={feature.properties.address.street_address}
            bordered={true}
            style={{ width: 300 }}
            size="small"
          >
            <div className="bg-white border rounded-lg p-4">
              <div className="text-sm text-gray-500 mb-3">
                {feature.properties.miles_away} miles away
              </div>
              <div className="text-sm text-gray-500 mb-3">
                {feature.properties.lot_size > 1
                  ? feature.properties.lot_size + " acres"
                  : feature.properties.lot_size + " acre"}
              </div>
              <div className="flex gap-2">
                <Button
                  type="primary"
                  className="bg-green-600"
                  ghost
                  onClick={() => onLocate(feature.geometry.coordinates)}
                >
                  <AimOutlined /> Locate
                </Button>
                <Button
                  type="primary"
                  className="bg-green-600"
                  onClick={() => handleViewDetails(feature.properties)}
                >
                  View Details
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};
export default RegridTab;
