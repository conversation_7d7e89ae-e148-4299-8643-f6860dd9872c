import { ChevronLeft, ChevronRight } from "lucide-react";
import { useSelector } from "react-redux";
import { useParcelBreakdown } from "../RegridTab";
import { restructurePropertyData } from "./utils";

const AdditionalItemsRenderer = ({ parcelToRender }) => {
  console.log("parcelToRender", parcelToRender);
  const { setCurrentView } = useParcelBreakdown();
  if (!parcelToRender) {
    return <></>;
  }

  const additional_items = parcelToRender.additional_items;

  if (!additional_items) {
    return <></>;
  }

  return (
    <div>
      <div
        className="flex cursor-pointer select-none"
        onClick={() => setCurrentView("ADDITIONALITEMS")}
      >
        {/* Title */}
        <div
          className="text-blue-500 underline"
          style={{
            textAlign: "left",
            fontSize: "18px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "28.29px",
            marginBottom: "12px",
          }}
        >
          Additional Items
        </div>
        <div className="px-2 items-center pt-[2px] text-blue-500">
          <ChevronRight />
        </div>
        {/* Horizontal Divider */}
        <div
          style={{
            height: "1px",
            flexShrink: 0,
            backgroundColor: "rgba(0, 0, 0, 0.12)",
            marginBottom: "16px",
          }}
        />
      </div>
      {/* Content */}
      <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px" }}>
        {/* Owner (Assessor)
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 400,
              lineHeight: "21px",
            }}
          >
            Owner (Assessor)
          </div>
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 700,
              lineHeight: "21px",
            }}
          >
            {owner.owner_assessor}
          </div> */}
      </div>
    </div>
  );
};

export default AdditionalItemsRenderer;

export const AdditionalItemsDetails = () => {
  const { setCurrentView, parcelInfo } = useParcelBreakdown();
  const parcelData = restructurePropertyData(parcelInfo.parcel);
  const additionalItemsDetails = parcelData.additional_items;

  console.log("AdditionalItemsDetails", additionalItemsDetails);

  return (
    <div className="p-6">
      <div className="flex cursor-pointer select-none" onClick={() => setCurrentView("MAIN")}>
        {/* Title */}
        <div className="px-2 items-center pt-[2px]">
          <ChevronLeft />
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            textAlign: "left",
            fontSize: "18px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "28.29px",
            marginBottom: "12px",
          }}
        >
          Additional Items Details
        </div>
      </div>

      {/* Horizontal Divider */}
      <div
        style={{
          height: "1px",
          flexShrink: 0,
          backgroundColor: "rgba(0, 0, 0, 0.12)",
          marginBottom: "16px",
        }}
      />

      {/* Content */}
      <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px" }}>
        {/* Account Number */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Account Number
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.account_number}
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Lowest Parcel Elevation
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.lowest_parcel_elevation}
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Highest Parcel Elevation
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.highest_parcel_elevation}
        </div>

        {/* Acres */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Acres
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.acres}
        </div>

        {/* AG Acres */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          AG Acres
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.ag_acres}
        </div>

        {/* AG Value */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          AG Value
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          ${additionalItemsDetails.agval?.toLocaleString() || 0}
        </div>

        {/* Appraisal Date */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Appraisal Date
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.appraisal_date}
        </div>

        {/* Appraised Value */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Appraised Value
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          ${additionalItemsDetails.appraised_value?.toLocaleString() || "N/A"}
        </div>

        {/* School District */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          School District
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.census_unified_school_district}
        </div>

        {/* Central Air */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Central Air
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.central_air_ind}
        </div>

        {/* Central Heat */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Central Heat
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.central_heat_ind}
        </div>

        {/* City */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          City
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.city}
        </div>

        {/* County */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          County
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.county}
        </div>

        {/* FEMA Flood Zone */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          FEMA Flood Zone
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.fema_flood_zone}
        </div>

        {/* FEMA NRI Risk Rating */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          FEMA NRI Risk Rating
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.fema_nri_risk_rating}
        </div>

        {/* Garage Capacity */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Garage Capacity
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.garage_capacity}
        </div>

        {/* Median Household Income */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Median Household Income
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          ${additionalItemsDetails.median_household_income?.toLocaleString() || "N/A"}
        </div>

        {/* Year Built */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Year Built
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.yearbuilt}
        </div>

        {/* Sale Date */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Sale Date
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.saledate}
        </div>

        {/* Tax Year */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Tax Year
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {additionalItemsDetails.taxyear}
        </div>
      </div>
    </div>
  );
};
