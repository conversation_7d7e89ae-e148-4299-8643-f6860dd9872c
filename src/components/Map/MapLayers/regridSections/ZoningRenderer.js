import { ChevronLeft, ChevronRight } from "lucide-react";
import { useParcelBreakdown } from "../RegridTab";

const ZoningRenderer = ({ parcelToRender }) => {
  const { setCurrentView } = useParcelBreakdown();
  if (!parcelToRender) {
    return <></>;
  }

  const zoning = parcelToRender.zoning;

  if (!zoning) {
    return <></>;
  }

  return (
    <div>
       <div className="flex cursor-pointer select-none" onClick={() => setCurrentView("ZONEOMICS")}>
        {/* Title */}
        <div
        className="text-blue-500 underline pb-3 items-center-safe"
          style={{
            textAlign: "left",
            fontSize: "18px",
            fontStyle: "normal",
            fontWeight: 700,
            // lineHeight: "28.29px",
          }}
        >
          Zoning
        </div>
        <div className="px-2 items-center pt-[2px] text-blue-500">
          <ChevronRight />
        </div>
        {/* Horizontal Divider */}
        <div
          style={{
            height: "1px",
            flexShrink: 0,
            backgroundColor: "rgba(0, 0, 0, 0.12)",
            marginBottom: "16px",

          }}
          
        />
      </div>
      {/* Content */}
      <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px" }}>
        {/* Zoning Type */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Zoning Type
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {zoning.zoning_type}
        </div>

        {/* Zoning */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Zoning
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {zoning.zoning}
        </div>

        {/* Zoning Description */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Zoning Description
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {zoning.zoning_description}
        </div>

        {/* Zoning Subtype */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Zoning Subtype
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {zoning.zoning_subtype}
        </div>

        {/* Zoning Id */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Zoning Id
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {zoning.zoning_id}
        </div>

        {/* Zoning Code Link */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Zoning Code Link
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {zoning.zoning_code_link ? (
            <a
              href={zoning.zoning_code_link}
              target="_blank"
              rel="noopener noreferrer"
              style={{
                color: "#1890ff",
                textDecoration: "underline",
                fontSize: "14px",
                fontWeight: 700,
                lineHeight: "21px",
              }}
            >
              Click to Open
            </a>
          ) : (
            "N/A"
          )}
        </div>

        {/* Parcel Use Code */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Parcel Use Code
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {zoning.parcel_use_code}
        </div>

        {/* Parcel Use Description */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Parcel Use Description
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {zoning.parcel_use_code_description}
        </div>
      </div>
    </div>
  );
};

export default ZoningRenderer;

