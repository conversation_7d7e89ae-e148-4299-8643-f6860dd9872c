import { ChevronLeft, ChevronRight } from "lucide-react";
import PropTypes from 'prop-types';
import React, { useMemo } from "react";
import { useParcelBreakdown } from "../RegridTab";
import { restructurePropertyData, formatCurrency, formatYear } from "./utils";
import { styles, classNames } from "./styles";

/**
 * TaxRenderer component displays tax-related information for a parcel
 * @param {Object} props - Component props
 * @param {Object} props.parcelToRender - Parcel data object containing tax information
 * @param {boolean} [props.isLoading] - Loading state of the component
 * @returns {React.ReactElement|null} The rendered component or null if no data
 */
const TaxRenderer = ({ parcelToRender, isLoading }) => {
  const { setCurrentView } = useParcelBreakdown();

  // Memoize tax details to prevent unnecessary recalculations
  const taxDetails = useMemo(() => {
    if (!parcelToRender?.additional_items) {
      return [];
    }

    const { additional_items, owner } = parcelToRender;
    const { taxyear: taxYear, propertyTax } = additional_items;

    return [
      {
        label: "Tax Rate Details",
        value: "Click to open",
        isLink: true,
        onClick: () => setCurrentView("TAXPROPER"),
        ariaLabel: "View detailed tax rate information"
      },
      {
        label: "Tax Year",
        value: formatYear(taxYear),
        ariaLabel: `Tax year: ${formatYear(taxYear)}`
      },
      {
        label: "Property Tax",
        value: formatCurrency(propertyTax),
        ariaLabel: `Property tax: ${formatCurrency(propertyTax)}`
      },
      {
        label: "Total Assd. Value",
        value: formatCurrency(owner?.total_assd_value),
        ariaLabel: `Total assessed value: ${formatCurrency(owner?.total_assd_value)}`
      }
    ];
  }, [parcelToRender, setCurrentView]);

  // Early return for loading state
  if (isLoading) {
    return (
      <div role="status" aria-label="Loading tax information">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3 mb-2"></div>
        </div>
      </div>
    );
  }

  // Early return if no data
  if (!parcelToRender?.additional_items) {
    return null;
  }

  return (
    <div role="region" aria-label="Tax Information">
      <div 
        className={classNames.clickableRow} 
        onClick={() => setCurrentView("TAXPROPER")}
        role="button"
        tabIndex={0}
        onKeyPress={(e) => e.key === 'Enter' && setCurrentView("TAXPROPER")}
        aria-label="Open tax details"
      >
        <div className={classNames.titleLink} style={styles.title}>
          Tax Details
        </div>
        <div className={classNames.chevronContainer}>
          <ChevronRight aria-hidden="true" />
        </div>
        <div style={styles.divider} role="separator" />
      </div>

      <div style={styles.gridContainer} role="list">
        {taxDetails.map(({ label, value, isLink, onClick, ariaLabel }, index) => (
          <React.Fragment key={label}>
            <div style={styles.label} role="listitem">{label}</div>
            <div 
              style={styles.value}
              className={isLink ? "underline text-blue-500 cursor-pointer" : undefined}
              onClick={onClick}
              role={isLink ? "button" : "listitem"}
              tabIndex={isLink ? 0 : undefined}
              onKeyPress={isLink ? (e) => e.key === 'Enter' && onClick() : undefined}
              aria-label={ariaLabel}
            >
              {value}
            </div>
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

TaxRenderer.propTypes = {
  parcelToRender: PropTypes.shape({
    additional_items: PropTypes.shape({
      taxyear: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      propertyTax: PropTypes.number,
      account_number: PropTypes.string,
      appraisal_date: PropTypes.string,
      appraised_value: PropTypes.number
    }),
    owner: PropTypes.shape({
      total_assd_value: PropTypes.number,
      owner_assessor: PropTypes.string,
      owner_address_assessor: PropTypes.string
    })
  }),
  isLoading: PropTypes.bool
};

TaxRenderer.defaultProps = {
  isLoading: false
};

/**
 * AdditionalItemsDetails component displays detailed tax and property information
 * @returns {React.ReactElement|null} The rendered component or null if no data
 */
export const AdditionalItemsDetails = () => {
  const { setCurrentView, parcelInfo } = useParcelBreakdown();
  
  // Memoize parcel data transformation
  const parcelData = useMemo(() => 
    parcelInfo?.parcel ? restructurePropertyData(parcelInfo.parcel) : null
  , [parcelInfo?.parcel]);

  // Memoize detail fields to prevent unnecessary recalculations
  const detailFields = useMemo(() => {
    if (!parcelData?.additional_items) {
      return [];
    }

    const additionalItemsDetails = parcelData.additional_items;

    return [
      { label: "Account Number", value: additionalItemsDetails.account_number },
      { label: "Lowest Parcel Elevation", value: additionalItemsDetails.lowest_parcel_elevation },
      { label: "Highest Parcel Elevation", value: additionalItemsDetails.highest_parcel_elevation },
      { label: "Acres", value: additionalItemsDetails.acres },
      { label: "AG Acres", value: additionalItemsDetails.ag_acres },
      { label: "AG Value", value: formatCurrency(additionalItemsDetails.agval) },
      { label: "Appraisal Date", value: additionalItemsDetails.appraisal_date },
      { label: "Appraised Value", value: formatCurrency(additionalItemsDetails.appraised_value) },
      { label: "School District", value: additionalItemsDetails.census_unified_school_district },
      { label: "Central Air", value: additionalItemsDetails.central_air_ind },
      { label: "Central Heat", value: additionalItemsDetails.central_heat_ind },
      { label: "City", value: additionalItemsDetails.city },
      { label: "County", value: additionalItemsDetails.county },
      { label: "FEMA Flood Zone", value: additionalItemsDetails.fema_flood_zone },
      { label: "FEMA NRI Risk Rating", value: additionalItemsDetails.fema_nri_risk_rating },
      { label: "Garage Capacity", value: additionalItemsDetails.garage_capacity },
      { label: "Median Household Income", value: formatCurrency(additionalItemsDetails.median_household_income) },
      { label: "Year Built", value: formatYear(additionalItemsDetails.yearbuilt) },
      { label: "Sale Date", value: additionalItemsDetails.saledate },
      { label: "Tax Year", value: formatYear(additionalItemsDetails.taxyear) }
    ];
  }, [parcelData]);

  if (!parcelData?.additional_items) {
    return null;
  }

  return (
    <div className="p-6" role="region" aria-label="Additional Property Details">
      <div 
        className={classNames.clickableRow} 
        onClick={() => setCurrentView("MAIN")}
        role="button"
        tabIndex={0}
        onKeyPress={(e) => e.key === 'Enter' && setCurrentView("MAIN")}
        aria-label="Return to main view"
      >
        <div className={classNames.chevronContainer}>
          <ChevronLeft aria-hidden="true" />
        </div>
        <div style={styles.title}>
          Additional Items Details
        </div>
      </div>

      <div style={styles.divider} role="separator" />

      <div style={styles.gridContainer} role="list">
        {detailFields.map(({ label, value }) => (
          <React.Fragment key={label}>
            <div style={styles.label} role="listitem">{label}</div>
            <div style={styles.value} role="listitem" aria-label={`${label}: ${value}`}>
              {value}
            </div>
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default TaxRenderer;
