export function restructurePropertyData(data) {
  // Helper function to convert camelCase/PascalCase to snake_case
  function toSnakeCase(str) {
    return str.replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`).replace(/^_/, "");
  }

  // Helper function to format address
  function formatAddress(siteAddress, siteCity, siteState, siteZip) {
    return `${siteAddress || ""}, ${siteCity || ""} ${siteState || ""} ${siteZip || ""}`.trim().replace(/,\s*,/, ",");
  }

  // Helper function to format mailing address
  function formatMailingAddress(mailingAddress, mailingCity, mailingState, mailingZip) {
    return `${mailingAddress || ""}, ${mailingCity || ""} ${mailingState || ""} ${mailingZip || ""}`.trim().replace(/,\s*,/, ",");
  }

  const realLastSaleDate = () => {
    const date1 = data.additionalItem.saledate;
    const date2 = data.ownerInformation.estated_owner.lastsale.deed_last_sale_date;
    const date3 = data.ownerInformation.estated_owner.lastsale.assr_last_sale_date;
    const dates = [date1, date2, date3]
        .filter(date => date && /^\d{4}-\d{2}-\d{2}$/.test(date))
        .map(date => new Date(date));
    return dates.length > 0 
        ? dates.reduce((latest, current) => current > latest ? current : latest)
               .toISOString().split('T')[0]
        : null;
};

  // Extract data from nested structure with null checks
  const parcelAddress = data?.parcelAddress || {};
  const otherOwner= data?.ownerInformation?.possible_owner
  const otherOwnerAddress =  data?.ownerInformation?.possible_owner_mailing_address
  const ownerInfo = data?.ownerInformation || {};
  const mailingInfo = ownerInfo?.mailingInformation || {};
  const salesValue = data?.propertySalesAndValue?.countyProvidedValues || {};
  const zoning = data?.zoningLandUseVacancy || {};
  const standardizedLandUse = zoning?.standardizedLandUseCodes || {};
  const structureDetails = data?.structureDetails?.regridCalculatedData || {};
  const geographicInfo = data?.geographicInformation || {};
  const geographicCalculated = geographicInfo?.regridCalculatedData || {};
  const centroidCoords = geographicInfo?.centroidCoordinates || "";
  const opportunityZone = geographicInfo?.opportunityZone || {};
  const censusGeographies = geographicInfo?.censusGeographies || {};
  const platBlockLot = geographicInfo?.platBlockLotLegalData || {};
  const additionalItems = data?.additionalItem || {};
  const estated = data?.ownerInformation.estated_owner
  
  additionalItems['propertyTax'] = salesValue.propertyTax

console.log("restru", data)
  return {
    site_address: {
      site_address: formatAddress(
        parcelAddress.siteAddress,
        parcelAddress.siteCity,
        parcelAddress.siteState,
        parcelAddress.siteZip
      ),
      parcel_number: parcelAddress.parcelNumber || "",
      objectId: parcelAddress.objectId || "",
      apn: parcelAddress.fipsCode
    },

    land_use: {
      land_use: standardizedLandUse.landUseCodeStructureDescription || "",
      building_area: `${
        structureDetails.regridCalculatedBuildingFootprintSquareFeet || "N/A"
      } SF | ${structureDetails.regridCalculatedBuildingCount || "N/A"} Units`,
      lot_area: `${
        geographicCalculated.calculatedParcelSqFt || "N/A"
      } SF (${
        geographicCalculated.calculatedAcres != null && typeof geographicCalculated.calculatedAcres === "number"
          ? Number(geographicCalculated.calculatedAcres.toFixed(2))
          : "N/A"
      } acres)`,
      adj_lots_owned: "TBD",
      year_built: additionalItems.yearbuilt || "N/A",
      details: {
        land_use_code_activity: standardizedLandUse.landUseCodeActivity || "",
        land_use_code_activity_description: standardizedLandUse.landUseCodeActivityDescription || "",
        land_use_code_function: standardizedLandUse.landUseCodeFunction || "",
        land_use_code_function_description: standardizedLandUse.landUseCodeFunctionDescription || "",
        land_use_code_structure: standardizedLandUse.landUseCodeStructure || "",
        land_use_code_structure_description: standardizedLandUse.landUseCodeStructureDescription || "",
        land_use_code_site: standardizedLandUse.landUseCodeSite || "",
        land_use_code_site_description: standardizedLandUse.landUseCodeSiteDescription || "",
      },
    },

    owner: {
      estated,
      otherOwner,
      otherOwnerAddress,
      owner_assessor: ownerInfo.ownerName || "",
      owner_address_assessor: formatMailingAddress(
        mailingInfo.mailingAddress,
        mailingInfo.mailingCity,
        mailingInfo.mailingState,
        mailingInfo.mailingZip
      ),
      last_market_sale: `${realLastSaleDate() || "N/A"} for $${salesValue.last_sale_amount === 0 ? "N/A" : salesValue.last_sale_amount}`,
      total_assd_value: salesValue.parcelValue || "",
      details: {
        owner_assessor: ownerInfo.ownerName || "",
        mail_address: formatMailingAddress(
          mailingInfo.mailingAddress,
          mailingInfo.mailingCity,
          mailingInfo.mailingState,
          mailingInfo.mailingZip
        ),
        last_market_sale: `${realLastSaleDate() || "N/A"} for $${salesValue.last_sale_amount || "N/A"}`,
        total_assd_value: salesValue.parcelValue || "",
        skip_trace: {}, // empty for now as requested
      },
    },

    zoning: {
      zoning_type: zoning.zoningType || "",
      zoning: zoning.zoning || "",
      zoning_description: zoning.zoningDescription || "",
      zoning_subtype: zoning.zoningSubtype || "",
      zoning_id: zoning.zoningId || "",
      zoning_code_link: zoning.zoningCodeLink || "",
      parcel_use_code: zoning.parcelUseCode || "",
      parcel_use_code_description: "N/A", // as requested
    },

    structure_details: {
      number_of_living_units: "N/A",
      structure_style: "N/A",
      regrid_calculated_building_count: structureDetails.regridCalculatedBuildingCount || "",
      regrid_calculated_building_footprint_square_feet:
        structureDetails.regridCalculatedBuildingFootprintSquareFeet || "",
    },

    geographic_info: {
      centroid_coordinates: centroidCoords,
      federal_qualified_opportunity_zone: opportunityZone.federalQualifiedOpportunityZone || "",
      qualified_opportunity_zone_tract_number: opportunityZone.federalQualifiedOpportunityZone || "",
      census_block: censusGeographies.censusBlock || "",
      census_blockgroup: censusGeographies.censusBlockgroup || "",
      census_tract: censusGeographies.censusTract || "",
      calculated_acres: geographicCalculated.calculatedAcres != null ? geographicCalculated.calculatedAcres : "",
      calculated_parcel_sq_ft: geographicCalculated.calculatedParcelSqFt || "",
      legal_description: platBlockLot.legalDescription || "",
      subdivision: "N/A",
    },

    additional_items: additionalItems,
  };
}

// Format currency values with dollar sign and commas
export const formatCurrency = (value) => {
  if (value == null || value === "") return "N/A";
  return `$${value.toLocaleString("en-US")}`;
};

// Format any value with fallback to N/A
export const formatValue = (value, formatter = (v) => v) => {
  if (value == null || value === "") return "N/A";
  return formatter(value);
};

// Format year values
export const formatYear = (year) => {
  if (!year) return "N/A";
  return year.toString();
};