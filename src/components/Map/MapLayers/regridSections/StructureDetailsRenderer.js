import { ChevronLeft, ChevronRight } from "lucide-react";
import { useSelector } from "react-redux";
import { useParcelBreakdown } from "../RegridTab";
import { restructurePropertyData } from "./utils";
import { Button } from "antd";

const StructureDetailsRenderer = ({ parcelToRender }) => {
    console.log("parcelToRender", parcelToRender);
    const { setCurrentView } = useParcelBreakdown();
    if (!parcelToRender) {
      return <></>;
    }
  
    const structure_details = parcelToRender.structure_details;
  
    if (!structure_details) {
      return <></>;
    }
  
    return (
      <div>
         <div className="flex cursor-pointer select-none" onClick={() => setCurrentView("STRUCTUREDETAILS")}>
          {/* Title */}
          <div
            className="text-blue-500 underline"
            style={{
              textAlign: "left",
              fontSize: "18px",
              fontStyle: "normal",
              fontWeight: 700,
              lineHeight: "28.29px",
              marginBottom: "12px",
            }}
          >
            Structure Details
          </div>
          <div className="px-2 items-center pt-[2px] text-blue-500">
          <ChevronRight />
        </div>
          {/* Horizontal Divider */}
          <div
            style={{
              height: "1px",
              flexShrink: 0,
              backgroundColor: "rgba(0, 0, 0, 0.12)",
              marginBottom: "16px",
            }}
          />
        </div>
        {/* Content */}
        <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px" }}>
          {/* Owner (Assessor)
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 400,
              lineHeight: "21px",
            }}
          >
            Owner (Assessor)
          </div>
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 700,
              lineHeight: "21px",
            }}
          >
            {owner.owner_assessor}
          </div> */}
  

        </div>
      </div>
    );
}

export default StructureDetailsRenderer

export const StructureDetails = () => {
  const { setCurrentView, parcelInfo } = useParcelBreakdown();
  const parcelData = restructurePropertyData(parcelInfo.parcel);
  const structure_details = parcelData.structure_details;
  
  console.log("structure_details", structure_details);

  return (
    <div className="p-6">
      <div className="flex cursor-pointer select-none" onClick={() => setCurrentView("MAIN")}>
        {/* Title */}
        <div className="px-2 items-center pt-[2px]">
          <ChevronLeft />
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            textAlign: "left",
            fontSize: "18px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "28.29px",
            marginBottom: "12px",
          }}
        >
          Structure Details
        </div>
      </div>

      {/* Horizontal Divider */}
      <div
        style={{
          height: "1px",
          flexShrink: 0,
          backgroundColor: "rgba(0, 0, 0, 0.12)",
          marginBottom: "16px",
        }}
      />

      {/* Content */}
      <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px" }}>
        {/* Number of Living Units */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Number of Living Units
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {structure_details.number_of_living_units || "N/A"}
        </div>

        {/* Structure Style */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Structure Style
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {structure_details.structure_style || "N/A"}
        </div>

        {/* Regrid Calculated Building Count */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Regrid Calculated Building Count
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {structure_details.regrid_calculated_building_count}
        </div>

        {/* Regrid Calculated Building Footprint Square Feet */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Regrid Calculated Building Footprint Square Feet
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {structure_details.regrid_calculated_building_footprint_square_feet}
        </div>
      </div>
    </div>
  );
};
