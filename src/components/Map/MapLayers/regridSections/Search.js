import { Search, X } from "lucide-react";
import { useParcelBreakdown } from "../RegridTab";
import { restructurePropertyData } from "./utils";
import React, { useState, useEffect, useMemo } from "react";

const ParcelSearch = () => {
  const { setCurrentView, parcelInfo, setSearchingParams, searchingParams } = useParcelBreakdown();
  const [searchValue, setSearchValue] = useState(searchingParams || "");
  console.log("Current searchingParams:", searchingParams);
  // Remove the automatic sync to prevent conflicts
  // useEffect(() => {
  //   if (searchingParams !== searchValue) {
  //     setSearchValue(searchingParams || "");
  //   }
  // }, [searchingParams]);

  // Update search immediately on input change
  const handleInputChange = (e) => {
    const value = e.target.value;
    console.log("Input value:", value);
    setSearchValue(value);
    setSearchingParams(value);
    console.log("Searching params set to:", value);
    if (value.trim()) {
      setCurrentView("SEARCH");
    } else {
      setCurrentView("MAIN");
    }
  };

  // Common search suggestions based on parcel data
  const getSearchSuggestions = () => {
    if (!parcelInfo || !parcelInfo.parcel) return [];

    const parcelData = restructurePropertyData(parcelInfo.parcel);
    const suggestions = [];

    // Add common search terms that users might want
    suggestions.push(
      { label: "Area", value: "area" },
      { label: "Owner", value: "owner" },
      { label: "Address", value: "address" },
      { label: "Zoning", value: "zoning" },
      { label: "Value", value: "value" },
      { label: "Census", value: "census" }
    );

    return suggestions.slice(0, 8); // Limit to 8 suggestions
  };

  const handleClear = () => {
    setSearchValue("");
    setSearchingParams("");
    setCurrentView("MAIN");
  };

  const handleSuggestionClick = (value) => {
    setSearchValue(value);
    setSearchingParams(value);
    setCurrentView("SEARCH");
  };

  const suggestions = getSearchSuggestions();

  return (
    <div className="w-full max-w-md mx-auto px-2 py-1" id="search-container">
      {/* Search Input */}
      <div className="relative">
        <div className="flex items-center bg-white border rounded-full shadow-sm">
          <div className="pl-4">
            <Search className="w-5 h-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search parcel information..."
            value={searchValue}
            onChange={handleInputChange}
            className="flex-1 px-3 py-2 bg-transparent border-none outline-none text-sm"
          />
          {searchValue && (
            <button onClick={handleClear} className="pr-4 text-gray-400 hover:text-gray-600">
              <X className="w-5 h-5" />
            </button>
          )}
        </div>
      </div>

      {/* Search Suggestions */}
      {!searchValue && suggestions.length > 0 && (
        <div className="mt-3 p-3">
          <div className="text-xs text-gray-500 mb-2">Quick search:</div>
          <div className="flex flex-wrap gap-2">
            {suggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => handleSuggestionClick(suggestion.value)}
                className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
              >
                {suggestion.label}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Search Tips */}
      {!searchValue && (
        <div className="mt-4 text-xs text-gray-400 text-center" id="search-tips">
          💡 Try searching for: area, owner name, zoning, year built, city, census, or any keyword
        </div>
      )}
    </div>
  );
};

export default ParcelSearch;

// Helper function to recursively flatten ALL fields from the parcel data
const createSearchableFields = (parcelData) => {
  const fields = [];

  // Function to recursively extract all fields from an object
  const extractFields = (obj, sectionName, prefix = "") => {
    if (!obj || typeof obj !== "object") return;

    Object.entries(obj).forEach(([key, value]) => {
      const fieldKey = prefix ? `${prefix} > ${key}` : key;

      if (value !== null && value !== undefined && typeof value !== "object") {
        // Convert camelCase/snake_case to readable names
        const readableKey = fieldKey
          .replace(/_/g, " ")
          .replace(/([A-Z])/g, " $1")
          .replace(/\b\w/g, (l) => l.toUpperCase())
          .trim();

        fields.push({
          key: readableKey,
          value: value,
          section: sectionName,
          originalKey: key,
        });
      } else if (typeof value === "object" && value !== null) {
        // Recursively process nested objects (but limit depth)
        if (!prefix || prefix.split(" > ").length < 2) {
          extractFields(value, sectionName, fieldKey);
        }
      }
    });
  };

  // Extract from all major sections
  if (parcelData.site_address) {
    extractFields(parcelData.site_address, "Site Address");
  }

  if (parcelData.land_use) {
    extractFields(parcelData.land_use, "Land Use");
  }

  if (parcelData.owner) {
    extractFields(parcelData.owner, "Owner");
  }

  if (parcelData.zoning) {
    extractFields(parcelData.zoning, "Zoning");
  }

  if (parcelData.structure_details) {
    extractFields(parcelData.structure_details, "Structure Details");
  }

  if (parcelData.geographic_info) {
    extractFields(parcelData.geographic_info, "Geographic Info");
  }

  if (parcelData.additional_items) {
    extractFields(parcelData.additional_items, "Additional Items");
  }

  return fields;
};

export const SearchResults = () => {
  const { setCurrentView, parcelInfo, searchingParams } = useParcelBreakdown();

  // Always call hooks first - get parcel data with null checking inside useMemo
  const parcelData = useMemo(() => {
    if (!parcelInfo || !parcelInfo.parcel) return null;
    return restructurePropertyData(parcelInfo.parcel);
  }, [parcelInfo]);

  const searchableFields = useMemo(() => {
    if (!parcelData) return [];
    return createSearchableFields(parcelData);
  }, [parcelData]);

  const filteredResults = useMemo(() => {
    if (!searchingParams || !searchingParams.trim()) return [];

    const query = searchingParams.toLowerCase();
    return searchableFields.filter(
      (field) =>
        field.key.toLowerCase().includes(query) ||
        (field.value && field.value.toString().toLowerCase().includes(query))
    );
  }, [searchableFields, searchingParams]);

  const groupedResults = useMemo(() => {
    const grouped = {};
    filteredResults.forEach((field) => {
      if (!grouped[field.section]) {
        grouped[field.section] = [];
      }
      grouped[field.section].push(field);
    });
    return grouped;
  }, [filteredResults]);

  // Check for loading state after hooks
  if (!parcelInfo || !parcelInfo.parcel) {
    return (
      <div className="p-6">
        <div className="text-center text-gray-500">Loading parcel data...</div>
      </div>
    );
  }

  const formatValue = (key, value) => {
    console.log("key", key, value);
    if (value === null || value === undefined) return "N/A";
  
    if (
      key === "Taxyear" ||
      key === "Yearbuilt" ||
      key === "Year Built" ||
      key === "Estated > Market Assessment > Year" ||
      key === "Estated > Taxes > Year" || 
      key === 'Zoning Id'
    )
      return value;
    if (typeof value === "number" && value > 1000) {
      return value.toLocaleString();
    }
    return value.toString();
  };

  if (!searchingParams || !searchingParams.trim()) {
    return (
      <div className="p-6">
        <div className="text-center text-gray-500">
          Enter a search term to find information about this parcel...
        </div>
      </div>
    );
  }

  if (filteredResults.length === 0) {
    return (
      <div className="p-6">
        <div className="text-center text-gray-500">No results found for "{searchingParams}"</div>
        <div className="text-center text-sm text-gray-400 mt-2">
          Try searching for: area, owner, zoning, year built, city, census, or any keyword
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-4">
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "18px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "28.29px",
          }}
        >
          Search Results ({filteredResults.length})
        </div>
        <div className="text-sm text-gray-600 mt-1">Showing results for "{searchingParams}"</div>
      </div>

      {/* Horizontal Divider */}
      <div
        style={{
          height: "1px",
          flexShrink: 0,
          backgroundColor: "rgba(0, 0, 0, 0.12)",
          marginBottom: "16px",
        }}
      />

      {Object.entries(groupedResults).map(([section, fields]) => (
        <div key={section} className="mb-6">
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "16px",
              fontStyle: "normal",
              fontWeight: 600,
              lineHeight: "24px",
              marginBottom: "12px",
            }}
          >
            {section}
          </div>

          <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px" }}>
            {fields.map((field, index) => (
              <React.Fragment key={`${section}-${index}`}>
                <div
                  style={{
                    color: "rgba(0, 0, 0, 0.88)",
                    fontSize: "14px",
                    fontStyle: "normal",
                    fontWeight: 400,
                    lineHeight: "21px",
                  }}
                >
                  
                  {field.key.replace('Estated', 'Assessor')}
                </div>
                <div
                  style={{
                    color: "rgba(0, 0, 0, 0.88)",
                    fontSize: "14px",
                    fontStyle: "normal",
                    fontWeight: 700,
                    lineHeight: "21px",
                  }}
                >
                  {formatValue(field.key, field.value)}
                </div>
              </React.Fragment>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};
