import { ChevronLeft, ChevronRight } from "lucide-react";
import { useSelector } from "react-redux";
import { useParcelBreakdown } from "../RegridTab";
import { restructurePropertyData } from "./utils";
import { Button } from "antd";

const GeographicInfoRenderer = ({ parcelToRender }) => {
  
  console.log("parcelToRender", parcelToRender);
  const { setCurrentView } = useParcelBreakdown();
  if (!parcelToRender) {
    return <></>;
  }

  const geographic_info = parcelToRender.geographic_info;

  if (!geographic_info) {
    return <></>;
  }

  return (
    <div>
      <div className="flex cursor-pointer select-none" onClick={() => setCurrentView("GEOGRAPHICINFO")}>
        {/* Title */}
        <div
          className="text-blue-500 underline"
          style={{
            textAlign: "left",
            fontSize: "18px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "28.29px",
            marginBottom: "12px",
          }}
        >
          Geographic Info
        </div>
        <div className="px-2 items-center pt-[2px] text-blue-500">
          <ChevronRight />
        </div>
        {/* Horizontal Divider */}
        <div
          style={{
            height: "1px",
            flexShrink: 0,
            backgroundColor: "rgba(0, 0, 0, 0.12)",
            marginBottom: "16px",
          }}
        />
      </div>
      {/* Content */}
      <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px" }}>
        {/* Owner (Assessor)
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 400,
              lineHeight: "21px",
            }}
          >
            Owner (Assessor)
          </div>
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 700,
              lineHeight: "21px",
            }}
          >
            {owner.owner_assessor}
          </div> */}
      </div>
    </div>
  );
};

export default GeographicInfoRenderer;

export const GeographicInfoDetails = () => {
  const { setCurrentView, parcelInfo } = useParcelBreakdown();
  const parcelData = restructurePropertyData(parcelInfo.parcel);
  const geographicInfoDetails = parcelData.geographic_info;
  
  console.log("GeographicInfoDetails", geographicInfoDetails);

  return (
    <div className="p-6">
      <div className="flex cursor-pointer select-none" onClick={() => setCurrentView("MAIN")}>
        {/* Title */}
        <div className="px-2 items-center pt-[2px]">
          <ChevronLeft />
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            textAlign: "left",
            fontSize: "18px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "28.29px",
            marginBottom: "12px",
          }}
        >
          Geographic Info Details
        </div>
      </div>

      {/* Horizontal Divider */}
      <div
        style={{
          height: "1px",
          flexShrink: 0,
          backgroundColor: "rgba(0, 0, 0, 0.12)",
          marginBottom: "16px",
        }}
      />

      {/* Content */}
      <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px" }}>
        {/* Centroid Coordinates */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Centroid Coordinates
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {geographicInfoDetails.centroid_coordinates}
        </div>

        {/* Federal Qualified Opportunity Zone */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Federal Qualified Opportunity Zone
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {geographicInfoDetails.federal_qualified_opportunity_zone}
        </div>

        {/* Qualified Opportunity Zone Tract Number */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Qualified Opportunity Zone Tract Number
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {geographicInfoDetails.qualified_opportunity_zone_tract_number}
        </div>

        {/* Census Block */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Census Block
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {geographicInfoDetails.census_block}
        </div>

        {/* Census Blockgroup */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Census Blockgroup
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {geographicInfoDetails.census_blockgroup}
        </div>

        {/* Census Tract */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Census Tract
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {geographicInfoDetails.census_tract}
        </div>

        {/* Calculated Acres */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Calculated Acres
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {geographicInfoDetails.calculated_acres}
        </div>

        {/* Calculated Parcel Square Feet */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Calculated Parcel Square Feet
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {geographicInfoDetails.calculated_parcel_sq_ft}
        </div>

        {/* Legal Description */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Legal Description
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {geographicInfoDetails.legal_description}
        </div>

        {/* Subdivision */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Subdivision
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {geographicInfoDetails.subdivision || "N/A"}
        </div>
      </div>
    </div>
  );
};