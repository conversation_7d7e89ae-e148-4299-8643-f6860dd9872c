const SiteAddressRenderer = ({ parcelToRender }) => {
  console.log("parcelToRender1", parcelToRender);

  if (!parcelToRender) {
    return <></>;
  }

  const siteAddress = parcelToRender.site_address;

  if (!siteAddress) {
    return <></>;
  }

  return (
    <div>
      <div className="flex">
        {/* Title */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            textAlign: "left",
            fontSize: "18px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "28.29px",
            marginBottom: "12px",
          }}
        >
          Site Address
        </div>

        {/* Horizontal Divider */}
        <div
          style={{
            height: "1px",
            flexShrink: 0,
            backgroundColor: "rgba(0, 0, 0, 0.12)",
            marginBottom: "16px",
          }}
        />
      </div>
      {/* Content */}
      <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px" }}>
        {/* Site Address */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Site Address
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {siteAddress.site_address}
        </div>


        {/* Object Id */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Object Id
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",

            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {siteAddress.objectId}
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          FIPS
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",

            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {parcelToRender.owner.estated.lastsale.fips}
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          APN (Source 1)
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",

            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
              {siteAddress.parcel_number}
          
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          APN (Source 2)
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",

            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
{parcelToRender.owner.estated.lastsale.apn || '-'}
        </div>
      </div>
    </div>
  );
};

export default SiteAddressRenderer;
