// Common styles used across regrid section components
export const styles = {
  title: {
    textAlign: "left",
    fontSize: "18px",
    fontStyle: "normal",
    fontWeight: 700,
    lineHeight: "28.29px",
    marginBottom: "12px",
  },
  divider: {
    height: "1px",
    flexShrink: 0,
    backgroundColor: "rgba(0, 0, 0, 0.12)",
    marginBottom: "16px",
  },
  gridContainer: {
    display: "grid",
    gridTemplateColumns: "1fr 1fr",
    gap: "8px",
  },
  label: {
    color: "rgba(0, 0, 0, 0.88)",
    fontSize: "14px",
    fontStyle: "normal",
    fontWeight: 400,
    lineHeight: "21px",
  },
  value: {
    color: "rgba(0, 0, 0, 0.88)",
    fontSize: "14px",
    fontStyle: "normal",
    fontWeight: 700,
    lineHeight: "21px",
  },
};

// Common class names
export const classNames = {
  titleLink: "text-blue-500 underline",
  chevronContainer: "px-2 items-center pt-[2px] text-blue-500",
  clickableRow: "flex cursor-pointer select-none",
}; 