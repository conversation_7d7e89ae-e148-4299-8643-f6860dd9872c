import { ChevronLeft, ChevronRight } from "lucide-react";
import { useSelector } from "react-redux";
import { useParcelBreakdown } from "../RegridTab";
import { restructurePropertyData } from "./utils";
import { <PERSON><PERSON>, Spin } from "antd";
import { useEffect, useState } from "react";
import { useQuery } from "react-query";
import { postSkipTrace } from "../../../../services/data";

const OwnerRenderer = ({ parcelToRender }) => {
  const { setCurrentView } = useParcelBreakdown();
  const [loading, setLoading] = useState(false);
  console.log("parcelToRender", parcelToRender);

  if (!parcelToRender) {
    return <></>;
  }

  const owner = parcelToRender.owner;

  if (!owner) {
    return <></>;
  }

  return (
    <div>
      <div className="flex cursor-pointer select-none" onClick={() => setCurrentView("OWNER")}>
        {/* Title */}
        <div
        className="text-blue-500 underline"
          style={{
            textAlign: "left",
            fontSize: "18px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "28.29px",
            marginBottom: "12px",
          }}
        >
          Owner
        </div>
        <div className="px-2 items-center pt-[2px] text-blue-500">
          <ChevronRight />
        </div>
        {/* Horizontal Divider */}
        <div
          style={{
            height: "1px",
            flexShrink: 0,
            backgroundColor: "rgba(0, 0, 0, 0.12)",
            marginBottom: "16px",
          }}
        />
      </div>
      {/* Content */}
      <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px" }}>
        {/* Owner (Assessor) */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Owner (Source 1)
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {owner.owner_assessor}
        </div>
      
        {/* Owner Address (Assessor) */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Owner Address (Source 1)
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {owner.owner_address_assessor}
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Owner (Source 2)
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {owner.otherOwner}
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Owner Address (Source 2)
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {owner.otherOwnerAddress}
        </div>
        {/* Last Market Sale */}
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 400,
            lineHeight: "21px",
          }}
        >
          Last Market Sale
        </div>
        <div
          style={{
            color: "rgba(0, 0, 0, 0.88)",
            fontSize: "14px",
            fontStyle: "normal",
            fontWeight: 700,
            lineHeight: "21px",
          }}
        >
          {owner.last_market_sale}
        </div>

      </div>
    </div>
  );
};

export default OwnerRenderer;

const ContactDetailsContent = ({ contactInfo }) => {
  if (!contactInfo || !contactInfo.persons || contactInfo.persons.length === 0) {
    return (
      <div
        style={{
          color: "rgba(0, 0, 0, 0.88)",
          fontSize: "14px",
          fontStyle: "normal",
          fontWeight: 700,
          lineHeight: "21px",
          gridColumn: "1 / -1",
        }}
      >
        No contact information found
      </div>
    );
  }

  const person = contactInfo.persons[0]; // Using first person in results

  return (
    <>
      {/* Contact Name */}
      {person.name && (
        <>
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 400,
              lineHeight: "21px",
            }}
          >
            Contact Name
          </div>
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 700,
              lineHeight: "21px",
            }}
          >
            {`${person.name.first || ""} ${person.name.middle || ""} ${
              person.name.last || ""
            }`.trim()}
          </div>
        </>
      )}

      {/* Phone Numbers */}
      {person.phoneNumbers && person.phoneNumbers.length > 0 && (
        <>
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 400,
              lineHeight: "21px",
            }}
          >
            Primary Phone
          </div>
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 700,
              lineHeight: "21px",
            }}
          >
            {person.phoneNumbers[0].number} ({person.phoneNumbers[0].type})
          </div>
        </>
      )}

      {/* Additional Phone Numbers */}
      {person.phoneNumbers && person.phoneNumbers.length > 1 && (
        <>
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 400,
              lineHeight: "21px",
            }}
          >
            Additional Phones
          </div>
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 700,
              lineHeight: "21px",
            }}
          >
            {person.phoneNumbers.slice(1, 3).map((phone, index) => (
              <div key={index}>
                {phone.number} ({phone.type})
              </div>
            ))}
          </div>
        </>
      )}

      {/* Email */}
      {person.emails && person.emails.length > 0 && (
        <>
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 400,
              lineHeight: "21px",
            }}
          >
            Email
          </div>
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 700,
              lineHeight: "21px",
            }}
          >
            {person.emails[0].email}
          </div>
        </>
      )}

      {/* Mailing Address */}
      {person.mailingAddress && (
        <>
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 400,
              lineHeight: "21px",
            }}
          >
            Mailing Address
          </div>
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 700,
              lineHeight: "21px",
            }}
          >
            {`${person.mailingAddress.street}, ${person.mailingAddress.city}, ${person.mailingAddress.state} ${person.mailingAddress.zip}`}
          </div>
        </>
      )}

      {/* Property Info */}
      {person.property && (
        <>
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 400,
              lineHeight: "21px",
            }}
          >
            Equity Percent
          </div>
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 700,
              lineHeight: "21px",
            }}
          >
            {person.property.equityPercent}%
          </div>

          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 400,
              lineHeight: "21px",
            }}
          >
            Absentee Owner
          </div>
          <div
            style={{
              color: "rgba(0, 0, 0, 0.88)",
              fontSize: "14px",
              fontStyle: "normal",
              fontWeight: 700,
              lineHeight: "21px",
            }}
          >
            {person.property.absenteeOwner ? "Yes" : "No"}
          </div>
        </>
      )}
    </>
  );
};

export const OwnerDetails = () => {
  const { setCurrentView, parcelInfo } = useParcelBreakdown();
  const parcelData = restructurePropertyData(parcelInfo.parcel);
  const owner = parcelData.owner;
  const ownerDetails = owner.details;
  const other_owner = owner.otherOwner;
  const map = useSelector((state) => state.Map.map);
  const [skipTraceInitiated, setSkipTraceInitiated] = useState(false);

  console.log("OwnerDetails", parcelInfo.parcel);

  const handleClick = () => {
    if (!map) return;
    console.log("Button clicked");
    map.fire("owner-other-parcel-land-search", {
      parcel: parcelInfo.parcel,
    });
  };

  const { parcelAddress } = parcelInfo.parcel;
  console.log("parcelAddress", parcelAddress);

  const { data, isError, isLoading, refetch } = useQuery(
    ["parcel-breakdown-contact-trace", parcelAddress?.parcelNumber],
    () => {
      if (!parcelAddress) {
        throw new Error("Parcel address information is not available");
      }
      return postSkipTrace({
        requests: [
          {
            propertyAddress: {
              city: parcelAddress.siteCity || "",
              street: parcelAddress.siteAddress || "",
              state: parcelAddress.siteState || "",
              zip: parcelAddress.siteZip || "",
            },
          },
        ],
      });
    },
    {
      enabled: false,
      staleTime: 1000 * 60 * 60 * 24 * 3, // 3 days
    }
  );

  const handleSkipTraceClick = () => {
    setSkipTraceInitiated(true);
    refetch();
  };

  function formatPhoneNumber(number) {
    if (!number) return "N/A";
    const numStr = number.toString();
    if (numStr.length < 10) return numStr;
    return `${numStr.slice(0, 3)}-${numStr.slice(3, 6)}-${numStr.slice(6)}`;
  }

  // Helper function to safely get person name
  const getPersonName = (person) => {
    if (!person || !person.name) return "N/A";
    const firstName = person.name.first || "";
    const lastName = person.name.last || "";
    return `${firstName} ${lastName}`.trim() || "N/A";
  };

  // Helper function to safely format phone details
  const formatPhoneDetails = (phone) => {
    if (!phone) return null;
    
    return (
      <div className="phone-entry">
        <div className="phone-number">
          {formatPhoneNumber(phone.number)} {phone.type ? `(${phone.type})` : ""}
        </div>
        <div className="phone-details">
          Last Updated {phone.lastReportedDate ? phone.lastReportedDate.split('T')[0] : "Unknown"}
        </div>
        <div className="phone-details">
          DNC List: {phone.dnc?.tcpa ? <span className="dnc-yes">Yes</span> : "No"}
        </div>
      </div>
    );
  };

  const renderSkipTrace = () => {
    if (!skipTraceInitiated) {
      return (
        <div className="skip-trace-button-container">
          <Button type="primary" size="small" onClick={handleSkipTraceClick}>
            Get Contact Info
          </Button>
        </div>
      );
    }

    if (isLoading) {
      return (
        <div className="skip-trace-loading">
          <Spin tip="Loading skip trace..." size="small" />
        </div>
      );
    }

    if (isError) {
      return (
        <div className="skip-trace-error">
          <span className="error-message">
            Failed to fetch skip trace data
          </span>
          <Button type="primary" size="small" onClick={handleSkipTraceClick}>
            Try Again
          </Button>
        </div>
      );
    }

    // Enhanced error handling for skip trace data
    if (!data || !data.results || !data.results.persons || !Array.isArray(data.results.persons) || data.results.persons.length === 0) {
      return (
        <div className="no-contact-info">
          No contact information available
        </div>
      );
    }

    const person = data.results.persons[0];
    
    // Additional safety check for person object
    if (!person || typeof person !== 'object') {
      return (
        <div className="no-contact-info">
          Invalid contact information received
        </div>
      );
    }

    return (
      <div className="contact-info-grid">
        <div className="contact-row">
          <div className="field-label">Name</div>
          <div className="field-value">
            {getPersonName(person)}
          </div>
        </div>

        <div className="contact-row">
          <div className="field-label">Primary Phone</div>
          <div className="field-value">
            {person.phoneNumbers && Array.isArray(person.phoneNumbers) && person.phoneNumbers.length > 0 ? (
              formatPhoneDetails(person.phoneNumbers[0])
            ) : (
              "N/A"
            )}
          </div>
        </div>

        {person.phoneNumbers && Array.isArray(person.phoneNumbers) && person.phoneNumbers.length > 1 && (
          <div className="contact-row">
            <div className="field-label">Additional Phones</div>
            <div className="field-value">
              {person.phoneNumbers.slice(1).map((phone, index) => (
                <div key={index}>
                  {formatPhoneDetails(phone)}
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="contact-row">
          <div className="field-label">Email</div>
          <div className="field-value">
            {person.emails && Array.isArray(person.emails) && person.emails.length > 0
              ? person.emails.map((email, index) => (
                  <div key={index}>{email?.email || "Invalid email"}</div>
                ))
              : "N/A"}
          </div>
        </div>

        <div className="contact-row">
          <div className="field-label">Mailing Address</div>
          <div className="field-value">
            {person.mailingAddress && typeof person.mailingAddress === 'object'
              ? `${person.mailingAddress.street || ""}, ${person.mailingAddress.city || ""}, ${person.mailingAddress.state || ""} ${person.mailingAddress.zip || ""}`.replace(/,\s*,/g, ',').replace(/^,\s*|,\s*$/g, '').trim() || "N/A"
              : "N/A"}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="owner-details-container">
      {/* Header */}
      <div className="header" onClick={() => setCurrentView("MAIN")}>
        <div className="back-button">
          <ChevronLeft />
        </div>
        <div className="title">Owner Details</div>
      </div>

      <div className="divider" />

      {/* Other Parcels Button */}
      <div className="other-parcels-button-container">
        <Button
          id="owner-other-parcel-land-search"
          type="default"
          size="small"
          onClick={handleClick}
        >
          Owner's Other Parcels
        </Button>
      </div>

      {/* Owner Information Grid */}
      <div className="owner-info-grid">
        <div className="field-label">Owner Assessor</div>
        <div className="field-value">{ownerDetails?.owner_assessor || "N/A"}</div>

        <div className="field-label">Owner (Regrid)</div>
        <div className="field-value">{other_owner || "N/A"}</div>

        <div className="field-label">Mail Address</div>
        <div className="field-value">{ownerDetails?.mail_address || "N/A"}</div>

        <div className="field-label">Last Market Sale</div>
        <div className="field-value">{ownerDetails?.last_market_sale || "N/A"}</div>

        <div className="field-label">Total Assessed Value</div>
        <div className="field-value">
          ${ownerDetails?.total_assd_value?.toLocaleString() || "N/A"}
        </div>
      </div>

      {/* Skip Trace Section */}
      <div className="skip-trace-section">
        <div className="section-title">Skip Trace Contact Info</div>
        <div className="divider" />
        <div className="skip-trace-content">
          {renderSkipTrace()}
        </div>
      </div>

      <style jsx>{`
        .owner-details-container {
          padding: 24px;
        }

        .header {
          display: flex;
          align-items: center;
          cursor: pointer;
          user-select: none;
          margin-bottom: 12px;
        }

        .back-button {
          padding: 0 8px;
          display: flex;
          align-items: center;
          padding-top: 2px;
        }

        .title {
          color: rgba(0, 0, 0, 0.88);
          font-size: 18px;
          font-weight: 700;
          line-height: 28.29px;
        }

        .section-title {
          color: rgba(0, 0, 0, 0.88);
          font-size: 18px;
          font-weight: 700;
          line-height: 28.29px;
          margin-bottom: 16px;
        }

        .divider {
          height: 1px;
          background-color: rgba(0, 0, 0, 0.12);
          margin-bottom: 16px;
        }

        .other-parcels-button-container {
          display: flex;
          justify-content: center;
          margin-bottom: 16px;
        }

        .owner-info-grid,
        .contact-info-grid {
          display: block;
        }

        .contact-row {
          display: grid;
          grid-template-columns: 120px 1fr;
          gap: 16px;
          margin-bottom: 16px;
          align-items: flex-start;
        }

        .phone-entry {
          margin-bottom: 12px;
        }

        .phone-entry:last-child {
          margin-bottom: 0;
        }

        .phone-number {
          font-weight: 700;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.88);
          margin-bottom: 2px;
        }

        .phone-details {
          font-size: 12px;
          color: rgba(0, 0, 0, 0.65);
          line-height: 1.3;
        }

        .dnc-yes {
          color: #ff4d4f;
          font-weight: 600;
        }

        .field-label {
          color: rgba(0, 0, 0, 0.88);
          font-size: 14px;
          font-weight: 400;
          line-height: 21px;
        }

        .field-value {
          color: rgba(0, 0, 0, 0.88);
          font-size: 14px;
          font-weight: 700;
          line-height: 21px;
        }

        .phone-item {
          margin-bottom: 4px;
        }

        .skip-trace-section {
          margin-top: 24px;
        }

        .skip-trace-content {
          display: block;
        }

        .skip-trace-button-container,
        .skip-trace-error {
          display: flex;
          flex-direction: column;
          gap: 8px;
          align-items: center;
        }

        .skip-trace-loading {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 16px 0;
        }

        .error-message {
          color: rgba(0, 0, 0, 0.88);
          font-size: 14px;
          font-weight: 700;
          line-height: 21px;
        }

        .no-contact-info {
          color: rgba(0, 0, 0, 0.88);
          font-size: 14px;
          font-weight: 700;
          line-height: 21px;
          text-align: center;
        }
      `}</style>
    </div>
  );
};