import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useSelector, useDispatch } from "react-redux";
import { getMultiFamilyInBounds } from "../../../services/data";
import Source from "./Source";
import Layer from "./Layer";
import { geojsonTemplate } from "../../../constants";
import { initPopup } from "../MapUtility/general";

const sourceId = "MapBound-MultiFamily";

const StatusColors = {
  Existing: "#8f8d8d",
  Proposed: "#0000FF",
  "Final Planning": "#008000",
  "Under Construction": "#FFA500",
  "Under Renovation": "#FF0000",
};

const minZoom = 8;

const pointStyle = {
  id: `${sourceId}PointStyle`,
  type: "circle",
  minzoom: minZoom,
  paint: {
    "circle-radius": ["interpolate", ["linear"], ["zoom"], 10, 4, 11, 6, 12, 8],
    "circle-color": [
      "match",
      ["get", "building_status"],
      "Existing",
      "#8f8d8d",
      "Proposed",
      "#0000FF",
      "Final Planning",
      "#008000",
      "Under Construction",
      "#FFA500",
      "Under Renovation",
      "#FF0000",
      "#000000",
    ],
    "circle-stroke-width": 2,
    "circle-stroke-color": "white",
  },
};

const popup = initPopup();

const getPopupHTML = (properties) => {
  const { property_name, property_address, building_status, number_of_units } =
    properties;

  return renderToString(
    <div style={{ padding: "5px 10px" }}>
      <strong>{property_name}</strong>
      <div style={{ display: "flex", flexDirection: "column" }}>
        <span>Address: {property_address}</span>
        <span>Status: {building_status}</span>
        <span>Units: {number_of_units}</span>
      </div>
    </div>
  );
};

const MultiFamily = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const [multiFamilyGeoJSON, setMultiFamilyGeoJSON] = useState(geojsonTemplate);

  useEffect(() => {
    if (!map) return;

    const getMultiFamilyData = async () => {
      if (!currentMapLayerOptions.includes("multi family")) return;
      if (map.getZoom() < minZoom) return;
      const bounds = map.getBounds().toArray();
      const data = await getMultiFamilyInBounds({
        lng1: bounds[0][0],
        lat1: bounds[0][1],
        lng2: bounds[1][0],
        lat2: bounds[1][1],
        returnRawData: false,
        returnGeoJSON: true,
      });
      if (data && data.geojson) {
        setMultiFamilyGeoJSON(data.geojson);
      } else {
        setMultiFamilyGeoJSON(geojsonTemplate);
      }
    };

    const mouseMove = (e) => {
      const feature = e.features[0];
      if (feature) {
        map.getCanvas().style.cursor = "pointer";
        popup
          .setLngLat(feature.geometry.coordinates)
          .setHTML(getPopupHTML(feature.properties))
          .addTo(map);
      } else {
        map.getCanvas().style.cursor = "";
        popup.remove();
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    getMultiFamilyData();
    map.on("moveend", getMultiFamilyData);
    map.on("mousemove", pointStyle.id, mouseMove);
    map.on("mouseleave", pointStyle.id, mouseLeave);
    return () => {
      map.off("moveend", getMultiFamilyData);
      map.off("mousemove", pointStyle.id, mouseMove);
      map.off("mouseleave", pointStyle.id, mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("multi family")) return null;
  return (
    <Source id={`${sourceId}`} type="geojson" data={multiFamilyGeoJSON}>
      <Layer {...pointStyle} />
    </Source>
  );
};

export default MultiFamily;

export const MultiFamilyLegend = () => {
  return (
    <div className="bg-white p-[10px] min-w-[200px]">
      <div className="text-center">
        <strong>MultiFamily</strong>
      </div>
      {Object.keys(StatusColors).map((key, index) => (
        <div className="flex flex-row gap-2 items-center px-[2px] py-[5px]">
          <div
            className="w-[17.5px] h-[17.5px]"
            style={{ backgroundColor: StatusColors[key] }}
          ></div>
          <span>{key}</span>
        </div>
      ))}
    </div>
  );
};
