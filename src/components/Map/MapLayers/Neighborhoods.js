import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { initPopup } from "../MapUtility/general";
import { formatter } from "../../../utils/money";
import { renderToString } from "react-dom/server";
import { MAP_LAYER_NAME_BASE } from "../../../constants";
import { setPaintPropertySafely } from "../MapUtility/general";

const sourceId = MAP_LAYER_NAME_BASE.neighborhood;
const sourceLayer = "neighborhood_objects_usa-48d9c5";
const minZoom = 9;

const lineStyle = {
  id: `${sourceId}LineStyle`,
  "source-layer": sourceLayer,
  type: "line",
  minzoom: minZoom,
  paint: {
    "line-opacity": 0.5,
    "line-color": "#de2d26",
    "line-width": ["interpolate", ["linear"], ["zoom"], 4, 0.5, 8, 1, 12, 3],
  },
};
const fillStyle = {
  id: `${sourceId}FillStyle`,
  "source-layer": sourceLayer,
  type: "fill",
  minzoom: minZoom,
  paint: {
    "fill-opacity": 0.3,
    "fill-color": "#fee0d2",
  },
};

const typeColor = {
  M: "#841d4e",
  N: "#a82265",
  S: "#d55aa0",
};

const popup = initPopup();

function Neighborhoods() {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const neighborhoodType = useSelector((state) => state.Map.neighborhoodType);

  useEffect(() => {
    if (!map) return;

    const mouseMove = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [fillStyle.id],
      });

      if (features.length > 0) {
        const { OBJ_NAME, OBJ_AREA } = features[0].properties;
        const coordinates = [e.lngLat.lng, e.lngLat.lat];

        // OBJ_AREA is in square meters, convert to acres
        let area = Math.round((OBJ_AREA / 4047) * 100) / 100;
        let decimal =
          area - Math.floor(area) > 0 ? area.toString().split(".")[1] : 0;

        area = decimal > 0 ? formatter(area) + `.${decimal}` : formatter(area);
        let areaText = `${area} acres`;

        const htmlRender = renderToString(
          <div
            style={{
              padding: "10px",
              display: "flex",
              flexDirection: "column",
            }}
          >
            <span>
              Name: <strong>{OBJ_NAME}</strong>
            </span>
            <span>
              Area: <strong>{areaText}</strong>
            </span>
          </div>
        );
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      } else {
        popup.remove();
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    // map.on("click", click);
    map.on("mousemove", fillStyle.id, mouseMove);
    map.on("mouseleave", fillStyle.id, mouseLeave);
    return () => {
      // map.off("click", click);
      map.off("mousemove", fillStyle.id, mouseMove);
      map.off("mouseleave", fillStyle.id, mouseLeave);
    };
  }, [map]);

  useEffect(() => {
    if (!map) return;
    if (!currentMapLayerOptions.includes("neighborhood")) return;

    map.setFilter(lineStyle.id, [
      "==",
      ["get", "OBJ_SUBTCD"],
      neighborhoodType,
    ]);
    map.setFilter(fillStyle.id, [
      "==",
      ["get", "OBJ_SUBTCD"],
      neighborhoodType,
    ]);

    setPaintPropertySafely(
      map,
      lineStyle.id,
      "line-color",
      typeColor[neighborhoodType]
    );
    setPaintPropertySafely(
      map,
      fillStyle.id,
      "fill-color",
      typeColor[neighborhoodType]
    );
  }, [neighborhoodType, currentMapLayerOptions]);

  return currentMapLayerOptions.includes("neighborhood") ? (
    <Source id={sourceId} type="vector" url="mapbox://sxbxchen.1sgz1dbe">
      <Layer {...fillStyle} />
      <Layer {...lineStyle} />
    </Source>
  ) : null;
}

export default Neighborhoods;
