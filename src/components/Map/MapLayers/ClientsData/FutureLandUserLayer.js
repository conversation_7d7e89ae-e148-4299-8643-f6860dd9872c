import { useState, useEffect, useRef } from "react";
import { renderToString } from "react-dom/server";
import { useDispatch, useSelector } from "react-redux";
import { convertToGeoJSON } from "../../../../utils/geography";
import { geojsonTemplate } from "../../../../constants";
import Source from "../Source";
import Layer from "../Layer";
import { initPopup } from "../../MapUtility/general";
import { getFutureLandUse } from "../../../../services/data";
import { Button } from "antd";

const sourceId = "allied_mineola_future_land_use";

const fillStyle = {
  id: `${sourceId}-fill`,
  type: "fill",
  source: sourceId,
  paint: {
    "fill-color": "#1A7770",
    "fill-opacity": 0.6,
  },
};

const outlineStyle = {
  id: `${sourceId}-outline`,
  type: "line",
  source: sourceId,
  paint: {
    "line-color": "#ffffff",
    "line-width": 1,
    "line-opacity": 0.8,
  },
};

const getTooltipHTML = (properties) => {
  return renderToString(
    <div
      style={{
        padding: "10px",
        backgroundColor: "white",
        borderRadius: "4px",
      }}
    >
      <span>FLU Code: {properties.flucode}</span>
      <br />
      <span>Area: {properties.acres.toFixed(2)} acres</span>
    </div>
  );
};

let popup = initPopup();

const FutureLandUseLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const dispatch = useDispatch();
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const [geojson, setGeojson] = useState(geojsonTemplate);
  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("allied_mineola_future_land_use")) return;
    map.fire("map.setThemeOption", {
      payload: { currentMapThemeOption: "Automatic" },
    });
    const loadData = async () => {
      try {
        const data = await getFutureLandUse();
        console.log("Raw data:", data);

        const converted = convertToGeoJSON({
          data,
          geomAccessor: (item) => item.geom,
          propertiesAccessor: (item) => ({
            flucode: item.flucode,
            acres: item.acres,
            city: item.city,
            objectid: item.objectid,
            globalid: item.globalid,
          }),
        });

        console.log("Converted geojson:", converted);
        setGeojson(converted);
        if (converted.features && converted.features.length > 0) {
          const firstFeature = converted.features[0];
          const coords = firstFeature.geometry.coordinates[0][0][0];
          dispatch({
            type: "Map/saveState",
            payload: {
              FutureLandUseFly: coords,
            },
          });
        }

        if (map.getSource(sourceId)) {
          console.log("Source exists:", sourceId);
        }
        if (map.getLayer(`${sourceId}-fill`)) {
          console.log("Fill layer exists");
        }
        if (map.getLayer(`${sourceId}-outline`)) {
          console.log("Outline layer exists");
        }
      } catch (error) {
        console.error("Error loading future land use data:", error);
      }
    };

    const mouseMove = (e) => {
      if (e.features && e.features.length > 0) {
        map.getCanvas().style.cursor = "pointer";
        const feature = e.features[0];
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = () => {
      map.getCanvas().style.cursor = "";
      popup.remove();
    };

    loadData();

    map.on("mouseenter", fillStyle.id, mouseMove);
    map.on("mousemove", fillStyle.id, mouseMove);
    map.on("mouseleave", fillStyle.id, mouseLeave);

    return () => {
      map.off("mouseenter", fillStyle.id, mouseMove);
      map.off("mousemove", fillStyle.id, mouseMove);
      map.off("mouseleave", fillStyle.id, mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  useEffect(() => {
    if (map && geojson.features && geojson.features.length > 0) {
      const firstFeature = geojson.features[0];
      if (firstFeature.geometry && firstFeature.geometry.coordinates) {
        console.log("First feature coordinates:", firstFeature.geometry.coordinates);
        console.log("Current map center:", map.getCenter());
        console.log("Current map zoom:", map.getZoom());
      }
    }
  }, [map, geojson]);

  if (!currentMapLayerOptions.includes("allied_mineola_future_land_use")) return null;

  return (
    <Source id={sourceId} type="geojson" data={geojson}>
      <Layer {...fillStyle} />
      <Layer {...outlineStyle} />
    </Source>
  );
};

export default FutureLandUseLayer;

export function FutureLandUseLegend() {
  const FutureLandUseFly = useSelector((state) => state.Map.FutureLandUseFly);
  const SanitarySewer = useSelector((state) => state.Map.SanitarySewer);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const map = useSelector((state) => state.Map.map);
  const fly = (coords) => {
    map.flyTo({
      center: [coords[0], coords[1]],
      zoom: 12,
      essential: true,
    });
  };
  return (
    <div className="bg-white z-[200] shadow-md rounded-md min-w-[300px] pb-5">
      <>
        <div className="flex flex-row justify-center p-2">
          <span className="font-[600]">Client's Data</span>
        </div>
        {currentMapLayerOptions.includes("allied_mineola_future_land_use") && (
          <div className="flex flex-row justify-between py-[6px] px-[10px]">
            <p>Mineola Future Land Use</p>
            <Button type="primary" size="small" onClick={()=>fly(FutureLandUseFly)}>
              Zoom
            </Button>
          </div>
        )}
        {currentMapLayerOptions.includes("allied_sanitary_sewer") && (
          <div className="flex flex-row justify-between py-[1px] px-[10px]">
            <p>Roanoke Sanitary Sewer</p>
            <Button type="primary" size="small" onClick={()=> fly(SanitarySewer)}>
              Zoom
            </Button>
          </div>
        )}
      </>
    </div>
  );
}
