import { useState, useEffect, useRef } from "react";
import { renderToString } from "react-dom/server";
import { useDispatch, useSelector } from "react-redux";
import { convertToGeoJSON } from "../../../../utils/geography";
import { geojsonTemplate } from "../../../../constants";
import Source from "../Source";
import Layer from "../Layer";
import { initPopup } from "../../MapUtility/general";
import { getSanitaySewer } from "../../../../services/data";

const sourceId = "allied_sanitary_sewer";
const lineStyle = {
  id: `${sourceId}-line`,
  type: "line",
  source: sourceId,
  paint: {
    "line-color": "#1A7770",
    "line-width": 3,
    "line-opacity": 0.8,
  },
};

const getTooltipHTML = (properties) => {
  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
        gap: "4px",
        backgroundColor: "white",
        borderRadius: "4px",
      }}
    >
      <span>
        <strong>{properties.pwname}</strong>
      </span>
      <span>Diameter: {properties.pipe_dia}</span>
      <span>Material: {properties.material}</span>
      <span>Type: {properties.type}</span>
      <span>Owner: {properties.owner}</span>
    </div>
  );
};

let popup = initPopup();

const SanitarySewerLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const [geojson, setGeojson] = useState(geojsonTemplate);
const dispatch = useDispatch()

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("allied_sanitary_sewer")) return;
    map.fire("map.setThemeOption", {
      payload: { currentMapThemeOption: "Automatic" },
    });
    const loadData = async () => {
      const data = await getSanitaySewer();
      const converted = convertToGeoJSON({
        data,
        geomAccessor: (item) => item.geom,
        propertiesAccessor: (item) => {
          const { geom, ...properties } = item;
          return properties;
        },
      });

      setGeojson(converted);

      // Only fly to location if it hasn't happened before
      if (converted.features && converted.features.length > 0) {
        const firstFeature = converted.features[0];
        const coords = firstFeature.geometry.coordinates[0][0];
        dispatch({
          type: "Map/saveState",
          payload: {
            SanitarySewer: coords,
          },
        });
      }
    };

    const mouseMove = (e) => {
      if (e.features && e.features.length > 0) {
        map.getCanvas().style.cursor = "pointer";
        const feature = e.features[0];
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = () => {
      map.getCanvas().style.cursor = "";
      popup.remove();
    };

    loadData();

    map.on("mouseenter", lineStyle.id, mouseMove);
    map.on("mousemove", lineStyle.id, mouseMove);
    map.on("mouseleave", lineStyle.id, mouseLeave);

    return () => {
      map.off("mouseenter", lineStyle.id, mouseMove);
      map.off("mousemove", lineStyle.id, mouseMove);
      map.off("mouseleave", lineStyle.id, mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("allied_sanitary_sewer")) return null;

  return (
    <Source id={sourceId} type="geojson" data={geojson}>
      <Layer {...lineStyle} />
    </Source>
  );
};

export default SanitarySewerLayer;

