import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useDispatch, useSelector } from "react-redux";
import { geojsonTemplate } from "../../../../constants";
import Source from "../Source";
import Layer from "../Layer";
import { initPopup } from "../../MapUtility/general";
import { getSAPublicHousingProperty } from "../../../../services/data";

const sourceId = "second_ave_public_housing_property";
const pointStyle = {
  id: `${sourceId}-point`,
  type: "circle",
  source: sourceId,
  paint: {
    "circle-radius": 6,
    "circle-color": "#005b94",
    "circle-stroke-color": "#ffffff",
    "circle-stroke-width": 2,
  },
};

const getTooltipHTML = (properties) => {
  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
        gap: "4px",
        backgroundColor: "white",
        borderRadius: "4px",
      }}
    >
      <strong>ID: {properties.id}</strong>
      <span>
        <strong>Address: {properties.address}</strong>
      </span>
    </div>
  );
};

let popup = initPopup();

// Function to convert the data to GeoJSON format
const convertToGeoJSON = (data) => {
  return {
    type: "FeatureCollection",
    features: data.map((item) => ({
      type: "Feature",
      geometry: {
        type: "Point",
        coordinates: [item.longitude, item.latitude]
      },
      properties: {
        id: item.id,
        address: item.address,
        city: item.city,
        state: item.state,
        zip_code: item.zip_code
      }
    }))
  };
};

const PublicHousingPropertyLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const [geojson, setGeojson] = useState(geojsonTemplate);
  const dispatch = useDispatch();

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("second_ave_public_housing_property")) return;
    
    const loadData = async () => {
      try {
        // Get data from API
        const data = await getSAPublicHousingProperty();
        
        // Convert data to GeoJSON format
        const converted = convertToGeoJSON(data);
        console.log("second_ave_public_housing_property", converted);
        setGeojson(converted);

        // Fly to first location if available
        if (converted.features && converted.features.length > 0) {
          const firstFeature = converted.features[0];
          const coords = firstFeature.geometry.coordinates;
          
          dispatch({
            type: "Map/saveState",
            payload: {
              SanitarySewer: coords,
            },
          });
        
        }
      } catch (error) {
        console.error("Error loading public housing data:", error);
      }
    };

    const mouseMove = (e) => {
      if (e.features && e.features.length > 0) {
        map.getCanvas().style.cursor = "pointer";
        const feature = e.features[0];
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = () => {
      map.getCanvas().style.cursor = "";
      popup.remove();
    };

    loadData();

    map.on("mouseenter", pointStyle.id, mouseMove);
    map.on("mousemove", pointStyle.id, mouseMove);
    map.on("mouseleave", pointStyle.id, mouseLeave);

    return () => {
      map.off("mouseenter", pointStyle.id, mouseMove);
      map.off("mousemove", pointStyle.id, mouseMove);
      map.off("mouseleave", pointStyle.id, mouseLeave);
    };
  }, [map, currentMapLayerOptions, dispatch]);

  if (!currentMapLayerOptions.includes("second_ave_public_housing_property")) return null;

  return (
    <Source id={sourceId} type="geojson" data={geojson}>
      <Layer {...pointStyle} />
    </Source>
  );
};

export default PublicHousingPropertyLayer;