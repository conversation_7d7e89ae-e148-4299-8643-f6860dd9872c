import { useState, useEffect, useRef } from "react";
import { renderToString } from "react-dom/server";
import { useDispatch, useSelector } from "react-redux";
import { convertToGeoJSON } from "../../../../utils/geography";
import { geojsonTemplate } from "../../../../constants";
import Source from "../Source";
import Layer from "../Layer";
import { initPopup } from "../../MapUtility/general";
import { getCoreSpacesPortfolio } from "../../../../services/data";

const sourceId = "core_spaces_portfolio";
const pointStyle = {
    id: `${sourceId}-point`,
    type: "circle",
    source: sourceId,
    paint: {
      "circle-radius": 6,
      "circle-color": "#1A7770",
      "circle-stroke-color": "#ffffff",
      "circle-stroke-width": 2,
    },
  };
const getTooltipHTML = (properties) => {
  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
        gap: "4px",
        backgroundColor: "white",
        borderRadius: "4px",
      }}
    >
      <span>
        <strong>Address: {properties.address}</strong>
      </span>
      <span>Current Occupancy: {properties.currentOccupancy}</span>
      <span>Home Type: {properties.homeType}</span>
      <span>Projected Completion Date: {properties.projectedCompletionDate}</span>
      <span># of Units: {properties.totalUnits}</span>
    </div>
  );
};

let popup = initPopup();

const CoreSpacesPortfolioLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const [geojson, setGeojson] = useState(geojsonTemplate);
const dispatch = useDispatch()

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("core_spaces_portfolio")) return;
    map.fire("map.setThemeOption", {
      payload: { currentMapThemeOption: "Automatic" },
    });
    const loadData = async () => {
      const data = await getCoreSpacesPortfolio();
      const converted = convertToGeoJSON({
        data,
        geomAccessor: (item) => item.geom,
        propertiesAccessor: (item) => {
          const { geom, ...properties } = item;
          return properties;
        },
      });
      console.log("core_spaces_portfolio", converted);
      setGeojson(converted);

      // Only fly to location if it hasn't happened before
      if (converted.features && converted.features.length > 0) {
        const firstFeature = converted.features[0];
        const coords = firstFeature.geometry.coordinates[0][0];
        dispatch({
          type: "Map/saveState",
          payload: {
            SanitarySewer: coords,
          },
        });
      }
    };

    const mouseMove = (e) => {
      if (e.features && e.features.length > 0) {
        map.getCanvas().style.cursor = "pointer";
        const feature = e.features[0];
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = () => {
      map.getCanvas().style.cursor = "";
      popup.remove();
    };

    loadData();

    map.on("mouseenter", pointStyle.id, mouseMove);
    map.on("mousemove", pointStyle.id, mouseMove);
    map.on("mouseleave", pointStyle.id, mouseLeave);

    return () => {
      map.off("mouseenter", pointStyle.id, mouseMove);
      map.off("mousemove", pointStyle.id, mouseMove);
      map.off("mouseleave", pointStyle.id, mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("core_spaces_portfolio")) return null;

  return (
    <Source id={sourceId} type="geojson" data={geojson}>
      <Layer {...pointStyle} />
    </Source>
  );
};

export default CoreSpacesPortfolioLayer;

