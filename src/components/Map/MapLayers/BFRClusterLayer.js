import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { getBFRClusterData } from "../../../services/data";
import { convertToGeoJSON } from "../../../utils/geography";
import { geojsonTemplate } from "../../../constants";
import Source from "./Source";
import Layer from "./Layer";
import { initPopup } from "../MapUtility/general";
import { renderToString } from "react-dom/server";

const sourceId = "bfr cluster-source";
const pointStyle = {
  id: "bfr cluster-point",
  type: "circle",
  source: sourceId,
  paint: {
    "circle-radius": ["step", ["zoom"], 8, 11, 14],
    "circle-color": "#FFa770",
    "circle-stroke-color": "#ffffff",
    "circle-stroke-width": 2,
  },
};

const textStyle = {
  id: "bfr cluster-label",
  type: "symbol",
  source: sourceId,
  layout: {
    "text-field": ["step", ["zoom"], "", 11, ["get", "totalCount"]],
    "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"],
    "text-size": 12,
    "text-offset": [0, -0.55],
    "text-anchor": "top",
    "text-allow-overlap": true,
    "icon-allow-overlap": true,
    "text-ignore-placement": true,
  },
  paint: {
    "text-color": "#000",
  },
};
const getFirst3Words = (str) => {
  if (!str) return '';
  return str.split(' ')
    .slice(0, 4)
    .join(' ')
    .trim() + (str.split(' ').length > 3 ? '...' : '');
};

const BFRClusterLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const [geojson, setGeojson] = useState(geojsonTemplate);

  // Create tooltip HTML content
  const getTooltipHTML = (properties) => {
    console.log("BFRClusterLayer", properties)
    return renderToString(
      <div style={{ padding: "10px", display: "flex", flexDirection: "column" }}>
        
        <span>
          <span style={{ fontWeight: "600" }}>Community Name:</span> {getFirst3Words(properties.commonSubdivision)}
        </span>
        {properties.website &&  <span>
          <span style={{ fontWeight: "600" }}>Website:</span> {properties.website}
        </span>}
       
      </div>
    );
  };

  // Initialize popup
  let popup = initPopup();

  // Mouse event handlers for tooltip
  const onMouseMove = (e) => {
    if (e.features.length > 0) {
      const feature = e.features[0];
      const coordinates = feature.geometry.coordinates.slice();
      const properties = feature.properties;

      // Ensure proper wrapping of coordinates
      while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
        coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
      }

      popup.setLngLat(coordinates).setHTML(getTooltipHTML(properties)).addTo(map);
    }
  };

  const onMouseLeave = () => {
    popup.remove();
  };

  // Fetch data effect
  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("bfr cluster")) return;

    const fetchData = async () => {
      const data = await getBFRClusterData();
      const convertedGeojson = convertToGeoJSON({
        data,
        geomAccessor: (item) => item.geom,
        propertiesAccessor: (item) => {
          const { geom, ...properties } = item;
          return properties;
        },
      });
      setGeojson(convertedGeojson);
    };

    fetchData();
  }, [map, currentMapLayerOptions]);

  // Setup mouse events effect
  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("bfr cluster")) return;

    map.on("mousemove", "bfr cluster-point", onMouseMove);
    map.on("mouseleave", "bfr cluster-point", onMouseLeave);

    return () => {
      map.off("mousemove", "bfr cluster-point", onMouseMove);
      map.off("mouseleave", "bfr cluster-point", onMouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("bfr cluster")) return null;

  return (
    <>
      <Source id={sourceId} type="geojson" data={geojson}>
        <Layer {...pointStyle} />
        <Layer {...textStyle} />
      </Source>
    </>
  );
};

export default BFRClusterLayer;