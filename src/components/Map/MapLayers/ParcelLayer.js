import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { renderToString } from "react-dom/server";
import Source from "./Source";
import Layer from "./Layer";
import {
  setParcelColorBySubdivision,
  setParcelColorByOwnerAndInstitution,
  setParcelColorByHOAFees,
  setParcelColorByOwnerAndInstitutionV2,
  setParcelColorByHOAFeesV2,
} from "../MapUtility/colors";
import { initPopup } from "../MapUtility/general";
import PropertyDetailPopup from "../MapControls/MapPopup/PropertyDetailPopup";
import { MAP_LAYER_NAME_BASE, PARCEL_TYPE } from "../../../constants";
import { tileURLRoot } from "../../../services/data";

export const zoomLevelToChangeBasemap = 14;
export const zoomLevelToShowParcelAVM = 17;

const sourceId = MAP_LAYER_NAME_BASE.parcel;
const sourceLayer = "parcel-dots";

const circleStyle = {
  id: `${sourceId}Layer`,
  type: "circle",
  "source-layer": sourceLayer,
  minzoom: zoomLevelToChangeBasemap,
  maxzoom: zoomLevelToShowParcelAVM,
  paint: {
    "circle-radius": [
      "interpolate",
      ["linear"],
      ["zoom"],
      zoomLevelToChangeBasemap,
      2,
      zoomLevelToShowParcelAVM,
      6,
    ],
    "circle-color": "#092d99",
    "circle-stroke-color": "#fff",
    "circle-stroke-width": 1,
  },
};

const avmStyle = {
  id: `${sourceId}LayerRentAVM`,
  type: "symbol",
  "source-layer": sourceLayer,
  minzoom: zoomLevelToShowParcelAVM,
  layout: {
    visibility: "visible",
    // "text-field": "${rent}",
    "text-field": [
      "case",
      ["==", ["get", "rent"], null],
      "",
      ["==", ["get", "rent"], 0],
      "",
      ["concat", "$", ["to-string", ["get", "rent"]]],
    ],
    "text-variable-anchor": ["center"],
    "text-justify": "center",
    "text-radial-offset": 1,
    // On how to use custom fonts in Mapbox, see: https://github.com/mapbox/mapbox-gl-js/issues/6666
    "text-font": [
      "Source Sans Pro Bold",
      "Open Sans Bold", // fallback font
    ],
    "text-size": [
      "interpolate",
      ["linear"],
      ["zoom"],
      zoomLevelToChangeBasemap,
      12,
      // 19, 15,
      22,
      20,
    ],
    "icon-allow-overlap": true,
    "text-allow-overlap": true,
  },
  paint: {
    // "text-color": "#fff",
    "text-halo-color": "#000",
    // "text-halo-width": 20, // use halo instead of an icon for text background
    "text-halo-width": [
      "case",
      ["==", ["get", "rent"], null],
      0,
      ["==", ["get", "sales"], null],
      0,
      ["==", ["get", "rent"], 0],
      0,
      ["==", ["get", "sales"], 0],
      0,
      20,
    ],
    "text-color": [
      "case",
      ["==", ["get", "rent"], null],
      "transparent",
      ["==", ["get", "rent"], 0],
      "transparent",
      "#fff",
    ],
  },
};

const nullAVMStyle = (leaseMode) => ({
  id: `${sourceId}LayerRentNullAVM`,
  type: "circle",
  "source-layer": sourceLayer,
  minzoom: zoomLevelToShowParcelAVM,
  paint: {
    "circle-radius": [
      "interpolate",
      ["linear"],
      ["zoom"],
      zoomLevelToChangeBasemap,
      3,
      zoomLevelToShowParcelAVM,
      6,
    ],
    // "circle-color": "#092d99",
    "circle-color": [
      "case",
      ["==", ["get", leaseMode ? "rent" : "sales"], null],
      "#092d99",
      "transparent",
    ],
    // "circle-stroke-color": "#fff",
    "circle-stroke-color": [
      "case",
      ["==", ["get", leaseMode ? "rent" : "sales"], null],
      "#fff",
      "transparent",
    ],
    "circle-stroke-width": [
      "case",
      ["==", ["get", leaseMode ? "rent" : "sales"], null],
      1,
      0,
    ],
  },
});

const setAVMLayerToSaleOrRent = (map, type) => {
  if (type === "sale") {
    map.setLayoutProperty(
      "parcelMapScopeLayerRentAVM",
      "text-field",
      // '${sales}',
      [
        "case",
        ["==", ["get", "sales"], null],
        "",
        ["==", ["get", "sales"], 0],
        "",
        // if list_price > 999999 after conversion...
        [">", ["get", "sales"], 999999],
        // ... here's the output:
        [
          "concat",
          [
            "number-format",
            ["/", ["get", "sales"], 1000000],
            { "min-fraction-digits": 0, "max-fraction-digits": 2 },
          ],
          "M",
        ],
        // else if 999499 < list_price < 999999...
        [
          "all",
          [">", ["get", "sales"], 999499],
          ["<", ["get", "sales"], 1000000],
        ],
        //... here's the output:
        "1M",
        // else if list price <= 999999, here's the output
        ["concat", ["round", ["/", ["get", "sales"], 1000]], "K"],
      ]
    );
  } else {
    map.setLayoutProperty(
      "parcelMapScopeLayerRentAVM",
      "text-field",
      // "${rent}"
      [
        "case",
        ["==", ["get", "rent"], null],
        "",
        ["==", ["get", "rent"], 0],
        "",
        ["concat", "$", ["to-string", ["get", "rent"]]],
      ]
    );
  }
};

let propertyDetailPopup = initPopup();

export let parcelHovered = false;

const getTileURL = (serverType, token) =>
  `${tileURLRoot(
    "cma",
    serverType
  )}/parcel/tile/{z}/{x}/{y}.mvt?access_token=${token}`;

const ParcelLayer = ({ userGroup }) => {
  const serverType = useSelector((state) => state.Configure.serverType);
  const map = useSelector((state) => state.Map.map);
  const parcelTypeShown = useSelector((state) => state.Map.parcelTypeShown);
  const selectedOwnerParcel = useSelector(
    (state) => state.Map.selectedOwnerParcel
  );
  const selectedHOAFeeParcel = useSelector(
    (state) => state.Map.selectedHOAFeeParcel
  );
  const CMALeaseMode = useSelector((state) => state.Map.CMALeaseMode);

  const [token, setToken] = useState(null);
  const parcelSubdivisionColor = useRef([]);
  parcelHovered = useRef(false);

  useEffect(() => {
    if (!map) return;

    const checkForLatestToken = async () => {
      const newToken = await window.spatiallaser.getUserToken("access");
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    const setStyles = () => {
      if (parcelTypeShown === PARCEL_TYPE.owner) {
        setParcelColorByOwnerAndInstitutionV2(
          map,
          selectedOwnerParcel,
          CMALeaseMode
        );
      } else if (parcelTypeShown === PARCEL_TYPE.hoaFee) {
        setParcelColorByHOAFeesV2(map, selectedHOAFeeParcel, CMALeaseMode);
      } else {
        const data = map.querySourceFeatures(sourceId, {
          sourceLayer: sourceLayer,
        });

        if (!data.length) return;

        const subdivisions = [];
        for (let i = 0; i < data.length; i++) {
          subdivisions.push(data[i].properties.subdivisionWithoutPhase);
        }
        const uniqueSubdivisions = [...new Set(subdivisions)];

        const fillColor = setParcelColorBySubdivision(
          map,
          uniqueSubdivisions,
          parcelSubdivisionColor.current,
          CMALeaseMode
        );
        parcelSubdivisionColor.current = fillColor;
      }
    };

    const sourceData = (e) => {
      if (e.sourceId === sourceId && e.isSourceLoaded) {
        setStyles();
      }
    };

    const mouseEnter = (e) => {
      const coordinates = e.features[0].geometry.coordinates.slice();

      const htmlText = renderToString(
        <PropertyDetailPopup
          hoverPropertyDetails={{
            ...e.features[0].properties,
            type: sourceId,
          }}
        />
      );

      parcelHovered.current = true;
      propertyDetailPopup.setLngLat(coordinates).setHTML(htmlText).addTo(map);
    };

    const mouseLeave = () => {
      parcelHovered.current = false;
      propertyDetailPopup.remove();
    };

    setStyles();
    checkForLatestToken();
    map.on("sourcedata", sourceData);
    map.on("movestart", checkForLatestToken);
    map.on("mouseenter", `${sourceId}LayerRentAVM`, mouseEnter);
    map.on("mouseleave", `${sourceId}LayerRentAVM`, mouseLeave);
    map.on("mouseenter", `${sourceId}LayerRentNullAVM`, mouseEnter);
    map.on("mouseleave", `${sourceId}LayerRentNullAVM`, mouseLeave);
    return () => {
      map.off("movestart", checkForLatestToken);
      map.off("sourcedata", sourceData);
      map.off("mouseenter", `${sourceId}LayerRentAVM`, mouseEnter);
      map.off("mouseleave", `${sourceId}LayerRentAVM`, mouseLeave);
      map.off("mouseenter", `${sourceId}LayerRentNullAVM`, mouseEnter);
      map.off("mouseleave", `${sourceId}LayerRentNullAVM`, mouseLeave);
    };
  }, [
    map,
    parcelTypeShown,
    selectedOwnerParcel,
    selectedHOAFeeParcel,
    CMALeaseMode,
  ]);

  useEffect(() => {
    if (!map || !map.style || !map.style._loaded) return;
    if (!map.getLayer("parcelMapScopeLayerRentAVM")) return;

    if (CMALeaseMode) {
      setAVMLayerToSaleOrRent(map, "Lease");
    } else {
      setAVMLayerToSaleOrRent(map, "sale");
    }
  }, [CMALeaseMode]);

  if (!token) return null;

  return (
    <Source id={sourceId} type="vector" tiles={[getTileURL(serverType, token)]}>
      <Layer {...circleStyle} />
      {userGroup && !userGroup.includes("ILE") && (
        <Layer {...avmStyle} />
      )}
      <Layer {...nullAVMStyle(CMALeaseMode)} />
    </Source>
  );
};

export default ParcelLayer;
