import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { getAdminTaxNationalLegendData } from "../../../services/data";
import { initPopup } from "../MapUtility/general";
import { renderToString } from "react-dom/server";
import { tileURLRoot } from "../../../services/data";

const sourceId = "tax";
const sourceLayer = "admin_tract";

const fillStyle = {
  id: "tax-fill",
  type: "fill",
  source: sourceId,
  "source-layer": sourceLayer,
  paint: {
    "fill-color": [
      "case",
      ["==", ["get", "quantile_rank"], 1],
      "#1a9641",
      ["==", ["get", "quantile_rank"], 2],
      "#58b453",
      ["==", ["get", "quantile_rank"], 3],
      "#97d265",
      ["==", ["get", "quantile_rank"], 4],
      "#c4e687",
      ["==", ["get", "quantile_rank"], 5],
      "#ecf7ad",
      ["==", ["get", "quantile_rank"], 6],
      "#ffedab",
      ["==", ["get", "quantile_rank"], 7],
      "#fec981",
      ["==", ["get", "quantile_rank"], 8],
      "#ffb987",
      ["==", ["get", "quantile_rank"], 9],
      "#e85b3a",
      ["==", ["get", "quantile_rank"], 10],
      "#d7191c",
      "transparent", // Default case if none of the above conditions are met
    ],
    "fill-opacity": 0.5,
  },
};

const popup = initPopup();

const popupHTML = (properties) => {
  const content = (
    <div style={{ padding: "10px", fontSize: "16px" }}>
      <span>
        Tax Rate: <strong>{properties.tax_rate.toFixed(2)}%</strong>
      </span>
    </div>
  );
  return renderToString(content);
};

const getTileURL = (serverType, token) =>
  `${tileURLRoot(
    "cma",
    serverType
  )}/admin-tract/tile/{z}/{x}/{y}.mvt?token=${token}`;

const TaxLayer = () => {
  const serverType = useSelector((state) => state.Configure.serverType);
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const [token, setToken] = useState(null);

  useEffect(() => {
    if (!map) return;

    const mouseMove = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [fillStyle.id],
      });
      if (features.length > 0) {
        const feature = features[0];
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        if (!feature.properties.tax_rate) return;
        popup
          .setLngLat(coordinates)
          .setHTML(popupHTML(feature.properties))
          .addTo(map);
      } else {
        popup.remove();
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    const checkForLatestToken = async () => {
      const newToken = await window.spatiallaser.getUserToken("access");
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    checkForLatestToken();
    map.on("movestart", checkForLatestToken);
    map.on("mousemove", fillStyle.id, mouseMove);
    map.on("mouseleave", fillStyle.id, mouseLeave);
    return () => {
      map.off("movestart", checkForLatestToken);
      map.off("mousemove", fillStyle.id, mouseMove);
      map.off("mouseleave", fillStyle.id, mouseLeave);
    };
  }, [map]);

  if (!token || !currentMapLayerOptions.includes("tax")) return null;
  return (
    <>
      <Source
        id={sourceId}
        type="vector"
        tiles={[getTileURL(serverType, token)]}
      >
        <Layer {...fillStyle} />
      </Source>
    </>
  );
};

export default TaxLayer;

const ranking = {
  1: "#1a9641",
  2: "#58b453",
  3: "#97d265",
  4: "#c4e687",
  5: "#ecf7ad",
  6: "#ffedab",
  7: "#fec981",
  8: "#ffb987",
  9: "#e85b3a",
  10: "#d7191c",
};
export const TaxLayerLegend = () => {
  const [legendData, setLegendData] = useState(null);

  useEffect(() => {
    const getLegendValues = async () => {
      const data = await getAdminTaxNationalLegendData();

      if (data) {
        const rawValues = data
          .sort((a, b) => a.quantile_rank - b.quantile_rank)
          .reduce((acc, curr) => {
            acc[curr.quantile_rank] = curr.max_raw_value;
            return acc;
          }, {});
        setLegendData(rawValues);
      }
    };
    getLegendValues();
  }, []);

  return (
    <div className="flex flex-col p-[10px] gap-[10px] bg-white">
      <div className="flex flex-col text-center">
        <span className="font-semibold text-base">
          Property Tax Millage Rate %
        </span>
      </div>
      <div>
        {!legendData && <span>Retrieving tax rates...</span>}
        {legendData && (
          <>
            <div className="flex flex-row">
              {Object.entries(legendData).map(([key], idx) => (
                <div key={idx} className="flex flex-row items-center">
                  <div
                    style={{
                      width: "40px",
                      height: "40px",
                      backgroundColor: ranking[key],
                    }}
                  ></div>
                </div>
              ))}
            </div>
            <div className="flex flex-row">
              {Object.entries(legendData).map(([key, value], idx) => (
                <div key={idx} className="flex flex-row items-center">
                  <div className="text-right w-[40px]">
                    {legendData[key].toFixed(2)}
                  </div>
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </div>
  );
};
