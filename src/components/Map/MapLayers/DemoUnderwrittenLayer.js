import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useSelector } from "react-redux";
import { getDemoUnderwrittenData } from "../../../services/data";
import { convertToGeoJSON } from "../../../utils/geography";
import { geojsonTemplate } from "../../../constants";
import Source from "./Source";
import Layer from "./Layer";
import { initPopup } from "../MapUtility/general";
import { getTooltipHTML } from "./OwnedProperty/DemoOwnedProperty";

const sourceId = "demo-underwritten-source";

const pointStyle = {
  id: "demo-underwritten-point",
  type: "circle",
  source: sourceId,
  paint: {
    "circle-radius": 7,
    "circle-color": "#41a390",
    "circle-stroke-color": "#ffffff",
    "circle-stroke-width": 2,
  },
};

const clusterStyle = {
  id: "demo-underwritten-cluster",
  type: "circle",
  source: sourceId,
  filter: ["has", "point_count"],
  paint: {
    "circle-color": "#41a390",
    "circle-radius": [
      "interpolate",
      ["linear"],
      ["zoom"],
      0,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 18],
      10.4,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 18],
      12,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 18],
    ],
    "circle-stroke-color": "#ffffff",
    "circle-stroke-width": 2,
  },
};

const clusterCountStyle = {
  id: "demo-underwritten-cluster-count",
  type: "symbol",
  source: sourceId,
  filter: ["has", "point_count"],
  layout: {
    "text-field": "{point_count_abbreviated}",
    "text-font": ["Open Sans Bold"],
    "text-size": 16,
  },
};

let popup = initPopup();

const DemoUnderwrittenLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const [geojson, setGeojson] = useState(geojsonTemplate);

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("demo underwritten")) return;

    const moveEnd = async (e) => {
      const mapBounds = map.getBounds();
      const coord1 = mapBounds.getSouthWest();
      const coord2 = mapBounds.getNorthEast();

      const data = await getDemoUnderwrittenData({
        lat1: coord1.lng,
        lng1: coord1.lat,
        lat2: coord2.lng,
        lng2: coord2.lat,
      });

      setGeojson(
        convertToGeoJSON({
          data,
          geomAccessor: (item) => item.geom,
          propertiesAccessor: (item) => {
            const { geom, ...properties } = item;
            return properties;
          },
        })
      );
    };

    const mouseMove = (e) => {
      const feature = e.features[0];

      if (feature && feature.properties && !feature.properties.point_count) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    const moveBehindOwnedLayer = () => {
      const layers = map.getStyle().layers;
      let foundOwnedLayer = false;
      for (let i = layers.length - 1; i >= 0; i--) {
        if (layers[i].source && layers[i].source.includes("OwnedProperty")) {
          foundOwnedLayer = true;
        } else if (
          layers[i].source &&
          !layers[i].source.includes("OwnedProperty") &&
          foundOwnedLayer
        ) {
          map.moveLayer(pointStyle.id, layers[i].id);
          break;
        }
      }
    };

    moveEnd();
    moveBehindOwnedLayer();

    map.on("moveend", moveEnd);
    map.on("mousemove", "demo-underwritten-point", mouseMove);
    map.on("mouseleave", "demo-underwritten-point", mouseLeave);
    return () => {
      map.off("moveend", moveEnd);
      map.off("mousemove", "demo-underwritten-point", mouseMove);
      map.off("mouseleave", "demo-underwritten-point", mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("demo underwritten")) return null;
  return (
    <>
      <Source
        id={sourceId}
        type="geojson"
        data={geojson}
        cluster={true}
        clusterMaxZoom={14}
        clusterRadius={50}
      >
        <Layer {...pointStyle} />
        <Layer {...clusterStyle} />
        <Layer {...clusterCountStyle} />
      </Source>
    </>
  );
};

export default DemoUnderwrittenLayer;
