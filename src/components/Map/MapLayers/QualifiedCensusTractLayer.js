import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useSelector } from "react-redux";
import { getQualifiedCensusTracts } from "../../../services/data";
import Source from "./Source";
import Layer from "./Layer";
import { geojsonTemplate } from "../../../constants";
import { initPopup } from "../MapUtility/general";

const sourceId = "qualified census tracts";
const minZoom = 8;

const polygonStyle = {
  id: `${sourceId}PolygonStyle`,
  type: "fill",
  minzoom: minZoom,
  paint: {
    "fill-color": "#1e88e5",
    "fill-opacity": 0.4,
    "fill-outline-color": "#ffffff"
  }
};

const outlineStyle = {
  id: `${sourceId}OutlineStyle`,
  type: "line",
  minzoom: minZoom,
  paint: {
    "line-color": "#1e88e5",
    "line-width": 1,
    "line-opacity": 0.8
  }
};

const popup = initPopup();

const getPopupHTML = (properties) => {
  const { 
    NAME,
    GEOID,
    COUNTY
  } = properties;

  return renderToString(
    <div style={{ padding: "5px 10px" }}>
      <strong>Name: {NAME}</strong>
      <br />
      
    </div>
  );
};

const QualifiedCensusTractLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const [geoJSON, setGeoJSON] = useState(geojsonTemplate);

  useEffect(() => {
    if (!map) return;

    const getCensusTractData = async () => {
      if (!currentMapLayerOptions.includes("qualified census tracts")) return;
      if (map.getZoom() < minZoom) return;
      
      const bounds = map.getBounds().toArray();
      const data = await getQualifiedCensusTracts({
        lng1: bounds[0][0],
        lat1: bounds[0][1],
        lng2: bounds[1][0],
        lat2: bounds[1][1],
      });

      if (data && data.features) {
        // Transform the ArcGIS data format to GeoJSON format
        const features = data.features.map(feature => ({
          type: "Feature",
          geometry: {
            type: "Polygon",
            coordinates: feature.geometry.rings
          },
          properties: {
            ...feature.attributes
          }
        }));

        setGeoJSON({
          type: "FeatureCollection",
          features: features
        });
      } else {
        setGeoJSON(geojsonTemplate);
      }
    };

    const mouseMove = (e) => {
      const features = e.features;
      if (features.length > 0) {
        map.getCanvas().style.cursor = "pointer";
        
        // Calculate centroid of polygon for popup placement
        const coordinates = features[0].geometry.coordinates[0];
        const centroid = coordinates.reduce((acc, curr) => [
          acc[0] + curr[0] / coordinates.length,
          acc[1] + curr[1] / coordinates.length
        ], [0, 0]);

        popup
          .setLngLat(centroid)
          .setHTML(getPopupHTML(features[0].properties))
          .addTo(map);
      } else {
        map.getCanvas().style.cursor = "";
        popup.remove();
      }
    };

    const mouseLeave = () => {
      map.getCanvas().style.cursor = "";
      popup.remove();
    };

    getCensusTractData();
    map.on("moveend", getCensusTractData);
    map.on("mousemove", polygonStyle.id, mouseMove);
    map.on("mouseleave", polygonStyle.id, mouseLeave);
    
    return () => {
      map.off("moveend", getCensusTractData);
      map.off("mousemove", polygonStyle.id, mouseMove);
      map.off("mouseleave", polygonStyle.id, mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("qualified census tracts")) return null;
  
  return (
    <Source id={sourceId} type="geojson" data={geoJSON}>
      <Layer {...polygonStyle} />
      <Layer {...outlineStyle} />
    </Source>
  );
};

export default QualifiedCensusTractLayer;