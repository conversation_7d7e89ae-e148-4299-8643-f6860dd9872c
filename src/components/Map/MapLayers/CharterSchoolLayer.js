import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useSelector } from "react-redux";
import { getCharterSchoolData } from "../../../services/data";
import { convertToGeoJSON } from "../../../utils/geography";
import { geojsonTemplate } from "../../../constants";
import Source from "./Source";
import Layer from "./Layer";
import { initPopup } from "../MapUtility/general";
import { capitalize } from "../../../utils/strings";

const sourceId = "charter-school-source";
const pointStyle = {
  id: "charter-school-point",
  type: "circle",
  source: sourceId,
  paint: {
    "circle-radius": 6,
    // Use an expression to set the circle color based on the rating property
    "circle-color": [
      "interpolate",
      ["linear"],
      ["get", "rating"], // Assuming the rating is a property of your data
      1,
      "#ff0000", // Red for rating 1
      5,
      "#ffff00", // Yellow for mid-range ratings
      10,
      "#2cba00", // Green for rating 10
    ],
    "circle-stroke-color": "#ffffff",
    "circle-stroke-width": 1,
  },
};

const getTooltipHTML = (prologis) => {
  return renderToString(
    <div style={{ padding: "10px", fontWeight: "500" }}>
      <h4 style={{ fontSize: "14px", fontWeight: "600", margin: 0 }}>{capitalize(prologis.name)}</h4>
      <span>Rating: {prologis.rating}</span>
    </div>
  );
};

let popup = initPopup();

const CharterSchoolLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const [geojson, setGeojson] = useState(geojsonTemplate);

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("charter school")) return;

    const moveEnd = async (e) => {
      const data = await getCharterSchoolData();
      console.log("charter school data", data);
      setGeojson(
        convertToGeoJSON({
          data,
          geomAccessor: (item) => item.geom,
          propertiesAccessor: (item) => {
            const { geom, ...properties } = item;
            return properties;
          },
        })
      );
      console.log("charter school geojson", geojson);
    };

    const mouseMove = (e) => {
      const feature = e.features[0];
      console.log("testv4", feature.properties);
      if (feature && feature.properties) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    const moveBehindOwnedLayer = () => {
      const layers = map.getStyle().layers;
      let foundOwnedLayer = false;
      console.log("moveBehindOwnedLayer", layers);
      for (let i = layers.length - 1; i >= 0; i--) {
        if (layers[i].source && layers[i].source.includes("OwnedProperty")) {
          foundOwnedLayer = true;
        } else if (
          layers[i].source &&
          !layers[i].source.includes("OwnedProperty") &&
          foundOwnedLayer
        ) {
          map.moveLayer(pointStyle.id, layers[i].id);
          console.log(`move ${pointStyle.id} behind ${layers[i].id}`);
          break;
        }
      }
    };

    moveEnd();
    moveBehindOwnedLayer();

    map.on("moveend", moveEnd);
    map.on("mousemove", "charter-school-point", mouseMove);
    map.on("mouseleave", "charter-school-point", mouseLeave);
    return () => {
      map.off("moveend", moveEnd);
      map.off("mousemove", "charter-school-point", mouseMove);
      map.off("mouseleave", "charter-school-point", mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("charter school")) return null;
  return (
    <>
      <Source id={sourceId} type="geojson" data={geojson}>
        <Layer {...pointStyle} />
      </Source>
    </>
  );
};

export default CharterSchoolLayer;
