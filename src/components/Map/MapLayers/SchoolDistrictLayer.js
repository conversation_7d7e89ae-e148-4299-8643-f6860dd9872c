import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import {
  MAP_LAYER_NAME_BASE,
  zoomLevelToChangeBasemap,
} from "../../../constants";
import { setDistrictColor } from "../MapUtility/colors";
import { hideVisibility, showVisibility } from "../MapUtility/layer";
import { setPaintPropertySafely } from "../MapUtility/general";

const sourceId = MAP_LAYER_NAME_BASE.district;
const zoomLevelToDistrictLayer = 9;
const districtDefaultOpacity = 0.15;
const districtSatelliteOpacity = 0.5;

const fillStyle = {
  id: `${sourceId}LayerFill`,
  type: "fill",
  minzoom: zoomLevelToDistrictLayer,
  paint: {
    "fill-opacity": districtDefaultOpacity,
  },
};

const outlineStyle = {
  id: `${sourceId}LayerOutline`,
  type: "line",
  minzoom: zoomLevelToDistrictLayer,
  layout: {
    visibility: "visible",
  },
  paint: {
    "line-color": "#000",
    "line-width": 2,
    "line-opacity": 0.2,
  },
};

const labelStyle = {
  id: `${sourceId}LayerLabel`,
  type: "symbol",
  minzoom: zoomLevelToDistrictLayer,
  layout: {
    "text-field": ["get", "obj_name"],
    "text-variable-anchor": ["center"],
    "text-justify": "auto",
    "text-size": 16,
    "text-font": ["Source Sans Pro Bold", "Open Sans Bold"],
    visibility: "visible",
  },
  paint: {
    "text-color": "#000",
    "text-opacity": 0.75,
  },
};

function SchoolDistrictLayer() {
  const [currentDistrictFillColor, setCurrentDistrictFillColor] = useState([]);

  const map = useSelector((state) => state.Map.map);
  const currentMapThemeOption = useSelector(
    (state) => state.Map.currentMapThemeOption
  );
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const currentDistrictMapScopeGeoJSON = useSelector(
    (state) => state.Map.currentDistrictMapScopeGeoJSON
  );
  const dispatch = useDispatch();

  const currentMapLayerOptionsRef = useRef(currentMapLayerOptions);
  currentMapLayerOptionsRef.current = currentMapLayerOptions;

  useEffect(() => {
    if (!map || !map.style || !map.style._loaded) return;

    if (
      !map.getLayer(`${sourceId}LayerFill`) ||
      !map.getLayer(`${sourceId}LayerOutline`) ||
      !map.getLayer(`${sourceId}LayerLabel`)
    )
      return;

    if (currentMapLayerOptions.includes("school districts")) {
      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToDistrictLayer) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getDistrictMapScope",
          payload: {
            // lng/lat switched
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });
      }
      showVisibility(map, `${sourceId}LayerFill`);
      showVisibility(map, `${sourceId}LayerOutline`);
      showVisibility(map, `${sourceId}LayerLabel`);
    } else {
      if (
        map.getLayoutProperty(`${sourceId}LayerFill`, "visibility") ===
          "visible" ||
        map.getLayoutProperty(`${sourceId}LayerOutline`, "visibility") ===
          "visible" ||
        map.getLayoutProperty(`${sourceId}LayerLabel`, "visibility") ===
          "visible"
      ) {
        hideVisibility(map, `${sourceId}LayerFill`);
        hideVisibility(map, `${sourceId}LayerOutline`);
        hideVisibility(map, `${sourceId}LayerLabel`);
      }
    }
  }, [currentMapLayerOptions]);

  useEffect(() => {
    if (!map || !map.style || !map.style._loaded) return;
    if (
      !map.getLayer(`${sourceId}LayerFill`) ||
      !map.getLayer(`${sourceId}LayerOutline`) ||
      !map.getLayer(`${sourceId}LayerLabel`)
    )
      return;
    if (!currentMapLayerOptions.includes("school districts")) return;

    if (currentMapThemeOption === "Satellite") {
      setPaintPropertySafely(
        map,
        `${sourceId}LayerFill`,
        "fill-opacity",
        districtSatelliteOpacity
      );
    } else if (currentMapThemeOption === "Automatic") {
      if (map.getZoom() >= zoomLevelToChangeBasemap) {
        setPaintPropertySafely(
          map,
          `${sourceId}LayerFill`,
          "fill-opacity",
          districtSatelliteOpacity
        );
      } else {
        setPaintPropertySafely(
          map,
          `${sourceId}LayerFill`,
          "fill-opacity",
          districtDefaultOpacity
        );
      }
    } else {
      setPaintPropertySafely(
        map,
        `${sourceId}LayerFill`,
        "fill-opacity",
        districtDefaultOpacity
      );
    }
  }, [currentMapThemeOption]);

  useEffect(() => {
    if (!map) return;

    const moveEnd = () => {
      if (!currentMapLayerOptionsRef.current.includes("school districts"))
        return;

      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToDistrictLayer) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getDistrictMapScope",
          payload: {
            // lng/lat switched
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });

        if (currentMapThemeOption === "Automatic") {
          if (map.getZoom() >= zoomLevelToChangeBasemap) {
            setPaintPropertySafely(
              map,
              `${sourceId}LayerFill`,
              "fill-opacity",
              districtSatelliteOpacity
            );
          } else {
            setPaintPropertySafely(
              map,
              `${sourceId}LayerFill`,
              "fill-opacity",
              districtDefaultOpacity
            );
          }
        }
      }
    };

    map.on("moveend", moveEnd);

    return () => {
      map.off("moveend", moveEnd);
    };
  }, [map]);

  useEffect(() => {
    if (!map) return;

    const fillColor = setDistrictColor(
      map,
      currentDistrictMapScopeGeoJSON,
      currentDistrictFillColor
    );
    setCurrentDistrictFillColor(fillColor);
  }, [currentDistrictMapScopeGeoJSON]);

  return (
    <>
      <Source
        id={sourceId}
        type="geojson"
        data={currentDistrictMapScopeGeoJSON}
      >
        <Layer {...fillStyle} />
        <Layer {...outlineStyle} />
        <Layer {...labelStyle} />
      </Source>
    </>
  );
}

export default SchoolDistrictLayer;
