import { useState, useEffect, useRef } from "react";
import { renderToString } from "react-dom/server";
import { useSelector, useDispatch } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { hideVisibility, showVisibility } from "../MapUtility/layer";
import { initPopup } from "../MapUtility/general";
import { formatter } from "../../../utils/money";
import { dateFormat, geojsonTemplate } from "../../../constants";
import { Segmented } from "antd";
import moment, { now } from "moment";

const sourceId = "newHomes";

const zoomLevelToNewHomes = 7;

const circleStyle = {
  id: `${sourceId}CircleLayer`,
  type: "circle",
  minzoom: zoomLevelToNewHomes,
  paint: {
    "circle-radius": 5,
    "circle-stroke-color": "#fff",
    "circle-stroke-width": 2,
    "circle-color": "#50C878",
  },
};

const clusterStyle = {
  id: `${sourceId}ClusterLayer`,
  type: "circle",
  filter: ["has", "point_count"],
  minzoom: zoomLevelToNewHomes,
  // paint: {
  //   "circle-color": [
  //     "step",
  //     ["get", "point_count"],
  //     "#51bbd6",
  //     100,
  //     "#f1f075",
  //     750,
  //     "#f28cb1",
  //   ],
  //   "circle-radius": ["step", ["get", "point_count"], 20, 100, 30, 750, 40],
  // },
  paint: {
    "circle-color": "#A9BBC0",
    "circle-radius": [
      // 'interpolate',
      // ['linear'],
      // ['^', ['get', 'point_count'], 0.5],
      // 1, 13, 30, 25, 300, 50
      "interpolate",
      ["linear"],
      ["zoom"],
      0,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 12],
      10.4,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 1], 12],
      12,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 4], 12],
    ],
    "circle-opacity": 0.75,
    "circle-stroke-width": 1,
    "circle-stroke-color": "rgba(255,255,255,1)",
  },
};

const clusterLabelStyle = {
  id: `${sourceId}ClusterSymbolLayer`,
  type: "symbol",
  filter: ["has", "point_count"],
  minzoom: zoomLevelToNewHomes,
  // layout: {
  //   "text-field": ["get", "point_count_abbreviated"],
  //   "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
  //   "text-size": 16,
  // },
  layout: {
    "text-font": ["Open Sans Bold"],
    "text-field": "{point_count}",
    "text-size": 14,
    "text-justify": "auto",
  },
  paint: {
    // 'text-color': 'rgba(255,255,255,1)',
    "text-color": "rgba(0,0,0,1)",
    // 'text-color': '#022386',
  },
};

const getCircleStyle = (name) => ({
  id: `${sourceId}${name}CircleLayer`,
  type: "circle",
  minzoom: zoomLevelToNewHomes,
  paint: {
    "circle-radius": 5,
    "circle-stroke-color": "#fff",
    "circle-stroke-width": 2,
    "circle-color": "#50C878",
  },
  beforeId: `${sourceId}${name}ClusterLayer`,
});

const getClusterStyle = (name, color) => ({
  id: `${sourceId}${name}ClusterLayer`,
  type: "circle",
  filter: ["has", "point_count"],
  minzoom: zoomLevelToNewHomes,
  paint: {
    "circle-color": color,
    "circle-radius": [
      "interpolate",
      ["linear"],
      ["zoom"],
      0,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 12],
      10.4,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 1], 12],
      12,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 4], 12],
    ],
    "circle-opacity": 0.75,
    "circle-stroke-width": 1,
    "circle-stroke-color": "rgba(255,255,255,1)",
  },
  beforeId: `${sourceId}${name}ClusterSymbolLayer`,
});

const getClusterLabelStyle = (name) => ({
  id: `${sourceId}${name}ClusterSymbolLayer`,
  type: "symbol",
  filter: ["has", "point_count"],
  minzoom: zoomLevelToNewHomes,
  layout: {
    "text-font": ["Open Sans Bold"],
    "text-field": "{point_count}",
    "text-size": 14,
    "text-justify": "auto",
    "text-allow-overlap": true,
  },
  paint: {
    "text-color": "rgba(0,0,0,1)",
  },
});

let newNomePopup;

const createNewHomePopup = (map, feature, coordinates) => {
  if (feature && feature.length > 0) {
    if (feature[0].properties.cluster) return;
    const { address, builder, price, first_seen, status } =
      feature[0].properties;

    const htmlRender = renderToString(
      <div style={{ padding: "10px", fontWeight: "500" }}>
        {address && (
          <h4 style={{ fontSize: "14px", fontWeight: "600", margin: 0 }}>
            {address}
          </h4>
        )}
        {builder && builder.length > 0 && (
          <p style={{ margin: 0 }}>Builder: {builder}</p>
        )}
        {first_seen && <p style={{ margin: 0 }}>First Seen: {first_seen}</p>}
        <p style={{ margin: 0 }}>Status: {status ? status : "Not Specified"}</p>
        {price && price > 0 && (
          <p style={{ margin: 0 }}>Price: ${formatter(price)}</p>
        )}
      </div>
    );

    newNomePopup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
  } else {
    newNomePopup.remove();
  }
};

const defaultFilter = {
  status: "All",
  builder: "All",
  minPrice: 0,
  maxPrice: 60000000,
  minBed: 0,
  maxBed: 6,
  minBath: 0,
  maxBath: 6,
  minSqft: 0,
  maxSqft: 2500,
  minFirstSeenDate: moment("02-01-2023", "MM-DD-YYYY").format(dateFormat),
  maxFirstSeenDate: moment().format(dateFormat),
  source: "NewHomeSource",
  // source: "Livabl",
};

function NewHomesLayer() {
  const map = useSelector((state) => state.Map.map);
  const currentNewHomesGeoJSON = useSelector(
    (state) => state.Map.currentNewHomesGeoJSON
  );
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const newBuildClusterType = useSelector(
    (state) => state.Map.newBuildClusterType
  );
  const dispatch = useDispatch();

  const currentMapLayerOptionsRef = useRef(currentMapLayerOptions);
  currentMapLayerOptionsRef.current = currentMapLayerOptions;

  const [newHomesFilter, setNewHomesFilter] = useState(defaultFilter);
  const newHomesFilterRef = useRef(newHomesFilter);
  newHomesFilterRef.current = newHomesFilter;

  useEffect(() => {
    if (!map) return;

    newNomePopup = initPopup();

    const moveEnd = () => {
      if (!currentMapLayerOptionsRef.current.includes("new construction"))
        return;

      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToNewHomes) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getNewHomes",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
            ...newHomesFilterRef.current,
          },
        });
      }
    };

    const mouseEnter = (e) => {
      const feature = map.queryRenderedFeatures(e.point, {
        layers: [`${sourceId}CircleLayer`],
      });
      const coordinates = [e.lngLat.lng, e.lngLat.lat];
      createNewHomePopup(map, feature, coordinates);
    };

    const mouseLeave = (e) => {
      newNomePopup.remove();
    };

    const updateNewHomeFilter = (e) => {
      const { payload } = e;
      const { filter } = payload;

      console.log("updateNewHomeFilter called");
      setNewHomesFilter(filter);

      if (currentMapLayerOptionsRef.current.includes("new construction")) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getNewHomes",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
            ...filter,
          },
        });
      }
    };
    moveEnd();
    map.on("moveend", moveEnd);
    map.on("mouseenter", `${sourceId}CircleLayer`, mouseEnter);
    map.on("mouseleave", `${sourceId}CircleLayer`, mouseLeave);
    map.on("NewHomesLayer.updateNewHomeFilter", updateNewHomeFilter);
    return () => {
      map.off("moveend", moveEnd);
      map.off("mouseenter", `${sourceId}CircleLayer`, mouseEnter);
      map.off("mouseleave", `${sourceId}CircleLayer`, mouseLeave);
      map.off("NewHomesLayer.updateNewHomeFilter", updateNewHomeFilter);
    };
  }, [map]);

  useEffect(() => {
    if (!map || !newHomesFilter || !newHomesFilter.source) return;
    if (!currentNewHomesGeoJSON || currentNewHomesGeoJSON.features.length === 0)
      return;

    const newHomeSourceLegend = [
      { label: "Not Specified", color: "#a9bbc0", count: 0 },
      { label: "Available", color: "#8ceb49", count: 0 },
      { label: "Ready to Build", color: "#ebd849", count: 0 },
      { label: "Now Building", color: "#eb6149", count: 0 },
      { label: "Available Now", color: "#12de4f", count: 0 },
      { label: "Model Home", color: "#1282de", count: 0 },
    ];

    const livablLegend = [
      { label: "For Rent", color: "#1E90FF", count: 0 },
      { label: "For Sale", color: "#FF4500", count: 0 },
      { label: "Plans", color: "#FFD700", count: 0 },
      { label: "Sold", color: "#32CD32", count: 0 },
    ];

    for (let i = 0; i < currentNewHomesGeoJSON.features.length; i++) {
      const status = currentNewHomesGeoJSON.features[i].properties.status;
      if (newHomesFilter.source === "Livabl") {
        if (status === "For Rent") {
          livablLegend[0].count++;
        } else if (status === "For Sale") {
          livablLegend[1].count++;
        } else if (status === "Plans") {
          livablLegend[2].count++;
        } else if (status === "Sold") {
          livablLegend[3].count++;
        }
      } else if (newHomesFilter.source === "NewHomeSource") {
        if (status === null) {
          newHomeSourceLegend[0].count++;
        } else if (status === "Available" || status === "Available Now") {
          newHomeSourceLegend[1].count++;
        } else if (status === "Ready to Build") {
          newHomeSourceLegend[2].count++;
        } else if (status === "Now Building") {
          newHomeSourceLegend[3].count++;
        } else if (status === "Model Home") {
          newHomeSourceLegend[5].count++;
        }
      }
    }

    dispatch({
      type: "Map/saveState",
      payload: {
        newBuildLegendCount:
          newHomesFilter.source === "Livabl"
            ? livablLegend
            : newHomeSourceLegend,
      },
    });

    // console.log("====================================");
    // console.log("Not Specified: ", notSpecified.features.length);
    // console.log("Available: ", available.features.length);
    // console.log("Ready to Build: ", readyToBuild.features.length);
    // console.log("Now Building: ", nowBuilding.features.length);
    // console.log("Available Now: ", availableNow.features.length);
    // console.log("Model Home: ", modelHome.features.length);
  }, [currentNewHomesGeoJSON, newHomesFilter]);

  const segmentedHandler = (value) => {
    dispatch({
      type: "Map/saveState",
      payload: { newBuildClusterType: value },
    });
  };

  return (
    <>
      {newBuildClusterType === "All" && (
        <Source
          id={sourceId}
          type="geojson"
          data={currentNewHomesGeoJSON}
          cluster={true}
          clusterMaxZoom={14}
          clusterRadius={75}
        >
          <Layer {...circleStyle} />
          <Layer {...clusterStyle} />
          <Layer {...clusterLabelStyle} />
        </Source>
      )}
      {newBuildClusterType === "Clustered By Status" &&
        newHomesFilter?.source === "NewHomeSource" && (
          <>
            <Source
              id="NotSpecified"
              type="geojson"
              data={currentNewHomesGeoJSON}
              cluster={true}
              clusterMaxZoom={14}
              clusterRadius={75}
              filter={["==", ["get", "status"], null]}
            >
              <Layer {...getCircleStyle("NotSpecified")} />
              <Layer {...getClusterStyle("NotSpecified", "#A9BBC0")} />
              <Layer {...getClusterLabelStyle("NotSpecified")} />
            </Source>
            <Source
              id="Available"
              type="geojson"
              data={currentNewHomesGeoJSON}
              cluster={true}
              clusterMaxZoom={14}
              clusterRadius={75}
              filter={["==", ["get", "status"], "Available"]}
            >
              <Layer {...getCircleStyle("Available")} />
              <Layer {...getClusterStyle("Available", "#8ceb49")} />
              <Layer {...getClusterLabelStyle("Available")} />
            </Source>
            <Source
              id="ReadyToBuild"
              type="geojson"
              data={currentNewHomesGeoJSON}
              cluster={true}
              clusterMaxZoom={14}
              clusterRadius={75}
              filter={["==", ["get", "status"], "Ready to Build"]}
            >
              <Layer {...getCircleStyle("ReadyToBuild")} />
              <Layer {...getClusterStyle("ReadyToBuild", "#ebd849")} />
              <Layer {...getClusterLabelStyle("ReadyToBuild")} />
            </Source>
            <Source
              id="NowBuilding"
              type="geojson"
              data={currentNewHomesGeoJSON}
              cluster={true}
              clusterMaxZoom={14}
              clusterRadius={75}
              filter={["==", ["get", "status"], "Now Building"]}
            >
              <Layer {...getCircleStyle("NowBuilding")} />
              <Layer {...getClusterStyle("NowBuilding", "#eb6149")} />
              <Layer {...getClusterLabelStyle("NowBuilding")} />
            </Source>
            <Source
              id="AvailableNow"
              type="geojson"
              data={currentNewHomesGeoJSON}
              cluster={true}
              clusterMaxZoom={14}
              clusterRadius={75}
              filter={["==", ["get", "status"], "Available Now"]}
            >
              <Layer {...getCircleStyle("AvailableNow")} />
              <Layer {...getClusterStyle("AvailableNow", "#12de4f")} />
              <Layer {...getClusterLabelStyle("AvailableNow")} />
            </Source>
            <Source
              id="ModelHome"
              type="geojson"
              data={currentNewHomesGeoJSON}
              cluster={true}
              clusterMaxZoom={14}
              clusterRadius={75}
              filter={["==", ["get", "status"], "Model Home"]}
            >
              <Layer {...getCircleStyle("ModelHome")} />
              <Layer {...getClusterStyle("ModelHome", "#1282de")} />
              <Layer {...getClusterLabelStyle("ModelHome")} />
            </Source>
          </>
        )}
      {newBuildClusterType === "Clustered By Status" &&
        newHomesFilter?.source === "Livabl" && (
          <>
            <Source
              id="ForRent"
              type="geojson"
              data={currentNewHomesGeoJSON}
              cluster={true}
              clusterMaxZoom={14}
              clusterRadius={75}
              filter={["==", ["get", "status"], "For Rent"]}
            >
              <Layer {...getCircleStyle("ForRent")} />
              <Layer {...getClusterStyle("ForRent", "#1E90FF")} />
              <Layer {...getClusterLabelStyle("ForRent")} />
            </Source>
            <Source
              id="ForSale"
              type="geojson"
              data={currentNewHomesGeoJSON}
              cluster={true}
              clusterMaxZoom={14}
              clusterRadius={75}
              filter={["==", ["get", "status"], "For Sale"]}
            >
              <Layer {...getCircleStyle("ForSale")} />
              <Layer {...getClusterStyle("ForSale", "#FF4500")} />
              <Layer {...getClusterLabelStyle("ForSale")} />
            </Source>
            <Source
              id="Plans"
              type="geojson"
              data={currentNewHomesGeoJSON}
              cluster={true}
              clusterMaxZoom={14}
              clusterRadius={75}
              filter={["==", ["get", "status"], "Plans"]}
            >
              <Layer {...getCircleStyle("Plans")} />
              <Layer {...getClusterStyle("Plans", "#FFD700")} />
              <Layer {...getClusterLabelStyle("Plans")} />
            </Source>
            <Source
              id="Sold"
              type="geojson"
              data={currentNewHomesGeoJSON}
              cluster={true}
              clusterMaxZoom={14}
              clusterRadius={75}
              filter={["==", ["get", "status"], "Sold"]}
            >
              <Layer {...getCircleStyle("Sold")} />
              <Layer {...getClusterStyle("Sold", "#32CD32")} />
              <Layer {...getClusterLabelStyle("Sold")} />
            </Source>
          </>
        )}
      {newBuildClusterType === "None" && (
        <Source id={sourceId} type="geojson" data={currentNewHomesGeoJSON}>
          <Layer {...circleStyle} />
        </Source>
      )}

      {currentMapLayerOptions.includes("new construction") && (
        <div
          style={{
            backgroundColor: "white",
            position: "absolute",
            bottom: "60px",
            left: "50%",
            transform: "translateX(-50%)",
            zIndex: 100,
            boxShadow:
              "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
          }}
        >
          <Segmented
            options={["All", "Clustered By Status", "None"]}
            onChange={segmentedHandler}
            value={newBuildClusterType}
          />
        </div>
      )}
    </>
  );
}

export default NewHomesLayer;
