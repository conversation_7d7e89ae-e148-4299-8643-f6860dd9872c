import { useState, useEffect, useCallback, useRef } from "react";
import { renderToString } from "react-dom/server";
import Source from "./Source";
import Layer from "./Layer";
import { useSelector, useDispatch } from "react-redux";
import { geojsonTemplate } from "../../../constants";
import { initPopup } from "../MapUtility/general";
import { regridIsHovered } from "./Regrid";
import { parcelHovered } from "./ParcelLayer";

const sourceId = "OSMResidential";

const minZoom = 9;

const lineStyle = {
  id: `${sourceId}LayerLine`,
  type: "line",
  minzoom: minZoom,
  paint: {
    "line-color": "#196fba",
    "line-opacity": 1,
    "line-width": ["interpolate", ["linear"], ["zoom"], 1, 2, 12, 3, 17, 18],
  },
};

const fillStyle = {
  id: `${sourceId}FillStyle`,
  type: "fill",
  minzoom: minZoom,
  paint: {
    "fill-opacity": 0.75,
    "fill-color": "#196fba",
  },
};

const pointStyle = {
  id: `${sourceId}PointStyle`,
  type: "circle",
  minzoom: minZoom,
  paint: {
    "circle-radius": [
      "interpolate",
      ["linear"],
      ["zoom"],
      10,
      3,
      14,
      10,
      17,
      8,
    ],
    "circle-color": "#196fba",
    "circle-stroke-width": 2,
    "circle-stroke-color": "white",
  },
};

const popup = initPopup();

const PermitTooltip = ({ data }) => {
  const address = `${data.street_no} ${data.street}, ${data.city}, ${data.state} ${data.zip}`;

  const parsedData = {
    address: address,
    county: data.county,
    jurisdicti: data.jurisdicti,
    type: data.type,
    subtype: data.subtype,
    "file date": data.file_date,
    status: data.status,
    description: data.descriptio,
  };

  return Object.keys(parsedData).map((key) => {
    if (key === "highway" && parsedData[key] === "construction") {
      return <span key={key}>Road construction</span>;
    }

    return (
      <span key={key}>
        {key}: <strong>{parsedData[key]}</strong>
      </span>
    );
  });
};

const getTooltipHTML = (properties, title) => {
  const { full_id, id, latitude, longitude, ...data } = properties;

  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
        gap: "5px",
      }}
    >
      <div
        style={{
          textAlign: "center",
          display: "flex",
          flexDirection: "column",
          lineHeight: "1.25",
        }}
      >
        <strong>{title}</strong>
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          lineHeight: "1.3",
        }}
      >
        {title != "Residential Building Permit" &&
          data &&
          Object.keys(data).map((key) => {
            if (key === "highway" && data[key] === "construction") {
              return <span key={key}>Road construction</span>;
            }

            return (
              <span key={key}>
                {key}: <strong>{data[key]}</strong>
              </span>
            );
          })}
        {title === "Residential Building Permit" && (
          <PermitTooltip data={data} />
        )}
      </div>
    </div>
  );
};

const OSMResidentialLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const currentOSMResidentialRoadGeoJSON = useSelector(
    (state) => state.Map.currentOSMResidentialRoadGeoJSON
  );
  const currentOSMResidentialPolygonGeoJSON = useSelector(
    (state) => state.Map.currentOSMResidentialPolygonGeoJSON
  );
  const currentBuildingPermitSampleGeoJSON = useSelector(
    (state) => state.Map.currentBuildingPermitSampleGeoJSON
  );

  const dispatch = useDispatch();

  useEffect(() => {
    if (!map) return;

    const fetchOSMResidentialData = async () => {
      const mapZoom = map.getZoom();
      if (mapZoom < minZoom) return;
      const mapScopeBBox = map.getBounds().toArray();
      if (currentMapLayerOptions.includes("residential development")) {
        dispatch({
          type: "Map/getOSMResidential",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });
      }
    };

    const moveEnd = () => {
      fetchOSMResidentialData();
    };

    const mouseMove = (e, title) => {
      if (
        e.features.length > 0 &&
        (!regridIsHovered || !regridIsHovered.current) &&
        !parcelHovered.current
      ) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(e.features[0].properties, title);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      } else {
        popup.remove();
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    const roadMouseMove = (e) => {
      mouseMove(e, "Residential Road in Development");
    };
    const polygonMouseMove = (e) => {
      mouseMove(e, "Residential Parcel in Development");
    };
    const pointMouseMove = (e) => {
      mouseMove(e, "Residential Building Permit");
    };

    fetchOSMResidentialData();
    map.on("moveend", moveEnd);
    map.on("mousemove", lineStyle.id, roadMouseMove);
    map.on("mouseleave", lineStyle.id, mouseLeave);
    map.on("mousemove", fillStyle.id, polygonMouseMove);
    map.on("mouseleave", fillStyle.id, mouseLeave);
    map.on("mousemove", pointStyle.id, pointMouseMove);
    map.on("mouseleave", pointStyle.id, mouseLeave);
    return () => {
      map.off("moveend", moveEnd);
      map.off("mousemove", lineStyle.id, roadMouseMove);
      map.off("mouseleave", lineStyle.id, mouseLeave);
      map.off("mousemove", fillStyle.id, polygonMouseMove);
      map.off("mouseleave", fillStyle.id, mouseLeave);
      map.off("mousemove", pointStyle.id, pointMouseMove);
      map.off("mouseleave", pointStyle.id, mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  useEffect(() => {
    if (
      !currentMapLayerOptions.includes("residential development") &&
      (currentOSMResidentialRoadGeoJSON.features.length > 0 ||
        currentOSMResidentialPolygonGeoJSON.features.length > 0 ||
        currentBuildingPermitSampleGeoJSON.features.length > 0)
    ) {
      dispatch({
        type: "Map/saveState",
        payload: {
          currentOSMResidentialRoadGeoJSON: geojsonTemplate,
          currentOSMResidentialPolygonGeoJSON: geojsonTemplate,
          currentBuildingPermitSampleGeoJSON: geojsonTemplate,
        },
      });
    }
  }, [
    currentMapLayerOptions,
    currentOSMResidentialRoadGeoJSON,
    currentOSMResidentialPolygonGeoJSON,
    currentBuildingPermitSampleGeoJSON,
  ]);

  if (currentMapLayerOptions.includes("residential development")) {
    return (
      <>
        <Source
          id={`${sourceId}Roads`}
          type="geojson"
          data={currentOSMResidentialRoadGeoJSON}
        >
          <Layer {...lineStyle} />
        </Source>

        <Source
          id={`${sourceId}Polygons`}
          type="geojson"
          data={currentOSMResidentialPolygonGeoJSON}
        >
          <Layer {...fillStyle} />
        </Source>

        <Source
          id={`${sourceId}BuildingPermitSample`}
          type="geojson"
          data={currentBuildingPermitSampleGeoJSON}
        >
          <Layer {...pointStyle} />
        </Source>
      </>
    );
  }
  return null;
};

export default OSMResidentialLayer;
