import { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { MAP_LAYER_NAME_BASE } from "../../../constants";

const sourceId = MAP_LAYER_NAME_BASE.parcelBoundary;

const zoomLevelToShowBoundary = 16;

const boundaryStyle = {
  id: `${sourceId}Layer`,
  type: "line",
  minzoom: zoomLevelToShowBoundary,
  paint: {
    "line-color": "#fff",
    "line-width": 3,
  },
};

function ParcelBoundaryLayer() {
  const map = useSelector((state) => state.Map.map);
  const currentParcelBoundaryMapScopeGeoJSON = useSelector(
    (state) => state.Map.currentParcelBoundaryMapScopeGeoJSON
  );
  const dispatch = useDispatch();

  useEffect(() => {
    if (!map) return;

    const moveEnd = () => {
      // temp fix, server cant handle big bbox when tilt is active
      if (map.getBearing() != 0 && map.getPitch() != 0) return;

      const currentZoomLevel = map.getZoom();
      const mapScopeBBox = map.getBounds().toArray();
      if (currentZoomLevel >= zoomLevelToShowBoundary) {
        dispatch({
          type: "Map/getParcelBoundaryMapScope",
          payload: {
            // lng/lat switched
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });
      }
    };

    map.on("moveend", moveEnd);

    return () => {
      map.off("moveend", moveEnd);
    };
  }, [map]);

  return (
    <>
      <Source
        id={sourceId}
        type="geojson"
        data={currentParcelBoundaryMapScopeGeoJSON}
      >
        <Layer {...boundaryStyle} />
      </Source>
    </>
  );
}

export default ParcelBoundaryLayer;
