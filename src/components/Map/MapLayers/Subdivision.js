import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { initPopup, setPaintPropertySafely } from "../MapUtility/general";
import { formatter } from "../../../utils/money";
import { renderToString } from "react-dom/server";
import { MAP_LAYER_NAME_BASE } from "../../../constants";
import { getSubdivision55PlusData } from "../../../services/data";

const sourceId = MAP_LAYER_NAME_BASE.subdivision;
const sourceLayer = "residential_objects_usa";
const minZoom = 9;

let sourceLoaded = false;

const lineStyle = {
  id: `${sourceId}LineStyle`,
  "source-layer": sourceLayer,
  type: "line",
  minzoom: minZoom,
  paint: {
    "line-opacity": 0.6,
    "line-color": "#df94aa",
    "line-width": ["interpolate", ["linear"], ["zoom"], 4, 0.5, 8, 1, 12, 3],
  },
};
const fillStyle = {
  id: `${sourceId}FillStyle`,
  "source-layer": sourceLayer,
  type: "fill",
  minzoom: minZoom,
  paint: {
    "fill-opacity": 0.3,
    "fill-color": "#df94aa",
  },
};

const popup = initPopup();

function Subdivision() {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const fiftyFivePlusSubdivisionEnabled = useSelector(
    (state) => state.Map.fiftyFivePlusSubdivisionEnabled
  );

  useEffect(() => {
    if (!map) return;

    const fetch55PlusSubdivision = async (params) => {
      const response = await getSubdivision55PlusData(params);
      if (response && response.length > 0) {
        const fillColor = ["match", ["get", "OBJ_ID"]];
        const fillOpacity = ["match", ["get", "OBJ_ID"]];
        const lineColor = ["match", ["get", "OBJ_ID"]];
        for (let i = 0; i < response.length; i++) {
          fillColor.push(response[i], "#737373");
          fillOpacity.push(response[i], 0.6);
          lineColor.push(response[i], "#636363");
        }
        fillColor.push("#df94aa");
        fillOpacity.push(0.3);
        lineColor.push("#df94aa");
        setPaintPropertySafely(map, fillStyle.id, "fill-color", fillColor);
        setPaintPropertySafely(map, fillStyle.id, "fill-opacity", fillOpacity);
        setPaintPropertySafely(map, lineStyle.id, "line-color", lineColor);
      }
    };

    const sourceDataLoading = (e) => {
      if (!fiftyFivePlusSubdivisionEnabled) return;

      if (e.sourceId === sourceId) {
        sourceLoaded = false;
      }
    };

    const sourceData = (e) => {
      if (!fiftyFivePlusSubdivisionEnabled) return;

      if (e.isSourceLoaded && e.sourceId === sourceId && !sourceLoaded) {
        const features = map.querySourceFeatures(sourceId, {
          sourceLayer: sourceLayer,
        });
        if (features.length > 0) {
          const ids = features.map((feature) => feature.properties.OBJ_ID);
          fetch55PlusSubdivision({ body: ids });
        }
        sourceLoaded = true;
      }
    };

    if (fiftyFivePlusSubdivisionEnabled) {
      const features = map.querySourceFeatures(sourceId, {
        sourceLayer: sourceLayer,
      });
      if (features.length > 0) {
        const ids = features.map((feature) => feature.properties.OBJ_ID);
        fetch55PlusSubdivision({ body: ids });
      }
    } else {
      if (map.style && map.style._loaded) {
        setPaintPropertySafely(map, fillStyle.id, "fill-color", "#df94aa");
        setPaintPropertySafely(map, fillStyle.id, "fill-opacity", 0.3);
        setPaintPropertySafely(map, lineStyle.id, "line-color", "#df94aa");
      }
    }

    const mouseMove = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [fillStyle.id],
      });

      if (features.length > 0) {
        const { OBJ_NAME, OBJ_AREA } = features[0].properties;
        const coordinates = [e.lngLat.lng, e.lngLat.lat];

        // OBJ_AREA is in square meters, convert to acres
        let area = Math.round((OBJ_AREA / 4047) * 100) / 100;
        let decimal =
          area - Math.floor(area) > 0 ? area.toString().split(".")[1] : 0;

        area = decimal > 0 ? formatter(area) + `.${decimal}` : formatter(area);
        let areaText = `${area} acres`;

        const htmlRender = renderToString(
          <div
            style={{
              padding: "10px",
              display: "flex",
              flexDirection: "column",
            }}
          >
            <span>
              Name: <strong>{OBJ_NAME}</strong>
            </span>
            <span>
              Area: <strong>{areaText}</strong>
            </span>
          </div>
        );
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      } else {
        popup.remove();
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    // // map.on("click", click);
    map.on("sourcedataloading", sourceDataLoading);
    map.on("sourcedata", sourceData);
    map.on("mousemove", fillStyle.id, mouseMove);
    map.on("mouseleave", fillStyle.id, mouseLeave);
    return () => {
      // map.off("click", click);
      map.off("sourcedataloading", sourceDataLoading);
      map.off("sourcedata", sourceData);
      map.off("mousemove", fillStyle.id, mouseMove);
      map.off("mouseleave", fillStyle.id, mouseLeave);
    };
  }, [map, fiftyFivePlusSubdivisionEnabled]);

  if (currentMapLayerOptions.includes("subdivision")) {
    return (
      <Source id={sourceId} type="vector" url="mapbox://sxbxchen.30sgdha3">
        <Layer {...fillStyle} />
        <Layer {...lineStyle} />
      </Source>
    );
  }
  return null;
}

export default Subdivision;
