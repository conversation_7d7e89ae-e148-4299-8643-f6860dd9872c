import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useSelector } from "react-redux";
import { getPrologisData } from "../../../services/data";
import { convertToGeoJSON } from "../../../utils/geography";
import { geojsonTemplate } from "../../../constants";
import Source from "./Source";
import Layer from "./Layer";
import { initPopup } from "../MapUtility/general";

const sourceId = "prologis-source";
const pointStyle = {
  id: "prologis-point",
  type: "circle",
  source: sourceId,
  paint: {
    "circle-radius": 6,
    "circle-color": "#1A7770",
    "circle-stroke-color": "#ffffff",
    "circle-stroke-width": 2,
  },
};

const getTooltipHTML = (prologis) => {
  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <span>{prologis.property_name}</span>
      <span>
        Address: {prologis.address_line1}, {prologis.city}, {prologis.state}, {prologis.postal_code}
      </span>
    </div>
  );
};

let popup = initPopup();

const PrologisLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const [geojson, setGeojson] = useState(geojsonTemplate);

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("prologis")) return;

    const moveEnd = async (e) => {
      const data = await getPrologisData();
      console.log("prologis data", data)
      setGeojson(
        convertToGeoJSON({
          data,
          geomAccessor: (item) => item.geom,
          propertiesAccessor: (item) => {
            const { geom, ...properties } = item;
            return properties;
          },
        })
      );
      console.log("prologis geojson", geojson)
    };

    const mouseMove = (e) => {
      const feature = e.features[0];
      console.log("testv4", feature.properties);
      if (feature && feature.properties) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    const moveBehindOwnedLayer = () => {
      const layers = map.getStyle().layers;
      let foundOwnedLayer = false;
      console.log("moveBehindOwnedLayer", layers);
      for (let i = layers.length - 1; i >= 0; i--) {
        if (layers[i].source && layers[i].source.includes("OwnedProperty")) {
          foundOwnedLayer = true;
        } else if (
          layers[i].source &&
          !layers[i].source.includes("OwnedProperty") &&
          foundOwnedLayer
        ) {
          map.moveLayer(pointStyle.id, layers[i].id);
          console.log(`move ${pointStyle.id} behind ${layers[i].id}`);
          break;
        }
      }
    };

    moveEnd();
    moveBehindOwnedLayer();

    map.on("moveend", moveEnd);
    map.on("mousemove", "prologis-point", mouseMove);
    map.on("mouseleave", "prologis-point", mouseLeave);
    return () => {
      map.off("moveend", moveEnd);
      map.off("mousemove", "prologis-point", mouseMove);
      map.off("mouseleave", "prologis-point", mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("prologis")) return null;
  return (
    <>
      <Source id={sourceId} type="geojson" data={geojson}>
        <Layer {...pointStyle} />
      </Source>
    </>
  );
};

export default PrologisLayer;
