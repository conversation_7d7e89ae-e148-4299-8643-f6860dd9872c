import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useSelector } from "react-redux";
import { getTrueHoldTargetData } from "../../../services/data";
import { convertToGeoJSON } from "../../../utils/geography";
import { geojsonTemplate } from "../../../constants";
import Source from "./Source";
import Layer from "./Layer";
import { initPopup } from "../MapUtility/general";

const sourceId = "truehold-target-source";
const pointStyle = {
  id: "truehold-target-point",
  type: "circle",
  source: sourceId,
  paint: {
    "circle-radius": 7,
    // Use a conditional expression to set the circle color
    "circle-color": [
      "case",
      ["==", ["get", "focus_flag"], false], // Check if focus_flag is false
      "#ff0000", // Color for false (e.g., red)
      "#4284f5"  // Default color for true or undefined (e.g., blue)
    ],
    "circle-stroke-color": "#ffffff",
    "circle-stroke-width": 2,
  },
};


const getTooltipHTML = (property) => {
  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <span>{property.address}</span>
      <span>
        {property.city}, {property.state}, {property.zip}
      </span>
      <span>Year Built: {property.year_built}</span>
      <span>
        Beds: {property.beds} | Baths: {property.baths}
      </span>
      <span>Owner Occupied: {"" + property.owner_occupied}</span>
      <span>Focus Flag: {"" + property.focus_flag}</span>
      <span>
        AVM: ${property.th_value.toLocaleString()} | Rent AVM: ${property.th_rent_avm}
      </span>
    </div>
  );
};

let popup = initPopup();

const TrueholdTargetLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const [geojson, setGeojson] = useState(geojsonTemplate);

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("truehold target")) return;

    const moveEnd = async (e) => {
      const mapBounds = map.getBounds();
      const coord1 = mapBounds.getSouthWest();
      const coord2 = mapBounds.getNorthEast();

      console.log("test coord", {coord1, coord2});
      const data = await getTrueHoldTargetData({
        lat1: coord1.lng,
        lng1: coord1.lat,
        lat2: coord2.lng,
        lng2: coord2.lat,
      });

      const test = convertToGeoJSON({
        data,
        geomAccessor: (item) => item.geog,
        propertiesAccessor: (item) => {
          const { geog, ...properties } = item;
          return properties;
        },
      })
      console.log("testv2 test",test)
      setGeojson(
        convertToGeoJSON({
          data,
          geomAccessor: (item) => item.geog,
          propertiesAccessor: (item) => {
            const { geog, ...properties } = item;
            return properties;
          },
        })
      );
    };

    const mouseMove = (e) => {
      const feature = e.features[0];

      if (feature && feature.properties) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }

      // const coordinates = [e.lngLat.lng, e.lngLat.lat];
      // const htmlRender = getTooltipHTML(feature[0].properties);
      // popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
    };

    const mouseLeave = () => {
      popup.remove();
    };

    const moveBehindOwnedLayer = () => {
      const layers = map.getStyle().layers;
      let foundOwnedLayer = false;
      console.log("moveBehindOwnedLayer", layers);
      for (let i = layers.length - 1; i >= 0; i--) {
        if (layers[i].source && layers[i].source.includes("OwnedProperty")) {
          foundOwnedLayer = true;
        } else if (
          layers[i].source &&
          !layers[i].source.includes("OwnedProperty") &&
          foundOwnedLayer
        ) {
          map.moveLayer(pointStyle.id, layers[i].id);
          console.log(`move ${pointStyle.id} behind ${layers[i].id}`);
          break;
        }
      }
    };

    moveEnd();
    moveBehindOwnedLayer();

    map.on("moveend", moveEnd);
    map.on("mousemove", "truehold-target-point", mouseMove);
    map.on("mouseleave", "truehold-target-point", mouseLeave);
    return () => {
      map.off("moveend", moveEnd);
      map.off("mousemove", "truehold-target-point", mouseMove);
      map.off("mouseleave", "truehold-target-point", mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("truehold target")) return null;
  return (
    <>
      <Source id={sourceId} type="geojson" data={geojson}>
        <Layer {...pointStyle} />
      </Source>
    </>
  );
};

export default TrueholdTargetLayer;
