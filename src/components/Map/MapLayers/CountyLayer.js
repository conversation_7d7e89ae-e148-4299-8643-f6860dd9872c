import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import {
  MAP_LAYER_NAME_BASE,
  zoomLevelToChangeBasemap,
} from "../../../constants";
import { setCountyColor } from "../MapUtility/colors";
import { hideVisibility, showVisibility } from "../MapUtility/layer";
import { setPaintPropertySafely } from "../MapUtility/general";

const sourceId = MAP_LAYER_NAME_BASE.county;

const zoomLevelToLayer = 9;
const countySatelliteOpacity = 0.5;
const countyDefaultOpacity = 0.15;

const fillStyle = {
  id: `${sourceId}LayerFill`,
  type: "fill",
  minzoom: zoomLevelToLayer,
  paint: {
    "fill-opacity": countyDefaultOpacity,
  },
  filter: ["in", "$type", "Polygon"],
};

const outlineStyle = {
  id: `${sourceId}LayerOutline`,
  type: "line",
  minzoom: zoomLevelToLayer,
  layout: {
    visibility: "visible",
  },
  paint: {
    "line-color": "#000",
    "line-width": 2,
    "line-opacity": 0.2,
  },
  filter: ["in", "$type", "Polygon"],
};

const defaultLabelStyle = {
  id: `${sourceId}LayerLabel`,
  type: "symbol",
  minzoom: 11,
  layout: {
    "text-field": ["get", "name"],
    "text-variable-anchor": ["center"],
    "text-justify": "auto",
    "text-size": 16,
    "text-font": ["Source Sans Pro Bold", "Open Sans Bold"],
    visibility: "visible",
  },
  paint: {
    "text-color": "#000",
    "text-opacity": 0.75,
  },
  filter: ["in", "$type", "Polygon"],
};

const centerLabelStyle = {
  id: `${sourceId}LayerCenterLabel`,
  type: "symbol",
  minzoom: zoomLevelToLayer,
  maxzoom: 11,
  layout: {
    "text-field": ["get", "name"],
    "text-variable-anchor": ["center"],
    "text-justify": "auto",
    "text-size": 24,
    "text-font": ["Source Sans Pro Bold", "Open Sans Bold"],
    visibility: "visible",
  },
  paint: {
    "text-color": "#000",
    "text-opacity": 0.75,
  },
  filter: ["in", "$type", "Point"],
};

function CountyLayer() {
  const [currentCountyFillColor, setCurrentCountyFillColor] = useState([]);

  const map = useSelector((state) => state.Map.map);
  const currentMapThemeOption = useSelector(
    (state) => state.Map.currentMapThemeOption
  );
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const currentCountyGeoJSON = useSelector(
    (state) => state.Map.currentCountyGeoJSON
  );
  const dispatch = useDispatch();

  const currentMapLayerOptionsRef = useRef(currentMapLayerOptions);
  currentMapLayerOptionsRef.current = currentMapLayerOptions;

  useEffect(() => {
    if (!map || !map.style || !map.style._loaded) return;
    if (
      !map.getLayer(`${sourceId}LayerFill`) ||
      !map.getLayer(`${sourceId}LayerOutline`) ||
      !map.getLayer(`${sourceId}LayerLabel`) ||
      !map.getLayer(`${sourceId}LayerCenterLabel`)
    )
      return;

    if (currentMapLayerOptions.includes("county")) {
      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToLayer) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getCounty",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });
      }
      showVisibility(map, `${sourceId}LayerFill`);
      showVisibility(map, `${sourceId}LayerOutline`);
      showVisibility(map, `${sourceId}LayerLabel`);
      showVisibility(map, `${sourceId}LayerCenterLabel`);
    } else {
      if (
        map.getLayoutProperty(`${sourceId}LayerFill`, "visibility") ===
          "visible" ||
        map.getLayoutProperty(`${sourceId}LayerOutline`, "visibility") ===
          "visible" ||
        map.getLayoutProperty(`${sourceId}LayerLabel`, "visibility") ===
          "visible" ||
        map.getLayoutProperty(`${sourceId}LayerCenterLabel`, "visibility") ===
          "visible"
      ) {
        hideVisibility(map, `${sourceId}LayerFill`);
        hideVisibility(map, `${sourceId}LayerOutline`);
        hideVisibility(map, `${sourceId}LayerLabel`);
        hideVisibility(map, `${sourceId}LayerCenterLabel`);
      }
    }
  }, [currentMapLayerOptions]);

  useEffect(() => {
    if (!map) return;

    const moveEnd = () => {
      if (!currentMapLayerOptionsRef.current.includes("county")) return;

      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToLayer) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getCounty",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });

        if (currentMapThemeOption === "Automatic") {
          if (map.getZoom() >= zoomLevelToChangeBasemap) {
            setPaintPropertySafely(
              map,
              `${sourceId}LayerFill`,
              "fill-opacity",
              countySatelliteOpacity
            );
          } else {
            setPaintPropertySafely(
              map,
              `${sourceId}LayerFill`,
              "fill-opacity",
              countyDefaultOpacity
            );
          }
        }
      }
    };

    map.on("moveend", moveEnd);

    return () => {
      map.off("moveend", moveEnd);
    };
  }, [map]);

  useEffect(() => {
    if (!map) return;

    const fillColor = setCountyColor(
      map,
      currentCountyGeoJSON,
      currentCountyFillColor
    );
    setCurrentCountyFillColor(fillColor);
  }, [currentCountyGeoJSON]);

  useEffect(() => {
    if (!map || !map.style || !map.style._loaded) return;
    if (!currentMapLayerOptions.includes("county")) return;
    if (!map.getLayer(`${sourceId}LayerFill`)) return;

    if (currentMapThemeOption === "Satellite") {
      setPaintPropertySafely(
        map,
        `${sourceId}LayerFill`,
        "fill-opacity",
        countySatelliteOpacity
      );
    } else if (currentMapThemeOption === "Automatic") {
      if (map.getZoom() >= zoomLevelToChangeBasemap) {
        setPaintPropertySafely(
          map,
          `${sourceId}LayerFill`,
          "fill-opacity",
          countySatelliteOpacity
        );
      } else {
        setPaintPropertySafely(
          map,
          `${sourceId}LayerFill`,
          "fill-opacity",
          countyDefaultOpacity
        );
      }
    } else {
      setPaintPropertySafely(
        map,
        `${sourceId}LayerFill`,
        "fill-opacity",
        countyDefaultOpacity
      );
    }
  }, [currentMapThemeOption]);

  return (
    <>
      <Source id={sourceId} type="geojson" data={currentCountyGeoJSON}>
        <Layer {...fillStyle} />
        <Layer {...outlineStyle} />
        <Layer {...defaultLabelStyle} />
        <Layer {...centerLabelStyle} />
      </Source>
    </>
  );
}

export default CountyLayer;
