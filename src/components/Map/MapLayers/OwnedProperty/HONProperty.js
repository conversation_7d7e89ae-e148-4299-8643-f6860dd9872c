import { useState, useEffect, useCallback, useRef } from "react";
import { renderToString } from "react-dom/server";
import Source from "../Source";
import Layer from "../Layer";
import { useSelector, useDispatch } from "react-redux";
import { geojsonTemplate } from "../../../../constants";
import honlayerImg from "../../../../assets/images/mapbox/controls/honlayer.png";
import { initPopup } from "../../MapUtility/general";
import { Segmented } from "antd";

const sourceId = "OwnedProperty";

// const minZoom = 9;
const circleStyle = {
  id: `${sourceId}-None-CircleLayer`,
  type: "circle",
  // minzoom: zoomLevelToNewHomes,
  paint: {
    "circle-radius": 6,
    "circle-stroke-color": "#fff",
    "circle-stroke-width": 3,
    "circle-color": "#50C878",
  },
};

const iconStyle = {
  id: `${sourceId}CircleLayer`,
  type: "symbol",
  layout: {
    "icon-image": "ownedIcon",
    "icon-size": [
      "interpolate",
      ["linear"],
      ["zoom"],
      10,
      0.2,
      14,
      0.4,
      16,
      0.6,
    ],
    "icon-allow-overlap": true,
  },
};

const clusterStyle = {
  id: `${sourceId}ClusterLayer`,
  type: "circle",
  filter: ["has", "point_count"],
  paint: {
    "circle-color": "#A9BBC0",
    "circle-radius": [
      "interpolate",
      ["linear"],
      ["zoom"],
      0,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 18],
      10.4,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 18],
      12,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 18],
    ],
    "circle-opacity": 1,
    "circle-stroke-width": 1,
    "circle-stroke-color": "rgba(255,255,255,1)",
  },
  beforeId: `${sourceId}ClusterSymbolLayer`,
};

const clusterLabelStyle = {
  id: `${sourceId}ClusterSymbolLayer`,
  type: "symbol",
  filter: ["has", "point_count"],
  layout: {
    "text-font": ["Open Sans Bold"],
    "text-field": "{point_count}",
    "text-size": 14,
    "text-justify": "auto",
    // "text-allow-overlap": true,
  },
  paint: {
    "text-color": "rgba(0,0,0,1)",
  },
};

let popup;

const formatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  minimumFractionDigits: 0,
  maximumFractionDigits: 0,
});

const getTooltipHTML = (property) => {
  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <div>
        <span style={{ fontSize: "14px", fontWeight: "600", margin: 0 }}>
          Rent: {formatter.format(property.rent)}
        </span>
      </div>
      {property.status && (
        <div>
          <span>Status: {property.status}</span>
        </div>
      )}
      <div>
        <span>{property.address}</span>
      </div>
      <div>
        <span>
          {property.city}, {property.state}
        </span>
      </div>
      <div>
        <span>{property.zip}</span>
      </div>
    </div>
  );
};

let iconImg;

function HONProperty() {
  const [clusterType, setClusterType] = useState("All");

  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const currentOwnedGeoJSON = useSelector(
    (state) => state.Map.currentOwnedGeoJSON
  );
  const user = useSelector((state) => state.Configure.user);
  const dispatch = useDispatch();

  if (user && user.userGroup) {
    if (user.userGroup.includes("HON")) {
      iconImg = honlayerImg;
    }
  }

  useEffect(() => {
    if (!map) return;

    popup = initPopup();

    const fetchHONOwnedData = () => {
      if (currentMapLayerOptions.includes("hon owned")) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getOwnedProperty",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
            userGroup: user.userGroup,
          },
        });
      } else {
        if (currentOwnedGeoJSON.features.length > 0) {
          dispatch({
            type: "Map/saveState",
            payload: {
              currentOwnedGeoJSON: geojsonTemplate,
            },
          });
        }
      }
    };

    const loadIconImage = () => {
      map.loadImage(iconImg, (error, image) => {
        if (error) throw error;
        if (!map.hasImage("ownedIcon")) {
          map.addImage("ownedIcon", image);
        }
      });
      // moves layer to the top of the map as the text label is not visible when zoomed out
      map.moveLayer(`${sourceId}ClusterSymbolLayer`);
    };

    const moveEnd = () => {
      fetchHONOwnedData();
    };

    const mouseMove = (e, layer) => {
      const feature = map.queryRenderedFeatures(e.point, {
        layers: [layer],
      });
      if (feature && feature.length > 0) {
        if (feature[0].properties.cluster) return;
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature[0].properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      } else {
        popup.remove();
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    fetchHONOwnedData();

    map.on("style.load", loadIconImage);
    map.on("moveend", moveEnd);
    map.on("mousemove", iconStyle.id, (e) => mouseMove(e, iconStyle.id));
    map.on("mouseleave", iconStyle.id, mouseLeave);
    map.on("mousemove", circleStyle.id, (e) => mouseMove(e, circleStyle.id));
    map.on("mouseleave", circleStyle.id, mouseLeave);
    return () => {
      map.off("style.load", loadIconImage);
      map.off("moveend", moveEnd);
      map.off("mousemove", iconStyle.id, (e) => mouseMove(e, iconStyle.id));
      map.off("mouseleave", iconStyle.id, mouseLeave);
      map.off("mousemove", circleStyle.id, (e) => mouseMove(e, circleStyle.id));
      map.off("mouseleave", circleStyle.id, mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (currentMapLayerOptions.includes("hon owned")) {
    return (
      <>
        {clusterType === "All" && (
          <Source
            id={sourceId}
            type="geojson"
            data={currentOwnedGeoJSON}
            cluster={true}
            clusterMaxZoom={10}
            clusterRadius={200}
          >
            <Layer {...iconStyle} />
            <Layer {...clusterStyle} />
            <Layer {...clusterLabelStyle} />
          </Source>
        )}

        {clusterType === "None" && (
          <Source id={sourceId} type="geojson" data={currentOwnedGeoJSON}>
            <Layer {...circleStyle} />
          </Source>
        )}

        <div
          style={{
            backgroundColor: "white",
            position: "absolute",
            bottom: "60px",
            left: "50%",
            transform: "translateX(-50%)",
            zIndex: 100,
            boxShadow:
              "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
          }}
        >
          <Segmented
            // options={["All", "Clustered By Status", "None"]}
            options={["All", "None"]}
            onChange={(value) => setClusterType(value)}
            value={clusterType}
          />
        </div>
      </>
    );
  }
  return null;
}

export default HONProperty;
