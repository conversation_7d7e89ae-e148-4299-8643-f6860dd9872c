import { useState, useEffect, useCallback, useRef } from "react";
import { renderToString } from "react-dom/server";
import Source from "../Source";
import Layer from "../Layer";
import { useSelector, useDispatch } from "react-redux";
import { geojsonTemplate } from "../../../../constants";
import gorlickImg from "../../../../assets/images/mapbox/controls/gorlicklayer.png";
import gorlickImgRehab from "../../../../assets/images/mapbox/controls/gorlicklayer-rehab.png";
import gorlickImgClosed from "../../../../assets/images/mapbox/controls/gorlicklayer-closed.png";
import gorlickImgLeased from "../../../../assets/images/mapbox/controls/gorlicklayer-leased.png";
import gorlickImgVacant from "../../../../assets/images/mapbox/controls/gorlicklayer-vacant.png";
import { initPopup } from "../../MapUtility/general";
import { Segmented } from "antd";

const sourceId = "OwnedProperty";

const COLORS = {
  closed: "#636e72",
  rehab: "#f9b42d",
  vacant: "#ff4c30",
  leased: "#2ecc71",
};

// const minZoom = 9;

const circleStyle = {
  id: `${sourceId}-None-CircleLayer`,
  type: "circle",
  // minzoom: zoomLevelToNewHomes,
  paint: {
    "circle-radius": 6,
    "circle-stroke-color": "#fff",
    "circle-stroke-width": 3,
    "circle-color": "#50C878",
  },
};

const iconStyle = {
  id: `${sourceId}CircleLayer`,
  type: "symbol",
  layout: {
    "icon-image": "ownedIcon",
    "icon-size": 0.4,
    "icon-allow-overlap": true,
  },
};

const clusterStyle = {
  id: `${sourceId}ClusterLayer`,
  type: "circle",
  filter: ["has", "point_count"],
  paint: {
    "circle-color": "#A9BBC0",
    "circle-radius": [
      "interpolate",
      ["linear"],
      ["zoom"],
      0,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 18],
      10.4,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 18],
      12,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 18],
    ],
    "circle-opacity": 0.75,
    "circle-stroke-width": 1,
    "circle-stroke-color": "rgba(255,255,255,1)",
  },
  beforeId: `${sourceId}ClusterSymbolLayer`,
};

const clusterLabelStyle = {
  id: `${sourceId}ClusterSymbolLayer`,
  type: "symbol",
  filter: ["has", "point_count"],
  layout: {
    "text-font": ["Open Sans Bold"],
    "text-field": "{point_count}",
    "text-size": 14,
    "text-justify": "auto",
    // "text-allow-overlap": true,
  },
  paint: {
    "text-color": "rgba(0,0,0,1)",
  },
};

const getIconStyle = (status) => {
  return {
    id: `${sourceId}-${status}-CircleLayer`,
    type: "symbol",
    layout: {
      // "icon-image": iconImage,
      "icon-image": [
        "match",
        ["get", "status"],
        "Closed",
        "ownedIconClosed",
        "Rehab",
        "ownedIconRehab",
        "Vacant",
        "ownedIconVacant",
        "Leased",
        "ownedIconLeased",
        "ownedIcon",
      ],
      "icon-size": [
        "interpolate",
        ["linear"],
        ["zoom"],
        10,
        0.2,
        14,
        0.4,
        16,
        0.6,
      ],
      "icon-allow-overlap": true,
    },
  };
};

const getClusterStyle = (status, color) => {
  return {
    id: `${sourceId}-${status}-ClusterLayer`,
    type: "circle",
    filter: ["has", "point_count"],
    paint: {
      "circle-color": color,
      "circle-radius": [
        "interpolate",
        ["linear"],
        ["zoom"],
        0,
        ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 18],
        10.4,
        ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 18],
        12,
        ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 18],
      ],
      "circle-opacity": 1,
      "circle-stroke-width": 1,
      "circle-stroke-color": "rgba(255,255,255,1)",
    },
    beforeId: `${sourceId}-${status}-ClusterSymbolLayer`,
  };
};

const getClusterLabelStyle = (status) => {
  return {
    id: `${sourceId}-${status}-ClusterSymbolLayer`,
    type: "symbol",
    filter: ["has", "point_count"],
    layout: {
      "text-font": ["Open Sans Bold"],
      "text-field": "{point_count}",
      "text-size": 14,
      "text-justify": "auto",
      // "text-allow-overlap": true,
    },
    paint: {
      "text-color": "rgba(0,0,0,1)",
    },
  };
};

let popup;

const formatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  minimumFractionDigits: 0,
  maximumFractionDigits: 0,
});

const getTooltipHTML = (property) => {
  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <div>
        <span style={{ fontSize: "14px", fontWeight: "600", margin: 0 }}>
          Rent: {formatter.format(property.rent)}
        </span>
      </div>
      {property.status && (
        <div>
          <span>Status: {property.status}</span>
        </div>
      )}
      <div>
        <span>{property.address}</span>
      </div>
      <div>
        <span>
          {property.city}, {property.state}
        </span>
      </div>
      <div>
        <span>{property.zip}</span>
      </div>
    </div>
  );
};

let iconImg;

// Closed
// Rehab
// Vacant
// Leased

function BridgeProperty() {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const currentOwnedGeoJSON = useSelector(
    (state) => state.Map.currentOwnedGeoJSON
  );
  const user = useSelector((state) => state.Configure.user);
  const bridgeOwnedClusterType = useSelector(
    (state) => state.Map.bridgeOwnedClusterType
  );
  const dispatch = useDispatch();

  const [statusOwnedGeoJSON, setStatusOwnedGeoJSON] = useState(geojsonTemplate);

  if (user && user.userGroup) {
    if (
      user.userGroup.includes("BridgeAdmin") ||
      user.userGroup.includes("BridgeFull")
    ) {
      iconImg = gorlickImg;
    }
  }

  // prettier-ignore
  useEffect(() => {
    if (!map) return;

    popup = initPopup();

    const fetchHONOwnedData = () => {
      if (currentMapLayerOptions.includes("gorlick owned")) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getOwnedProperty",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
            userGroup: user.userGroup,
          },
        });
      } else {
        if (currentOwnedGeoJSON.features.length > 0) {
          dispatch({
            type: "Map/saveState",
            payload: {
              currentOwnedGeoJSON: geojsonTemplate,
            },
          });
        }
      }
    };

    const loadIconImage = () => {
      map.loadImage(gorlickImg, (error, image) => {
        if (error) throw error;
        if (!map.hasImage("ownedIcon")) {
          map.addImage("ownedIcon", image);
        }
      });

      map.loadImage(gorlickImgRehab, (error, image) => {
        if (error) throw error;
        if (!map.hasImage("ownedIconRehab")) {
          map.addImage("ownedIconRehab", image);
        }
      });

      map.loadImage(gorlickImgClosed, (error, image) => {
        if (error) throw error;
        if (!map.hasImage("ownedIconClosed")) {
          map.addImage("ownedIconClosed", image);
        }
      });

      map.loadImage(gorlickImgLeased, (error, image) => {
        if (error) throw error;
        if (!map.hasImage("ownedIconLeased")) {
          map.addImage("ownedIconLeased", image);
        }
      });

      map.loadImage(gorlickImgVacant, (error, image) => {
        if (error) throw error;
        if (!map.hasImage("ownedIconVacant")) {
          map.addImage("ownedIconVacant", image);
        }
      });

      // moves layer to the top of the map as the text label is not visible when zoomed out
      map.moveLayer(`${sourceId}ClusterSymbolLayer`);
      map.moveLayer(`${sourceId}-All-ClusterLayer`);
      map.moveLayer(`${sourceId}-Closed-ClusterLayer`);
      map.moveLayer(`${sourceId}-Rehab-ClusterLayer`);
      map.moveLayer(`${sourceId}-Vacant-ClusterLayer`);
      map.moveLayer(`${sourceId}-Leased-ClusterLayer`);
    };

    const moveEnd = () => {
      fetchHONOwnedData();
      console.log(map.getZoom());
    };

    const mouseMove = (e, layer) => {
      const feature = map.queryRenderedFeatures(e.point, {
        // layers: [`${sourceId}CircleLayer`],
        layers: [layer],
      });
      if (feature && feature.length > 0) {
        if (feature[0].properties.cluster) return;
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature[0].properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      } else {
        popup.remove();
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    fetchHONOwnedData();

    map.on("style.load", loadIconImage);
    map.on("moveend", moveEnd);
    // map.on("mousemove", `${sourceId}CircleLayer`, mouseMove);
    // map.on("mouseleave", `${sourceId}CircleLayer`, mouseLeave);
    map.on("mousemove", `${sourceId}-All-CircleLayer`, (e) => mouseMove(e, `${sourceId}-All-CircleLayer`));
    map.on("mouseleave", `${sourceId}-All-CircleLayer`, mouseLeave);
    map.on("mousemove", `${sourceId}-Closed-CircleLayer`, (e) => mouseMove(e, `${sourceId}-Closed-CircleLayer`));
    map.on("mouseleave", `${sourceId}-Closed-CircleLayer`, mouseLeave);
    map.on("mousemove", `${sourceId}-Rehab-CircleLayer`, (e) => mouseMove(e, `${sourceId}-Rehab-CircleLayer`));
    map.on("mouseleave", `${sourceId}-Rehab-CircleLayer`, mouseLeave);
    map.on("mousemove", `${sourceId}-Vacant-CircleLayer`, (e) => mouseMove(e, `${sourceId}-Vacant-CircleLayer`));
    map.on("mouseleave", `${sourceId}-Vacant-CircleLayer`, mouseLeave);
    map.on("mousemove", `${sourceId}-Leased-CircleLayer`, (e) => mouseMove(e, `${sourceId}-Leased-CircleLayer`));
    map.on("mouseleave", `${sourceId}-Leased-CircleLayer`, mouseLeave);
    map.on("mousemove", `${sourceId}-None-CircleLayer`, (e) => mouseMove(e, `${sourceId}-None-CircleLayer`));
    map.on("mouseleave", `${sourceId}-None-CircleLayer`, mouseLeave);
    return () => {
      map.off("style.load", loadIconImage);
      map.off("moveend", moveEnd);
      // map.off("mousemove", `${sourceId}CircleLayer`, mouseMove);
      // map.off("mouseleave", `${sourceId}CircleLayer`, mouseLeave);
      map.off("mousemove", `${sourceId}-All-CircleLayer`, (e) => mouseMove(e, `${sourceId}-All-CircleLayer`));
    map.off("mouseleave", `${sourceId}-All-CircleLayer`, mouseLeave);
    map.off("mousemove", `${sourceId}-Closed-CircleLayer`, (e) => mouseMove(e, `${sourceId}-Closed-CircleLayer`));
    map.off("mouseleave", `${sourceId}-Closed-CircleLayer`, mouseLeave);
    map.off("mousemove", `${sourceId}-Rehab-CircleLayer`, (e) => mouseMove(e, `${sourceId}-Rehab-CircleLayer`));
    map.off("mouseleave", `${sourceId}-Rehab-CircleLayer`, mouseLeave);
    map.off("mousemove", `${sourceId}-Vacant-CircleLayer`, (e) => mouseMove(e, `${sourceId}-Vacant-CircleLayer`));
    map.off("mouseleave", `${sourceId}-Vacant-CircleLayer`, mouseLeave);
    map.off("mousemove", `${sourceId}-Leased-CircleLayer`, (e) => mouseMove(e, `${sourceId}-Leased-CircleLayer`));
    map.off("mouseleave", `${sourceId}-Leased-CircleLayer`, mouseLeave);
    };
  }, [map, currentMapLayerOptions, bridgeOwnedClusterType]);

  useEffect(() => {
    const currentOwnedSplitGeoJSON = {
      closed: {
        type: "FeatureCollection",
        features: [],
      },
      rehab: {
        type: "FeatureCollection",
        features: [],
      },
      vacant: {
        type: "FeatureCollection",
        features: [],
      },
      leased: {
        type: "FeatureCollection",
        features: [],
      },
    };

    currentOwnedGeoJSON.features.forEach((feature) => {
      if (feature.properties.status === "Closed") {
        currentOwnedSplitGeoJSON.closed.features.push(feature);
      } else if (feature.properties.status === "Rehab") {
        currentOwnedSplitGeoJSON.rehab.features.push(feature);
      } else if (feature.properties.status === "Vacant") {
        currentOwnedSplitGeoJSON.vacant.features.push(feature);
      } else if (feature.properties.status === "Leased") {
        currentOwnedSplitGeoJSON.leased.features.push(feature);
      }
    });

    setStatusOwnedGeoJSON(currentOwnedSplitGeoJSON);
    dispatch({
      type: "Map/saveState",
      payload: {
        currentBridgeOwnedStatusCount: {
          closed: currentOwnedSplitGeoJSON.closed.features.length,
          rehab: currentOwnedSplitGeoJSON.rehab.features.length,
          vacant: currentOwnedSplitGeoJSON.vacant.features.length,
          leased: currentOwnedSplitGeoJSON.leased.features.length,
        },
      },
    });
  }, [currentOwnedGeoJSON]);

  if (currentMapLayerOptions.includes("gorlick owned")) {
    return (
      <>
        {bridgeOwnedClusterType === "All" && (
          <Source
            id={`${sourceId}-All`}
            type="geojson"
            data={currentOwnedGeoJSON}
            cluster={true}
            clusterMaxZoom={10}
            clusterRadius={200}
          >
            <Layer {...getIconStyle("All")} />
            <Layer {...getClusterStyle("All", "#A9BBC0")} />
            <Layer {...getClusterLabelStyle("All")} />
          </Source>
        )}

        {bridgeOwnedClusterType === "Clustered By Status" && (
          <>
            <Source
              id={`${sourceId}-Closed`}
              type="geojson"
              data={statusOwnedGeoJSON.closed}
              cluster={true}
              clusterMaxZoom={10}
              clusterRadius={200}
            >
              <Layer {...getIconStyle("Closed")} />
              <Layer {...getClusterStyle("Closed", COLORS.closed)} />
              <Layer {...getClusterLabelStyle("Closed")} />
            </Source>
            <Source
              id={`${sourceId}-Rehab`}
              type="geojson"
              data={statusOwnedGeoJSON.rehab}
              cluster={true}
              clusterMaxZoom={10}
              clusterRadius={200}
            >
              <Layer {...getIconStyle("Rehab")} />
              <Layer {...getClusterStyle("Rehab", COLORS.rehab)} />
              <Layer {...getClusterLabelStyle("Rehab")} />
            </Source>
            <Source
              id={`${sourceId}-Vacant`}
              type="geojson"
              data={statusOwnedGeoJSON.vacant}
              cluster={true}
              clusterMaxZoom={10}
              clusterRadius={200}
            >
              <Layer {...getIconStyle("Vacant")} />
              <Layer {...getClusterStyle("Vacant", COLORS.vacant)} />
              <Layer {...getClusterLabelStyle("Vacant")} />
            </Source>
            <Source
              id={`${sourceId}-Leased`}
              type="geojson"
              data={statusOwnedGeoJSON.leased}
              cluster={true}
              clusterMaxZoom={10}
              clusterRadius={200}
            >
              <Layer {...getIconStyle("Leased")} />
              <Layer {...getClusterStyle("Leased", COLORS.leased)} />
              <Layer {...getClusterLabelStyle("Leased")} />
            </Source>
          </>
        )}

        {bridgeOwnedClusterType === "None" && (
          <Source id={sourceId} type="geojson" data={currentOwnedGeoJSON}>
            <Layer {...circleStyle} />
          </Source>
        )}

        <div
          style={{
            backgroundColor: "white",
            position: "absolute",
            bottom: "60px",
            left: "50%",
            transform: "translateX(-50%)",
            zIndex: 100,
            boxShadow:
              "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
          }}
        >
          <Segmented
            options={["All", "Clustered By Status", "None"]}
            onChange={(value) =>
              dispatch({
                type: "Map/saveState",
                payload: {
                  bridgeOwnedClusterType: value,
                },
              })
            }
            value={bridgeOwnedClusterType}
          />
        </div>
      </>
    );
  }
  return null;
}

export default BridgeProperty;

export function BridgeOwnedPropertyLegend() {
  const currentBridgeOwnedStatusCount = useSelector(
    (state) => state.Map.currentBridgeOwnedStatusCount
  );

  const itemRowStyle = {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    padding: "2.5px 10px",
  };

  return (
    <div
      style={{
        backgroundColor: "white",
        zIndex: 200,
        boxShadow:
          "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
        borderRadius: "5px",
        minWidth: "200px",
        paddingBottom: "5px",
      }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "center",
          padding: "10px",
        }}
      >
        <span style={{ fontWeight: "700" }}>Owned Property</span>
      </div>
      <div>
        <div style={itemRowStyle}>
          <div>
            <span>Closed ({currentBridgeOwnedStatusCount.closed || 0})</span>
          </div>
          <div
            style={{
              width: "20px",
              height: "20px",
              backgroundColor: COLORS.closed,
            }}
          ></div>
        </div>
        <div style={itemRowStyle}>
          <div>
            <span>Rehab ({currentBridgeOwnedStatusCount.rehab || 0})</span>
          </div>
          <div
            style={{
              width: "20px",
              height: "20px",
              backgroundColor: COLORS.rehab,
            }}
          ></div>
        </div>

        <div style={itemRowStyle}>
          <div>
            <span>Vacant ({currentBridgeOwnedStatusCount.vacant || 0})</span>
          </div>
          <div
            style={{
              width: "20px",
              height: "20px",
              backgroundColor: COLORS.vacant,
            }}
          ></div>
        </div>
        <div style={itemRowStyle}>
          <div>
            <span>Leased ({currentBridgeOwnedStatusCount.leased || 0})</span>
          </div>
          <div
            style={{
              width: "20px",
              height: "20px",
              backgroundColor: COLORS.leased,
            }}
          ></div>
        </div>
      </div>
    </div>
  );
}
