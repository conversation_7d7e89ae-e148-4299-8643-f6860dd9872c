import { useState, useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import { renderToString } from "react-dom/server";
import { getInvitationHomeBTR } from "../../../../services/data";
import Source from "../Source";
import Layer from "../Layer";
import { geojsonTemplate } from "../../../../constants";
import { convertToGeoJSON } from "../../../../utils/geography";
import { initPopup } from "../../MapUtility/general";

const sourceId = "invh-btr-source";

const layerStyle = {
  id: "invh-btr-layer",
  type: "circle",
  source: sourceId,
  paint: {
    "circle-radius": 6,
    "circle-color": "#9dcc38",
    "circle-stroke-color": "#000000",
    "circle-stroke-width": 0.75,
  },
};

const popup = initPopup({
  closeButton: false,
  closeOnClick: false,
  offset: -5,
});

const getTooltipHTML = (property) => {
  const keys = ["id", "street", "city", "state"];

  return renderToString(
    <div
      id="invh-btr-popup"
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {keys
        .filter((key) => property[key])
        .map((key) => (
          <div key={key}>
            <span> <b>{key.toUpperCase()}:</b> {property[key]}</span>
          </div>
        ))}
    </div>
  );
};

const InvitationHomesBTR = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);

  const mouseOnPopup = useRef(false);
  const [geojson, setGeojson] = useState(geojsonTemplate);

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("invitationhomes btr")) return;

    const fetchInvitationHomeBTR = async () => {
      const data = await getInvitationHomeBTR();
      console.log("data", data);
      // data = [
      //   {
      //     "id": "1",
      //     "street": "6400 Inspire Cir S",
      //     "city": "Cottage Grove",
      //     "state": "MN",
      //     "lat": "44.855716",
      //     "long": "-92.93619",
      //     "geom": {
      //         "type": "Point",
      //         "coordinates": [
      //             -92.93619,
      //             44.855716
      //         ]
      //     }
      // },
      const geojsonData = convertToGeoJSON({
        data: data,
        geomAccessor: (item) => item.geom,
        propertiesAccessor: (item) => {
          const { geom, ...properties } = item;
          return properties;
        }
      });
      setGeojson(geojsonData);
    };

    const mouseEnter = (e) => {
      map.getCanvas().style.cursor = "pointer";
      const coordinates = e.features[0].geometry.coordinates.slice();
      const content = getTooltipHTML(e.features[0].properties);
      popup.setLngLat(coordinates).setHTML(content).addTo(map);

      popup.getElement().addEventListener("mouseleave", () => {
        mouseOnPopup.current = false;
        map.getCanvas().style.cursor = "";
        popup.remove();
      });

      popup.getElement().addEventListener("mouseenter", () => {
        mouseOnPopup.current = true;
      });
    };

    const mouseLeave = () => {
      if (mouseOnPopup.current) return;
      map.getCanvas().style.cursor = "";
      popup.remove();
    };

    fetchInvitationHomeBTR();
    map.on("mouseenter", "invh-btr-layer", mouseEnter);
    map.on("mouseleave", "invh-btr-layer", mouseLeave);

    return () => {
      map.off("mouseenter", "invh-btr-layer", mouseEnter);
      map.off("mouseleave", "invh-btr-layer", mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("invitationhomes btr")) return null;
  return (
    <Source id={sourceId} type="geojson" data={geojson}>
      <Layer {...layerStyle} />
    </Source>
  );
};

export default InvitationHomesBTR;
