import { useState, useEffect, useCallback, useRef } from "react";
import { renderToString } from "react-dom/server";
import Source from "../Source";
import Layer from "../Layer";
import { useSelector, useDispatch } from "react-redux";
import { geojsonTemplate } from "../../../../constants";
import trueholdImg from "../../../../assets/images/mapbox/controls/truehold.png";
import trueholdImgVacant from "../../../../assets/images/mapbox/controls/truehold-vacant.png";
import trueholdImgOccupied from "../../../../assets/images/mapbox/controls/truehold-occupied.png";
import locatealphaImg from "../../../../assets/images/mapbox/controls/locatealphaicon.png";
import locateAlphaGrayImg from "../../../../assets/images/mapbox/controls/LocateAlpha_logo_gray.png";
import locateAlphaPurpleImg from "../../../../assets/images/mapbox/controls/LocateAlpha_logo_purple.png";
import { initPopup } from "../../MapUtility/general";
import { Segmented } from "antd";

const sourceId = "OwnedProperty";

const COLORS = {
  vacant: "#d33632",
  occupied: "#2b7a64",
};

// const minZoom = 9;

const circleStyle = {
  id: `${sourceId}-None-CircleLayer`,
  type: "circle",
  // minzoom: zoomLevelToNewHomes,
  paint: {
    "circle-radius": 6,
    "circle-stroke-color": "#fff",
    "circle-stroke-width": 3,
    "circle-color": "#50C878",
  },
};

const getIconStyle = (status) => {
  return {
    id: `${sourceId}-${status}-CircleLayer`,
    type: "circle",
    paint: {
      "circle-radius": 8,
      "circle-color": [
        "match",
        ["get", "type"],
        "Owned-Vaccant", "#800080", // Purple for vacant
        "Owned-Occupied", "#008000", // Green for occupied
        "#808080" // Grey as default color
      ],
      "circle-stroke-width": 3,
      "circle-stroke-color": "#ffffff"
    }
  };
};

const getClusterStyle = (status, color) => {
  return {
    id: `${sourceId}-${status}-ClusterLayer`,
    type: "circle",
    filter: ["has", "point_count"],
    paint: {
      "circle-color": color,
      "circle-radius": [
        "interpolate",
        ["linear"],
        ["zoom"],
        0,
        ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 18],
        10.4,
        ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 18],
        12,
        ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 18],
      ],
      "circle-opacity": 1,
      "circle-stroke-width": 1,
      "circle-stroke-color": "rgba(255,255,255,1)",
    },
    beforeId: `${sourceId}-${status}-ClusterSymbolLayer`,
  };
};

const getClusterLabelStyle = (status) => {
  return {
    id: `${sourceId}-${status}-ClusterSymbolLayer`,
    type: "symbol",
    filter: ["has", "point_count"],
    layout: {
      "text-font": ["Open Sans Bold"],
      "text-field": "{point_count}",
      "text-size": 14,
      "text-justify": "auto",
      // "text-allow-overlap": true,
    },
    paint: {
      "text-color": "rgba(0,0,0,1)",
    },
  };
};

let popup;

const formatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  minimumFractionDigits: 0,
  maximumFractionDigits: 0,
});

export const getTooltipHTML = (property) => {
  const titleFormatter = (key) => {
    return key
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const valueFormatter = (key, value) => {
    if (key === "stabilized_cap_rate") return `${Math.round(value * 100 * 100) / 100}%`;
    if (["initial_capex", "rent_offer", "final_purchase_offer"].includes(key))
      return `$${value.toLocaleString()}`;
    return value;
  };

  function formatCurrency(value) {
    const formatter = new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });

    return formatter.format(value);
  }
  console.log("property", property);

  const {
    id,
    address,
    city,
    state,
    zip_code,
    year_built,
    sqft,
    beds,
    baths,
    pool,
    market_value,
    market_rent,
    caprate,
    type,
  } = property;
  const addressString = `${address}, ${city}, ${state}, ${zip_code} `;
  const typeString = type === "Owned-Vaccant" ? "Vacant" : "Occupied";
  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <div>
        <span style={{ fontSize: "14px", fontWeight: "600", margin: 0 }}>{addressString}</span>
      </div>

      <div>
        <span style={{ fontSize: "12px", margin: 0 }}>Type: {typeString}</span>
      </div>
      <hr
        style={{
          border: "none",
          borderTop: "1px dotted black",
          margin: "5px 0",
          color: "black",
          backgroundColor: "transparent",
        }}
      />
      <div>
        <span style={{ fontSize: "12px", margin: 0 }}>Year built: {year_built}</span>
      </div>
      <div>
        <span style={{ fontSize: "12px", margin: 0 }}>
          {sqft} sqft | {beds} bd | {baths} ba | Pool: {pool}
        </span>
      </div>
      <div>
        <span style={{ fontSize: "12px", margin: 0 }}>
          Market Value: {formatCurrency(market_value)}{" "}
        </span>
      </div>
      <div>
        <span style={{ fontSize: "12px", margin: 0 }}>
          Market Rent: {formatCurrency(market_rent)}{" "}
        </span>
      </div>
      <div>
        <span style={{ fontSize: "12px", margin: 0 }}>Cap Rate: {caprate} </span>
      </div>
      {/* <div>
        <span style={{ fontSize: "12px", margin: 0 }}>Bid Price: {formatCurrency(bid_price)} </span>
      </div>
      <div>
        <span style={{ fontSize: "12px", margin: 0 }}>Projected Yield on Offer Price: {(Number(projected_yield_on_offer_price) * 100).toFixed(2) + "%"} </span>
      </div>
      <div>
        <span style={{ fontSize: "12px", margin: 0 }}>Gross Yield on Offer Price: {(Number(gross_yield_on_offer_price) * 100).toFixed(2) + "%"} </span>
      </div> */}
    </div>
  );
};

// Vacant
// Occcupied

function DemoOwnedProperty() {
  const userGroup = useSelector((state) => state.Configure.user.userGroup);
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const currentOwnedGeoJSON = useSelector((state) => state.Map.currentOwnedGeoJSON);
  const user = useSelector((state) => state.Configure.user);
  const bridgeOwnedClusterType = useSelector((state) => state.Map.bridgeOwnedClusterType);
  const dispatch = useDispatch();

  const [statusOwnedGeoJSON, setStatusOwnedGeoJSON] = useState(geojsonTemplate);

  // console.log("currentMapLayerOptions: ", currentMapLayerOptions);
  // console.log("currentOwnedGeoJSON: ", currentOwnedGeoJSON);

  // prettier-ignore
  useEffect(() => {
    if (!map) return;

    popup = initPopup();

    const fetchHONOwnedData = () => {
      if (currentMapLayerOptions.includes("demo owned")) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getDemoOwnedProperty",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
            userGroup: user.userGroup,
          },
        });
      } else {
        if (currentOwnedGeoJSON.features.length > 0) {
          dispatch({
            type: "Map/saveState",
            payload: {
              currentOwnedGeoJSON: geojsonTemplate,
            },
          });
        }
      }
    };

    const loadIconImage = () => {
      const isTruehold = userGroup.includes("Truehold");
      

      map.loadImage(isTruehold ? trueholdImgVacant : locateAlphaPurpleImg, (error, image) => {
        if (error) throw error;
        if (!map.hasImage("ownedIconVacant")) {
          map.addImage("ownedIconVacant", image);
        }
      });

      map.loadImage(isTruehold ? trueholdImgOccupied : locatealphaImg, (error, image) => {
        if (error) throw error;
        if (!map.hasImage("ownedIconOccupied")) {
          map.addImage("ownedIconOccupied", image);
        }
      });

      map.loadImage(isTruehold ? trueholdImg : locateAlphaGrayImg, (error, image) => {
        if (error) throw error;
        if (!map.hasImage("ownedIcon")) {
          map.addImage("ownedIcon", image);
        }
      });

      // moves layer to the top of the map as the text label is not visible when zoomed out
      if (map.getLayer(`${sourceId}ClusterSymbolLayer`)) map.moveLayer(`${sourceId}ClusterSymbolLayer`);
      if (map.getLayer(`${sourceId}-All-ClusterLayer`)) map.moveLayer(`${sourceId}-All-ClusterLayer`);
      if (map.getLayer(`${sourceId}-Vacant-ClusterLayer`)) map.moveLayer(`${sourceId}-Vacant-ClusterLayer`);
      if (map.getLayer(`${sourceId}-Occupied-ClusterLayer`)) map.moveLayer(`${sourceId}-Occupied-ClusterLayer`);
    };

    const moveEnd = () => {
      fetchHONOwnedData();
      // console.log(map.getZoom());
    };

    const mouseMove = (e, layer) => {
      const feature = map.queryRenderedFeatures(e.point, {
        // layers: [`${sourceId}CircleLayer`],
        layers: [layer],
      });
      if (feature && feature.length > 0) {
        if (feature[0].properties.cluster) return;
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature[0].properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      } else {
        popup.remove();
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    fetchHONOwnedData();

    map.on("style.load", loadIconImage);
    map.on("moveend", moveEnd);
    // map.on("mousemove", `${sourceId}CircleLayer`, mouseMove);
    // map.on("mouseleave", `${sourceId}CircleLayer`, mouseLeave);
    map.on("mousemove", `${sourceId}-All-CircleLayer`, (e) => mouseMove(e, `${sourceId}-All-CircleLayer`));
    map.on("mouseleave", `${sourceId}-All-CircleLayer`, mouseLeave);
    map.on("mousemove", `${sourceId}-Vacant-CircleLayer`, (e) => mouseMove(e, `${sourceId}-Vacant-CircleLayer`));
    map.on("mouseleave", `${sourceId}-Vacant-CircleLayer`, mouseLeave);
    map.on("mousemove", `${sourceId}-Occupied-CircleLayer`, (e) => mouseMove(e, `${sourceId}-Occupied-CircleLayer`));
    map.on("mouseleave", `${sourceId}-Occupied-CircleLayer`, mouseLeave);
    map.on("mousemove", `${sourceId}-Null-CircleLayer`, (e) => mouseMove(e, `${sourceId}-Null-CircleLayer`));
    map.on("mouseleave", `${sourceId}-Null-CircleLayer`, mouseLeave);
    map.on("mousemove", `${sourceId}-None-CircleLayer`, (e) => mouseMove(e, `${sourceId}-None-CircleLayer`));
    map.on("mouseleave", `${sourceId}-None-CircleLayer`, mouseLeave);
    map.on("error", (e) => console.log(e));
    return () => {
      map.off("style.load", loadIconImage);
      map.off("moveend", moveEnd);
      // map.off("mousemove", `${sourceId}CircleLayer`, mouseMove);
      // map.off("mouseleave", `${sourceId}CircleLayer`, mouseLeave);
      map.off("mousemove", `${sourceId}-All-CircleLayer`, (e) => mouseMove(e, `${sourceId}-All-CircleLayer`));
    map.off("mouseleave", `${sourceId}-All-CircleLayer`, mouseLeave);
    map.off("mousemove", `${sourceId}-Vacant-CircleLayer`, (e) => mouseMove(e, `${sourceId}-Vacant-CircleLayer`));
    map.off("mouseleave", `${sourceId}-Vacant-CircleLayer`, mouseLeave);
    map.off("mousemove", `${sourceId}-Occupied-CircleLayer`, (e) => mouseMove(e, `${sourceId}-Occupied-CircleLayer`));
    map.off("mouseleave", `${sourceId}-Occupied-CircleLayer`, mouseLeave);
    map.off("mousemove", `${sourceId}-Null-CircleLayer`, (e) => mouseMove(e, `${sourceId}-Null-CircleLayer`));
    map.off("mouseleave", `${sourceId}-Null-CircleLayer`, mouseLeave);
    };
  }, [map, currentMapLayerOptions, bridgeOwnedClusterType, userGroup]);

  useEffect(() => {
    const currentOwnedSplitGeoJSON = {
      vacant: {
        type: "FeatureCollection",
        features: [],
      },
      occupied: {
        type: "FeatureCollection",
        features: [],
      },
      NA: {
        type: "FeatureCollection",
        features: [],
      },
    };

    currentOwnedGeoJSON.features.forEach((feature) => {
      if (feature.properties.type === "Owned-Vaccant") {
        currentOwnedSplitGeoJSON.vacant.features.push(feature);
      } else if (feature.properties.status === "Owned-Occupied") {
        currentOwnedSplitGeoJSON.occupied.features.push(feature);
      } else if (feature.properties.status === null) {
        currentOwnedSplitGeoJSON.NA.features.push(feature);
      }
    });

    setStatusOwnedGeoJSON(currentOwnedSplitGeoJSON);
    dispatch({
      type: "Map/saveState",
      payload: {
        currentBridgeOwnedStatusCount: {
          vacant: currentOwnedSplitGeoJSON.vacant.features.length,
          occupied: currentOwnedSplitGeoJSON.occupied.features.length,
          NA: currentOwnedSplitGeoJSON.NA.features.length,
        },
      },
    });
  }, [currentOwnedGeoJSON]);

  if (currentMapLayerOptions.includes("demo owned")) {
    return (
      <>
        {bridgeOwnedClusterType === "All" && (
          <Source
            id={`${sourceId}-All`}
            type="geojson"
            data={currentOwnedGeoJSON}
            cluster={true}
            clusterMaxZoom={9}
            clusterRadius={30}
          >
            <Layer {...getIconStyle("All")} />
            <Layer {...getClusterStyle("All", "#A9BBC0")} />
            <Layer {...getClusterLabelStyle("All")} />
          </Source>
        )}

        {bridgeOwnedClusterType === "Clustered By Status" && (
          <>
            <Source
              id={`${sourceId}-Vacant`}
              type="geojson"
              data={statusOwnedGeoJSON.vacant}
              cluster={true}
              clusterMaxZoom={9}
              clusterRadius={30}
            >
              <Layer {...getIconStyle("Owned-Vaccant")} />
              <Layer {...getClusterStyle("Owned-Vaccant", COLORS.vacant)} />
              <Layer {...getClusterLabelStyle("Owned-Vaccant")} />
            </Source>
            <Source
              id={`${sourceId}-Occupied`}
              type="geojson"
              data={statusOwnedGeoJSON.occupied}
              cluster={true}
              clusterMaxZoom={9}
              clusterRadius={30}
            >
              <Layer {...getIconStyle("Owned-Occupied")} />
              <Layer {...getClusterStyle("Owned-Occupied", COLORS.occupied)} />
              <Layer {...getClusterLabelStyle("Owned-Occupied")} />
            </Source>
            <Source
              id={`${sourceId}-Null`}
              type="geojson"
              data={statusOwnedGeoJSON.NA}
              cluster={true}
              clusterMaxZoom={9}
              clusterRadius={30}
            >
              <Layer {...getIconStyle("Null")} />
              <Layer {...getClusterStyle("Null", "#A9BBC0")} />
              <Layer {...getClusterLabelStyle("Null")} />
            </Source>
          </>
        )}

        {bridgeOwnedClusterType === "None" && (
          <Source id={sourceId} type="geojson" data={currentOwnedGeoJSON}>
            <Layer {...circleStyle} />
          </Source>
        )}

        <div
          style={{
            backgroundColor: "white",
            position: "absolute",
            bottom: "60px",
            left: "50%",
            transform: "translateX(-50%)",
            zIndex: 100,
            boxShadow: "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
          }}
        >
          <Segmented
            options={["All", "Clustered By Status", "None"]}
            onChange={(value) =>
              dispatch({
                type: "Map/saveState",
                payload: {
                  bridgeOwnedClusterType: value,
                },
              })
            }
            value={bridgeOwnedClusterType}
          />
        </div>
      </>
    );
  }
  return null;
}

export default DemoOwnedProperty;

export function DemoOwnedPropertyLegend({ clusterType }) {
  const userGroup = useSelector((state) => state.Configure.user.userGroup);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const currentBridgeOwnedStatusCount = useSelector(
    (state) => state.Map.currentBridgeOwnedStatusCount
  );

  const itemRowStyle = {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    padding: "2.5px 10px",
  };

  const getOwned = useCallback(() => {
    return {
      Vacant: userGroup.includes("Truehold") ? trueholdImgVacant : locateAlphaPurpleImg,
      Occupied: userGroup.includes("Truehold") ? trueholdImgOccupied : locatealphaImg,
    };
  }, [userGroup]);

  const getTile = useCallback(() => {
    if (userGroup.includes("Truehold")) {
      return "Truehold Properties";
    } else if (userGroup.includes("demo-users") || userGroup.includes("demo-CMA-DFW-only")) {
      return "Portfolio";
    }
  }, [userGroup]);

  return (
    <div className="bg-white z-[200] shadow-md rounded-md min-w-[200px] pb-5">
      {currentMapLayerOptions.includes("demo owned") && (
        <>
          <div className="flex flex-row justify-center p-2">
            <span className="font-[700]">{getTile()}</span>
          </div>
          <div className="flex flex-row justify-center p-2">
            <span className="font-[600]">Owned</span>
          </div>
          <div className="flex flex-row justify-between py-[1px] px-[10px]">
          <div
            className="w-[20px] h-[20px] rounded-full border border-solid border-black"
            style={{ backgroundColor: "#008000" }}
          ></div>
          <div>
            <span>Occupied</span>
          </div>
        </div>
        <div className="flex flex-row justify-between py-[1px] px-[10px]">
          <div
            className="w-[20px] h-[20px] rounded-full border border-solid border-black"
            style={{ backgroundColor: "#800080" }}
          ></div>
          <div>
            <span>Vaccant</span>
          </div>
        </div>

          {/* {clusterType === "Clustered By Status" && (
            <div>
              <div className="text-center font-[600]">
                <span>Cluster Count</span>
              </div>
              <div className="flex flex-row justify-between py-[1px] px-[10px]">
                <div className="w-[20px] h-[20px]" style={{ backgroundColor: COLORS.vacant }}></div>
                <div>
                  <span>Vacant ({currentBridgeOwnedStatusCount.vacant || 0})</span>
                </div>
              </div>
              <div className="flex flex-row justify-between py-[1px] px-[10px]">
                <div
                  className="w-[20px] h-[20px]"
                  style={{ backgroundColor: COLORS.occupied }}
                ></div>
                <div>
                  <span>Occcupied ({currentBridgeOwnedStatusCount.occupied || 0})</span>
                </div>
              </div>
              <div className="flex flex-row justify-between py-[1px] px-[10px]">
                <div className="w-[20px] h-[20px] bg-white border border-solid border-black"></div>
                <div>
                  <span>N/A ({currentBridgeOwnedStatusCount.NA || 0})</span>
                </div>
              </div>
            </div>
          )} */}
        </>
      )}

      {currentMapLayerOptions.includes("demo underwritten") && (
        <>
          <div className="flex flex-row justify-center p-2">
            <span className="font-[600]">Underwritten</span>
          </div>

          <div className="flex flex-row justify-between py-[1px] px-[10px]">
            <div
              className="w-[20px] h-[20px] rounded-full border border-solid border-black"
              style={{ backgroundColor: "#41a390" }}
            ></div>
            <div>
              <span>Underwritten</span>
            </div>
          </div>
        </>
      )}

      {currentMapLayerOptions.includes("demo target") && (
        <>
          <div className="flex flex-row justify-center p-2">
            <span className="font-[600]">Target</span>
          </div>

          <div className="flex flex-row justify-between py-[1px] px-[10px]">
            <div
              className="w-[20px] h-[20px] rounded-full border border-solid border-black"
              style={{ backgroundColor: "#4284f5" }}
            ></div>
            <div>
              <span>Force Flag: True</span>
            </div>
          </div>

          <div className="flex flex-row justify-between py-[1px] px-[10px]">
            <div
              className="w-[20px] h-[20px] rounded-full border border-solid border-black"
              style={{ backgroundColor: "#ff0000" }}
            ></div>
            <div>
              <span>Force Flag: False</span>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
