import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useDispatch, useSelector } from "react-redux";
import { convertToGeoJSON } from "../../../../utils/geography";
import { geojsonTemplate } from "../../../../constants";
import Source from "../Source";
import Layer from "../Layer";
import { initPopup } from "../../MapUtility/general";
import { getFundriseOwnedCommunitiesBoundary } from "../../../../services/data";

const sourceId = "fundrise-owned-community-source";
const pointStyle = {
  id: "fundrise-owned-community-point",
  type: "circle",
  source: sourceId,
  paint: {
    "circle-radius": 8,
    "circle-color": "#f56239",
    "circle-stroke-color": "#ffffff",
    "circle-stroke-width": 2,
  },
};

const getTooltipHTML = (community) => {
  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <span className="font-semibold">{community.community}</span>
      <span>{community.city}, {community.state}</span>
      {community.floorplanName && (
        <span>Floorplan: {community.floorplanName}</span>
      )}
    </div>
  );
};

let popup = initPopup();

const FundriseOwnedCommunities = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const [geojson, setGeojson] = useState(geojsonTemplate);
  const [isLoading, setIsLoading] = useState(false);
const dispatch = useDispatch()
  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("fundrise owned community")) return;

    const moveEnd = async () => {
      try {
        setIsLoading(true);
        
        // Get the current map bounds
        const bounds = map.getBounds();
        
        // Extract coordinates for the boundary
        const southWest = bounds.getSouthWest();
        const northEast = bounds.getNorthEast();
        
        const lat1 = southWest.lat;
        const lng1 = southWest.lng;
        const lat2 = northEast.lat;
        const lng2 = northEast.lng;
        
        console.log(`Fetching for boundary: ${lat1},${lng1} to ${lat2},${lng2}`);
        
        // Call the API with the boundary coordinates
        const data = await getFundriseOwnedCommunitiesBoundary({
          lat1, 
          lng1, 
          lat2, 
          lng2
        });
        
        console.log("Communities data:", data);
        dispatch({
            type: "Map/saveState",
            payload: {
                fundriseCommunities: data,
            },
          });
        if (data && data.length > 0) {
          setGeojson(
            convertToGeoJSON({
              data,
              geomAccessor: (item) => item.geom,
              propertiesAccessor: (item) => {
                const { geom, ...properties } = item;
                return properties;
              },
            })
          );
        }

      } catch (error) {
        console.error("Error fetching Fundrise communities:", error);
      } finally {
        setIsLoading(false);
      }
    };

    const mouseMove = (e) => {
      const feature = e.features[0];
      if (feature && feature.properties) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    const moveBehindOwnedLayer = () => {
      const layers = map.getStyle().layers;
      let foundOwnedLayer = false;
      for (let i = layers.length - 1; i >= 0; i--) {
        if (layers[i].source && layers[i].source.includes("OwnedProperty")) {
          foundOwnedLayer = true;
        } else if (
          layers[i].source &&
          !layers[i].source.includes("OwnedProperty") &&
          foundOwnedLayer
        ) {
          map.moveLayer(pointStyle.id, layers[i].id);
          console.log(`move ${pointStyle.id} behind ${layers[i].id}`);
          break;
        }
      }
    };

    // Initial load
    moveEnd();
    
    // Try to move the layer after data is loaded
    setTimeout(moveBehindOwnedLayer, 500);

    map.on("moveend", moveEnd);
    map.on("mousemove", "fundrise-owned-community-point", mouseMove);
    map.on("mouseleave", "fundrise-owned-community-point", mouseLeave);
    
    return () => {
      map.off("moveend", moveEnd);
      map.off("mousemove", "fundrise-owned-community-point", mouseMove);
      map.off("mouseleave", "fundrise-owned-community-point", mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("fundrise owned community")) return null;
  
  return (
    <>
      <Source id={sourceId} type="geojson" data={geojson}>
        <Layer {...pointStyle} />
      </Source>
      {isLoading && (
        <div className="absolute top-2 right-2 bg-white p-2 rounded shadow">
          Loading communities...
        </div>
      )}
    </>
  );
};

export default FundriseOwnedCommunities;