import { useSelector } from "react-redux";
import HONProperty from "./HONProperty";
import BridgeProperty from "./BridgeProperty";
import FundriseProperty from "./FundriseProperty";
import TrueholdProperty from "./TrueholdProperty";
import BT<PERSON><PERSON>tyBTR from "./BTPropertyBTR";
import DemoOwnedProperty from "./DemoOwnedProperty";
import GreatGulfPortfolio from "./GreatGulfPortfolio";
import FundriseOwnedCommunities from "./FundriseOwnedCommunities";

const OwnedProperty = () => {
  const user = useSelector((state) => state.Configure.user);

  if (user && user.userGroup && user.userGroup.length > 0) {
    return (
      <>
        {user.userGroup.includes("HON") && <HONProperty />}
        {(user.userGroup.includes("BridgeAdmin") ||
          user.userGroup.includes("BridgeFull")) && <BridgeProperty />}
        {user.userGroup.includes("Fundrise") && <>
          <FundriseProperty />
          <FundriseOwnedCommunities />
        </>}
        {["Truehold"].includes(user.userGroup[0]) && <TrueholdProperty />}
        {user.userGroup.includes("BridgeTower") && <BTPropertyBTR />}
        {user.userGroup.includes("demo-users") && <DemoOwnedProperty />}
        {user.userGroup.includes("demo-CMA-DFW-only") && <DemoOwnedProperty />}
        {user.userGroup.includes("GreatGulf") && <GreatGulfPortfolio />}
      </>
    );
  }
  return null;
};

export default OwnedProperty;

// demo-CMA-DFW-only
