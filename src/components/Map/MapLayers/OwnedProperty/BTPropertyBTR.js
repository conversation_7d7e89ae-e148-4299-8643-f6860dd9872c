import { useState, useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import { renderToString } from "react-dom/server";
import { getBT_btrPropertyData } from "../../../../services/data";
import Source from "../Source";
import Layer from "../Layer";
import { geojsonTemplate } from "../../../../constants";
import { convertToGeoJSON } from "../../../../utils/geography";
import { initPopup } from "../../MapUtility/general";

const sourceId = "bt-property-btr-source";

const layerStyle = {
  id: "bt-property-btr-layer",
  type: "circle",
  source: sourceId,
  paint: {
    "circle-radius": 10,
    "circle-color": "#244581",
    "circle-stroke-color": "#ffffff",
    "circle-stroke-width": 3,
  },
};

const popup = initPopup({
  closeButton: false,
  closeOnClick: false,
  offset: -5,
});

const getTooltipHTML = (property) => {
  const keys = [
    "name",
    "asking_price",
    "bid_price",
    "city",
    "contract_price",
    "contract__file",
    "county",
    "deal_status",
    "due_date",
    "latest_envelope_status",
    "property_type",
    "sfr_analyst",
    "seller_email",
    "source",
    "state",
    "status",
    "subitems",
    "text",
    "underwriting_link",
    "underwriting_notes",
    "yoc",
    "zip_code",
  ];

  const titleFormatter = (key) => {
    if (key === "underwriting_link") return "Underwriting Page";
    return key
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const valueFormatter = (key, value) => {
    if (["asking_price", "bid_price", "contract_price"].includes(key))
      return `$${value.toLocaleString()}`;

    if (key === "underwriting_link" && value != null) {
      try {
        const url = new URL(value.slice(value.indexOf("http")));
        return (
          <a href={url.href} target="_blank">
            Go to page
          </a>
        );
      } catch (err) {
        return value;
      }
    }

    return value;
  };

  return renderToString(
    <div
      id="bt-property-btr-popup"
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {keys
        .filter((key) => property[key])
        .map((key) => (
          <div key={key}>
            <span>
              {titleFormatter(key)}: {valueFormatter(key, property[key])}
            </span>
          </div>
        ))}
    </div>
  );
};

const BTPropertyBTR = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );

  const mouseOnPopup = useRef(false);
  const [geojson, setGeojson] = useState(geojsonTemplate);

  useEffect(() => {
    if (!map) return;

    const fetchBTPropertyBTR = async () => {
      const data = await getBT_btrPropertyData();

      setGeojson(
        convertToGeoJSON({
          data: data.filter((d) => d.lat && d.lng) || [],
          geomAccessor: (d) => ({ type: "Point", coordinates: [d.lng, d.lat] }),
          propertiesAccessor: (d) => {
            const { lat, lng, ...properties } = d;
            return properties;
          },
        })
      );
    };

    const mouseEnter = (e) => {
      map.getCanvas().style.cursor = "pointer";
      const coordinates = e.features[0].geometry.coordinates.slice();
      const content = getTooltipHTML(e.features[0].properties);
      popup.setLngLat(coordinates).setHTML(content).addTo(map);

      popup.getElement().addEventListener("mouseleave", () => {
        mouseOnPopup.current = false;
        map.getCanvas().style.cursor = "";
        popup.remove();
      });

      popup.getElement().addEventListener("mouseenter", () => {
        mouseOnPopup.current = true;
      });
    };

    const mouseLeave = () => {
      if (mouseOnPopup.current) return;
      map.getCanvas().style.cursor = "";
      popup.remove();
    };

    fetchBTPropertyBTR();
    map.on("mouseenter", "bt-property-btr-layer", mouseEnter);
    map.on("mouseleave", "bt-property-btr-layer", mouseLeave);

    return () => {
      map.off("mouseenter", "bt-property-btr-layer", mouseEnter);
      map.off("mouseleave", "bt-property-btr-layer", mouseLeave);
    };
  }, [map]);

  if (!currentMapLayerOptions.includes("Bridge Tower BTR")) return null;
  return (
    <Source id={sourceId} type="geojson" data={geojson}>
      <Layer {...layerStyle} />
    </Source>
  );
};

export default BTPropertyBTR;
