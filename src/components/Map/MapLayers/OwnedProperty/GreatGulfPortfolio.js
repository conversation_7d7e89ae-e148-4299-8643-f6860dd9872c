import { useState, useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import { renderToString } from "react-dom/server";
import Source from "../Source";
import Layer from "../Layer";
import data from "../../../../data/greatgulf_portfolio.json";
import { geojsonTemplate } from "../../../../constants";
import blueMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-blue.png";
import turf_bbox from "@turf/bbox";

import { initPopup } from "../../MapUtility/general";

const sourceId = "greatgulf-portfolio";
const layerStyle = {
  id: `${sourceId}-style`,
  type: "symbol",
  layout: {
    "icon-image": "blueMarker",
    "icon-size": 1,
    "icon-allow-overlap": true,
  },
  paint: {
    "icon-color": "white",
    "icon-halo-color": "red",
    "icon-halo-width": 10,
  },
};

const popup = initPopup();

const formatValue = (key, value) => {
  if (key === "price") {
    return `$${value.toLocaleString()}`;
  } else if (key === "acres") {
    return `${value} ac`;
  }
  return value;
};

const formatKey = (key) => {
  return key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
};

const popupHTML = (properties) => {
  const content = (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        padding: "10px",
      }}
    >
      {Object.keys(properties).map((key, idx) => (
        <div key={`${idx}-${key}`}>
          <span style={{ fontWeight: "bold" }}>{formatKey(key)}</span>:{" "}
          {formatValue(key, properties[key])}
        </div>
      ))}
    </div>
  );
  return renderToString(content);
};

const GreatGulfPortfolio = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const [geojson, setGeojson] = useState(geojsonTemplate);

  const mouseOnPopup = useRef(false);

  useEffect(() => {
    if (!map) return;

    const loadIconImage = () => {
      map.loadImage(blueMarker, (error, image) => {
        if (error) throw error;
        if (!map.hasImage("blueMarker")) {
          map.addImage("blueMarker", image);
        }
      });
    };

    const filteredData = structuredClone(geojsonTemplate);
    if (currentMapLayerOptions.includes("greatgulf portfolio")) {
      filteredData.features = [...filteredData.features, ...data.features];
    }

    setGeojson(filteredData);

    if (filteredData.features.length) {
      const bbox = turf_bbox(filteredData);
      map.fitBounds(
        [
          [bbox[0], bbox[1]],
          [bbox[2], bbox[3]],
        ],
        {
          padding: 50,
        }
      );
    }

    const mouseMove = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [layerStyle.id],
      });
      if (features.length > 0) {
        const feature = features[0];
        popup
          .setLngLat(feature.geometry.coordinates)
          .setHTML(popupHTML(feature.properties))
          .addTo(map);

        popup.getElement().addEventListener("mouseleave", () => {
          mouseOnPopup.current = false;
          map.getCanvas().style.cursor = "pointer";
          popup.remove();
        });

        popup.getElement().addEventListener("mouseenter", () => {
          mouseOnPopup.current = true;
        });
      } else {
        popup.remove();
      }
    };

    const mouseLeave = () => {
      setTimeout(() => {
        if (mouseOnPopup.current) return;
        map.getCanvas().style.cursor = "";
        popup.remove();
      }, 250);
    };

    map.on("style.load", loadIconImage);
    map.on("mousemove", layerStyle.id, mouseMove);
    map.on("mouseleave", layerStyle.id, mouseLeave);
    return () => {
      map.off("style.load", loadIconImage);
      map.off("mousemove", layerStyle.id, mouseMove);
      map.off("mouseleave", layerStyle.id, mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (
    !["greatgulf portfolio"].some((item) =>
      currentMapLayerOptions.includes(item)
    )
  )
    return null;

  return (
    <Source id={sourceId} type="geojson" data={geojson}>
      <Layer {...layerStyle} />
    </Source>
  );
};

export default GreatGulfPortfolio;
