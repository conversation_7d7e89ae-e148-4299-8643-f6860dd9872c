import { useState, useEffect, useCallback, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { getFundriseOwnedData } from "../../../../services/data";
import { geojsonTemplate } from "../../../../constants";
import { Tree } from "antd";
import Source from "../Source";
import Layer from "../Layer";
import blueMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-blue.png";
import grayMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-gray.png";
import greenMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-green.png";
import orangeMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-orange.png";
import pinkMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-pink.png";
import purpleMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-purple.png";
import redMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-red.png";
import yellowMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-yellow.png";
import fundriseCW from "../../../../assets/images/mapbox/controls/fundrise-cw.png";
import fundriseProgress from "../../../../assets/images/mapbox/controls/fundrise-progress.png";
import { initPopup } from "../../MapUtility/general";
import { renderToString } from "react-dom/server";

const icons = {
  "Fundrise CW": fundriseCW,
  "Fundrise Progress": fundriseProgress,
  AMH: yellowMarker,
  "DHI Residential": blueMarker,
  "Invitation Homes": greenMarker,
  Miscellaneous: purpleMarker,
  "NexMetro Communities": pinkMarker,
  "Quinn Residences": redMarker,
  "Tricon Residential": grayMarker,
  "Wan Bridge": orangeMarker,
};

const imgIcon = {
  fundriseCW: fundriseCW,
  fundriseProgress: fundriseProgress,
  yellowMarker: yellowMarker,
  blueMarker: blueMarker,
  greenMarker: greenMarker,
  pinkMarker: pinkMarker,
  redMarker: redMarker,
  grayMarker: grayMarker,
  orangeMarker: orangeMarker,
  purpleMarker: purpleMarker,
};

const reducer = (data) => {
  const features = data.features;
  if (features && features.length > 0) {
    const branches = [
      ...features
        .reduce((acc, feature) => {
          if (
            feature.properties.group != "Miscellaneous" &&
            !acc.includes(feature.properties.brand)
          )
            acc.push(feature.properties.brand);
          if (
            feature.properties.group === "Miscellaneous" &&
            !acc.includes(feature.properties.group)
          )
            acc.push(feature.properties.group);

          return acc;
        }, [])
        .sort((a, b) => {
          if (a.includes("Fundrise")) return -1;
          return a.localeCompare(b);
        })
        .map((brand, brandIdx) => {
          return {
            title: brand,
            key: `${brandIdx}`,
            children: [
              ...features
                .filter((feature) => {
                  if (
                    brand === "Miscellaneous" &&
                    feature.properties.group === brand
                  ) {
                    return true;
                  } else if (
                    brand != "Miscellaneous" &&
                    brand === feature.properties.brand
                  ) {
                    return true;
                  }
                  return false;
                })
                .map((feature, featureIdx) => {
                  return {
                    title: feature.properties.community_name,
                    key: `${brandIdx}-${featureIdx}`,
                    // children: [],
                  };
                }),
            ],
          };
        }),
    ];
    return branches;
  }
  return [];
};

const sourceId = "fundrise";

const circleStyle = {
  id: `${sourceId}-None-CircleLayer`,
  type: "circle",
  // minzoom: zoomLevelToNewHomes,
  paint: {
    "circle-radius": 6,
    "circle-stroke-color": "#fff",
    "circle-stroke-width": 3,
    "circle-color": "#50C878",
  },
};

const ownedIconStyle = {
  id: `${sourceId}-owned-icon-marker`,
  type: "symbol",
  layout: {
    "icon-image": [
      "match",
      ["get", "brand"],
      "Fundrise CW",
      "fundriseCW",
      "Fundrise Progress",
      "fundriseProgress",
      "",
    ],
    "icon-size": 0.5,
    "icon-allow-overlap": true,
  },
};

const competitorIconStyle = {
  id: `${sourceId}-competitor-icon-marker`,
  type: "symbol",
  layout: {
    "icon-image": [
      "match",
      ["get", "brand"],
      "AMH",
      "yellowMarker",
      "DHI Residential",
      "blueMarker",
      "Invitation Homes",
      "greenMarker",
      "NexMetro Communities",
      "pinkMarker",
      "Quinn Residences",
      "redMarker",
      "Tricon Residential",
      "grayMarker",
      "Wan Bridge",
      "orangeMarker",
      "",
    ],
    "icon-size": 1,
    "icon-allow-overlap": true,
  },
};

const miscellaneousIconStyle = {
  id: `${sourceId}-miscellaneous-icon-marker`,
  type: "symbol",
  layout: {
    "icon-image": [
      "match",
      ["get", "group"],
      "Miscellaneous",
      "purpleMarker",
      "",
    ],
    "icon-size": 1,
    "icon-allow-overlap": true,
  },
};

// "static",
//       ["match", ["get", "group"], "Miscellaneous", "purpleMarker"],
//       "",
let popup = initPopup({
  closeButton: true,
  closeOnClick: true,
  offset: 15,
});

const formatKey = (key) => {
  key = key.replace(/_/g, " ");
  key = key.replace(/([A-Z])/g, " $1");
  return key.charAt(0).toUpperCase() + key.slice(1);
};

const getTooltipHTML = (property) => {
  const ignoreKeys = ["id", "lat", "long"];

  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {Object.keys(property).map((key) => {
        if (ignoreKeys.includes(key)) return null;
        return (
          <div>
            <span style={{ fontWeight: "bold" }}>{formatKey(key)}: </span>
            {key === "website" ? (
              <a href={property[key]} target="_blank" title={property[key]}>
                {property[key]}
              </a>
            ) : (
              <span>{property[key]}</span>
            )}
          </div>
        );
      })}
    </div>
  );
};

const FundriseProperty = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const unCheckedFundrise = useSelector((state) => state.Map.unCheckedFundrise);
  const fundrisePropertiesLoading = useSelector(
    (state) => state.Map.fundrisePropertiesLoading
  );

  const dispatch = useDispatch();

  const [geojson, setGeojson] = useState(geojsonTemplate);
  const [treeData, setTreeData] = useState([]);

  useEffect(() => {
    if (!map) return;

    const fetchProperties = async () => {
      if (!currentMapLayerOptions.includes("fundrise properties")) {
        if (geojson.features.length > 0) {
          setGeojson(geojsonTemplate);
          dispatch({
            type: "Map/saveState",
            payload: { fundrisePropertyGeoJSON: geojsonTemplate },
          });
        }
        if (fundrisePropertiesLoading) {
          dispatch({
            type: "Map/saveState",
            payload: { fundrisePropertiesLoading: false },
          });
        }
        return;
      }
      dispatch({
        type: "Map/saveState",
        payload: { fundrisePropertiesLoading: true },
      });
      const mapScopeBBox = map.getBounds().toArray();
      let type = "";
      if (currentMapLayerOptions.includes("fundrise properties")) {
        type = "all";
      } else {
        // TODO: remove
        if (currentMapLayerOptions.includes("fundrise owned")) {
          type = "owned";
        } else if (currentMapLayerOptions.includes("fundrise competitor")) {
          type = "competitor";
        }
      }
      const data = await getFundriseOwnedData({
        lng1: mapScopeBBox[0][0],
        lat1: mapScopeBBox[0][1],
        lng2: mapScopeBBox[1][0],
        lat2: mapScopeBBox[1][1],
        type: type,
        returnGeoJSON: "true",
        returnRawData: "false",
      });

      setGeojson(data.geojson);
      dispatch({
        type: "Map/saveState",
        payload: {
          fundrisePropertyGeoJSON: data.geojson,
          fundrisePropertiesLoading: false,
        },
      });
      setTreeData(reducer(data.geojson));
    };

    const moveEnd = () => {
      fetchProperties();
    };

    const mouseClick = (e, layer) => {
      const feature = map.queryRenderedFeatures(e.point, {
        layers: [layer],
      });
      console.log("feature: ", feature);
      if (feature && feature.length > 0) {
        if (feature[0].properties.cluster) return;
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature[0].properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      } else {
        popup.remove();
      }
    };

    const click = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [
          ownedIconStyle.id,
          competitorIconStyle.id,
          miscellaneousIconStyle.id,
        ],
      });
      const feature = features.filter((f) => f.source.includes("fundrise"));
      if (feature && feature.length > 0) {
        if (feature[0].properties.cluster) return;
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature[0].properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      } else {
        popup.remove();
      }
    };

    const mouseMove = (e, layer) => {
      const feature = map.queryRenderedFeatures(e.point, {
        layers: [layer],
      });
      if (feature && feature.length > 0) {
        map.getCanvas().style.cursor = "pointer";
      }
    };

    const mouseLeave = () => {
      // popup.remove();
      map.getCanvas().style.cursor = "";
    };

    const loadIconImage = () => {
      Object.keys(imgIcon).forEach((key) => {
        map.loadImage(imgIcon[key], (error, image) => {
          // console.log("key", key);
          // console.log("img", image);
          if (error) throw error;
          if (!map.hasImage(key)) {
            map.addImage(key, image);
          }
        });
      });
    };
    map.on("error", (e) => {
      console.log(e);
    });

    fetchProperties();
    map.on("style.load", loadIconImage);
    map.on("moveend", moveEnd);
    map.on("mousemove", ownedIconStyle.id, (e) =>
      mouseMove(e, ownedIconStyle.id)
    );
    // map.on("click", ownedIconStyle.id, (e) => mouseClick(e, ownedIconStyle.id));
    // map.on("click", ownedIconStyle.id, click);
    map.on("click", click);
    map.on("mouseleave", ownedIconStyle.id, mouseLeave);
    map.on("mousemove", competitorIconStyle.id, (e) =>
      mouseMove(e, competitorIconStyle.id)
    );
    map.on("mouseleave", competitorIconStyle.id, mouseLeave);
    map.on("mousemove", miscellaneousIconStyle.id, (e) =>
      mouseMove(e, miscellaneousIconStyle.id)
    );
    map.on("mouseleave", miscellaneousIconStyle.id, mouseLeave);
    return () => {
      map.off("moveend", moveEnd);
    };
  }, [map, currentMapLayerOptions]);

  // useEffect(() => {
  //   if (!map) return;

  //   if (unCheckedFundrise.length > 0) {
  //   }
  // }, [unCheckedFundrise]);

  const filterFn = useCallback(
    (geojson) => {
      const fn = (feature) => {
        if (
          feature.properties.group === "Miscellaneous" &&
          unCheckedFundrise.includes(feature.properties.group)
        ) {
          return false;
        } else if (unCheckedFundrise.includes(feature.properties.brand)) {
          return false;
        }
        return true;
      };

      return {
        type: "FeatureCollection",
        features: geojson.features.filter(fn),
      };
    },
    [unCheckedFundrise]
  );

  return (
    <Source id={sourceId} type="geojson" data={filterFn(geojson)}>
      <Layer {...ownedIconStyle} />
      <Layer {...competitorIconStyle} />
      <Layer {...miscellaneousIconStyle} />
    </Source>
  );
};

export default FundriseProperty;
