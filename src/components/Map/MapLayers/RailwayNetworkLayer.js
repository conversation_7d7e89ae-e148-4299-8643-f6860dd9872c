import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { initPopup } from "../MapUtility/general";
import { renderToString } from "react-dom/server";
import { tileURLRoot } from "../../../services/data";

const sourceId = "railway_network";
const sourceLayer = "rail_network";
let hoveredStateId = null;

const lineStyle = {
  id: `${sourceId}LayerLine`,
  type: "line",
  // minzoom: minZoom,
  "source-layer": sourceLayer,
  paint: {
    "line-color": "#196fba",
    "line-opacity": [
      "case",
      ["boolean", ["feature-state", "hover"], false],
      1,
      0.7,
    ],
    "line-width": [
      "case",
      ["boolean", ["feature-state", "hover"], false],
      8,
      3,
    ],
  },
};

const popup = initPopup();

const getTooltipHTML = (properties) => {
  console.log("properties", properties);

  let keys = Object.keys(properties);

  // ignore keys
  keys = keys.filter((key) => !["SHAPE__Length", "OBJECTID"].includes(key));

  const formatter = (value) => {
    if (typeof value === "number" && value % 1 !== 0) {
      return value.toFixed(2);
    }
    return value;
  };

  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
        gap: "5px",
      }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          lineHeight: "1.3",
        }}
      >
        {keys.map((key) => (
          <span>
            {key}: <strong>{formatter(properties[key])}</strong>
          </span>
        ))}
      </div>
    </div>
  );
};

const getTileURL = (serverType, token) =>
  `${tileURLRoot(
    "cma",
    serverType
  )}/rail-network/tile/{z}/{x}/{y}.mvt?token=${token}`;

function RailwayNetworkLayer() {
  const serverType = useSelector((state) => state.Configure.serverType);
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );

  const [token, setToken] = useState(null);

  useEffect(() => {
    if (!map) return;

    const mouseMove = (e) => {
      if (e.features.length > 0) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(e.features[0].properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);

        if (hoveredStateId !== null) {
          map.setFeatureState(
            {
              source: sourceId,
              sourceLayer: sourceLayer,
              id: hoveredStateId,
            },
            { hover: false }
          );
        }

        hoveredStateId = e.features[0].id;
        map.setFeatureState(
          {
            source: sourceId,
            sourceLayer: sourceLayer,
            id: hoveredStateId,
          },
          { hover: true }
        );
      } else {
        popup.remove();
        if (hoveredStateId !== null) {
          map.setFeatureState(
            {
              source: sourceId,
              sourceLayer: sourceLayer,
              id: hoveredStateId,
            },
            { hover: false }
          );
        }
        hoveredStateId = null;
      }
    };

    const mouseLeave = () => {
      popup.remove();
      if (hoveredStateId !== null) {
        map.setFeatureState(
          {
            source: sourceId,
            sourceLayer: sourceLayer,
            id: hoveredStateId,
          },
          { hover: false }
        );
      }
      hoveredStateId = null;
    };

    const checkForLatestToken = async () => {
      const newToken = await window.spatiallaser.getUserToken("access");
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    checkForLatestToken();
    map.on("movestart", checkForLatestToken);
    map.on("mousemove", lineStyle.id, mouseMove);
    map.on("mouseleave", lineStyle.id, mouseLeave);

    map.on("error", (e) => {
      console.log(e);
    });
    return () => {
      map.off("movestart", checkForLatestToken);
      map.off("mousemove", lineStyle.id, mouseMove);
      map.off("mouseleave", lineStyle.id, mouseLeave);
    };
  }, [map, currentMapLayerOptions, token]);

  if (!token || !currentMapLayerOptions.includes("rail network")) return null;
  return (
    <Source id={sourceId} type="vector" tiles={[getTileURL(serverType, token)]}>
      <Layer {...lineStyle} />
    </Source>
  );
}

export default RailwayNetworkLayer;
