import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useDispatch, useSelector } from "react-redux";
import { getMobileHomeParkData } from "../../../services/data";
import { convertToGeoJSON } from "../../../utils/geography";
import { geojsonTemplate } from "../../../constants";
import Source from "./Source";
import Layer from "./Layer";
import { initPopup } from "../MapUtility/general";

const sourceId = "mhp-source";

const toTitleCase = (str) => {
  if (!str) return "";
  return str
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
};

// Update point style to use different sizes
const pointStyle = {
  id: "mhp-point",
  type: "circle",
  source: sourceId,
  filter: ["!", ["has", "point_count"]],
  paint: {
    "circle-radius": [
      "match",
      ["get", "size"],
      "SMALL (<50)",
      6,
      "MEDIUM (51-100)",
      8,
      "LARGE (>100)",
      10,
      8
    ],
    "circle-color": "#E87627",
    "circle-stroke-color": "#ffffff",
    "circle-stroke-width": 2,
  },
};

const clusterLayer = {
  id: "mhp-clusters",
  type: "circle",
  source: sourceId,
  filter: ["has", "point_count"],
  paint: {
    "circle-color": "#E87627",
    "circle-radius": ["step", ["get", "point_count"], 12, 10, 16, 30, 20],
    "circle-stroke-width": 2,
    "circle-stroke-color": "#ffffff",
  },
};

const clusterCountLayer = {
  id: "mhp-cluster-count",
  type: "symbol",
  source: sourceId,
  filter: ["has", "point_count"],
  layout: {
    "text-field": "{point_count_abbreviated}",
    "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
    "text-size": 14,
  },
  paint: {
    "text-color": "#ffffff",
  },
};

const getTooltipHTML = (properties) => {
  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
        gap: "4px",
      }}
    >
      {properties.name !== "NOT AVAILABLE" && (
        <span>
          <strong>Name:</strong> {toTitleCase(properties.name)}
        </span>
      )}
      <span>
        <strong>Description:</strong> {toTitleCase(properties.naicsDesc)}
      </span>
      <span>
        <strong>Type:</strong> {toTitleCase(properties.type)}
      </span>
      <span>
        <strong>Address:</strong> {toTitleCase(properties.address)}, {toTitleCase(properties.city)},{" "}
        {properties.state?.toUpperCase()}, {properties.zip}
      </span>
      <span>
        <strong>Size:</strong> {toTitleCase(properties.size)}
      </span>
    </div>
  );
};

let popup = initPopup();

const MobileHomeParkLayer = () => {
  const dispatch = useDispatch();
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const selectedMobileHomeParkType = useSelector((state) => state.Map.selectedMobileHomeParkType);
  const [geojson, setGeojson] = useState(geojsonTemplate);

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("mobile home park")) return;
    console.log("selectedMobileHomeParkType", selectedMobileHomeParkType);
    const moveEnd = async (e) => {
      dispatch({
        type: "Map/saveState",
        payload: {
          selectedMobileHomeParkLoading: true,
        },
      });
      const data = await getMobileHomeParkData();
      let filteredData;
      if (selectedMobileHomeParkType) {
        switch (selectedMobileHomeParkType) {
          case "all":
            filteredData = data;
            break;
          case "MOBILE HOME PARK":
            filteredData = data.filter((park) => {
              return park.type === "MOBILE HOME PARK";
            });
            break;
          case "RECREATIONAL VEHICLE PARK":
            filteredData = data.filter((park) => {
              return park.type === "RECREATIONAL VEHICLE PARK";
            });
            break;
          case "MHP/RV/MIGRANT HOUSING":
            filteredData = data.filter((park) => {
              return park.type === "MHP/RV/MIGRANT HOUSING";
            });
            break;
          default:
            break;
        }
      }

      // In the moveEnd function, modify the setGeojson call:
      setGeojson(
        convertToGeoJSON({
          data: filteredData, // Changed from just filteredData to {data: filteredData}
          geomAccessor: (item) => item.geom,
          propertiesAccessor: (item) => {
            const { geom, ...properties } = item;
            return properties;
          },
        })
      );
      dispatch({
        type: "Map/saveState",
        payload: {
          selectedMobileHomeParkLoading: false,
        },
      });
    };

    const mouseMove = (e) => {
      const feature = e.features[0];
      if (feature && feature.properties) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    const handleClusterClick = (e) => {
      const features = map.queryRenderedFeatures(e.point, { layers: ["mhp-clusters"] });
      const clusterId = features[0].properties.cluster_id;
      map.getSource(sourceId).getClusterExpansionZoom(clusterId, (err, zoom) => {
        if (err) return;
        map.easeTo({
          center: features[0].geometry.coordinates,
          zoom: zoom,
        });
      });
    };

    const moveBehindOwnedLayer = () => {
      const layers = map.getStyle().layers;
      let foundOwnedLayer = false;
      for (let i = layers.length - 1; i >= 0; i--) {
        if (layers[i].source && layers[i].source.includes("OwnedProperty")) {
          foundOwnedLayer = true;
        } else if (
          layers[i].source &&
          !layers[i].source.includes("OwnedProperty") &&
          foundOwnedLayer
        ) {
          map.moveLayer(pointStyle.id, layers[i].id);
          map.moveLayer(clusterLayer.id, layers[i].id);
          map.moveLayer(clusterCountLayer.id, layers[i].id);
          break;
        }
      }
    };

    moveEnd();
    moveBehindOwnedLayer();

    map.on("moveend", moveEnd);
    map.on("mousemove", "mhp-point", mouseMove);
    map.on("mouseleave", "mhp-point", mouseLeave);
    map.on("click", "mhp-clusters", handleClusterClick);

    return () => {
      map.off("moveend", moveEnd);
      map.off("mousemove", "mhp-point", mouseMove);
      map.off("mouseleave", "mhp-point", mouseLeave);
      map.off("click", "mhp-clusters", handleClusterClick);
    };
  }, [map, currentMapLayerOptions, selectedMobileHomeParkType]);

  if (!currentMapLayerOptions.includes("mobile home park")) return null;
  return (
    <>
      <Source
        id={sourceId}
        type="geojson"
        data={geojson}
        cluster={true}
        clusterMaxZoom={14}
        clusterRadius={50}
      >
        <Layer {...clusterLayer} />
        <Layer {...clusterCountLayer} />
        <Layer {...pointStyle} />
      </Source>
    </>
  );
};

export default MobileHomeParkLayer;
