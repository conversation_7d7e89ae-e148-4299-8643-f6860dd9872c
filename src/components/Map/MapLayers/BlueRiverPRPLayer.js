import { useEffect, useState, useRef } from "react";
import { renderToString } from "react-dom/server";
import { useSelector } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { getBlueRiverPRPData } from "../../../services/data";
import { geojsonTemplate } from "../../../constants";
import yellowMarker from "../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-yellow-bordered.png";
import { initPopup } from "../MapUtility/general";

const sourceId = "blueriver-prp";

const circleStyle = {
  id: `${sourceId}CircleStyle`,
  type: "circle",
  type: "symbol",
  layout: {
    "icon-image": "yellowMarker",
    "icon-size": 1,
    "icon-allow-overlap": true,
  },
};

const popup = initPopup();

const popupHTML = (properties) => {
  const content = Object.keys(properties)
    .filter(
      (key) =>
        ![
          "latitude",
          "longitude",
          "project_notes",
          "map_link",
          "unique_id",
        ].includes(key)
    )
    .sort()
    .reduce((acc, key) => {
      if (properties[key]) {
        const label = key
          .replace(/_/g, " ")
          .replace(/\b\w/g, (l) => l.toUpperCase());
        if (key.includes("doc")) {
          acc.push(
            <span>
              {label}:{" "}
              <a href={properties[key]} target="_blank" rel="noreferrer">
                Go to link
              </a>
            </span>
          );
        } else {
          acc.push(
            <span>
              {label}: {properties[key]}
            </span>
          );
        }
      }
      return acc;
    }, []);

  return renderToString(
    <div style={{ padding: "6px 10px" }}>
      <h3 style={{ textAlign: "center" }}>Public Record Prospect</h3>
      <div style={{ display: "flex", flexDirection: "column", gap: "1px" }}>
        {content}
      </div>
    </div>
  );
};

const BlueRiverPRPLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const [geojon, setGeojson] = useState(geojsonTemplate);

  const mouseOnPopup = useRef(false);

  useEffect(() => {
    if (!map) return;

    const moveEnd = async () => {
      if (!currentMapLayerOptions.includes("public record prospect")) return;
      const mapScopeBBox = map.getBounds().toArray();
      const data = await getBlueRiverPRPData({
        lng1: mapScopeBBox[0][1],
        lat1: mapScopeBBox[0][0],
        lng2: mapScopeBBox[1][1],
        lat2: mapScopeBBox[1][0],
      });
      if (data && data) {
        setGeojson(data);
      } else {
        setGeojson(geojsonTemplate);
      }
    };

    const loadIconImage = () => {
      map.loadImage(yellowMarker, (error, image) => {
        if (error) throw error;
        if (!map.hasImage("yellowMarker")) {
          map.addImage("yellowMarker", image);
        }
      });
    };

    const mouseMove = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [circleStyle.id],
      });
      if (features.length > 0) {
        const feature = features[0];
        popup
          .setLngLat(feature.geometry.coordinates)
          .setHTML(popupHTML(feature.properties))
          .addTo(map);

        popup.getElement().addEventListener("mouseleave", () => {
          mouseOnPopup.current = false;
          map.getCanvas().style.cursor = "pointer";
          popup.remove();
        });

        popup.getElement().addEventListener("mouseenter", () => {
          mouseOnPopup.current = true;
        });
      }
    };

    const mouseLeave = () => {
      setTimeout(() => {
        if (mouseOnPopup.current) return;
        map.getCanvas().style.cursor = "";
        popup.remove();
      }, 250);
    };

    moveEnd();
    map.on("moveend", moveEnd);
    map.on("style.load", loadIconImage);
    map.on("mousemove", circleStyle.id, mouseMove);
    map.on("mouseleave", circleStyle.id, mouseLeave);
    return () => {
      map.off("moveend", moveEnd);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("public record prospect")) return null;
  return (
    <Source id={sourceId} type="geojson" data={geojon}>
      <Layer {...circleStyle} />
    </Source>
  );
};

export default BlueRiverPRPLayer;
