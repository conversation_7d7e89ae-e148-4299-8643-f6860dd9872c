import React from "react";
import Source from "./Source";
import Layer from "./Layer";
import { useSelector } from "react-redux";
import { tileURLRoot } from "../../../services/data";
import { useSLMapAccessToken } from "../../hooks/useSLMapAccessToken";

const sourceId = "historical-zoning";
const sourceLayer = sourceId;

const zoningTypeLabelStyle = {
  id: `${sourceId}-zoning-type-label`,
  type: "symbol",
  source: sourceId,
  "source-layer": sourceLayer,
  minzoom: 12,
  maxzoom: 14,
  layout: {
    "text-field": ["get", "zoning_type"],
    "text-size": 18,
    "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"],
    "symbol-spacing": 500,
  },
  paint: {
    "text-color": "purple",
    "text-halo-color": "white",
    "text-halo-width": 4,
  },
};

const zoningSubtypeLabelStyle = {
  id: `${sourceId}-zoning-subtype-label`,
  type: "symbol",
  minzoom: 14,
  // maxzoom: 20,
  source: sourceId,
  "source-layer": sourceLayer,
  layout: {
    "text-field": "{zoning}",
    "text-size": 18,
    "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"],
  },
  paint: {
    "text-color": "purple",
    "text-halo-color": "white",
    "text-halo-width": 4,
  },
};

const zoningTypeFillStyle = {
  id: `${sourceId}-zoning-type-fill`,
  type: "fill",
  source: sourceId,
  "source-layer": sourceLayer,
  minzoom: 12,
  maxzoom: 14,
  paint: {
    "fill-color": [
      "case",
      ["==", ["get", "zoning_type"], "Residential"],
      "#ebfd1f",
      ["==", ["get", "zoning_type"], "Commercial"],
      "#ac3c34",
      ["==", ["get", "zoning_type"], "Special"],
      "#e087b4",
      ["==", ["get", "zoning_type"], "Mixed"],
      "#8d6698",
      ["==", ["get", "zoning_type"], "Planned"],
      "#d8925f",
      ["==", ["get", "zoning_type"], "Agriculture"],
      "#67a25b",
      ["==", ["get", "zoning_type"], "Industrial"],
      "#4a6692",
      ["==", ["get", "zoning_type"], "Overlay"],
      "#8d5436",
      ["==", ["get", "zoning_type"], "Others"],
      "#c6c6c6",
      "transparent", // Default case if none of the above conditions are met
    ],
  },
};

const zoningSubtypeFillStyle = {
  id: `${sourceId}-zoning-subtype`,
  type: "fill",
  source: sourceId,
  "source-layer": sourceLayer,
  minzoom: 14,
  // maxzoom: 14,
  paint: {
    "fill-color": [
      "case",
      ["==", ["get", "zoning_subtype"], "Single Family"],
      "#a6cee3",
      ["==", ["get", "zoning_subtype"], "Two Family"],
      "#1f78b4",
      ["==", ["get", "zoning_subtype"], "Multi Family"],
      "#b2df8a",
      ["==", ["get", "zoning_subtype"], "Mobile Home Park"],
      "#33a02c",
      ["==", ["get", "zoning_subtype"], "General Commercial"],
      "#fb9a99",
      ["==", ["get", "zoning_subtype"], "Core Commercial"],
      "#e31a1c",
      ["==", ["get", "zoning_subtype"], "Retail Commercial"],
      "#fdbf6f",
      ["==", ["get", "zoning_subtype"], "Neighborhood Commercial"],
      "#ff7f00",
      ["==", ["get", "zoning_subtype"], "Office"],
      "#cab2d6",
      ["==", ["get", "zoning_subtype"], "Special Commercial"],
      "#6a3d9a",
      ["==", ["get", "zoning_subtype"], "Mixed Use"],
      "#ffff99",
      ["==", ["get", "zoning_subtype"], "Industrial"],
      "#b15928",
      ["==", ["get", "zoning_subtype"], "Light Industrial"],
      "#dfc27d",
      ["==", ["get", "zoning_subtype"], "Special"],
      "#66bd63",
      ["==", ["get", "zoning_subtype"], "Planned"],
      "#80cdc1",
      ["==", ["get", "zoning_subtype"], "Overlay"],
      "#878787",
      "transparent", // Default case if none of the above conditions are met
    ],
    // "fill-opacity": 0.6,
  },
};

const getTileURL = (serverType, token) =>
  `${tileURLRoot(
    "sbs",
    serverType
  )}/api/v1/historical-zoning/tile/${sourceLayer}/{z}/{x}/{y}.mvt?periods=2024-11&access_token=${token}`;

export const ZoningChange = () => {
  const map = useSelector((state) => state.Map.map);
  const serverType = useSelector((state) => state.Configure.serverType);
  const token = useSLMapAccessToken({ map });

  if (!token) return null;
  return (
    <Source id={sourceId} type="vector" tiles={[getTileURL(serverType, token)]}>
      <Layer {...zoningTypeFillStyle} />
      <Layer {...zoningSubtypeFillStyle} />
      <Layer {...zoningTypeLabelStyle} />
      <Layer {...zoningSubtypeLabelStyle} />
    </Source>
  );
};
