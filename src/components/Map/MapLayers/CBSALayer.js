import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import {
  MAP_LAYER_NAME_BASE,
  zoomLevelToChangeBasemap,
} from "../../../constants";
import { setCBSAColor } from "../MapUtility/colors";
import { hideVisibility, showVisibility } from "../MapUtility/layer";
import { setPaintPropertySafely } from "../MapUtility/general";
import { default as turf_bbox } from "@turf/bbox";

const sourceId = MAP_LAYER_NAME_BASE.cbsa;

const zoomLevelToCBSA = 8;
const cbsaDefaultOpacity = 0.4;
const cbsaSatelliteOpacity = 0.6;

const fillStyle = {
  id: `${sourceId}LayerFill`,
  type: "fill",
  minzoom: zoomLevelToCBSA,
  paint: {
    "fill-opacity": cbsaDefaultOpacity,
  },
  filter: ["in", "$type", "Polygon"],
};

const outlineStyle = {
  id: `${sourceId}LayerOutline`,
  type: "line",
  minzoom: zoomLevelToCBSA,
  layout: {
    visibility: "visible",
  },
  paint: {
    "line-color": "#000",
    "line-width": 2,
    "line-opacity": 0.2,
  },
  filter: ["in", "$type", "Polygon"],
};

// Default label
const defaultLabelStyle = {
  id: `${sourceId}LayerLabel`,
  type: "symbol",
  minzoom: 11,
  layout: {
    "text-field": ["get", "name"],
    "text-variable-anchor": ["center"],
    "text-justify": "auto",
    "text-size": 16,
    // "text-size": [
    //   "interpolate",
    //   ["linear"],
    //   ["zoom"],
    //   zoomLevelToCBSA,
    //   12,
    //   9,
    //   16,
    // ],
    "text-font": ["Source Sans Pro Bold", "Open Sans Bold"],
    visibility: "visible",
  },
  paint: {
    "text-color": "#000",
    "text-opacity": 0.75,
  },
};

// Center label
const centerLabelStyle = {
  id: `${sourceId}LayerCenterLabel`,
  type: "symbol",
  minzoom: zoomLevelToCBSA,
  maxzoom: 11,
  layout: {
    "text-field": ["get", "name"],
    "text-variable-anchor": ["center"],
    "text-justify": "auto",
    "text-size": 24,
    // "text-size": [
    //   "interpolate",
    //   ["linear"],
    //   ["zoom"],
    //   zoomLevelToCBSA,
    //   12,
    //   9,
    //   25,
    // ],
    "text-font": ["Source Sans Pro Bold", "Open Sans Bold"],
    visibility: "visible",
  },
  paint: {
    "text-color": "#000",
    "text-opacity": 0.75,
  },
  filter: ["in", "$type", "Point"],
};

function CBSALayer() {
  const [currentCBSAFillColor, setCurrentCBSAFillColor] = useState([]);

  const map = useSelector((state) => state.Map.map);
  const currentMapThemeOption = useSelector(
    (state) => state.Map.currentMapThemeOption
  );
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const currentCBSAGeoJSON = useSelector(
    (state) => state.Map.currentCBSAGeoJSON
  );
  const dispatch = useDispatch();

  const currentMapLayerOptionsRef = useRef(currentMapLayerOptions);
  currentMapLayerOptionsRef.current = currentMapLayerOptions;

  useEffect(() => {
    if (!map || !map.style || !map.style._loaded) return;
    if (
      !map.getLayer(`${sourceId}LayerFill`) ||
      !map.getLayer(`${sourceId}LayerOutline`) ||
      !map.getLayer(`${sourceId}LayerLabel`) ||
      !map.getLayer(`${sourceId}LayerCenterLabel`)
    )
      return;

    if (currentMapLayerOptions.includes("cbsa")) {
      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToCBSA) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getCBSA",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });
      }
      showVisibility(map, `${sourceId}LayerFill`);
      showVisibility(map, `${sourceId}LayerOutline`);
      showVisibility(map, `${sourceId}LayerLabel`);
      showVisibility(map, `${sourceId}LayerCenterLabel`);
    } else {
      if (
        map.getLayoutProperty(`${sourceId}LayerFill`, "visibility") ===
          "visible" ||
        map.getLayoutProperty(`${sourceId}LayerOutline`, "visibility") ===
          "visible" ||
        map.getLayoutProperty(`${sourceId}LayerLabel`, "visibility") ===
          "visible" ||
        map.getLayoutProperty(`${sourceId}LayerCenterLabel`, "visibility") ===
          "visible"
      ) {
        hideVisibility(map, `${sourceId}LayerFill`);
        hideVisibility(map, `${sourceId}LayerOutline`);
        hideVisibility(map, `${sourceId}LayerLabel`);
        hideVisibility(map, `${sourceId}LayerCenterLabel`);
      }
    }
  }, [currentMapLayerOptions]);

  useEffect(() => {
    if (!map) return;

    const moveEnd = () => {
      if (!currentMapLayerOptionsRef.current.includes("cbsa")) return;

      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToCBSA) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getCBSA",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });

        if (currentMapThemeOption === "Automatic") {
          if (map.getZoom() >= zoomLevelToChangeBasemap) {
            setPaintPropertySafely(
              map,
              `${sourceId}LayerFill`,
              "fill-opacity",
              cbsaSatelliteOpacity
            );
          } else {
            setPaintPropertySafely(
              map,
              `${sourceId}LayerFill`,
              "fill-opacity",
              cbsaDefaultOpacity
            );
          }
        }
      }
    };

    map.on("moveend", moveEnd);

    return () => {
      map.off("moveend", moveEnd);
    };
  }, [map]);

  useEffect(() => {
    if (!map) return;

    const fillColor = setCBSAColor(
      map,
      currentCBSAGeoJSON,
      currentCBSAFillColor
    );
    setCurrentCBSAFillColor(fillColor);
  }, [currentCBSAGeoJSON]);

  useEffect(() => {
    if (!map || !map.style || !map.style._loaded) return;
    if (!currentMapLayerOptions.includes("cbsa")) return;
    if (!map.getLayer(`${sourceId}LayerFill`)) return;

    if (currentMapThemeOption === "Satellite") {
      setPaintPropertySafely(
        map,
        `${sourceId}LayerFill`,
        "fill-opacity",
        cbsaSatelliteOpacity
      );
    } else if (currentMapThemeOption === "Automatic") {
      if (map.getZoom() >= zoomLevelToChangeBasemap) {
        setPaintPropertySafely(
          map,
          `${sourceId}LayerFill`,
          "fill-opacity",
          cbsaSatelliteOpacity
        );
      } else {
        setPaintPropertySafely(
          map,
          `${sourceId}LayerFill`,
          "fill-opacity",
          cbsaDefaultOpacity
        );
      }
    } else {
      setPaintPropertySafely(
        map,
        `${sourceId}LayerFill`,
        "fill-opacity",
        cbsaDefaultOpacity
      );
    }
  }, [currentMapThemeOption]);

  // if (currentCBSAGeoJSON && currentCBSAGeoJSON.features.length > 0) {
  //   for (let i = 0; i < currentCBSAGeoJSON.features.length; i++) {
  //     const cbsaFeature = currentCBSAGeoJSON.features[i];
  //     if (
  //       (cbsaFeature.properties.name.includes("Cleveland") ||
  //       cbsaFeature.properties.name.includes("Tulsa") ||
  //       cbsaFeature.properties.name.includes("Columbus") ||
  //       cbsaFeature.properties.name.includes("Kansas City") ||
  //       cbsaFeature.properties.name.includes("Akron") ||
  //       cbsaFeature.properties.name.includes("Dayton") ||
  //       cbsaFeature.properties.name.includes("Pittsburgh")) &&
  //       cbsaFeature.geometry.type === 'MultiPolygon'
  //     ) {
  //       console.log(cbsaFeature.properties.name, JSON.stringify(turf_bbox(cbsaFeature)), cbsaFeature);
  //     }
  //   }
  // }

  return (
    <>
      <Source id={sourceId} type="geojson" data={currentCBSAGeoJSON}>
        <Layer {...fillStyle} />
        <Layer {...outlineStyle} />
        <Layer {...defaultLabelStyle} />
        <Layer {...centerLabelStyle} />
      </Source>
    </>
  );
}

export default CBSALayer;
