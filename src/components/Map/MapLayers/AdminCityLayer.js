import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { tileURLRoot } from "../../../services/data";

const sourceId = "admin-city-source";
const sourceLayer = "admin_place";
const sourceLayerCenter = "admin_placeCenterPoint";

// Add zIndex to ensure layers are on top
const HIGH_Z_INDEX = 999;

const fillStyle = {
  id: `${HIGH_Z_INDEX}-admin-city-fill`,
  type: "fill",
  "source-layer": sourceLayer,
  minzoom: 8,
  paint: {
    "fill-color": "#000",
    "fill-opacity": 0.3,
  },
};

const outlineStyle = {
  id: `${HIGH_Z_INDEX}-${sourceId}LayerOutline`,
  type: "line",
  "source-layer": sourceLayer,
  layout: {
    visibility: "visible",
  },
  minzoom: 8,
  paint: {
    "line-color": "#000",
    "line-width": ["interpolate", ["linear"], ["zoom"], 0, 0.5, 22, 8],
    "line-opacity": 0.75,
  },
  filter: ["in", "$type", "Polygon"],
};

const labelStyle = {
  id: `${HIGH_Z_INDEX}-${sourceId}LayerLabel`,
  type: "symbol",
  "source-layer": sourceLayerCenter,
  minzoom: 8,
  layout: {
    "text-field": ["get", "name"],
    "text-variable-anchor": ["center"],
    "text-justify": "auto",
    "text-size": ["interpolate", ["linear"], ["zoom"], 0, 12, 17, 30],
    "text-font": ["Source Sans Pro Bold", "Open Sans Bold"],
    visibility: "visible",
  },
  paint: {
    "text-color": "#fff",
    "text-opacity": 1,
    "text-halo-color": "black",
    "text-halo-width": 1,
    "text-halo-blur": 1,
  },
};

const getTileURL = (serverType, token) =>
  `${tileURLRoot("cma", serverType)}/city/tile/{z}/{x}/{y}.mvt?token=${token}`;
const getTileCenterURL = (serverType, token) =>
  `${tileURLRoot(
    "cma",
    serverType
  )}/city/tile/center/{z}/{x}/{y}.mvt?token=${token}`;

const AdminCityLayer = () => {
  const serverType = useSelector((state) => state.Configure.serverType);
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );

  const [token, setToken] = useState(null);

  useEffect(() => {
    if (!map) return;

    const checkForLatestToken = async () => {
      const newToken = await window.spatiallaser.getUserToken("access");
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    checkForLatestToken();
    map.on("movestart", checkForLatestToken);
    
    // Force layers to top when map style loads or changes
    const moveLayersToTop = () => {
      if (!map) return;
      
      // Check if our layers exist and move them to the top
      const layerIds = [
        `${HIGH_Z_INDEX}-admin-city-fill`, 
        `${HIGH_Z_INDEX}-${sourceId}LayerOutline`, 
        `${HIGH_Z_INDEX}-${sourceId}LayerLabel`
      ];
      
      layerIds.forEach(id => {
        if (map.getLayer(id)) {
          map.moveLayer(id);
        }
      });
    };
    
    map.on('style.load', moveLayersToTop);
    map.on('sourcedata', moveLayersToTop);
    
    return () => {
      map.off("movestart", checkForLatestToken);
      map.off('style.load', moveLayersToTop);
      map.off('sourcedata', moveLayersToTop);
    };
  }, [map, token, sourceId]);

  if (!token || !currentMapLayerOptions.includes("city")) return null;
  
  return (
    <>
      <Source
        id={sourceId}
        type="vector"
        tiles={[getTileURL(serverType, token)]}
      >
        <Layer {...fillStyle} />
        <Layer {...outlineStyle} />
      </Source>
      <Source
        id={`${sourceId}-center`}
        type="vector"
        tiles={[getTileCenterURL(serverType, token)]}
      >
        <Layer {...labelStyle} />
      </Source>
    </>
  );
};

export default AdminCityLayer;