import { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Checkbox } from "antd";
import Source from "./Source";
import Layer from "./Layer";
import { initPopup } from "../MapUtility/general";
import { renderToString } from "react-dom/server";
import { tileURLRoot } from "../../../services/data";

const sourceId = "powerlines";
const sourceLayer = "powerlines";
let hoveredStateId = null;

const lineStyle = {
  id: `${sourceId}LayerLine`,
  type: "line",
  // minzoom: minZoom,
  "source-layer": sourceLayer,
  paint: {
    // "line-color": "#196fba",
    "line-color": [
      "match",
      ["get", "VOLT_CLASS"],
      "UNDER 100",
      "#2593c7",
      "100-161",
      "#e64b49",
      "220-287",
      "#924c92",
      "345",
      "#f8891d",
      "500",
      "#fdda33",
      "735 AND ABOVE",
      "#f27fd4",
      "NOT AVAILABLE",
      "#9ebe2b",
      "#9ebe2b",
    ],
    "line-opacity": [
      "case",
      ["boolean", ["feature-state", "hover"], false],
      1,
      0.7,
    ],
    "line-width": [
      "case",
      ["boolean", ["feature-state", "hover"], false],
      8,
      3,
    ],
  },
};

const popup = initPopup();

const getTooltipHTML = (properties) => {
  const {
    OWNER,
    NAICS_DESC,
    NAICS_CODE,
    STATUS,
    SUB_1,
    SUB_2,
    TYPE,
    VOLTAGE,
    VOLT_CLASS,
  } = properties;

  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
        gap: "5px",
      }}
    >
      <div
        style={{
          textAlign: "center",
          display: "flex",
          flexDirection: "column",
          lineHeight: "1.25",
        }}
      >
        <strong>Voltage: {VOLTAGE} (Kilovolts)</strong>
        <span>(Type: {TYPE})</span>
      </div>
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          lineHeight: "1.3",
        }}
      >
        {STATUS && STATUS.length > 0 && (
          <span>
            Status: <strong>{STATUS}</strong>
          </span>
        )}
        {OWNER && OWNER.length > 0 && (
          <span>
            Owner: <strong>{OWNER}</strong>
          </span>
        )}
        {NAICS_DESC && NAICS_DESC.length > 0 && (
          <span>
            NAICS Description: <strong>{NAICS_DESC}</strong>
          </span>
        )}
        {NAICS_CODE && NAICS_CODE.length > 0 && (
          <span>
            NAICS Code: <strong>{NAICS_CODE}</strong>
          </span>
        )}
        {VOLT_CLASS && VOLT_CLASS.length > 0 && (
          <span>
            Voltage Classification: <strong>{VOLT_CLASS} (Kilovolts)</strong>
          </span>
        )}
        {SUB_1 && SUB_1.length > 0 && (
          <span>
            Substation 1: <strong>{SUB_1}</strong>
          </span>
        )}
        {SUB_2 && SUB_2.length > 0 && (
          <span>
            Substation 2: <strong>{SUB_2}</strong>
          </span>
        )}
      </div>
    </div>
  );
};

const getTileURL = (serverType, token) =>
  `${tileURLRoot(
    "cma",
    serverType
  )}/powerline/tile/{z}/{x}/{y}.mvt?token=${token}`;

function PowerLinesLayer() {
  const serverType = useSelector((state) => state.Configure.serverType);
  const map = useSelector((state) => state.Map.map);

  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const checkedVoltClass = useSelector((state) => state.Map.checkedVoltClass);

  const [token, setToken] = useState(null);

  useEffect(() => {
    if (!map) return;

    const mouseMove = (e) => {
      if (e.features.length > 0) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(e.features[0].properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);

        if (hoveredStateId !== null) {
          map.setFeatureState(
            {
              source: sourceId,
              sourceLayer: sourceLayer,
              id: hoveredStateId,
            },
            { hover: false }
          );
        }

        hoveredStateId = e.features[0].id;
        map.setFeatureState(
          {
            source: sourceId,
            sourceLayer: sourceLayer,
            id: hoveredStateId,
          },
          { hover: true }
        );
      } else {
        popup.remove();
        if (hoveredStateId !== null) {
          map.setFeatureState(
            {
              source: sourceId,
              sourceLayer: sourceLayer,
              id: hoveredStateId,
            },
            { hover: false }
          );
        }
        hoveredStateId = null;
      }
    };

    const mouseLeave = () => {
      popup.remove();
      if (hoveredStateId !== null) {
        map.setFeatureState(
          {
            source: sourceId,
            sourceLayer: sourceLayer,
            id: hoveredStateId,
          },
          { hover: false }
        );
      }
      hoveredStateId = null;
    };

    const checkForLatestToken = async () => {
      const newToken = await window.spatiallaser.getUserToken("access");
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    checkForLatestToken();
    map.on("movestart", checkForLatestToken);
    map.on("mousemove", lineStyle.id, mouseMove);
    map.on("mouseleave", lineStyle.id, mouseLeave);
    map.on("error", (e) => {
      console.log(e);
    });
    return () => {
      map.off("movestart", checkForLatestToken);
      map.off("mousemove", lineStyle.id, mouseMove);
      map.off("mouseleave", lineStyle.id, mouseLeave);
    };
  }, [map, currentMapLayerOptions, token]);

  useEffect(() => {
    if (!map) return;

    const voltClass = Object.keys(checkedVoltClass)
      .filter((key) => checkedVoltClass[key].checked)
      .map((key) => key.toUpperCase());

    if (voltClass.length === 0) {
      map.setFilter(lineStyle.id, ["!has", "VOLT_CLASS"]);
    } else {
      map.setFilter(lineStyle.id, ["in", "VOLT_CLASS", ...voltClass]);
    }
  }, [checkedVoltClass]);

  if (!token || !currentMapLayerOptions.includes("power lines")) return null;
  return (
    <Source id={sourceId} type="vector" tiles={[getTileURL(serverType, token)]}>
      <Layer {...lineStyle} />
    </Source>
  );
}

const classifications = new Map();
classifications.set("Under 100", "#2593c7");
classifications.set("100-161", "#e64b49");
classifications.set("220-287", "#924c92");
classifications.set("345", "#f8891d");
classifications.set("500", "#fdda33");
classifications.set("735 and above", "#f27fd4");
classifications.set("Not available", "#9ebe2b");

export const PowerLinesLegend = () => {
  const checkedVoltClass = useSelector((state) => state.Map.checkedVoltClass);

  const dispatch = useDispatch();

  const onChange = (checked, key) => {
    dispatch({
      type: "Map/saveState",
      payload: {
        checkedVoltClass: {
          ...checkedVoltClass,
          [key]: { checked: checked },
        },
      },
    });
  };

  return (
    <div className="w-[300px] bg-white p-2">
      <div>
        <strong>U.S. Electric Power Transmission Lines</strong>
      </div>
      <div className="py-1 px-3">
        {Array.from(classifications.keys()).map((key, idx) => {
          return (
            <div key={key} className="flex items-center gap-2 my-2">
              <Checkbox
                checked={checkedVoltClass[key].checked}
                onChange={(e) => onChange(e.target.checked, key)}
              ></Checkbox>
              <div
                className="w-[50px] h-[4px]"
                style={{ backgroundColor: classifications.get(key) }}
              ></div>
              <div>
                {key} {key !== "Not available" ? "(Kilovolts)" : ""}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default PowerLinesLayer;
