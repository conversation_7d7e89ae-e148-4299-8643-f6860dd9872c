import React from "react";
import Source from "./Source";
import Layer from "./Layer";
import { useSelector, useDispatch } from "react-redux";
import { tileURLRoot } from "../../../services/data";
import { initPopup } from "../MapUtility/general";
import { renderToString } from "react-dom/server";

const SOURCE_LAYER = "multifamily-apartments";
const MIN_ZOOM = 4;

const currentYear = new Date().getFullYear();

const getTileURL = (serverType, token) =>
  `${tileURLRoot(
    "sbs",
    serverType
  )}/api/v1/multifamily/${SOURCE_LAYER}/{z}/{x}/{y}.mvt?access_token=${token}`;

const groupByStatus = [
  { name: `>= ${currentYear + 1}`, color: "#e7298a" },
  { name: currentYear, color: "#7570b3" },
  { name: currentYear - 1, color: "#d95f02" },
  { name: `<= ${currentYear - 2}`, color: "#1b9e77" },
];

const circleStyle = {
  id: `${SOURCE_LAYER}-circle-style`,
  type: "circle",
  "source-layer": SOURCE_LAYER,
  minzoom: MIN_ZOOM,
  paint: {
    "circle-stroke-width": 2,
    "circle-stroke-color": "white",
    "circle-color": "#4c7a2e",
    "circle-radius": 8,
    "circle-color": [
      "case",
      [">", ["get", "year_built"], currentYear],
      "#e7298a",
      groupByStatus
        ? [
            "match",
            ["get", "year_built"],
            ...groupByStatus
              .filter(
                (item) =>
                  item.name !== `<= ${currentYear - 2}` &&
                  item.name !== `>= ${currentYear + 1}`
              )
              .flatMap((item) => [item.name, item.color]),
            "#1b9e77", // default if no match
          ]
        : "#1b9e77",
    ],
  },
};

const popup = initPopup();

const createPopupInfo = (map, feature, coordinates) => {
  if (feature && feature.length > 0) {
    const info = feature[0].properties;
    const htmlRender = renderToString(
      <div style={{ padding: "10px", fontWeight: "500" }}>
        <div className="flex flex-row justify-between gap-2 items-center">
          <p>{info.name && <strong>{info.name}</strong>}</p>
          {info.phone && <p className="text-nowrap">{info.phone}</p>}
        </div>
        <p>
          {info.address ? `${info.address}` : ``}
          {info.city ? `, ${info.city}` : ``}
          {info.state ? `, ${info.state}` : ``}
          {info.zipcode ? ` ${info.zipcode}` : ``}
        </p>

        <div className="flex flex-row gap-2 items-center text-nowrap">
          <p>
            Rent: ${Number(info.min_rent).toLocaleString()} - $
            {Number(info.max_rent).toLocaleString()}
          </p>
          <span> | </span>
          <p>
            Min Beds:
            {info.min_bedrooms !== undefined &&
              (info.min_bedrooms === 0 ? "Studio" : info.min_bedrooms)}
          </p>
        </div>
        {info.unit_availabilities && (
          <p>Available Units: {info.unit_availabilities}</p>
        )}
        <p>Property Type: {info.property_type}</p>
        <p>Year Built: {info.year_built}</p>
        <p>
          Total Units: {info.total_property_units} {info.property_unit_type}
        </p>
        <p>{info.listing_dma}</p>
      </div>
    );

    popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
  } else {
    popup.remove();
  }
};

const useAccessToken = ({ map }) => {
  const [token, setToken] = React.useState(null);

  React.useEffect(() => {
    if (!map) return;

    const checkForLatestToken = async () => {
      popup.isOpen() && popup.remove();
      const newToken = await window.spatiallaser.getUserToken("access");
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    checkForLatestToken();
    map.on("movestart", checkForLatestToken);
  }, [map]);

  return token;
};

export const MultiFamilyApartmentsLegend = () => {
  return (
    <div className="flex flex-col gap-2 px-2 py-1 bg-white">
      <p className="font-semibold">Multi-Family Community</p>
      <div className="flex flex-col gap-1">
        {[
          { name: `>= ${currentYear + 1}`, color: "#e7298a" },
          { name: currentYear, color: "#7570b3" },
          { name: currentYear - 1, color: "#d95f02" },
          { name: `<= ${currentYear - 2}`, color: "#1b9e77" },
        ].map((item) => (
          <div className="flex flex-row gap-2 items-center">
            <div className="w-6 h-6" style={{ background: item.color }} />
            <div>{item.name}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export const MultiFamilyApartments = () => {
  const map = useSelector((state) => state.Map.map);
  const serverType = useSelector((state) => state.Configure.serverType);
  const token = useAccessToken({ map });
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );

  React.useEffect(() => {
    if (!map) return;

    const mouseEnter = (e) => {
      const feature = map.queryRenderedFeatures(e.point, {
        layers: [circleStyle.id],
      });

      console.log("feature", feature);

      const coordinates = [e.lngLat.lng, e.lngLat.lat];
      createPopupInfo(map, feature, coordinates);
    };
    const mouseLeave = () => {
      popup.remove();
    };

    map.on("mouseenter", circleStyle.id, mouseEnter);
    map.on("mouseleave", circleStyle.id, mouseLeave);
    return () => {
      map.off("mouseenter", circleStyle.id, mouseEnter);
      map.off("mouseleave", circleStyle.id, mouseLeave);
    };
  }, [map, currentMapLayerOptions]);
  return (
    <>
      <Source
        id={SOURCE_LAYER}
        type="vector"
        tiles={[getTileURL(serverType, token)]}
      >
        <Layer {...circleStyle} />
      </Source>
    </>
  );
};
