import { useEffect, useRef } from "react";
import { renderToString } from "react-dom/server";
import { useSelector, useDispatch } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import {
  MAP_LAYER_NAME_BASE,
  // zoomLevelToChangeBasemap,
  ACTIVITY_CENTER_COLOR,
} from "../../../constants";
// import { setActivityCenterColor } from "../MapUtility/colors";
import { hideVisibility, showVisibility } from "../MapUtility/layer";
import { initPopup } from "../MapUtility/general";

const sourceId = MAP_LAYER_NAME_BASE.activityCenter;

const zoomLevelToLayer = 9;
// const activityCenterSatelliteOpacity = 0.6;
const activityCenterDefaultOpacity = 0.6;

const fillStyle = {
  id: `${sourceId}LayerFill`,
  type: "fill",
  minzoom: zoomLevelToLayer,
  paint: {
    "fill-opacity": activityCenterDefaultOpacity,
    "fill-color": [
      "match",
      ["get", "type"],
      "major center",
      ACTIVITY_CENTER_COLOR.majorColor,
      "minor center",
      ACTIVITY_CENTER_COLOR.minorColor,
      "monocenter",
      ACTIVITY_CENTER_COLOR.monoColor,
      "#000",
    ],
  },
};

const OutlineLayer = {
  id: `${sourceId}LayerOutline`,
  type: "line",
  minzoom: zoomLevelToLayer,
  paint: {
    "line-color": "#000",
    "line-width": 3,
  },
};

let activityCenterPopup;

function ActivityCenterLayer() {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const currentActivityCenterMapScopeGeoJSON = useSelector(
    (state) => state.Map.currentActivityCenterMapScopeGeoJSON
  );
  const heatmapType = useSelector((state) => state.Map.heatmapType);
  const dispatch = useDispatch();

  const currentMapLayerOptionsRef = useRef(currentMapLayerOptions);
  currentMapLayerOptionsRef.current = currentMapLayerOptions;

  useEffect(() => {
    if (!map || !map.style || !map.style._loaded) return;
    if (!map.getLayer(`${sourceId}LayerFill`)) return;

    if (currentMapLayerOptions.includes("activity centers")) {
      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToLayer) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getActivityCenterMapScope",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });
      }
      showVisibility(map, `${sourceId}LayerFill`);
    } else {
      if (
        map.getLayoutProperty(`${sourceId}LayerFill`, "visibility") ===
        "visible"
      ) {
        hideVisibility(map, `${sourceId}LayerFill`);
      }
    }
  }, [currentMapLayerOptions]);

  useEffect(() => {
    if (!map) return;

    activityCenterPopup = initPopup();

    const moveEnd = () => {
      if (!currentMapLayerOptionsRef.current.includes("activity centers"))
        return;

      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToLayer) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getActivityCenterMapScope",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });
      }
    };

    const mouseMove = (e) => {
      const feature = map.queryRenderedFeatures(e.point, {
        layers: [`${sourceId}LayerFill`],
      });
      const coordinates = [e.lngLat.lng, e.lngLat.lat];

      if (feature && feature.length > 0) {
        const subtype = feature[0].properties.subtype;

        const htmlRender = renderToString(
          <span style={{ padding: "10px", fontWeight: "500" }}>{subtype}</span>
        );

        activityCenterPopup
          .setLngLat(coordinates)
          .setHTML(htmlRender)
          .addTo(map);
      } else {
        activityCenterPopup.remove();
      }
    };

    const mouseLeave = () => {
      activityCenterPopup.remove();
    };

    map.on("moveend", moveEnd);
    map.on("mousemove", `${sourceId}LayerFill`, mouseMove);
    map.on("mouseleave", `${sourceId}LayerFill`, mouseLeave);

    return () => {
      map.off("moveend", moveEnd);
      map.off("mousemove", `${sourceId}LayerFill`, mouseMove);
      map.off("mouseleave", `${sourceId}LayerFill`, mouseLeave);
    };
  }, [map]);

  return (
    <>
      <Source
        id={sourceId}
        type="geojson"
        data={currentActivityCenterMapScopeGeoJSON}
      >
        <Layer {...fillStyle} />
        {heatmapType &&
          (heatmapType.includes("demographics") ||
            heatmapType.includes("submarket")) &&
          currentMapLayerOptionsRef.current.includes("activity centers") && (
            <Layer {...OutlineLayer} />
          )}
      </Source>
    </>
  );
}

export default ActivityCenterLayer;
