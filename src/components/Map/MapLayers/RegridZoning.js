import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { tileURLRoot } from "../../../services/data";
import { initPopup } from "../MapUtility/general";

const sourceId = "regrid-zoning";
const sourceLayer = "3e7347267aded0e7bb2a281ff1fbcbeb9ef8698a";

const fillStyle = {
  id: "regrid-zoning-fill",
  type: "fill",
  source: sourceId,
  "source-layer": sourceLayer,
  minzoom: 12,
  maxzoom: 14,
  paint: {
    "fill-color": [
      "case",
      ["==", ["get", "zoning_type"], "Residential"],
      "#ebfd1f",
      ["==", ["get", "zoning_type"], "Commercial"],
      "#ac3c34",
      ["==", ["get", "zoning_type"], "Special"],
      "#e087b4",
      ["==", ["get", "zoning_type"], "Mixed"],
      "#8d6698",
      ["==", ["get", "zoning_type"], "Planned"],
      "#d8925f",
      ["==", ["get", "zoning_type"], "Agriculture"],
      "#67a25b",
      ["==", ["get", "zoning_type"], "Industrial"],
      "#4a6692",
      ["==", ["get", "zoning_type"], "Overlay"],
      "#8d5436",
      ["==", ["get", "zoning_type"], "Others"],
      "#c6c6c6",
      "transparent", // Default case if none of the above conditions are met
    ],
    "fill-opacity": 0.6,
  },
};

const labelStyle = {
  id: "regrid-zoning-label",
  type: "symbol",
  source: sourceId,
  "source-layer": sourceLayer,
  minzoom: 12,
  maxzoom: 14,
  layout: {
    "text-field": ["get", "zoning_type"],
    "text-size": 18,
    "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"],
    // "text-allow-overlap": true,
    "symbol-spacing": 500,
  },
  paint: {
    "text-color": "purple",
    "text-halo-color": "white",
    "text-halo-width": 4,
  },
};

const getTileURL = (serverType, token) =>
  `${tileURLRoot(
    "sbs",
    serverType
  )}/api/v1/parcel/zoning/tile/{z}/{x}/{y}.mvt?access_token=${token}`;

const popup = initPopup();

const popupHTML = (properties) => {
  const { zoning_type } = properties;
  return `
    <div style="padding: 10px 20px; font-size: 16px">
      <strong>${zoning_type}</strong>
    </div>
  `;
};

const RegridZoning = () => {
  const serverType = useSelector((state) => state.Configure.serverType);
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const [token, setToken] = useState(null);

  useEffect(() => {
    if (!map) return;

    const mouseMove = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [fillStyle.id],
      });
      if (map.getZoom() < 14 && features.length > 0) {
        const feature = features[0];
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        if (!feature.properties) return;
        popup
          .setLngLat(coordinates)
          .setHTML(popupHTML(feature.properties))
          .addTo(map);
      } else {
        popup.remove();
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    const checkForLatestToken = async () => {
      const newToken = await window.spatiallaser.getUserToken("access");
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    checkForLatestToken();
    map.on("movestart", checkForLatestToken);
    map.on("mousemove", fillStyle.id, mouseMove);
    map.on("mouseleave", fillStyle.id, mouseLeave);
    return () => {
      map.off("movestart", checkForLatestToken);
      map.off("mousemove", fillStyle.id, mouseMove);
      map.off("mouseleave", fillStyle.id, mouseLeave);
    };
  }, [map]);

  if (!token || !currentMapLayerOptions.includes("zoning types")) return null;
  return (
    <>
      <Source
        id={sourceId}
        type="vector"
        tiles={[getTileURL(serverType, token)]}
      >
        <Layer {...fillStyle} />
        <Layer {...labelStyle} />
      </Source>
    </>
  );
};

export default RegridZoning;

const zoningTypes = {
  Residential: "#ebfd1f",
  Commercial: "#ac3c34",
  Special: "#e087b4",
  Mixed: "#8d6698",
  Planned: "#d8925f",
  Agriculture: "#67a25b",
  Industrial: "#4a6692",
  Overlay: "#8d5436",
  Others: "#c6c6c6",
};

const zoningSubTypes = {
  "Single Family": "#a6cee3",
  "Two Family": "#1f78b4",
  "Multi Family": "#b2df8a",
  "Mobile Home Park": "#33a02c",
  "General Commercial": "#fb9a99",
  "Core Commercial": "#e31a1c",
  "Retail Commercial": "#fdbf6f",
  "Neighborhood Commercial": "#ff7f00",
  Office: "#cab2d6",
  "Special Commercial": "#6a3d9a",
  "Mixed Use": "#ffff99",
  Industrial: "#b15928",
  "Light Industrial": "#dfc27d",
  Special: "#66bd63",
  Planned: "#80cdc1",
  Overlay: "#878787",
};

export const RegridZoningLegend = () => {
  const map = useSelector((state) => state.Map.map);
  const [types, setTypes] = useState(zoningTypes);
  const [title, setTitle] = useState("Zoning Types");

  useEffect(() => {
    if (!map) return;

    const zoomend = () => {
      const zoom = map.getZoom();
      if (zoom >= 12 && zoom < 14) {
        setTypes(zoningTypes);
        setTitle("Zoning Types");
      } else if (zoom >= 14) {
        setTypes(zoningSubTypes);
        setTitle("Zoning Subtypes");
      }
    };

    map.on("zoomend", zoomend);

    return () => {
      map.off("zoomend", zoomend);
    };
  }, [map]);

  return (
    <div className="bg-white p-[10px]">
      <div className="font-bold text-center mb-1">{title}</div>
      <div className="flex flex-col">
        {Object.keys(types).map((key) => (
          <div key={key} className="flex flex-row gap-[10px]">
            <div
              className="w-[22px] h-[22px]"
              style={{ backgroundColor: types[key] }}
            ></div>
            <div className="">{key}</div>
          </div>
        ))}
      </div>
    </div>
  );
};
