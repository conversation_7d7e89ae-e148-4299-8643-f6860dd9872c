import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { default as turf_bbox } from "@turf/bbox";
import { geojsonTemplate } from "../../../constants";

// getDriveTime

const sourceId = "driveTime";

const getDriveTimeFillStyle = (time) => {
  return {
    id: `${sourceId}${time}Layer`,
    type: "fill",
    paint: {
      "fill-color": "#5a3fc0",
      "fill-opacity": 0.3,
    },
  };
};

const getOutlineLayer = (time) => {
  return {
    id: `${sourceId}-${time}-LayerOutline`,
    type: "line",
    paint: {
      "line-color": "#000",
      "line-width": 3,
    },
  };
};

function DriveTimeLayer() {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const driveTimeOptions = useSelector((state) => state.Map.driveTimeOptions);
  const driveTimeTraffic = useSelector((state) => state.Map.driveTimeTraffic);
  const driveTime5MinGeoJSON = useSelector(
    (state) => state.Map.driveTime5MinGeoJSON
  );
  const driveTime10MinGeoJSON = useSelector(
    (state) => state.Map.driveTime10MinGeoJSON
  );
  const driveTime15MinGeoJSON = useSelector(
    (state) => state.Map.driveTime15MinGeoJSON
  );
  const driveTime20MinGeoJSON = useSelector(
    (state) => state.Map.driveTime20MinGeoJSON
  );
  const driveTime30MinGeoJSON = useSelector(
    (state) => state.Map.driveTime30MinGeoJSON
  );
  const driveTime60MinGeoJSON = useSelector(
    (state) => state.Map.driveTime60MinGeoJSON
  );
  const eventCoordinates = useSelector((state) => state.Map.eventCoordinates);
  const heatmapType = useSelector((state) => state.Map.heatmapType);

  const dispatch = useDispatch();

  useEffect(() => {
    if (!map) return;
    if (eventCoordinates.length === 0) return;

    if (currentMapLayerOptions.includes("drive time")) {
      // It's fine to fetch again everytime driveTimeOptions changes
      // because of caching
      dispatch({
        type: "Map/getDriveTime",
        payload: {
          lng: eventCoordinates[0],
          lat: eventCoordinates[1],
        },
      });
    } else {
      if (
        driveTime5MinGeoJSON.features.length > 0 ||
        driveTime10MinGeoJSON.features.length > 0 ||
        driveTime15MinGeoJSON.features.length > 0 ||
        driveTime20MinGeoJSON.features.length > 0 ||
        driveTime30MinGeoJSON.features.length > 0 ||
        driveTime60MinGeoJSON.features.length > 0
      ) {
        dispatch({
          type: "Map/saveState",
          payload: {
            driveTime5MinGeoJSON: geojsonTemplate,
            driveTime10MinGeoJSON: geojsonTemplate,
            driveTime15MinGeoJSON: geojsonTemplate,
            driveTime20MinGeoJSON: geojsonTemplate,
            driveTime30MinGeoJSON: geojsonTemplate,
            driveTime60MinGeoJSON: geojsonTemplate,
          },
        });
      }
    }
  }, [
    eventCoordinates,
    currentMapLayerOptions,
    driveTimeOptions,
    driveTimeTraffic,
  ]);

  useEffect(() => {
    if (
      driveTimeOptions["60 Minutes"] &&
      driveTime60MinGeoJSON.features.length > 0
    ) {
      map.fitBounds(turf_bbox(driveTime60MinGeoJSON), { padding: 32 });
    } else if (
      driveTimeOptions["30 Minutes"] &&
      driveTime30MinGeoJSON.features.length > 0
    ) {
      map.fitBounds(turf_bbox(driveTime30MinGeoJSON), { padding: 32 });
    } else if (
      driveTimeOptions["20 Minutes"] &&
      driveTime20MinGeoJSON.features.length > 0
    ) {
      map.fitBounds(turf_bbox(driveTime20MinGeoJSON), { padding: 32 });
    } else if (
      driveTimeOptions["15 Minutes"] &&
      driveTime15MinGeoJSON.features.length > 0
    ) {
      map.fitBounds(turf_bbox(driveTime15MinGeoJSON), { padding: 32 });
    } else if (
      driveTimeOptions["10 Minutes"] &&
      driveTime10MinGeoJSON.features.length > 0
    ) {
      map.fitBounds(turf_bbox(driveTime10MinGeoJSON), { padding: 32 });
    } else if (
      driveTimeOptions["5 Minutes"] &&
      driveTime5MinGeoJSON.features.length > 0
    ) {
      map.fitBounds(turf_bbox(driveTime5MinGeoJSON), { padding: 32 });
    }
  }, [
    driveTime60MinGeoJSON,
    driveTime30MinGeoJSON,
    driveTime20MinGeoJSON,
    driveTime15MinGeoJSON,
    driveTime10MinGeoJSON,
    driveTime5MinGeoJSON,
    driveTimeOptions,
  ]);

  useEffect(() => {
    if (!map) return;
    if (!currentMapLayerOptions.includes("drive time")) return;

    const layers = ["5min", "10min", "15min", "20min", "30min", "60min"];
    const timeMap = {
      "5min": "5 Minutes",
      "10min": "10 Minutes",
      "15min": "15 Minutes",
      "20min": "20 Minutes",
      "30min": "30 Minutes",
      "60min": "60 Minutes",
    };

    for (let i = 0; i < layers.length; i++) {
      if (driveTimeOptions[timeMap[layers[i]]]) {
        map.setLayoutProperty(
          `${sourceId}${layers[i]}Layer`,
          "visibility",
          "visible"
        );
        map.setLayoutProperty(
          `${sourceId}-${layers[i]}-LayerOutline`,
          "visibility",
          "visible"
        );
      } else {
        map.setLayoutProperty(
          `${sourceId}${layers[i]}Layer`,
          "visibility",
          "none"
        );
        map.setLayoutProperty(
          `${sourceId}-${layers[i]}-LayerOutline`,
          "visibility",
          "none"
        );
      }
    }
  }, [driveTimeOptions, currentMapLayerOptions]);

  if (currentMapLayerOptions.includes("drive time")) {
    return (
      <>
        <Source
          id={`${sourceId}5min`}
          type="geojson"
          data={driveTime5MinGeoJSON}
        >
          <Layer {...getDriveTimeFillStyle("5min")} />
          {heatmapType &&
            (heatmapType.includes("demographics") ||
              heatmapType.includes("submarket")) &&
            currentMapLayerOptions.includes("drive time") && (
              <Layer {...getOutlineLayer("5min")} />
            )}
        </Source>
        <Source
          id={`${sourceId}10min`}
          type="geojson"
          data={driveTime10MinGeoJSON}
        >
          <Layer {...getDriveTimeFillStyle("10min")} />
          {heatmapType &&
            (heatmapType.includes("demographics") ||
              heatmapType.includes("submarket")) &&
            currentMapLayerOptions.includes("drive time") && (
              <Layer {...getOutlineLayer("10min")} />
            )}
        </Source>
        <Source
          id={`${sourceId}15min`}
          type="geojson"
          data={driveTime15MinGeoJSON}
        >
          <Layer {...getDriveTimeFillStyle("15min")} />
          {heatmapType &&
            (heatmapType.includes("demographics") ||
              heatmapType.includes("submarket")) &&
            currentMapLayerOptions.includes("drive time") && (
              <Layer {...getOutlineLayer("15min")} />
            )}
        </Source>
        <Source
          id={`${sourceId}20min`}
          type="geojson"
          data={driveTime20MinGeoJSON}
        >
          <Layer {...getDriveTimeFillStyle("20min")} />
          {heatmapType &&
            (heatmapType.includes("demographics") ||
              heatmapType.includes("submarket")) &&
            currentMapLayerOptions.includes("drive time") && (
              <Layer {...getOutlineLayer("20min")} />
            )}
        </Source>
        <Source
          id={`${sourceId}30min`}
          type="geojson"
          data={driveTime30MinGeoJSON}
        >
          <Layer {...getDriveTimeFillStyle("30min")} />
          {heatmapType &&
            (heatmapType.includes("demographics") ||
              heatmapType.includes("submarket")) &&
            currentMapLayerOptions.includes("drive time") && (
              <Layer {...getOutlineLayer("30min")} />
            )}
        </Source>
        <Source
          id={`${sourceId}60min`}
          type="geojson"
          data={driveTime60MinGeoJSON}
        >
          <Layer {...getDriveTimeFillStyle("60min")} />
          {heatmapType &&
            (heatmapType.includes("demographics") ||
              heatmapType.includes("submarket")) &&
            currentMapLayerOptions.includes("drive time") && (
              <Layer {...getOutlineLayer("60min")} />
            )}
        </Source>
      </>
    );
  }
  return null;
}

export default DriveTimeLayer;
