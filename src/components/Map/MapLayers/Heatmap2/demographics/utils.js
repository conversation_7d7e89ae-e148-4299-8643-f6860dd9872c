import * as d3 from "d3";
import { renderToString } from "react-dom/server";

export const generateClassRange = (num) => {
  const step = 1 / (num - 1);
  const range = Array.from({ length: num }, (_, i) => i * step);
  return range;
};

export const getBlueSequentialColors = () => {
  const scale = d3.scaleSequential(d3.interpolateBlues);
  return generateClassRange(10).map((q) => scale(q));
};

export const getQuantileScale = (data, color) => {
  const scale = d3.scaleQuantile().domain(data).range(color);
  return scale;
};

export const getCountryDemographicScale = (range, color) => {
  if (range) {
    function scale(value) {
      if (value === null || value === undefined) return null;
      for (let i = 0; i < range.length; i++) {
        if (i + 1 < range.length) {
          if (value >= range[i].value && value < range[i + 1].value)
            return color ? color[i] : range[i].color;
        } else {
          if (value >= range[i].value) return color ? color[i] : range[i].color;
        }
      }
      // if below range
      return color ? color[0] : range[0].color;
    }
    scale.quantiles = function () {
      const result = range.map((r) => r.value);
      // result.shift();
      result.pop();
      return result;
    };
    scale.range = function () {
      if (color) {
        return color;
      }
      return range.map((r) => r.color);
    };

    return scale;
  }
};

export const getColorScaleScore = (scale, value) =>
  scale ? scale.range().indexOf(scale(value)) + 1 : 0;

export const titleFormatter = (text) =>
  ({
    median_hh_income: "Median HH Income",
    five_year_pop_growth: "5 Year Population Growth",
    five_year_income_growth: "5 Year Income Growth",
    bachelors_and_above: "Bachelors and Above",
    median_age: "Median Age",
    household_size: "Household Size",
    population_density: "Population Density (per mile)",
    fifty_five_plus: "55+ Percentage",
    household_growth: "Household Growth",
    rental_growth_5_years: "Rent CAGR trailing 5 years",
    rent_vs_owner_percentage: "Rent vs Owner Percentage",
    rent_vs_own: "Mtg Payment - Rent",
    median_home_value: "Median Home Value",
    median_rent: "Median Home Rent",
    crime_score: "Crime Score",
    combined_filter_score: "Combined Color Score",
  }[text] || text);

const isValidKey = (key) => {
  return (
    [
      "median_hh_income",
      "five_year_pop_growth",
      "bachelors_and_above",
      "median_age",
      "household_size",
      "population_density",
      "fifty_five_plus",
      "household_growth",
      "rent_vs_owner_percentage",
      "rent_vs_own",
      "median_home_value",
      "median_rent",
      "rental_growth_5_years",
      "crime_score",
    ].includes(key) || key.includes("_combined_score")
  );
};

export const valueFormatter = (title, value) => {
  if (isValidKey(title) && (value == null || !isFinite(value))) {
    console.log("valueFormatter", title, value);
    return "--";
  }
  const formatDollar = (num) =>
    num.toLocaleString("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });

  const formatPercentage = (num) => {
    return `${Number(num).toLocaleString("en-US", {
      minimumFractionDigits: 1,
      maximumFractionDigits: 1,
    })}%`;
    // return `${Math.round(num * 10) / 10 || 0}%`;
  };
  const formatRoundNumber = (num, decimals = 0) =>
    num.toLocaleString("en-US", {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }) || 0;
  if (
    [
      "median_hh_income",
      "median_home_value",
      "median_rent",
      "rent_vs_own",
    ].includes(title)
  ) {
    return formatDollar(value);
  } else if (
    [
      "five_year_pop_growth",
      "five_year_income_growth",
      "bachelors_and_above",
      "household_growth",
      "rental_growth_5_years",
      "rent_vs_owner_percentage",
    ].includes(title)
  ) {
    return formatPercentage(value);
  } else if (
    ["population_density", "fifty_five_plus", "median_age"].includes(title)
  ) {
    return formatRoundNumber(value);
  } else if (
    title.includes("combined_filter_score") ||
    title.includes("_combined_score") ||
    title.includes("household_size")
  ) {
    return formatRoundNumber(value, 1);
  }
  return value;
};

export const getTooltipHTML = (data, active, colorScore) => {
  if (!Object.keys(data).includes("combined_filter_score")) {
    const ignore = ["id", active, "color_score"];
    return renderToString(
      <div style={{ padding: "10px" }}>
        <div>
          <span>
            Id: <strong>{data.id}</strong>
          </span>
        </div>

        <div>
          <strong>{titleFormatter(active)}: </strong>
          <strong>{valueFormatter(active, data[active])}</strong>
        </div>

        {Object.keys(data)
          .filter((type) => !ignore.includes(type))
          .map((type) => {
            return (
              <div key={type}>
                <span>{titleFormatter(type)}: </span>
                <strong>{valueFormatter(type, data[type])}</strong>
              </div>
            );
          })}
        <div>
          <span>{titleFormatter(active)}: </span>
          <strong>{valueFormatter(active, data[active])}</strong>
        </div>
        <div>
          <span>Color Score: </span>
          <strong>{colorScore}</strong>
        </div>
      </div>
    );
  } else {
    const ignore = ["id", active, "color_score", "combined_filter_score"];
    return renderToString(
      <div style={{ padding: "10px" }}>
        <div>
          <span>
            Id: <strong>{data.id}</strong>
          </span>
        </div>

        <div>
          <strong>{titleFormatter(active)}: </strong>

          {Object.keys(data).includes(`${active}_combined_score`) ? (
            <span>
              {valueFormatter(active, data[active])}{" "}
              <strong>
                (
                {valueFormatter(
                  `${active}_combined_score`,
                  data[`${active}_combined_score`]
                )}
                )
              </strong>
            </span>
          ) : (
            <strong>{valueFormatter(active, data[active])}</strong>
          )}
        </div>

        {Object.keys(data)
          .filter(
            (type) =>
              !ignore.includes(type) && !type.includes("_combined_score")
          )
          .map((type) => {
            return (
              <div key={type}>
                <span>{titleFormatter(type)}: </span>
                {Object.keys(data).includes(`${type}_combined_score`) ? (
                  <span>
                    {valueFormatter(type, data[type])}{" "}
                    <strong>
                      (
                      {valueFormatter(
                        `${type}_combined_score`,
                        data[`${type}_combined_score`]
                      )}
                      )
                    </strong>
                  </span>
                ) : (
                  <strong>{valueFormatter(type, data[type])}</strong>
                )}
              </div>
            );
          })}
        {active != "combined_filter_score" && (
          <div>
            <span>Combined Color Score: </span>
            <strong>
              {valueFormatter(
                "combined_filter_score",
                data["combined_filter_score"]
              )}
            </strong>
          </div>
        )}
      </div>
    );
  }
};
