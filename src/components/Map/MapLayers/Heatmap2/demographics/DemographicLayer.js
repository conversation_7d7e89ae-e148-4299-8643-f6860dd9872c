import React, { useCallback, useState } from "react";
import { useDemographicsContext } from "./context";
import Layer from "../../Layer";
import {
  sourceId,
  sourceLayer,
  minZoom,
  getFillStyle,
  demographicsRanges,
} from "../constants";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { getHeatmapDomainData } from "../../../../../services/data";
import {
  getCountryDemographicScale,
  getBlueSequentialColors,
  getQuantileScale,
  getTooltipHTML,
  getColorScaleScore,
} from "./utils";
import usePrevious from "../../../../hooks/usePrevious";
import { initPopup, setPaintPropertySafely } from "../../../MapUtility/general";
import isEqual from "lodash.isequal";

let hoveredID = null;

let domainFetchController = {};

let popup = initPopup();
const fillStyle = getFillStyle("demographic-layer");
const defaultFilterFn = (data) => true;

const getCountryCombinedScoringFn = (combinedKeys) => (data) => {
  if (!data) return 0;
  const response = {};
  const total = combinedKeys.reduce((acc, key) => {
    if (!data[key]) return acc;
    const sc = getCountryDemographicScale(demographicsRanges(key));
    const score = getColorScaleScore(sc, data[key]);
    response[`${key}_combined_score`] = score;
    return acc + score;
  }, 0);

  const combinedScore = total / combinedKeys.length;
  response.combined_filter_score = combinedScore;
  return response;
};

const getQuantileCombinedScoringFn = (combinedKeys, metroDomain) => {
  const keys = combinedKeys.filter((key) => !key.includes("combined"));

  const colorArray = getBlueSequentialColors();
  const scales = keys.reduce((acc, key) => {
    acc[key] = getQuantileScale(metroDomain[key], colorArray);
    return acc;
  }, {});

  return function (data) {
    if (!data) return 0;
    const response = {};
    const total = keys.reduce((acc, key) => {
      if (!data[key] || !scales[key]) return acc;
      const score = getColorScaleScore(scales[key], data[key]);
      response[`${key}_combined_score`] = score;
      return acc + score;
    }, 0);

    const combinedScore = total / keys.length;
    response.combined_filter_score = combinedScore;
    return response;
  };
};

let baseOpacity = 0.6;

const DemographicLayer = ({
  source,
  activeKey,
  combinedKeys,
  benchmark = "country",
  rangeType = "default",
  filterFn = defaultFilterFn,
}) => {
  const {
    currentMetro,
    demographicsData,
    setDemographicsData,
    onDataChange,
    setIsLoading,
  } = useDemographicsContext();
  const map = useSelector((state) => state.Map.map);

  const [processedData, setProcessedData] = useState([]);
  const [domainData, setDomainData] = useState({});
  const [combinedFilterScoreDomain, setCombinedFilterScoreDomain] = useState(
    []
  );
  const [colorScaleFn, setColorScaleFn] = useState(null);

  const [stage, setStage] = useState(null);

  const prevActiveKey = usePrevious(activeKey);
  const prevCombinedKeys = usePrevious(combinedKeys);
  const prevDomainData = usePrevious(domainData);
  const prevDemographicsData = usePrevious(demographicsData);
  const prevProcessedData = usePrevious(processedData);

  // console.log("activeKey: ", activeKey);
  // console.log("combinedKeys: ", combinedKeys);
  // console.log("benchmark: ", benchmark);
  // console.log("rangeType: ", rangeType);

  useEffect(() => {
    if (!map) return;
    onDataChange([]);

    const layers = map.getStyle().layers;
    let firstLayerId;
    for (const layer of layers) {
      if (
        layer.source &&
        layer.source !== "composite" &&
        !layer.source.includes("mapbox")
      ) {
        firstLayerId = layer.id;
        break;
      }
    }
    map.moveLayer(fillStyle.id, firstLayerId);
  }, []);

  useEffect(() => {
    if (isEqual(prevProcessedData, processedData)) return;
    onDataChange(processedData);
  }, [processedData]);

  useEffect(() => {
    if (!map) return;

    const mouseMove = (e) => {
      if (processedData.length === 0) return;
      if (e.features.length === 0) return;
      if (hoveredID) {
        map.setFeatureState(
          { source: sourceId, id: hoveredID, sourceLayer: sourceLayer },
          { hover: false }
        );
      }
      hoveredID = e.features[0].id;
      map.setFeatureState(
        { source: sourceId, id: hoveredID, sourceLayer: sourceLayer },
        { hover: true }
      );

      const blockId = e.features[0].properties.Name;
      const data = processedData.find((d) => d.id === blockId);
      if (activeKey && data && filterFn(data)) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const colorScore = getColorScaleScore(colorScaleFn, data[activeKey]);
        const htmlRender = getTooltipHTML(data, activeKey, colorScore);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = (e) => {
      if (hoveredID !== null) {
        map.setFeatureState(
          { id: hoveredID, source: sourceId, sourceLayer: sourceLayer },
          { hover: false }
        );
      }
      hoveredID = null;
      popup.remove();
    };

    const updateOpacity = ({ payload }) => {
      const { heatmapOpacity } = payload;
      baseOpacity = heatmapOpacity / 100;
      if (map.getLayer(fillStyle.id)) {
        map.setPaintProperty(fillStyle.id, "fill-opacity", [
          "case",
          ["boolean", ["feature-state", "hover"], false],
          0.9,
          baseOpacity,
        ]);
      }
    };

    map.on("heatmap.menu.updateHeatmapOpacity", updateOpacity);
    map.on("mousemove", fillStyle.id, mouseMove);
    map.on("mouseleave", fillStyle.id, mouseLeave);
    return () => {
      map.off("mousemove", fillStyle.id, mouseMove);
      map.off("mouseleave", fillStyle.id, mouseLeave);
    };
  }, [map, processedData, filterFn, activeKey, colorScaleFn]);

  const fetchMetroDomainData = useCallback(
    async (filterKey) => {
      if (Object.keys(domainData).includes(filterKey)) {
        domainFetchController[filterKey]?.abort();
      }

      domainFetchController[filterKey] = new AbortController();
      const { signal } = domainFetchController[filterKey];

      const mapCenter = map.getCenter();
      let response = [];
      if (filterKey === "rent_vs_own") {
        const home_sale = await getHeatmapDomainData({
          type: "median_home_value",
          lat: mapCenter.lat,
          lng: mapCenter.lng,
          signal,
        });
        const home_rent = await getHeatmapDomainData({
          type: "median_rent",
          lat: mapCenter.lat,
          lng: mapCenter.lng,
          signal,
        });

        if (!home_sale || !home_rent) return;

        for (let i = 0; i < home_sale.length; i++) {
          for (let j = 0; j < home_rent.length; j++) {
            if (home_sale[i].block_group_id === home_rent[j].block_group_id) {
              const mortgage =
                home_sale[i].median_home_value * 0.965 * 0.006157172 + 600;
              const rent_vs_own = mortgage - home_rent[i].median_rent;
              if (rent_vs_own) {
                response.push(rent_vs_own);
              }
            }
          }
        }
      } else if (["median_home_value", "median_rent"].includes(filterKey)) {
        const home_rent_price = await getHeatmapDomainData({
          type: filterKey,
          lat: mapCenter.lat,
          lng: mapCenter.lng,
          signal,
        });

        response = home_rent_price.map((d) => d[filterKey]);
      } else {
        response = await getHeatmapDomainData({
          type: filterKey,
          lat: mapCenter.lat,
          lng: mapCenter.lng,
          signal,
        });
      }
      setDomainData((prevState) => ({
        ...prevState,
        [filterKey]: response ? response : [],
      }));
    },
    [map, currentMetro, benchmark, activeKey, combinedKeys]
  );

  useEffect(() => {
    if (
      (!activeKey || !demographicsRanges(activeKey)) &&
      demographicsData.length > 0
    ) {
      setProcessedData(demographicsData);
    } else if (
      activeKey &&
      demographicsData.length > 0 &&
      benchmark === "country"
    ) {
      setProcessedData(demographicsData);
    }
  }, [demographicsData, activeKey, benchmark]);

  useEffect(() => {
    if (!map || !currentMetro || !activeKey || map.getZoom() < minZoom) return;

    if (benchmark === "metro") {
      setIsLoading(true);

      const fetchKeys = [...new Set([activeKey, ...combinedKeys])].filter(
        (key) => !key.includes("combined")
      );
      for (let i = 0; i < fetchKeys.length; i++) {
        fetchMetroDomainData(fetchKeys[i]);
      }
    }
  }, [map, demographicsData, currentMetro, benchmark, activeKey, combinedKeys]);

  useEffect(() => {
    if (!activeKey || demographicsData.length === 0) return;
    if (combinedKeys.length > 0) {
      if (benchmark === "country") {
        const scoringFn = getCountryCombinedScoringFn(combinedKeys);
        const scoredData = demographicsData.map((data) => {
          const scores = scoringFn(data);
          return {
            ...data,
            ...scores,
          };
        });

        console.log("country scored: ", scoredData);
        setProcessedData(scoredData);
      } else if (benchmark === "metro") {
        const uniqueFilterKeys = [...new Set([activeKey, ...combinedKeys])];
        const containEveryMetroDomain = uniqueFilterKeys
          .filter((key) => !key.includes("combined"))
          .every((key) => domainData[key] && domainData[key].length > 0);

        console.log("containEveryMetroDomain: ", containEveryMetroDomain);

        if (!containEveryMetroDomain) {
          console.log(
            "keys: ",
            uniqueFilterKeys.filter((key) => !key.includes("combined"))
          );
          console.log("metro domain: ", domainData);
          return;
        }

        const activeDomain = [];

        const scoringFn = getQuantileCombinedScoringFn(
          uniqueFilterKeys,
          domainData
        );

        const newData = demographicsData.map((data) => {
          const scores = scoringFn(data);
          const res = {
            ...data,
            ...scores,
          };
          activeDomain.push(res[activeKey]);
          return res;
        });

        if (activeKey === "combined_filter_score") {
          setCombinedFilterScoreDomain(activeDomain);
        }
        console.log("metro scored: ", newData);
        setProcessedData(newData);
      }
    }
  }, [demographicsData, domainData, activeKey, combinedKeys, benchmark]);

  useEffect(() => {
    if (
      !activeKey ||
      !demographicsRanges(activeKey) ||
      processedData.length === 0
    )
      return;
    if (benchmark === "metro") {
      if (
        activeKey.includes("combined") &&
        combinedFilterScoreDomain.length === 0
      )
        return;
      if (
        !activeKey.includes("combined") &&
        (!domainData[activeKey] || domainData[activeKey].length === 0)
      ) {
        return;
      }
    }
    const expression = ["match", ["get", "Name"]];

    const colorArray =
      rangeType === "default"
        ? demographicsRanges(activeKey).map((item) => item.color)
        : getBlueSequentialColors();

    const scale =
      benchmark === "metro"
        ? getQuantileScale(
            activeKey.includes("combined")
              ? combinedFilterScoreDomain
              : domainData[activeKey],
            colorArray
          )
        : getCountryDemographicScale(demographicsRanges(activeKey), colorArray);

    if (scale) {
      const filteredData = processedData.filter((data) => filterFn(data));

      filteredData.forEach((item) => {
        const value = item[activeKey];
        const color = scale(value);
        expression.push(item.id, color != null ? color : "transparent");
      });

      setColorScaleFn(() => scale);
    }

    expression.push("transparent");
    if (expression.length > 3) {
      if (map.getLayer(fillStyle.id)) {
        map.setPaintProperty(fillStyle.id, "fill-color", expression);
        map.setPaintProperty(fillStyle.id, "fill-opacity", [
          "case",
          ["boolean", ["feature-state", "hover"], false],
          0.9,
          baseOpacity,
        ]);
      }
    }

    setIsLoading(false);
  }, [
    processedData,
    domainData[activeKey],
    benchmark,
    rangeType,
    activeKey,
    filterFn,
  ]);

  return <Layer {...fillStyle} source={source} />;
};

export default DemographicLayer;
