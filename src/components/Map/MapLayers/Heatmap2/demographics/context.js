import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { useSelector } from "react-redux";
import { sourceId, sourceURL, sourceLayer } from "../constants";
import Source from "../../Source";
import Layer from "../../Layer";
import {
  getHeatmapDemographicsData,
  getBlockGroupHomeValueData,
  getHeatmapSFRGrowthData,
  getBlockGroupCrimeValueData,
  getMLSListingSummaryPointWithinLayerData,
  getAllDemographicsInSquareData,
} from "../../../../../services/data";
import debounce from "lodash.debounce";

export const dempographicsContext = createContext({});

const fetchDemographicsData = async (params) => {
  const defaultData = await getHeatmapDemographicsData(params);
  const homeValueData = await getBlockGroupHomeValueData(params);
  const sfrGrowthData = await getHeatmapSFRGrowthData(params);
  const crimeData = await getBlockGroupCrimeValueData(params);

  return defaultData.map((item) => {
    const newData = { ...item };
    const homeValue = homeValueData.find(
      (homeValueItem) => homeValueItem.id == item.id
    );
    if (homeValue) {
      const housePrice = homeValue.median_home_value;
      const houseRent = homeValue.median_rent;
      const mortgage = housePrice * 0.965 * 0.006157172 + 600;
      const rent_vs_own = mortgage - houseRent;
      newData.rent_vs_own = rent_vs_own;
      newData.median_home_value = housePrice;
      newData.median_rent = houseRent;
    }

    const sfrGrowth = sfrGrowthData.find(
      (sfrGrowthItem) => sfrGrowthItem.block_group_id == item.id
    );
    if (sfrGrowth) {
      newData.rental_growth_5_years = sfrGrowth.rental_growth_5_years;
    }
    const crime = crimeData.find((crimeItem) => crimeItem.id == item.id);
    if (crime) {
      newData.crime_score = crime.crime_score;
    }

    delete newData["five_year_income_growth"];
    return newData;
  });
};

const fetchFullDemographicsData = async (params) =>
  await getAllDemographicsInSquareData(params);

const Context = (props) => {
  const map = useSelector((state) => state.Map.map);

  const [currentMetro, setCurrentMetro] = useState(null);
  const [demographicsData, setDemographicsData] = useState([]);
  const [demographicFilterFn, setDemographicFilterFn] = useState(
    () => () => true
  );
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (!map) return;

    const getMetro = async () => {
      const mapCenter = map.getCenter();
      const response = await getMLSListingSummaryPointWithinLayerData({
        lat: mapCenter.lat,
        lng: mapCenter.lng,
      });
      if (response && response.length > 0) {
        const metro = response.filter((item) => item.type === "metro");
        if (metro && metro.length > 0) {
          if (
            currentMetro &&
            currentMetro.key === metro[0].key &&
            currentMetro.name === metro[0].name
          ) {
            return;
          }
          setCurrentMetro(metro[0]);
        }
      }
    };

    const getDemographicsData = async () => {
      const bounds = map.getBounds().toArray();

      const response = await fetchFullDemographicsData({
        lng1: bounds[0][0],
        lat1: bounds[0][1],
        lng2: bounds[1][0],
        lat2: bounds[1][1],
      });

      if (response) {
        setDemographicsData(response);
      } else {
        setDemographicsData([]);
      }
    };

    const moveEnd = debounce(() => {
      try {
        getMetro();
        getDemographicsData();
        setIsLoading(true);
      } catch (err) {
        console.log(err);
      }
    }, 200);

    moveEnd();
    // map.on("moveend", debounce(moveEnd, 200));
    map.on("moveend", moveEnd);
    return () => {
      // map.off("moveend", debo÷unce(moveEnd, 200));
      map.off("moveend", moveEnd);
    };
  }, [map]);

  useEffect(() => {
    if (!map) return;

    map.fire("heatmap.isLoading", { payload: { isLoading } });
  }, [isLoading]);

  const onDataChangeWrapper = useCallback(
    (data) => {
      console.log("onDataChange: ", data);
      if (props.onDataChange) {
        props.onDataChange(data);
      }
    },
    [props.onDataChange]
  );

  return (
    <dempographicsContext.Provider
      value={{
        currentMetro,
        demographicsData,
        setDemographicsData,
        demographicFilterFn,
        onDataChange: onDataChangeWrapper,
        isLoading,
        setIsLoading,
      }}
    >
      <Source id={sourceId} type="vector" url={sourceURL}>
        {props.children}
      </Source>
    </dempographicsContext.Provider>
  );
};

export default Context;

export const useDemographicsContext = () => {
  const context = useContext(dempographicsContext);
  if (!context) {
    throw new Error(
      "useDemographicsContext must be used within a dempographicsContext Provider"
    );
  }
  return context;
};
