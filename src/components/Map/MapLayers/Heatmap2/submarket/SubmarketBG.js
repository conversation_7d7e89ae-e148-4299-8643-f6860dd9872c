import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import Source from "../../Source";
import Layer from "../../Layer";
import {
  sourceId,
  sourceLayer,
  minZoom,
  getFillStyle,
  submarketRanges,
  sourceURL,
} from "../constants";
import { useSubmarketContext } from "./context";
import { getSubmarketBGDomainData } from "../../../../../services/data";
import {
  getBlueSequentialColors,
  getQuantileScale,
  getCountrySubmarketScale,
  getTooltipHTML,
  getColorScaleScore,
} from "./utils";
import { initPopup, setPaintPropertySafely } from "../../../MapUtility/general";
let hoveredID = null;

let popup = initPopup();

const submarketSourceId = `${sourceId}-submarket-BG`;

const defaultFilterFn = () => true;

let baseOpacity = 0.6;

const SubmarketBG = ({
  activeKey,
  benchmark,
  rangeType,
  filterFn = defaultFilterFn,
}) => {
  const fillStyle = getFillStyle(`submarket-BG`);

  const { currentMetro, submarketBGData, onDataChange, setSubmarketShapeType } =
    useSubmarketContext();

  const map = useSelector((state) => state.Map.map);
  const [domainData, setDomainData] = useState({});
  const [colorScaleFn, setColorScaleFn] = useState(null);

  useEffect(() => {
    if (!map) return;
    setSubmarketShapeType("Block Groups");
    onDataChange([]);

    const layers = map.getStyle().layers;
    let firstLayerId;
    for (const layer of layers) {
      if (
        layer.source &&
        layer.source !== "composite" &&
        !layer.source.includes("mapbox")
      ) {
        firstLayerId = layer.id;
        break;
      }
    }
    map.moveLayer(fillStyle.id, firstLayerId);
  }, []);

  console.log("submarketBGData: ", submarketBGData);

  useEffect(() => {
    onDataChange(submarketBGData);
  }, [submarketBGData]);

  useEffect(() => {
    if (!map) return;

    const mouseMove = (e) => {
      if (submarketBGData.length === 0) return;
      if (e.features.length === 0) return;
      if (hoveredID) {
        map.setFeatureState(
          {
            source: submarketSourceId,
            id: hoveredID,
            sourceLayer: sourceLayer,
          },
          { hover: false }
        );
      }
      hoveredID = e.features[0].id;
      map.setFeatureState(
        { source: submarketSourceId, id: hoveredID, sourceLayer: sourceLayer },
        { hover: true }
      );

      const blockId = e.features[0].properties.Name;
      const data = submarketBGData.find((item) => item.id === blockId);
      if (data && filterFn(data)) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const colorScore = getColorScaleScore(colorScaleFn, data[activeKey]);
        const htmlRender = getTooltipHTML(data, activeKey, colorScore);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = (e) => {
      if (hoveredID !== null) {
        map.setFeatureState(
          {
            id: hoveredID,
            source: submarketSourceId,
            sourceLayer: sourceLayer,
          },
          { hover: false }
        );
      }
      hoveredID = null;
      popup.remove();
    };

    const updateOpacity = ({ payload }) => {
      const { heatmapOpacity } = payload;
      baseOpacity = heatmapOpacity / 100;
      if (map.getLayer(fillStyle.id)) {
        map.setPaintProperty(fillStyle.id, "fill-opacity", [
          "case",
          ["boolean", ["feature-state", "hover"], false],
          0.9,
          baseOpacity,
        ]);
      }
    };

    map.on("heatmap.menu.updateHeatmapOpacity", updateOpacity);
    map.on("mousemove", fillStyle.id, mouseMove);
    map.on("mouseleave", fillStyle.id, mouseLeave);
    return () => {
      map.off("mousemove", fillStyle.id, mouseMove);
      map.off("mouseleave", fillStyle.id, mouseLeave);
    };
  }, [map, submarketBGData, filterFn, colorScaleFn]);

  useEffect(() => {
    if (!map || !currentMetro || map.getZoom() < minZoom) return;
    if (benchmark != "metro") return;

    const fetchDomainData = async () => {
      if (activeKey !== "rental_growth_5_years") {
        const mapCenter = map.getCenter();
        const response = await getSubmarketBGDomainData({
          type: activeKey,
          lat: mapCenter.lat,
          lng: mapCenter.lng,
        });

        setDomainData((prevState) => ({
          ...prevState,
          [activeKey]: response ? response : [],
        }));
      }
    };

    fetchDomainData();
  }, [map, currentMetro, benchmark, activeKey]);

  useEffect(() => {
    if (submarketBGData.length === 0) return;

    const colorArray =
      rangeType === "default"
        ? submarketRanges(activeKey).map((r) => r.color)
        : getBlueSequentialColors();

    const filteredData = submarketBGData.filter((data) => filterFn(data));

    if (benchmark === "country") {
      const scale = getCountrySubmarketScale(
        submarketRanges(activeKey),
        colorArray
      );
      renderToMap(scale, activeKey, filteredData);
    } else if (benchmark === "metro" && domainData[activeKey]) {
      const scale = getQuantileScale(domainData[activeKey], colorArray);
      renderToMap(scale, activeKey, filteredData);
    }
  }, [submarketBGData, domainData, filterFn, activeKey, benchmark, rangeType]);

  const renderToMap = (scale, activeKey, data) => {
    if (scale) {
      const expression = ["match", ["get", "Name"]];

      data.forEach((item) => {
        const value = item[activeKey];
        const color = scale(value);
        expression.push(item.id, color != null ? color : "transparent");
      });

      expression.push("transparent");
      if (expression.length > 3) {
        if (map.getLayer(fillStyle.id)) {
          setPaintPropertySafely(map, fillStyle.id, "fill-color", expression);
          setPaintPropertySafely(map, fillStyle.id, "fill-opacity", [
            "case",
            ["boolean", ["feature-state", "hover"], false],
            0.8,
            baseOpacity,
          ]);
        }
      }

      setColorScaleFn(() => scale);
    }
  };

  if (activeKey === "rental_growth_5_years") return null;
  return (
    <Source id={submarketSourceId} type="vector" url={sourceURL}>
      <Layer {...fillStyle} />
    </Source>
  );
};

export default SubmarketBG;
