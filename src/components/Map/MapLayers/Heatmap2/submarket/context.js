import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { useSelector } from "react-redux";
import { sourceId, sourceURL, sourceLayer } from "../constants";
import Source from "../../Source";
import Layer from "../../Layer";
import {
  getHeatmapSubmarketData,
  getHeatmapSubmarketRentalGrowthData,
  getHeatmapSubmarketBGData,
  getMLSListingSummaryPointWithinLayerData,
} from "../../../../../services/data";
import debounce from "lodash.debounce";

export const submarketContext = createContext({});

const fetchSubmarketPolygonData = async (params) => {
  const submarketRes = await getHeatmapSubmarketData(params);
  const rentalGrowthRes = await getHeatmapSubmarketRentalGrowthData(params);

  if (submarketRes && submarketRes.length > 0) {
    const response = submarketRes.map((item) => {
      const geogname = item.geogname;
      const rentalGrowth =
        rentalGrowthRes &&
        rentalGrowthRes.find((rentalGrowthItem) =>
          rentalGrowthItem.geogname.includes(geogname)
        );
      if (rentalGrowth) {
        return {
          ...item,
          rental_growth_5_years: rentalGrowth.rental_growth_5_years,
        };
      } else {
        return item;
      }
    });

    return response;
  }
};

const fetchSubmarketBGData = async (params) => {
  const response = await getHeatmapSubmarketBGData(params);
  return response;
};

const Context = (props) => {
  const map = useSelector((state) => state.Map.map);
  const heatmapType = useSelector((state) => state.Map.heatmapType);

  const [currentMetro, setCurrentMetro] = useState(null);
  const [submarketPolygonData, setSubmarketPolygonData] = useState([]);
  const [submarketBGData, setSubmarketBGData] = useState([]);
  // const [submarketShapeType, setSubmarketShapeType] = useState("polygon"); // polygon or blockgroup
  const [submarketShapeType, setSubmarketShapeType] = useState("Blockgroup"); // polygon or blockgroup
  const [submarketFilterFn, setSubmarketFilterFn] = useState(() => () => true);

  useEffect(() => {
    if (!map) return;

    const getMetro = async () => {
      const mapCenter = map.getCenter();
      const response = await getMLSListingSummaryPointWithinLayerData({
        lat: mapCenter.lat,
        lng: mapCenter.lng,
      });
      if (response && response.length > 0) {
        const metro = response.filter((item) => item.type === "metro");
        if (metro && metro.length > 0) {
          if (
            currentMetro &&
            currentMetro.key === metro[0].key &&
            currentMetro.name === metro[0].name
          ) {
            return;
          }
          setCurrentMetro(metro[0]);
        }
      }
    };

    const getSubmarketPolygon = async () => {
      const bounds = map.getBounds().toArray();
      const response = await fetchSubmarketPolygonData({
        lng1: bounds[0][1],
        lat1: bounds[0][0],
        lng2: bounds[1][1],
        lat2: bounds[1][0],
      });
      if (response) {
        setSubmarketPolygonData(response);
      } else {
        setSubmarketPolygonData([]);
      }
    };

    const getSubmarketBGData = async () => {
      const features = map.querySourceFeatures(`${sourceId}-submarket-BG`, {
        sourceLayer: sourceLayer,
      });

      if (features.length > 0) {
        const ids = features.map((feature) => feature.properties.Name);
        const mapCenter = map.getCenter();

        const response = await fetchSubmarketBGData({
          body: JSON.stringify(ids),
          lat: mapCenter.lat,
          lng: mapCenter.lng,
        });

        if (response) {
          setSubmarketBGData(response);
        } else {
          setSubmarketBGData([]);
        }
      } else {
        setSubmarketBGData([]);
      }
    };

    const moveEnd = () => {
      try {
        getMetro();
        if (submarketShapeType === "Polygon") {
          console.log("Fetch 1");
          getSubmarketPolygon();
        } else if (submarketShapeType === "Block Groups") {
          console.log("Fetch 2");
          getSubmarketBGData();
        }
      } catch (err) {
        console.log(err);
      }
    };

    if (submarketShapeType === "Polygon") {
      moveEnd();
    } else if (submarketShapeType === "Block Groups") {
      map.once("idle", () => {
        moveEnd();
      });
    }
    map.on("moveend", debounce(moveEnd, 1000));
  }, [map, submarketShapeType]);

  const onDataChangeWrapper = useCallback(
    (data) => {
      console.log("onDataChange: ", data);
      if (props.onDataChange) {
        props.onDataChange(data);
      }
    },
    [props.onDataChange]
  );

  return (
    <submarketContext.Provider
      value={{
        currentMetro,
        submarketBGData,
        submarketPolygonData,
        submarketFilterFn,
        setSubmarketShapeType,
        onDataChange: onDataChangeWrapper,
      }}
    >
      {/* <Source id={sourceId} type="vector" url={sourceURL}> */}
      {props.children}
      {/* </Source> */}
    </submarketContext.Provider>
  );
};

export default Context;

export const useSubmarketContext = () => {
  const context = useContext(submarketContext);
  if (!context) {
    throw new Error(
      "useSubmarketContext must be used within a submarketContext Provider"
    );
  }
  return context;
};
