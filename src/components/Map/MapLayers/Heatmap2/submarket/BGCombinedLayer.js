import React, { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import Source from "../../Source";
import Layer from "../../Layer";
import {
  sourceId,
  sourceLayer,
  minZoom,
  getFillStyle,
  submarketRanges,
  sourceURL,
} from "../constants";
import { useSubmarketContext } from "./context";
import { getSubmarketBGDomainData } from "../../../../../services/data";
import {
  getBlueSequentialColors,
  getQuantileScale,
  getCountrySubmarketScale,
  getTooltipHTML,
  getColorScaleScore,
} from "./utils";
import { initPopup, setPaintPropertySafely } from "../../../MapUtility/general";
let hoveredID = null;

let popup = initPopup();

const submarketSourceId = `${sourceId}-submarket-BG`;

const getCountryCombinedScoringFn = (combinedKeys) => (data) => {
  if (!data) return 0;
  const response = {};
  const total = combinedKeys.reduce((acc, key) => {
    if (!data[key]) return acc;

    const sc = getCountrySubmarketScale(submarketRanges(key));
    const score = getColorScaleScore(sc, data[key]);
    response[`${key}_combined_score`] = score;
    return acc + score;
  }, 0);

  const combinedScore = total / combinedKeys.length;
  response.combined_filter_score = combinedScore;
  return response;
};

const getQuantileCombinedScoringFn = (combinedKeys, metroDomain) => {
  const colorArray = getBlueSequentialColors();
  const scales = combinedKeys.reduce((acc, key) => {
    acc[key] = getQuantileScale(metroDomain[key], colorArray);
    return acc;
  }, {});

  return function (data) {
    if (!data) return 0;
    const response = {};
    const total = combinedKeys.reduce((acc, key) => {
      if (!data[key] || !scales[key]) return acc;
      const score = getColorScaleScore(scales[key], data[key]);
      response[`${key}_combined_score`] = score;
      return acc + score;
    }, 0);

    const combinedScore = total / combinedKeys.length;
    response.combined_filter_score = combinedScore;
    return response;
  };
};

const BGCombinedLayer = ({
  activeKey,
  combinedKeys,
  benchmark = "country",
  rangeType = "default",
  filterFn = () => true,
}) => {
  const fillStyle = getFillStyle(`submarket-${activeKey}-combined`);

  const { currentMetro, submarketBGData } = useSubmarketContext();

  const map = useSelector((state) => state.Map.map);
  const [combinedData, setCombinedData] = useState([]);
  const [domainData, setDomainData] = useState([]);
  const [combinedDomainData, setCombinedDomainData] = useState({});
  const [colorScaleFn, setColorScaleFn] = useState(null);

  useEffect(() => {
    if (!map) return;

    const mouseMove = (e) => {
      if (submarketBGData.length === 0) return;
      if (e.features.length === 0) return;
      if (hoveredID) {
        map.setFeatureState(
          {
            source: hoveredID,
            id: submarketSourceId,
            sourceLayer: sourceLayer,
          },
          { hover: false }
        );
      }
      hoveredID = e.features[0].id;
      map.setFeatureState(
        { source: hoveredID, id: submarketSourceId, sourceLayer: sourceLayer },
        { hover: true }
      );

      const blockId = e.features[0].properties.Name;
      const data = combinedData.find((item) => item.id === blockId);
      if (data && filterFn(data)) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const colorScore = getColorScaleScore(colorScaleFn, data[activeKey]);
        const htmlRender = getTooltipHTML(data, activeKey, colorScore);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = (e) => {
      if (hoveredID !== null) {
        map.setFeatureState(
          {
            id: hoveredID,
            source: submarketSourceId,
            sourceLayer: sourceLayer,
          },
          { hover: false }
        );
      }
      hoveredID = null;
      popup.remove();
    };

    map.on("mousemove", fillStyle.id, mouseMove);
    map.on("mouseleave", fillStyle.id, mouseLeave);
    return () => {
      map.off("mousemove", fillStyle.id, mouseMove);
      map.off("mouseleave", fillStyle.id, mouseLeave);
    };
  }, [map, combinedData, filterFn]);

  useEffect(() => {
    if (!map || !currentMetro || map.getZoom() < minZoom) return;
    if (benchmark != "metro") return;

    const mapCenter = map.getCenter();
    const fetchDomainData = async (filterKey) => {
      return await getSubmarketBGDomainData({
        type: filterKey,
        lat: mapCenter.lat,
        lng: mapCenter.lng,
      });
    };

    const fetchAllMetroDomainData = async () => {
      if (activeKey !== "rental_growth_5_years") {
        const combinedDomains = await combinedKeys
          .filter((key) => key !== "rental_growth_5_years")
          .reduce(async (acc, key) => {
            const response = await acc;
            const domains = await fetchDomainData(key);
            response[key] = domains;
            return response;
          }, {});

        setCombinedDomainData(combinedDomains);
      }
    };

    fetchAllMetroDomainData();
  }, [map, currentMetro, benchmark, combinedKeys]);

  useEffect(() => {
    if (submarketBGData.length === 0) return;

    if (benchmark === "country") {
      const scoringFn = getCountryCombinedScoringFn(combinedKeys);
      const scoredData = submarketBGData.map((data) => {
        const scores = scoringFn(data);
        return { ...data, ...scores };
      });
      setCombinedData(scoredData);
    } else if (benchmark === "metro") {
      if (
        !combinedKeys.every((key) =>
          Object.keys(combinedDomainData).includes(key)
        )
      )
        return;

      const activeDomain = [];

      const scoringFn = getQuantileCombinedScoringFn(
        combinedKeys,
        combinedDomainData
      );

      const newData = submarketBGData.map((data) => {
        const scores = scoringFn(data);
        const res = {
          ...data,
          ...scores,
        };
        activeDomain.push(res[activeKey]);
        return res;
      });

      setCombinedData(newData);
      setDomainData(activeDomain);
    }
  }, [submarketBGData, combinedDomainData, combinedKeys]);

  useEffect(() => {
    if (submarketBGData.length === 0) return;
    const expression = ["match", ["get", "Name"]];

    const colorArray =
      rangeType === "default"
        ? submarketRanges(activeKey).map((r) => r.color)
        : getBlueSequentialColors();

    const scale =
      benchmark === "metro"
        ? getQuantileScale(domainData, colorArray)
        : getCountrySubmarketScale(submarketRanges(activeKey), colorArray);

    if (scale) {
      const filteredData = combinedData.filter((data) => filterFn(data));

      filteredData.forEach((item) => {
        const value = item[activeKey];
        const color = scale(value);
        expression.push(item.id, color != null ? color : "transparent");
      });

      setColorScaleFn(() => scale);
    }

    expression.push("transparent");
    if (expression.length > 3) {
      if (map.getLayer(fillStyle.id)) {
        setPaintPropertySafely(map, fillStyle.id, "fill-color", expression);
        setPaintPropertySafely(map, fillStyle.id, "fill-opacity", [
          "case",
          ["boolean", ["feature-state", "hover"], false],
          0.6,
          0.8,
        ]);
      }
    }
  }, [combinedData, domainData, combinedDomainData, filterFn]);

  if (activeKey === "rental_growth_5_years") return null;
  return (
    <Source id={submarketSourceId} type="vector" url={sourceURL}>
      <Layer {...fillStyle} />
    </Source>
  );
};

export default BGCombinedLayer;
