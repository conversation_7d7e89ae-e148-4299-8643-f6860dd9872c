import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { useSubmarketContext } from "./context";
import Source from "../../Source";
import Layer from "../../Layer";
import {
  sourceId,
  sourceLayer,
  minZoom,
  getPolygonFillStyle,
  submarketRanges,
} from "../constants";
import { geojsonTemplate } from "../../../../../constants";
import {
  getBlueSequentialColors,
  getCountrySubmarketScale,
  getQuantileScale,
  getTooltipHTML,
} from "./utils";
import { getSubmarketDomainData } from "../../../../../services/data";
import { getColorScaleScore } from "./utils";
import { initPopup, setPaintPropertySafely } from "../../../MapUtility/general";

let hoveredID = null;

let popup = initPopup();
const defaultFilterFn = () => true;

let baseOpacity = 0.6;

const SubmarketPolygon = ({
  activeKey,
  rangeType,
  benchmark,
  filterFn = defaultFilterFn,
}) => {
  const fillStyle = getPolygonFillStyle(activeKey);

  const map = useSelector((state) => state.Map.map);

  const {
    currentMetro,
    submarketPolygonData,
    setSubmarketShapeType,
    onDataChange,
  } = useSubmarketContext();

  const [geojson, setGeojson] = useState(geojsonTemplate);
  const [domainData, setDomainData] = useState({});
  const [colorScaleFn, setColorScaleFn] = useState(null);

  useEffect(() => {
    if (!map) return;
    setSubmarketShapeType("Polygon");
    onDataChange([]);

    const layers = map.getStyle().layers;
    let firstLayerId;
    for (const layer of layers) {
      if (
        layer.source &&
        layer.source !== "composite" &&
        !layer.source.includes("mapbox")
      ) {
        firstLayerId = layer.id;
        break;
      }
    }
    map.moveLayer(fillStyle.id, firstLayerId);
  }, []);

  useEffect(() => {
    if (
      (!activeKey || !submarketRanges(activeKey)) &&
      submarketPolygonData.length > 0
    ) {
      onDataChange(submarketPolygonData);
    }

    if (submarketPolygonData.length > 0) {
      const convertedGeojson = structuredClone(geojsonTemplate);
      convertedGeojson.features = submarketPolygonData.reduce((acc, cur) => {
        if (filterFn(cur)) {
          const { geom, ...properties } = cur;
          acc.push({
            type: "Feature",
            properties: properties,
            geometry: geom,
          });
        }
        return acc;
      }, []);

      setGeojson(convertedGeojson);
    }
  }, [activeKey, submarketPolygonData, filterFn]);

  useEffect(() => {
    if (!map) return;

    const mouseMove = (e) => {
      if (submarketPolygonData.length === 0) return;
      if (e.features.length === 0) return;
      if (hoveredID) {
        map.setFeatureState(
          { source: sourceId, id: hoveredID, sourceLayer: sourceLayer },
          { hover: false }
        );
      }
      hoveredID = e.features[0].id;
      map.setFeatureState(
        { source: sourceId, id: hoveredID, sourceLayer: sourceLayer },
        { hover: true }
      );

      const data = e.features[0].properties;
      const coordinates = [e.lngLat.lng, e.lngLat.lat];
      const colorScore = getColorScaleScore(colorScaleFn, data[activeKey]);
      const htmlRender = getTooltipHTML(data, activeKey, colorScore);
      popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
    };

    const mouseLeave = (e) => {
      if (hoveredID !== null) {
        map.setFeatureState(
          { id: hoveredID, source: sourceId, sourceLayer: sourceLayer },
          { hover: false }
        );
      }
      hoveredID = null;
      popup.remove();
    };

    const updateOpacity = ({ payload }) => {
      const { heatmapOpacity } = payload;
      baseOpacity = heatmapOpacity / 100;
      if (map.getLayer(fillStyle.id)) {
        map.setPaintProperty(fillStyle.id, "fill-opacity", [
          "case",
          ["boolean", ["feature-state", "hover"], false],
          0.9,
          baseOpacity,
        ]);
      }
    };

    map.on("heatmap.menu.updateHeatmapOpacity", updateOpacity);
    map.on("mousemove", fillStyle.id, mouseMove);
    map.on("mouseleave", fillStyle.id, mouseLeave);
    return () => {
      map.off("mousemove", fillStyle.id, mouseMove);
      map.off("mouseleave", fillStyle.id, mouseLeave);
    };
  }, [map, submarketPolygonData]);

  useEffect(() => {
    if (!map || !currentMetro || map.getZoom() < minZoom) return;
    if (benchmark != "metro") return;

    const fetchDomainData = async () => {
      const mapCenter = map.getCenter();
      const response = await getSubmarketDomainData({
        type: activeKey,
        lat: mapCenter.lat,
        lng: mapCenter.lng,
      });

      setDomainData((prevState) => {
        return {
          ...prevState,
          [activeKey]: response ? response : [],
        };
      });
    };
    fetchDomainData();
  }, [map, currentMetro, activeKey, benchmark]);

  useEffect(() => {
    if (!activeKey || !submarketRanges(activeKey)) return;

    const colorArray =
      rangeType === "default"
        ? submarketRanges(activeKey).map((r) => r.color)
        : getBlueSequentialColors();

    if (benchmark === "country") {
      const scale = getCountrySubmarketScale(
        submarketRanges(activeKey),
        colorArray
      );
      if (scale) {
        setColorScaleFn(() => scale);
        renderToMap(scale, activeKey);
      }
    } else if (benchmark === "metro" && domainData[activeKey]) {
      const scale = getQuantileScale(domainData[activeKey], colorArray);
      if (scale) {
        setColorScaleFn(() => scale);
        renderToMap(scale, activeKey);
      }
    }
  }, [activeKey, rangeType, benchmark, domainData]);

  const renderToMap = (scale, activeKey) => {
    if (scale) {
      const colorStyle = scale.quantiles().reduce((acc, cur, i) => {
        const color = scale.range()[i];
        acc.push(cur, color);
        return acc;
      }, []);

      setPaintPropertySafely(map, fillStyle.id, "fill-color", [
        "interpolate",
        ["linear"],
        ["get", activeKey],
        ...colorStyle,
      ]);
      setPaintPropertySafely(map, fillStyle.id, "fill-opacity", [
        "case",
        ["boolean", ["feature-state", "hover"], false],
        0.9,
        baseOpacity,
      ]);

      setColorScaleFn(() => scale);
    }
  };

  return (
    <Source id={`${sourceId}-submarket-polygon`} type="geojson" data={geojson}>
      <Layer {...fillStyle} />
    </Source>
  );
};

export default SubmarketPolygon;
