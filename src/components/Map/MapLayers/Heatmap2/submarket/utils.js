import * as d3 from "d3";
import { renderToString } from "react-dom/server";
import { submarketRanges } from "../constants";

export const generateClassRange = (num) => {
  const step = 1 / (num - 1);
  const range = Array.from({ length: num }, (_, i) => i * step);
  return range;
};

export const getBlueSequentialColors = () => {
  const scale = d3.scaleSequential(d3.interpolateBlues);
  return generateClassRange(10).map((q) => scale(q));
};

export const getQuantileScale = (data, color) => {
  const scale = d3.scaleQuantile().domain(data).range(color);
  return scale;
};

export const getColorScaleScore = (scale, value) =>
  scale ? scale.range().indexOf(scale(value)) + 1 : 0;

export const getCountrySubmarketScale = (range, color) => {
  if (range) {
    function scale(value) {
      if (value === null) return value;
      for (let i = 0; i < range.length; i++) {
        if (i + 1 < range.length) {
          if (value >= range[i].value && value < range[i + 1].value)
            return color ? color[i] : range[i].color;
        } else {
          if (value >= range[i].value) return color ? color[i] : range[i].color;
        }
      }
      // if below range
      return color ? color[0] : range[0].color;
    }
    scale.quantiles = function () {
      const result = range.map((r) => r.value);
      result.shift();
      // result.pop();
      return result;
    };
    scale.range = function () {
      if (color) {
        return color;
      }
      const result = range.map((r) => r.color);
      return result;
    };
    return scale;
  }
};

export const titleFormatter = (text) =>
  ({
    vacancy_rate: "Vacancy Rate (stabilized)",
    market_effective_rent_growth_12_months:
      "Market Effective Rent Growth 12 Months",
    market_effective_rent_sf: "Market Effective Rent/Sqft",
    market_effective_rent_unit: "Market Effective Rent/Unit",
    rental_growth_5_years: "Rent CAGR trailing 5 years",
    combined_filter_score: "Combined Color Score",
  }[text] || text);

const isValidKey = (key) => {
  return (
    [
      "vacancy_rate",
      "market_effective_rent_growth_12_months",
      "market_effective_rent_sf",
      "market_effective_rent_unit",
      "rental_growth_5_years",
    ].includes(key) || key.includes("_combined_score")
  );
};

export const valueFormatter = (title, value) => {
  if (isValidKey(title) && (value == null || !isFinite(value))) {
    console.log("submarket valueFormatter", title, value);
    return "--";
  }
  const formatDollar = (num) =>
    num.toLocaleString("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });

  const formatPercentage = (num) => `${Math.round(num * 10) / 10 || 0}%`;
  const formatRoundNumber = (num) => Math.round(num) || 0;
  if (
    [
      "vacancy_rate",
      "market_effective_rent_growth_12_months",
      "five_year_pop_growth",
      "five_year_income_growth",
      "rental_growth_5_years",
    ].includes(title)
  ) {
    return formatPercentage(value);
  } else if (
    ["market_effective_rent_sf", "market_effective_rent_unit"].includes(title)
  ) {
    return formatDollar(value);
  } else if (
    title.includes("combined_filter_score") ||
    title.includes("_combined_score")
  ) {
    return Math.round(value * 10) / 10;
  }
  return value;
};

export const getTooltipHTML = (data, active, colorScore) => {
  if (Object.keys(data).includes("geogname")) {
    const ignore = ["geogname", active, "color_score"];
    return renderToString(
      <div style={{ padding: "10px" }}>
        <div>
          <span>
            Location: <strong>{data.geogname}</strong>
          </span>
        </div>

        <div>
          <strong>{titleFormatter(active)}: </strong>
          <strong>{valueFormatter(active, data[active])}</strong>
        </div>

        {Object.keys(data)
          .filter((type) => !ignore.includes(type))
          .map((type) => {
            return (
              <div key={type}>
                <span>{titleFormatter(type)}: </span>
                <strong>{valueFormatter(type, data[type])}</strong>
              </div>
            );
          })}
        <div>
          <span>{titleFormatter(active)}: </span>
          <strong>{valueFormatter(active, data[active])}</strong>
        </div>
        <div>
          <span>Color Score: </span>
          <strong>{colorScore}</strong>
        </div>
      </div>
    );
  } else {
    if (!Object.keys(data).includes("combined_filter_score")) {
      const ignore = ["id", active, "color_score"];
      return renderToString(
        <div style={{ padding: "10px" }}>
          <div>
            <span>
              Id: <strong>{data.id}</strong>
            </span>
          </div>

          <div>
            <strong>{titleFormatter(active)}: </strong>
            <strong>{valueFormatter(active, data[active])}</strong>
          </div>

          {Object.keys(data)
            .filter((type) => !ignore.includes(type))
            .map((type) => {
              return (
                <div key={type}>
                  <span>{titleFormatter(type)}: </span>
                  <strong>{valueFormatter(type, data[type])}</strong>
                </div>
              );
            })}
          <div>
            <span>Color Score: </span>
            <strong>{colorScore}</strong>
          </div>
        </div>
      );
    } else {
      const ignore = ["id", active, "color_score", "combined_filter_score"];
      return renderToString(
        <div style={{ padding: "10px" }}>
          <div>
            <span>
              Id: <strong>{data.id}</strong>
            </span>
          </div>

          <div>
            <strong>{titleFormatter(active)}: </strong>

            {Object.keys(data).includes(`${active}_combined_score`) ? (
              <span>
                {valueFormatter(active, data[active])}{" "}
                <strong>
                  (
                  {valueFormatter(
                    `${active}_combined_score`,
                    data[`${active}_combined_score`]
                  )}
                  )
                </strong>
              </span>
            ) : (
              <strong>{valueFormatter(active, data[active])}</strong>
            )}
          </div>

          {Object.keys(data)
            .filter(
              (type) =>
                !ignore.includes(type) && !type.includes("_combined_score")
            )
            .map((type) => {
              return (
                <div key={type}>
                  <span>{titleFormatter(type)}: </span>
                  {Object.keys(data).includes(`${type}_combined_score`) ? (
                    <span>
                      {valueFormatter(type, data[type])}{" "}
                      <strong>
                        (
                        {valueFormatter(
                          `${type}_combined_score`,
                          data[`${type}_combined_score`]
                        )}
                        )
                      </strong>
                    </span>
                  ) : (
                    <strong>{valueFormatter(type, data[type])}</strong>
                  )}
                </div>
              );
            })}
          {active != "combined_filter_score" && (
            <div>
              <span>Combined Color Score: </span>
              <strong>
                {valueFormatter(
                  "combined_filter_Score",
                  data["combined_filter_score"]
                )}
              </strong>
            </div>
          )}
        </div>
      );
    }
  }
};
