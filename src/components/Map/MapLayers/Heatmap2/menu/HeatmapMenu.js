import { useState, useEffect, useCallback } from "react";

import { Checkbox, InputNumber, Segmented, Slider, Table, Switch } from "antd";
import { titleFormatter, valueFormatter } from "../demographics/utils";
import {
  titleFormatter as subTitleFormatter,
  valueFormatter as subValueFormatter,
} from "../submarket/utils";
import { FaFilter } from "@react-icons/all-files/fa/FaFilter";

import isEqual from "lodash.isequal";
// import { useSelector } from "react-redux";

import { LoadingOutlined } from "@ant-design/icons";
import { Spin } from "antd";

const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

const mergeTitleFormatter = (title) => {
  const newTitle1 = titleFormatter(title);
  const newTitle2 = subTitleFormatter(title);
  if (newTitle1 !== title) return newTitle1;
  if (newTitle2 !== title) return newTitle2;
  return title;
};

const mergeValueFormatter = (title, value) => {
  const newValue1 = valueFormatter(title, value);
  const newValue2 = subValueFormatter(title, value);
  if (newValue1 !== value) return newValue1;
  if (newValue2 !== value) return newValue2;
  return value;
};

const HeatmapMenu = ({
  map,
  // data,
  // activeKey,
  // setActiveKey,
  // combinedFilter,
  // setCombinedFilter,
  // benchmark,
  // setBenchmark,
  // rangeType,
  // setRangeType,
  // setFilterFn,
}) => {
  // const map = useSelector((state) => state.Map.map);

  const [data, setData] = useState({});
  const [activeKey, setActiveKey] = useState(undefined);
  const [combinedFilter, setCombinedFilter] = useState({
    enabled: false,
    keys: [],
  });
  const [benchmark, setBenchmark] = useState("metro");
  const [rangeType, setRangeType] = useState("default");

  const [filterFn, setFilterFn] = useState(() => () => true);

  const [tableData, setTableData] = useState([]);
  const [filterData, setFilterData] = useState({});

  const [submarketShapeType, setSubmarketShapeType] = useState("Polygon"); // polygon or blockgroup

  const [isLoading, setIsLoading] = useState(false);

  const [heatmapType, setHeatmapType] = useState("demographics");

  const [heatmapOpacity, setHeatmapOpacity] = useState(60);

  useEffect(() => {
    if (!map) return;

    const getHeatmapUpdateData = ({ payload }) => {
      const { data } = payload;
      setData(data);
    };

    const updateIsLoading = ({ payload }) => {
      setIsLoading(payload.isLoading);
    };

    map.on("heatmap.summaryUpdate", getHeatmapUpdateData);
    map.on("heatmap.isLoading", updateIsLoading);
    return () => {
      map.off("heatmap.summaryUpdate", getHeatmapUpdateData);
      map.off("heatmap.isLoading", updateIsLoading);
    };
  }, [map]);

  useEffect(() => {
    if (!data || Object.keys(data).length === 0) {
      return;
    }

    const filterKeys = Object.keys(data);

    if (filterKeys.includes("median_hh_income")) {
      setHeatmapType("demographics");
    } else if (filterKeys.includes("vacancy_rate")) {
      setHeatmapType("submarket");
    }

    if (activeKey === undefined || !filterKeys.includes(activeKey)) {
      // initialise
      updateActiveKey(filterKeys[0]);
      updateCombinedFilter({ enabled: false, keys: [] });
      updateBenchmark("country");
      updateRangeType("default");
      updateFilterFn(() => () => true);
    }

    setTableData(
      filterKeys.map((key) => ({
        infoType: key,
        min: data[key].min,
        median: data[key].median,
        max: data[key].max,
      }))
    );
    setFilterData({
      ...filterData,
      ...filterKeys.reduce((acc, key) => {
        if (checkIfFiltered(key)) return acc;

        const min = (acc[key] = {
          min: filterData[key]
            ? Math.min(filterData[key].min, data[key].min)
            : data[key].min,
          max: filterData[key]
            ? Math.max(filterData[key].max, data[key].max)
            : data[key].max,
          enabled: filterData[key] ? filterData[key].enabled : false,
        });
        return acc;
      }, {}),
    });
  }, [data]);

  useEffect(() => {
    if (combinedFilter.enabled) {
      const newCombinedKeys = Object.keys(filterData).reduce((acc, key) => {
        if (key.includes("combined")) return acc;
        if (checkIfFiltered(key)) acc.push(key);
        return acc;
      }, []);
      if (!isEqual(combinedFilter.keys, newCombinedKeys)) {
        updateCombinedFilter({
          ...combinedFilter,
          keys: newCombinedKeys,
        });
      }
    } else {
      if (activeKey === "combined_filter_score") {
        updateActiveKey("median_hh_income");
      }
      if (combinedFilter.keys.length > 0) {
        updateCombinedFilter({ ...combinedFilter, keys: [] });
      }
    }
  }, [filterData, combinedFilter.enabled]);

  useEffect(() => {
    const newFilterFn = (data) => {
      const filterKeys = Object.keys(filterData);
      return filterKeys.every((key) => {
        if (!filterData[key].enabled) return true;
        if (data[key] < filterData[key].min) return false;
        if (data[key] > filterData[key].max) return false;
        return true;
      });
    };

    updateFilterFn(newFilterFn);
  }, [filterData]);

  const checkIfFiltered = useCallback(
    (filterKey) => {
      if (filterData[filterKey] && filterData[filterKey].enabled) return true;
      // if (
      //   filterData[filterKey] &&
      //   data[filterKey] &&
      //   filterData[filterKey].min !== data[filterKey].min
      // )
      //   return true;
      // if (
      //   filterData[filterKey] &&
      //   data[filterKey] &&
      //   filterData[filterKey].max !== data[filterKey].max
      // )
      //   return true;
    },
    [filterData, data]
  );

  // map.on("heatmap.menu.updateActiveKey", updateActiveKey);
  // map.on("heatmap.menu.updateCombinedFilter", updateCombinedFilter);
  // map.on("heatmap.menu.updateBenchmark", updateBenchmark);
  // map.on("heatmap.menu.updateRangeType", updateRangeType);
  // map.on("heatmap.menu.updateFilterFn", setFilterFn);

  const updateActiveKey = useCallback(
    (key) => {
      if (!map) return;
      if (activeKey === key) return;
      setActiveKey(key);
      map.fire("heatmap.menu.updateActiveKey", {
        payload: { activeKey: key },
      });
    },
    [map, activeKey]
  );

  const updateCombinedFilter = useCallback(
    (filter) => {
      if (!map) return;
      setCombinedFilter(filter);
      map.fire("heatmap.menu.updateCombinedFilter", {
        payload: { combinedFilter: filter },
      });
    },
    [map]
  );

  const updateBenchmark = useCallback(
    (benchmarkValue) => {
      if (!map) return;
      setBenchmark(benchmarkValue);
      map.fire("heatmap.menu.updateBenchmark", {
        payload: { benchmark: benchmarkValue },
      });
    },
    [map]
  );

  const updateRangeType = useCallback(
    (rangeTypeValue) => {
      if (!map) return;
      setRangeType(rangeTypeValue);
      map.fire("heatmap.menu.updateRangeType", {
        payload: { rangeType: rangeTypeValue },
      });
    },
    [map]
  );

  const updateFilterFn = useCallback(
    (newFilterFn) => {
      if (!map) return;
      setFilterFn(() => newFilterFn);
      map.fire("heatmap.menu.updateFilterFn", {
        payload: { filterFn: () => newFilterFn },
      });
    },
    [map]
  );

  const updateSubmarketShapeType = useCallback(
    (value) => {
      if (!map) return;
      setSubmarketShapeType(value);
      map.fire("heatmap.menu.updateSubmarketShapeType", {
        payload: { submarketShapeType: value },
      });
    },
    [map]
  );

  const updateHeatmapOpacity = useCallback(
    (newOpacity) => {
      if (!map) return;
      setHeatmapOpacity(newOpacity);
      map.fire("heatmap.menu.updateHeatmapOpacity", {
        payload: { heatmapOpacity: newOpacity },
      });
    },
    [map]
  );

  if (!map) return null;

  return (
    <div className="flex flex-col gap-2 h-full">
      <div className="flex flex-row gap-2 items-center">
        {/* {heatmapType === "demographics" ? (
          <h2>Demographic Heatmap</h2>
        ) : (
          <h2>Submarket Heatmap</h2>
        )} */}
        {/* {isLoading && <Spin indicator={antIcon} />} */}
      </div>
      <div className="">
        <Table
          rowKey={(record) => record.infoType}
          rowClassName={(record) =>
            `cursor-pointer hover:bg-slate-200 ${
              record.infoType === activeKey ? "bg-blue-200" : ""
            }`
          }
          columns={[
            {
              title: "Information",
              dataIndex: "infoType",
              render: (text) => {
                return (
                  <div>
                    {checkIfFiltered(text) && (
                      <span>
                        <FaFilter />
                      </span>
                    )}
                    <span>{mergeTitleFormatter(text)}</span>
                  </div>
                );
              },
              children: data.combined_filter_score
                ? [
                    {
                      key: "combined_filter_score",
                      title: (
                        <div
                          className={`cursor-pointer hover:bg-slate-100 ${
                            activeKey === "combined_filter_score"
                              ? "bg-blue-200"
                              : ""
                          }`}
                        >
                          {checkIfFiltered("combined_filter_score") && (
                            <span>
                              <FaFilter />
                            </span>
                          )}
                          <span>
                            {mergeTitleFormatter("combined_filter_score")}
                          </span>
                        </div>
                      ),
                      dataIndex: "infoType",
                      render: (text) => {
                        return (
                          <div>
                            {checkIfFiltered(text) && (
                              <span>
                                <FaFilter />
                              </span>
                            )}
                            <span>{mergeTitleFormatter(text)}</span>
                          </div>
                        );
                      },
                    },
                  ]
                : [],
            },
            {
              title: "Min",
              dataIndex: "min",
              width: 100,
              render: (text, record) =>
                mergeValueFormatter(record.infoType, text),
              children: data.combined_filter_score
                ? [
                    {
                      title: mergeValueFormatter(
                        "combined_filter_score",
                        data.combined_filter_score.min
                      ),
                      dataIndex: "min",
                      width: 100,
                      render: (text, record) =>
                        mergeValueFormatter(record.infoType, text),
                    },
                  ]
                : [],
            },
            {
              title: "Median",
              dataIndex: "median",
              width: 100,
              render: (text, record) =>
                mergeValueFormatter(record.infoType, text),
              children: data.combined_filter_score
                ? [
                    {
                      title: mergeValueFormatter(
                        "combined_filter_score",
                        data.combined_filter_score.median
                      ),
                      dataIndex: "median",
                      width: 100,
                      render: (text, record) =>
                        mergeValueFormatter(record.infoType, text),
                    },
                  ]
                : [],
            },
            {
              title: "Max",
              dataIndex: "max",
              width: 100,
              render: (text, record) =>
                mergeValueFormatter(record.infoType, text),
              children: data.combined_filter_score
                ? [
                    {
                      title: mergeValueFormatter(
                        "combined_filter_score",
                        data.combined_filter_score.max
                      ),
                      dataIndex: "max",
                      width: 100,
                      render: (text, record) =>
                        mergeValueFormatter(record.infoType, text),
                    },
                  ]
                : [],
            },
          ]}
          dataSource={tableData.filter(
            (item) => item.infoType !== "combined_filter_score"
          )}
          scroll={{ y: 400 }}
          bordered={true}
          // pagination={{ size: "small", position: ["bottomCenter"] }}
          size="small"
          pagination={false}
          onRow={(record, rowIndex) => ({
            onClick: () => updateActiveKey(record.infoType),
          })}
          onHeaderRow={(column, index) => {
            return {
              onClick: () => {
                if (column && column.length > 0) {
                  if (
                    column[0].key &&
                    column[0].key === "combined_filter_score"
                  ) {
                    updateActiveKey("combined_filter_score");
                  }
                }
              },
            };
          }}
          // loading={isLoading}
        />
        <div className="w-full h-[2px] bg-slate-200 my-2" />
      </div>
      <div className="flex flex-col justify-center min-h-[200px] h-[250px]">
        <div className="">
          <h3 className="text-center">{mergeTitleFormatter(activeKey)}</h3>
          <div className="flex flex-row flex-wrap gap-4 justify-center">
            <div className="flex flex-col gap-4 justify-center">
              {heatmapType === "submarket" && (
                <div className="flex flex-row items-center gap-2">
                  <Segmented
                    value={submarketShapeType}
                    options={["Polygon", "Block Groups"]}
                    onChange={(value) => updateSubmarketShapeType(value)}
                  />
                </div>
              )}
              {heatmapType === "demographics" && (
                <div className="flex flex-row items-center gap-2">
                  <Switch
                    checked={combinedFilter.enabled}
                    onChange={(checked) =>
                      updateCombinedFilter({
                        ...combinedFilter,
                        enabled: checked,
                      })
                    }
                    size="small"
                  />
                  <span>Combine Filter Score</span>
                </div>
              )}
              <div className="flex flex-row justify-between items-center">
                <span>Benchmark: </span>
                <Segmented
                  value={benchmark.charAt(0).toUpperCase() + benchmark.slice(1)}
                  options={["Country", "Metro"]}
                  onChange={(value) => updateBenchmark(value.toLowerCase())}
                />
              </div>
              <div className="flex flex-row justify-between items-center">
                <span>Color Type: </span>
                <Segmented
                  value={rangeType.charAt(0).toUpperCase() + rangeType.slice(1)}
                  options={["Default", "Blues"]}
                  onChange={(value) => updateRangeType(value.toLowerCase())}
                />
              </div>
            </div>
            {Object.keys(filterData).length > 0 && (
              <div className="w-1/2 flex flex-col justify-center ">
                <div>
                  <span>Opacity:</span>
                  <Slider
                    value={heatmapOpacity}
                    min={0}
                    max={100}
                    onChange={(value) => setHeatmapOpacity(value)}
                    onAfterChange={(value) => updateHeatmapOpacity(value)}
                  />
                </div>
                <div className="h-[2px] w-full my-1 mb-4 px-20 bg-gray-100 bg-clip-content" />
                <div>
                  <div className="flex flex-row justify-center gap-2 items-center">
                    <div>
                      <Checkbox
                        checked={filterData[activeKey].enabled}
                        onChange={(e) =>
                          setFilterData({
                            ...filterData,
                            [activeKey]: {
                              ...filterData[activeKey],
                              enabled: e.target.checked,
                            },
                          })
                        }
                      >
                        Enabled
                      </Checkbox>
                    </div>
                    <div>
                      <span>Min: </span>
                      <InputNumber
                        disabled={!filterData[activeKey].enabled}
                        value={filterData[activeKey].min}
                        min={Math.min(
                          data[activeKey] ? data[activeKey].min : 0,
                          activeKey === "combined_filter_score"
                            ? 0
                            : filterData[activeKey].min
                        )}
                        max={Math.max(
                          data[activeKey] ? data[activeKey].max : 0,
                          activeKey === "combined_filter_score"
                            ? 10
                            : filterData[activeKey].max
                        )}
                        onChange={(value) => {
                          setFilterData({
                            ...filterData,
                            [activeKey]: {
                              ...filterData[activeKey],
                              min: value,
                            },
                          });
                        }}
                      />
                    </div>
                    <div>
                      <span>Max: </span>
                      <InputNumber
                        disabled={!filterData[activeKey].enabled}
                        value={filterData[activeKey].max}
                        min={Math.min(
                          data[activeKey] ? data[activeKey].min : 0,
                          activeKey === "combined_filter_score"
                            ? 0
                            : filterData[activeKey].min
                        )}
                        max={Math.max(
                          data[activeKey] ? data[activeKey].max : 0,
                          activeKey === "combined_filter_score"
                            ? 10
                            : filterData[activeKey].max
                        )}
                        formatter={(value) =>
                          mergeValueFormatter(activeKey, value)
                        }
                        onChange={(value) => {
                          setFilterData({
                            ...filterData,
                            [activeKey]: {
                              ...filterData[activeKey],
                              max: value,
                            },
                          });
                        }}
                      />
                    </div>
                  </div>
                  <div>
                    <Slider
                      disabled={!filterData[activeKey].enabled}
                      range={{ draggableTrack: true }}
                      min={data[activeKey] ? data[activeKey].min : 0}
                      max={data[activeKey] ? data[activeKey].max : 0}
                      value={[
                        filterData[activeKey].min,
                        filterData[activeKey].max,
                      ]}
                      tooltip={{
                        formatter: (value) =>
                          mergeValueFormatter(activeKey, value),
                      }}
                      onChange={(value) => {
                        setFilterData({
                          ...filterData,
                          [activeKey]: {
                            ...filterData[activeKey],
                            min: value[0],
                            max: value[1],
                          },
                        });
                      }}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeatmapMenu;
