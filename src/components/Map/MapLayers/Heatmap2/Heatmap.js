import { useState, useEffect, useCallback, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import DemographicsContext from "./demographics/context";
import DemographicLayer from "./demographics/DemographicLayer";

import SubmarketContext from "./submarket/context";
import SubmarketBG from "./submarket/SubmarketBG";
import SubmarketPolygon from "./submarket/SubmarketPolygon";
import BGCombinedLayer from "./submarket/BGCombinedLayer";

import HeatmapMenu from "./menu/HeatmapMenu";

import Draggable from "react-draggable";
import { Modal } from "antd";

import { useMap } from "../../MapProvider";

function calculateMedian(numbers) {
  const sorted = Array.from(numbers).sort((a, b) => a - b);
  const middle = Math.floor(sorted.length / 2);

  if (sorted.length % 2 === 0) {
    return (sorted[middle - 1] + sorted[middle]) / 2;
  }

  return sorted[middle];
}

const filters = [
  "median_hh_income",
  "five_year_pop_growth",
  "bachelors_and_above",
  "median_age",
  "household_size",
  "population_density",
  "fifty_five_plus",
  "household_growth",
  "rent_vs_owner_percentage",
  "rent_vs_own",
  "median_home_value",
  "median_rent",
  "rental_growth_5_years",
  "crime_score",
  "combined_filter_score",
];

const Heatmap = () => {
  // const activeKey = "vacancy_rate";
  // const activeKey = "market_effective_rent_growth_12_months";
  // const activeKey = "market_effective_rent_sf";
  // const activeKey = "market_effective_rent_unit";
  // const activeKey = "rental_growth_5_years";
  // const activeKey = "combined_filter_score";
  const { setActiveLayers } = useMap();

  const map = useSelector((state) => state.Map.map);
  const heatmapType = useSelector((state) => state.Map.heatmapType);
  const devMode = useSelector((state) => state.Map.devMode);
  const dispatch = useDispatch();

  const [open, setOpen] = useState(false);
  const [heatmapSummaryData, setHeatmapSummaryData] = useState({});
  const [activeKey, setActiveKey] = useState(undefined);
  const [benchmark, setBenchmark] = useState(undefined);
  const [rangeType, setRangeType] = useState(undefined);
  const [combinedFilter, setCombinedFilter] = useState({
    enabled: false,
    keys: [],
  });
  const [filterFn, setFilterFn] = useState(() => () => true);

  const [submarketShapeType, setSubmarketShapeType] = useState("Polygon"); // polygon or blockgroup
  const [disabled, setDisabled] = useState(true);
  const [bounds, setBounds] = useState({
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  });
  const draggleRef = useRef(null);

  const onStart = (_event, uiData) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    if (!targetRect) {
      return;
    }
    setBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };

  useEffect(() => {
    if (!map) return;

    try {
      const updateHeatmapType = ({ payload }) => {
        const type = payload.heatmapType;

        setActiveLayers((prevState) => {
          if (type === "submarket" && !prevState.includes("submarket")) {
            return [...prevState, "submarket"];
          } else if (
            type === "demographics" &&
            !prevState.includes("demographics")
          ) {
            return [...prevState, "demographics"];
          } else if (type === null) {
            return prevState.filter(
              (layer) => layer !== "submarket" && layer !== "demographics"
            );
          }
          return prevState;
        });

        dispatch({ type: "Map/saveState", payload: { heatmapType: type } });
      };

      // const updateHeatmapData = ({ payload }) => {
      //   const { data } = payload;
      //   setHeatmapSummaryData(data);
      // };

      const updateActiveKey = ({ payload }) => {
        const { activeKey } = payload;
        setActiveKey(activeKey);
      };

      const updateCombinedFilter = ({ payload }) => {
        const { enabled, keys } = payload.combinedFilter;
        setCombinedFilter({ enabled, keys });
      };

      const updateBenchmark = ({ payload }) => {
        setBenchmark(payload.benchmark);
      };

      const updateRangeType = ({ payload }) => {
        setRangeType(payload.rangeType);
      };

      const updateFilterFn = ({ payload }) => {
        setFilterFn(payload.filterFn);
      };

      const updateSubmarketShapeType = ({ payload }) => {
        setSubmarketShapeType(payload.submarketShapeType);
      };

      map.on("heatmap.updateType", updateHeatmapType);
      map.on("heatmap.menu.updateActiveKey", updateActiveKey);
      map.on("heatmap.menu.updateCombinedFilter", updateCombinedFilter);
      map.on("heatmap.menu.updateBenchmark", updateBenchmark);
      map.on("heatmap.menu.updateRangeType", updateRangeType);
      map.on("heatmap.menu.updateFilterFn", updateFilterFn);
      map.on("heatmap.menu.updateSubmarketShapeType", updateSubmarketShapeType);
      return () => {
        map.off("heatmap.updateType", updateHeatmapType);
      };
    } catch (err) {
      console.log("err: ", err);
    }
  }, [map]);

  console.log("heatmapType: ", heatmapType);

  console.log("submarketShapeType: ", submarketShapeType);

  const onCancel = useCallback(() => {
    setActiveLayers((prevState) =>
      prevState.filter(
        (layer) => layer !== "demographics" && layer !== "submarket"
      )
    );
    dispatch({ type: "Map/saveState", payload: { heatmapType: null } });
  }, []);

  console.log("heatmapType: ", heatmapType);

  return (
    <>
      {/* <button onClick={() => setOpen(!open)}>Open Heatmap</button> */}

      {/* {process.env.NODE_ENV === "development" && devMode && ( */}
      <Modal
        title={
          <div
            style={{
              width: "100%",
              cursor: "move",
            }}
            onMouseOver={() => {
              if (disabled) {
                setDisabled(false);
              }
            }}
            onMouseOut={() => {
              setDisabled(true);
            }}
            // fix eslintjsx-a11y/mouse-events-have-key-events
            // https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/master/docs/rules/mouse-events-have-key-events.md
            onFocus={() => {}}
            onBlur={() => {}}
            // end
          >
            Heatmap
          </div>
        }
        width={700}
        open={heatmapType != null}
        onCancel={() => onCancel()}
        footer={false}
        modalRender={(modal) => (
          <Draggable
            disabled={disabled}
            bounds={bounds}
            nodeRef={draggleRef}
            onStart={(event, uiData) => onStart(event, uiData)}
          >
            <div ref={draggleRef}>{modal}</div>
          </Draggable>
        )}
        mask={false}
        wrapClassName="pointer-events-none"
      >
        <HeatmapMenu
          map={map}
          // data={heatmapSummaryData}
          // activeKey={activeKey}
          // setActiveKey={setActiveKey}
          // combinedFilter={combinedFilter}
          // setCombinedFilter={setCombinedFilter}
          // benchmark={benchmark}
          // setBenchmark={setBenchmark}
          // rangeType={rangeType}
          // setRangeType={setRangeType}
          // setFilterFn={setFilterFn}
        />
      </Modal>
      {/* <DraggableModalProvider>
        <DraggableModal
          open={heatmapType != null}
          onCancel={() => onCancel()}
          footer={false}
          zIndex={1000}
          title="Heatmap"
          mask={false}
          style={{ padding: "20px" }}
          className={"chainStoreMenu"}
          initialHeight={750}
        >
          <HeatmapMenu
            map={map}
            // data={heatmapSummaryData}
            // activeKey={activeKey}
            // setActiveKey={setActiveKey}
            // combinedFilter={combinedFilter}
            // setCombinedFilter={setCombinedFilter}
            // benchmark={benchmark}
            // setBenchmark={setBenchmark}
            // rangeType={rangeType}
            // setRangeType={setRangeType}
            // setFilterFn={setFilterFn}
          />
        </DraggableModal>
      </DraggableModalProvider> */}
      {/* )} */}
      {heatmapType === "demographics" && (
        <DemographicsContext
          onDataChange={(data) => {
            if (!data || data.length === 0) {
              map.fire("heatmap.summaryUpdate", {
                payload: { data: {} },
              });
              return;
            }
            console.log("onChangeData:", data);

            const dataInfo = filters.reduce((accData, key) => {
              const values = data.reduce((acc, item) => {
                if (item[key] && !isNaN(item[key]) && item[key] !== 0)
                  acc.push(item[key]);
                return acc;
              }, []);

              if (values.length === 0) return accData;

              console.log("key: ", key);
              console.log("values: ", values);

              const min = Math.min(...values);
              const max = Math.max(...values);
              const median = calculateMedian(values);
              const infoType = key;
              accData[infoType] = { min, max, median };

              return accData;
            }, {});

            console.log("dataInfo: ", dataInfo);
            map.fire("heatmap.summaryUpdate", {
              payload: { data: dataInfo },
            });
            // setHeatmapSummaryData(dataInfo);
          }}
        >
          <DemographicLayer
            activeKey={activeKey}
            combinedKeys={combinedFilter.keys}
            benchmark={benchmark}
            rangeType={rangeType}
            filterFn={filterFn}
          />
        </DemographicsContext>
      )}
      {heatmapType === "submarket" && (
        <SubmarketContext
          onDataChange={(data) => {
            console.log("submarket data: ", data);
            if (!data || data.length === 0) {
              map.fire("heatmap.summaryUpdate", {
                payload: { data: {} },
              });
              return;
            }

            const filters = [
              "vacancy_rate",
              "rental_growth_5_years",
              "market_effective_rent_sf",
              "market_effective_rent_unit",
              "market_effective_rent_growth_12_months",
            ];

            const dataInfo = filters.reduce((accData, key) => {
              const values = data.reduce((acc, item) => {
                if (item[key] && !isNaN(item[key]) && item[key] !== 0)
                  acc.push(item[key]);
                return acc;
              }, []);

              if (values.length === 0) return accData;

              console.log("key: ", key);
              console.log("values: ", values);

              const min = Math.min(...values);
              const max = Math.max(...values);
              const median = calculateMedian(values);
              const infoType = key;
              accData[infoType] = { min, max, median };

              return accData;
            }, {});

            console.log("dataInfo: ", dataInfo);
            map.fire("heatmap.summaryUpdate", {
              payload: { data: dataInfo },
            });
          }}
        >
          {submarketShapeType === "Polygon" && (
            <SubmarketPolygon
              activeKey={activeKey}
              benchmark={benchmark}
              rangeType={rangeType}
              filterFn={filterFn}
            />
          )}
          {submarketShapeType === "Block Groups" && (
            <SubmarketBG
              activeKey={activeKey}
              benchmark={benchmark}
              rangeType={rangeType}
              filterFn={filterFn}
            />
          )}
        </SubmarketContext>
      )}
    </>
  );
};

export default Heatmap;
