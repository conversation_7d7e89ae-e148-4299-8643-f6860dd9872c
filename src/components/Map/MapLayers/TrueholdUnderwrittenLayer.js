import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useSelector, useDispatch } from "react-redux";
import { getTrueHoldUnderwrittenData } from "../../../services/data";
import { convertToGeoJSON } from "../../../utils/geography";
import { geojsonTemplate } from "../../../constants";
import Source from "./Source";
import Layer from "./Layer";
import { initPopup } from "../MapUtility/general";

const sourceId = "truehold-underwritten-source";

const pointStyle = {
  id: "truehold-underwritten-point",
  type: "circle",
  source: sourceId,
  paint: {
    "circle-radius": 7,
    "circle-color": "#41a390",
    "circle-color": [
      "case",
      ["==", ["get", "opportunity_stage_name"], "Closed Lost"],
      "#8c8c8c",
      "#41a390",
    ],
    "circle-stroke-color": "#ffffff",
    "circle-stroke-width": 2,
  },
};

const getTooltipHTML = (property) => {
  const keys = [
    "opportunity_stage_name",
    "initial_capex",
    "stabilized_cap_rate",
    "rent_offer",
    "final_purchase_offer",
  ];

  const titleFormatter = (key) => {
    if (key === "opportunity_stage_name") return "Stage";
    return key
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const valueFormatter = (key, value) => {
    if (key === "stabilized_cap_rate")
      return `${Math.round(value * 100 * 100) / 100}%`;
    if (["initial_capex", "rent_offer", "final_purchase_offer"].includes(key))
      return `$${value.toLocaleString()}`;
    return value;
  };

  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {keys.map((key) => (
        <div key={key}>
          <span>
            {titleFormatter(key)}: {valueFormatter(key, property[key])}
          </span>
        </div>
      ))}
      <span>
        Beds: {property.beds} | Baths: {property.baths} | Sqft:{" "}
        {property.sqft.toLocaleString()}
      </span>
      <span>Address:</span>
      <span>{property.address}</span>
      <span>
        {property.city}, {property.state}
      </span>
      <span>{property.zip}</span>
    </div>
  );
};

let popup = initPopup();

const TrueholdUnderwrittenLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const [geojson, setGeojson] = useState(geojsonTemplate);

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("truehold underwritten"))
      return;

    const moveEnd = async (e) => {
      const mapBounds = map.getBounds();
      const coord1 = mapBounds.getSouthWest();
      const coord2 = mapBounds.getNorthEast();

      const data = await getTrueHoldUnderwrittenData({
        lat1: coord1.lng,
        lng1: coord1.lat,
        lat2: coord2.lng,
        lng2: coord2.lat,
      });

      setGeojson(
        convertToGeoJSON({
          data,
          geomAccessor: (item) => item.geog,
          propertiesAccessor: (item) => {
            const { geog, ...properties } = item;
            return properties;
          },
        })
      );
    };

    const mouseMove = (e) => {
      const feature = e.features[0];

      if (feature && feature.properties) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }

      // const coordinates = [e.lngLat.lng, e.lngLat.lat];
      // const htmlRender = getTooltipHTML(feature[0].properties);
      // popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
    };

    const mouseLeave = () => {
      popup.remove();
    };

    const moveBehindOwnedLayer = () => {
      const layers = map.getStyle().layers;
      let foundOwnedLayer = false;
      console.log("moveBehindOwnedLayer", layers);
      for (let i = layers.length - 1; i >= 0; i--) {
        if (layers[i].source && layers[i].source.includes("OwnedProperty")) {
          foundOwnedLayer = true;
        } else if (
          layers[i].source &&
          !layers[i].source.includes("OwnedProperty") &&
          foundOwnedLayer
        ) {
          map.moveLayer(pointStyle.id, layers[i].id);
          console.log(`move ${pointStyle.id} behind ${layers[i].id}`);
          break;
        }
      }
    };

    moveEnd();
    moveBehindOwnedLayer();

    map.on("moveend", moveEnd);
    map.on("mousemove", "truehold-underwritten-point", mouseMove);
    map.on("mouseleave", "truehold-underwritten-point", mouseLeave);
    return () => {
      map.off("moveend", moveEnd);
      map.off("mousemove", "truehold-underwritten-point", mouseMove);
      map.off("mouseleave", "truehold-underwritten-point", mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("truehold underwritten")) return null;
  return (
    <>
      <Source id={sourceId} type="geojson" data={geojson}>
        <Layer {...pointStyle} />
      </Source>
    </>
  );
};

export default TrueholdUnderwrittenLayer;
