import { useState, useEffect, useRef } from "react";
import { renderToString } from "react-dom/server";
import { useSelector, useDispatch } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { geojsonTemplate } from "../../../constants";
import { initPopup } from "../MapUtility/general";
import { formatter } from "../../../utils/money";

const sourceId = "airBnB";

const minZoom = 7;

const circleStyle = {
  id: `${sourceId}CircleLayer`,
  type: "circle",
  minzoom: minZoom,
  paint: {
    "circle-radius": 5,
    "circle-stroke-color": "#fff",
    "circle-stroke-width": 2,
    "circle-color": "#50C878",
  },
};

const clusterStyle = {
  id: `${sourceId}ClusterLayer`,
  type: "circle",
  filter: ["has", "point_count"],
  minzoom: minZoom,
  // paint: {
  //   "circle-color": [
  //     "step",
  //     ["get", "point_count"],
  //     "#51bbd6",
  //     100,
  //     "#f1f075",
  //     750,
  //     "#f28cb1",
  //   ],
  //   "circle-radius": ["step", ["get", "point_count"], 20, 100, 30, 750, 40],
  // },
  paint: {
    "circle-color": "#fe595d",
    "circle-radius": [
      // 'interpolate',
      // ['linear'],
      // ['^', ['get', 'point_count'], 0.5],
      // 1, 13, 30, 25, 300, 50
      "interpolate",
      ["linear"],
      ["zoom"],
      0,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 0.1], 15],
      10.4,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 1], 15],
      12,
      ["max", ["*", ["^", ["get", "point_count"], 0.5], 4], 15],
    ],
    "circle-opacity": 0.75,
    "circle-stroke-width": 1,
    "circle-stroke-color": "rgba(255,255,255,1)",
  },
};

const clusterLabelStyle = {
  id: `${sourceId}ClusterSymbolLayer`,
  type: "symbol",
  filter: ["has", "point_count"],
  minzoom: minZoom,
  // layout: {
  //   "text-field": ["get", "point_count_abbreviated"],
  //   "text-font": ["DIN Offc Pro Medium", "Arial Unicode MS Bold"],
  //   "text-size": 16,
  // },
  layout: {
    "text-font": ["Open Sans Bold"],
    "text-field": "{point_count}",
    "text-size": 14,
    "text-justify": "auto",
  },
  paint: {
    // "text-color": "rgba(255,255,255,1)",
    // "text-halo-color": "rgba(0,0,0,.4)",
    // "text-halo-width": 1,
    "text-color": "rgba(0,0,0,1)",
    // 'text-color': '#022386',
  },
};

let popup;

export let airBnBHovered;

const createPopup = (map, feature, coordinates) => {
  if (feature && feature.length > 0 && !feature[0].properties.cluster) {
    const { name, price, bathrooms, bedrooms, room_type, property_type } =
      feature[0].properties;

    const htmlRender = renderToString(
      <div style={{ padding: "10px", fontWeight: "500" }}>
        {name && (
          <h4 style={{ fontSize: "14px", fontWeight: "600", margin: 0 }}>
            {name}
          </h4>
        )}
        {price && price > 0 && (
          <p style={{ margin: 0 }}>Price: ${formatter(price)}</p>
        )}
        {bathrooms && <p style={{ margin: 0 }}>Bath: {bathrooms}</p>}
        {bedrooms && <p style={{ margin: 0 }}>Bed: {bedrooms}</p>}
        {room_type && <p style={{ margin: 0 }}>Room Type: {room_type}</p>}
        {property_type && (
          <p style={{ margin: 0 }}>Property Type: {property_type}</p>
        )}
      </div>
    );

    popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
    airBnBHovered.current = true;
  } else {
    popup.remove();
    airBnBHovered.current = false;
  }
};

const AirBnBLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentAirBnBGeoJSON = useSelector(
    (state) => state.Map.currentAirBnBGeoJSON
  );
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );

  const dispatch = useDispatch();

  airBnBHovered = useRef(false);

  useEffect(() => {
    if (!map) return;

    popup = initPopup();

    const fetchAirBnBData = () => {
      if (currentMapLayerOptions.includes("airbnb")) {
        const currentZoomLevel = map.getZoom();
        if (currentZoomLevel >= minZoom) {
          const mapScopeBBox = map.getBounds().toArray();
          dispatch({
            type: "Map/getAirBnB",
            payload: {
              lng1: mapScopeBBox[0][1],
              lat1: mapScopeBBox[0][0],
              lng2: mapScopeBBox[1][1],
              lat2: mapScopeBBox[1][0],
            },
          });
        }
      } else {
        if (currentAirBnBGeoJSON.features.length > 0) {
          dispatch({
            type: "Map/saveState",
            payload: {
              currentAirBnBGeoJSON: geojsonTemplate,
            },
          });
        }
      }
    };

    const mouseEnter = (e) => {
      const feature = map.queryRenderedFeatures(e.point, {
        layers: [`${sourceId}CircleLayer`],
      });

      const coordinates = [e.lngLat.lng, e.lngLat.lat];
      createPopup(map, feature, coordinates);
    };

    const mouseLeave = () => {
      popup.remove();

      airBnBHovered.current = false;
    };

    const moveEnd = () => {
      fetchAirBnBData();
    };

    fetchAirBnBData();

    map.on("mouseenter", `${sourceId}CircleLayer`, mouseEnter);
    map.on("mouseleave", `${sourceId}CircleLayer`, mouseLeave);
    map.on("moveend", moveEnd);
    return () => {
      map.off("mouseenter", `${sourceId}CircleLayer`, mouseEnter);
      map.off("mouseleave", `${sourceId}CircleLayer`, mouseLeave);
      map.off("moveend", moveEnd);
    };
  }, [map, currentMapLayerOptions]);

  if (currentMapLayerOptions.includes("airbnb")) {
    return (
      <Source
        id={sourceId}
        type="geojson"
        data={currentAirBnBGeoJSON}
        cluster={true}
        clusterMaxZoom={14}
        clusterRadius={75}
      >
        <Layer {...circleStyle} />
        <Layer {...clusterStyle} />
        <Layer {...clusterLabelStyle} />
      </Source>
    );
  }
  return null;
};

export default AirBnBLayer;
