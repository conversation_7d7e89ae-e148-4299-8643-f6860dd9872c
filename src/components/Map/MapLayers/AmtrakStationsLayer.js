import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useSelector } from "react-redux";
import { getAmtrakStations } from "../../../services/data";
import Source from "./Source";
import Layer from "./Layer";
import { geojsonTemplate } from "../../../constants";
import { initPopup } from "../MapUtility/general";

const sourceId = "arcgis-amtrak";
const minZoom = 8;

const pointStyle = {
  id: `${sourceId}PointStyle`,
  type: "circle",
  minzoom: minZoom,
  paint: {
    "circle-radius": [
      "interpolate",
      ["linear"],
      ["zoom"],
      8, 5,
      12, 6,
      15, 7
    ],
    "circle-color": "#1e88e5",
    "circle-stroke-width": 1,
    "circle-stroke-color": "#ffffff",
    "circle-opacity": 0.9
  }
};

const popup = initPopup();

const getPopupHTML = (properties) => {
  const { 
    StationAliases,
    StnType,
    Address1,
    StationName
  } = properties;

  return renderToString(
    <div style={{ padding: "5px 10px" }}>
      <strong>{StationAliases}</strong>
      <div style={{ display: "flex", flexDirection: "column" }}>
        <span>Address: {Address1}, {StationName}</span>
        <span>Type: {StnType}</span>
      </div>
    </div>
  );
};

const AmtrakStationsLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const [geoJSON, setGeoJSON] = useState(geojsonTemplate);

  useEffect(() => {
    if (!map) return;

    const getBusStopData = async () => {
      if (!currentMapLayerOptions.includes("arcgis amtrak")) return;
      if (map.getZoom() < minZoom) return;
      
      const bounds = map.getBounds().toArray();
      const data = await getAmtrakStations({
        lng1: bounds[0][0],
        lat1: bounds[0][1],
        lng2: bounds[1][0],
        lat2: bounds[1][1],
      });

      if (data && data.features) {
        // Transform the ArcGIS data format to GeoJSON format
        const features = data.features.map(feature => ({
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [feature.geometry.x, feature.geometry.y]
          },
          properties: {
            ...feature.attributes
          }
        }));

        setGeoJSON({
          type: "FeatureCollection",
          features: features
        });
      } else {
        setGeoJSON(geojsonTemplate);
      }
    };

    const mouseMove = (e) => {
      const features = e.features;
      if (features.length > 0) {
        map.getCanvas().style.cursor = "pointer";
        popup
          .setLngLat(features[0].geometry.coordinates)
          .setHTML(getPopupHTML(features[0].properties))
          .addTo(map);
      } else {
        map.getCanvas().style.cursor = "";
        popup.remove();
      }
    };

    const mouseLeave = () => {
      map.getCanvas().style.cursor = "";
      popup.remove();
    };

    getBusStopData();
    map.on("moveend", getBusStopData);
    map.on("mousemove", pointStyle.id, mouseMove);
    map.on("mouseleave", pointStyle.id, mouseLeave);
    
    return () => {
      map.off("moveend", getBusStopData);
      map.off("mousemove", pointStyle.id, mouseMove);
      map.off("mouseleave", pointStyle.id, mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("arcgis amtrak")) return null;
  
  return (
    <Source id={sourceId} type="geojson" data={geoJSON}>
      <Layer {...pointStyle} />
    </Source>
  );
};

export default AmtrakStationsLayer;