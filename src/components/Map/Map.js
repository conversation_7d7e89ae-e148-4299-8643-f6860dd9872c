import React, { useRef, useEffect, useState, forwardRef } from "react";
import mapboxgl from "mapbox-gl"; // eslint-disable-line import/no-webpack-loader-syntax
import "mapbox-gl/dist/mapbox-gl.css";
import styles from "./Map.module.css";
import "../MapWrapper.css";
import isEmpty from "lodash.isempty";
import {
  MAPBOX_STREET,
  MAPBOX_SATELLITE,
  MAPBOX_MONOCHROME,
  MAPBOX_TERRAIN,
} from "../../constants";
import { swapStyle } from "./MapUtility/layer";
import { connect, useSelector, useDispatch } from "react-redux";
// import { useSelector, useDispatch } from "react-redux";;
import MapControls from "./MapControls/MapControls";
import MapLegends from "./MapLegends/MapLegends";
import ParcelLayer from "./MapLayers/ParcelLayer";
import ParcelBoundaryLayer from "./MapLayers/ParcelBoundaryLayer";
import SchoolDistrictLayer from "./MapLayers/SchoolDistrictLayer";
import SchoolAttendanceLayer from "./MapLayers/SchoolAttendanceLayer";
import CBSALayer from "./MapLayers/CBSALayer";
import CountyLayer from "./MapLayers/CountyLayer";
import ZIPCodeLayer from "./MapLayers/ZIPCodeLayer";
import ActivityCenterLayer from "./MapLayers/ActivityCenterLayer";
import OpportunityZoneLayer from "./MapLayers/OpportunityZoneLayer";
import FloodZoneLayer from "./MapLayers/FloodZoneLayer";

import MapLayout from "./MapLayout/MapLayout";
import MapStreetview from "./MapStreetview/MapStreetview";
import NewHomesLayer from "./MapLayers/NewHomesLayer";
import HeatmapLayer from "./MapLayers/Heatmap/HeatmapLayer";
import { setServerType } from "../../services/data";
import Subdivision from "./MapLayers/Subdivision";
import Neighborhoods from "./MapLayers/Neighborhoods";
import WetLands from "./MapLayers/WetLands";
import PowerLinesLayer from "./MapLayers/PowerLinesLayer";
// import OwnedProperty from "./MapLayers/OwnedProperty";
// import DriveTimeLayer from "./MapLayers/DriveTimeLayer";
import Regrid from "./MapLayers/Regrid";
import AirBnBLayer from "./MapLayers/AirBnBLayer";
import OSMResidentialLayer from "./MapLayers/OSMResidentialLayer";
import ParcelOwnerHeatmap from "./MapLayers/ParcelOwnerHeatmap";
import OwnedProperty from "./MapLayers/OwnedProperty/OwnedProperty";
import MultiFamily from "./MapLayers/MultiFamily";
import Heatmap from "./MapLayers/Heatmap2/Heatmap";
import WaterDistrict from "./MapLayers/WaterDistrict";
import { getMapTileSourcesData } from "../../services/data";
import RailwayNetworkLayer from "./MapLayers/RailwayNetworkLayer";
import TrueholdUnderwrittenLayer from "./MapLayers/TrueholdUnderwrittenLayer";
import AdminCityLayer from "./MapLayers/AdminCityLayer";
import { jwtDecode } from "jwt-decode";
import { useInterval } from "../hooks/useInterval";
import BlueRiverPRPLayer from "./MapLayers/BlueRiverPRPLayer";
import TrueholdTargetLayer from "./MapLayers/TrueholdTargetLayer";
import BlueRiverMarkettingDeals from "./MapLayers/BlueRiverMarkettingDeals";
import TaxLayer from "./MapLayers/TaxLayer";
import { ContextMenu } from "../features/context-menu/index";
import { SitePlanGenerator } from "../features/context-menu/components/site-plan";
import RegridZoning from "./MapLayers/RegridZoning";
import { HistoricalZoningLayers } from "../features/historical-zoning/layer";
import { NewlySplitParcels } from "../features/parcels/newly-split";
import PrologisLayer from "./MapLayers/PrologisLayer";
import MobileHomeParkLayer from "./MapLayers/MobileHomeParkLayer";
import { DriveTimeLayer } from "../features/drive-time/index";

import LandBankLayer from "../features/land-bank/LandBankLayer";
import { MajorEmployerLayer } from "../features/major-employer/MajorEmployerLayer";
import { GNLayer } from "../features/gentrifying-neibourhoods/GNLayer";

import HUDPublicHousingLayer from "../features/public-housing/HUDPublicHousingLayer";
import { AssistedMultifamilyPropertiesLayer } from "../features/public-housing/AssistedMultifamilyProperties";
import { PublicHousingAuthoritiesLayer } from "../features/public-housing/PublicHousingAuthoritiesLayer";
import { EHASALayer } from "../features/public-housing/EHASALayer";
import { AffordableHousingLayer } from "../features/public-housing/AffordableHousingLayer";
import { HUDInsuredLayer } from "../features/public-housing/HUDInsuredLayer";
import { PHDLayer } from "../features/public-housing/PHDLayer";
import { VoucherTractLayer } from "../features/public-housing/HousingChoiceVoucherTractLayer";

import { IndustrialParcelLayer } from "../features/industrial-parcels/layer";
import DemoUnderwrittenLayer from "./MapLayers/DemoUnderwrittenLayer";
import DemoOwnedLandLayer from "./MapLayers/DemoOwnedLandLayer";
import DemoUnderwrittenLandLayer from "./MapLayers/DemoUnderwrittenLandLayer";
import CharterSchoolLayer from "./MapLayers/CharterSchoolLayer";
import SanitarySewerLayer from "./MapLayers/ClientsData/SanitarySewerLayer";
import FutureLandUseLayer from "./MapLayers/ClientsData/FutureLandUserLayer";
import TransitLineLayer from "./MapLayers/TransitLineLayer";
import BusStopsLayer from "./MapLayers/BusStopsLayer";
import AmtrakStationsLayer from "./MapLayers/AmtrakStationsLayer";
import CARailStationsLayer from "./MapLayers/CARailStationsLayer";
import QualifiedCensusTractLayer from "./MapLayers/QualifiedCensusTractLayer";
import CoreSpacesPortfolioLayer from "./MapLayers/ClientsData/CoreSpacesPortfolioLayer";
import IndustryNewsLayer from "../features/industry-news/layers";
import BFRClusterLayer from "./MapLayers/BFRClusterLayer";
import PublicHousingPropertyLayer from "./MapLayers/ClientsData/PublicHousingPropertyLayer";
import { MultiFamilyApartments } from "./MapLayers/MultiFamilyApartments";
import { MudTaxLayer } from "../features/greystar/mudTaxLayer";
import { HoustonCompsLayer } from "../features/greystar/HoustonCompsLayer";
const DEFAULT_LNG = -97.040443;
const DEFAULT_LAT = 32.89748;
const DEFAULT_ZOOM = 10.39787299330436;

export let mapContainer;
export let mapToken;
export const zoomLevelToChangeBasemap = 14;
export const zoomLevelToShowParcelAVM = 17;

const HEATMAP_USER_ACCESS_LIST = [
  "Avanta",
  "BlueRiver",
  "BridgeAdmin",
  "BridgeFull",
  "BridgeTower",
  "DRHorton",
  "HON",
  "Nhimble",
  "Truehold",
  "VentureREI",
  "UrbanRowGroup",
  "LendingOne",
  "demo-users",
  "dev",
  "demo-CMA-DFW-only",
  "GEMRC",
];

const MULTIFAMILY_USER_ACCESS_LIST = ["Avanta", "BridgeTower", "demo-users"];

const getToken = async (tokenRef) => {
  const decoded = tokenRef.current ? jwtDecode(tokenRef.current) : null;
  const now = Math.floor(Date.now() / 1000); // in seconds
  if (!decoded || decoded.exp > now) {
    let token = await window.spatiallaser.getUserToken("access");
    tokenRef.current = token;
  }
  return tokenRef;
};

function Map(props) {
  mapContainer = useRef(null);
  const map = useRef(null);
  const [lng, setLng] = useState(DEFAULT_LNG);
  const [lat, setLat] = useState(DEFAULT_LAT);
  const [zoom, setZoom] = useState(DEFAULT_ZOOM);

  const [showParcelLayer, setShowParcelLayer] = useState(true);
  const [showParcelBoundaryLayer, setShowParcelBoundaryLayer] = useState(true);

  mapToken = props.token;

  const dispatch = useDispatch();

  const tokenRef = useRef(null);

  useEffect(() => {
    getToken(tokenRef);
  }, []);

  useInterval(() => {
    getToken(tokenRef);
  }, 1000 * 61);

  useEffect(() => {
    if (map.current) return; // initialize map only once

    mapboxgl.accessToken = mapToken;

    const initProperties = !isEmpty(props.initProperties)
      ? props.initProperties
      : { center: [lng, lat], zoom: zoom };

    const { theme, transformRequest, ...properties } = initProperties;

    let style = MAPBOX_STREET;
    let themeSetting = "Automatic";
    if (theme) {
      if (theme === "Satellite") {
        style = MAPBOX_SATELLITE;
        themeSetting = theme;
      } else if (theme === "Monochrome") {
        style = MAPBOX_MONOCHROME;
        themeSetting = theme;
      } else if (theme === "Terrain") {
        style = MAPBOX_TERRAIN;
        themeSetting = theme;
      } else if (theme === "Street") {
        style = MAPBOX_STREET;
        themeSetting = theme;
      }
      dispatch({
        type: "Map/saveState",
        payload: { currentMapThemeOption: themeSetting },
      });
    }

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: `mapbox://styles/${style}`,
      ...properties,
    });
    map.current.addControl(
      new mapboxgl.ScaleControl({
        maxWidth: 120,
        unit: "imperial",
      })
    );
    if (props.exposeMapRef) {
      props.exposeMapRef.current = map.current;
    }
    if (props.getMap) {
      props.getMap(map.current);
    }

    dispatch({
      type: "Map/saveState",
      payload: { map: map.current, devMode: props.devMode },
    });
    dispatch({ type: "Configure/saveState", payload: props.configure });

    if (
      props.serverType &&
      (props.serverType === "prod" ||
        props.serverType === "exp" ||
        props.serverType === "canary" ||
        props.serverType === "dev")
    ) {
      dispatch({
        type: "Configure/saveState",
        payload: { serverType: props.serverType },
      });
      setServerType(props.serverType);
    }

    // console.log("Map module: props.user", props.user);
    if (props.user && props.user.userGroup && props.user.userGroup.length > 0) {
      // console.log("Map module: props.user", props.user);
      dispatch({
        type: "Configure/saveState",
        payload: { user: props.user },
      });
    } else {
      // TODO: show map error
    }

    console.log("Map mounted");

    return () => {
      if (
        props.map &&
        props.map.style &&
        props.map.style._loaded &&
        map.current &&
        map.current.style &&
        map.current.style._loaded
      ) {
        if (dispatch) {
          dispatch({ type: "Map/saveState", payload: { map: null } });
        }
        map.current.remove();
        console.log("Map unmounted");
      }
    };
  });

  // props.user will change a few times during initialization
  const { user } = props;
  useEffect(() => {
    if (user && user.userGroup && user.userGroup.length > 0) {
      // console.log("Map module: user", user);
      dispatch({
        type: "Configure/saveState",
        payload: { user: user },
      });

      const getMapTileSources = async () => {
        const sources = await getMapTileSourcesData();
        if (sources && sources.sources && sources.sources.length > 0) {
          dispatch({
            type: "Map/saveState",
            payload: { tileSources: sources.sources },
          });
        }
      };
      // getMapTileSources();
    } else {
      // TODO: show map error
    }
  }, [JSON.stringify(user)]);

  useEffect(() => {
    if (!props.map) return;

    const leaseModeHandler = (e) => {
      const leaseMode = e.payload.leaseMode;
      dispatch({
        type: "Map/saveState",
        payload: { CMALeaseMode: leaseMode },
      });
    };

    const scorecardHandler = (e) => {
      const scorecardModalOpen = e.payload.scorecardModalOpen;

      dispatch({
        type: "Map/saveState",
        payload: { CMAscorecardModalOpen: scorecardModalOpen },
      });
    };

    const currentMapLayerOptionsHandler = (e) => {
      console.log("currentMapLayerOptionsHandler", e);
      const currentMapLayerOptions = e.payload.currentMapLayerOptions;

      dispatch({
        type: "Map/saveState",
        payload: { currentMapLayerOptions: currentMapLayerOptions },
      });
    };

    // if (props.user) {
    //   if (props.user.userGroup.includes("HON")) {
    //     dispatch({
    //       type: "Map/saveState",
    //       payload: { currentMapLayerOptions: ["hon owned"] },
    //     });
    //   } else if (
    //     props.user.userGroup.includes("BridgeAdmin") ||
    //     props.user.userGroup.includes("BridgeFull")
    //   ) {
    //     dispatch({
    //       type: "Map/saveState",
    //       payload: { currentMapLayerOptions: ["gorlick owned"] },
    //     });
    //   } else if (props.user.userGroup.includes("Truehold")) {
    //     dispatch({
    //       type: "Map/saveState",
    //       payload: { currentMapLayerOptions: ["truehold owned"] },
    //     });
    //   }
    // }

    const updateShowParcelLayer = ({ payload }) => {
      setShowParcelLayer(payload.showParcelLayer);
    };

    const updateShowParcelBoundaryLayer = ({ payload }) => {
      setShowParcelBoundaryLayer(payload.showParcelBoundaryLayer);
    };

    props.map.on("layers.updateShowParcelLayer", updateShowParcelLayer);
    props.map.on("layers.updateShowParcelBoundaryLayer", updateShowParcelBoundaryLayer);

    props.map.on("cma.leaseMode", leaseModeHandler);
    props.map.on("cma.scorecard", scorecardHandler);
    props.map.on("mapLayers.currentMapLayerOptions", currentMapLayerOptionsHandler);
    return () => {
      props.map.off("layers.showParcelLayer", updateShowParcelLayer);
      props.map.off("layers.showParcelBoundaryLayer", updateShowParcelBoundaryLayer);
      props.map.off("cma.leaseMode", leaseModeHandler);
      props.map.off("cma.scorecard", scorecardHandler);
      props.map.off("mapLayers.currentMapLayerOptions", currentMapLayerOptionsHandler);
    };
  }, [props.map]);

  useEffect(() => {
    if (!props.map) return;
    console.log("props.currentMapThemeOption", props.currentMapThemeOption);
    const zoomEnd = () => {
      if (!props.map || !props.map.style || !props.map.style._loaded) return;
      const currentZoomLevel = props.map.getZoom();

      if (props.currentMapThemeOption === "Automatic") {
        const mapStyleName = props.map.getStyle().name;
        if (currentZoomLevel >= zoomLevelToChangeBasemap) {
          if (mapStyleName !== "Satellite Streets") {
            swapStyle(props.map, MAPBOX_SATELLITE);
          }
        } else {
          if (mapStyleName !== "Streets") {
            swapStyle(props.map, MAPBOX_STREET);
          }
        }
      }
    };

    props.map.on("zoomend", zoomEnd);
    return () => {
      props.map.off("zoomend", zoomEnd);
    };
  }, [props.map, props.currentMapThemeOption]);

  useEffect(() => {
    if (!props.map || !props.map.style || !props.map.style._loaded) return;

    if (props.currentMapThemeOption === "Satellite") {
      swapStyle(props.map, MAPBOX_SATELLITE);
    } else if (props.currentMapThemeOption === "Street") {
      swapStyle(props.map, MAPBOX_STREET);
    } else if (props.currentMapThemeOption === "Monochrome") {
      swapStyle(props.map, MAPBOX_MONOCHROME);
    } else if (props.currentMapThemeOption === "Terrain") {
      swapStyle(props.map, MAPBOX_TERRAIN);
    } else {
      const mapStyleName = props.map.getStyle().name;
      if (props.map.getZoom() >= zoomLevelToChangeBasemap) {
        if (mapStyleName !== "Satellite Streets") {
          swapStyle(props.map, MAPBOX_SATELLITE);
        }
      } else {
        if (mapStyleName !== "Streets") {
          swapStyle(props.map, MAPBOX_STREET);
        }
      }
    }
  }, [props.currentMapThemeOption]);
  console.log("usergroup", props.user.userGroup);

  return (
    <div id="Map" className={styles.container}>
      <div id="mapContainer" ref={mapContainer} className={styles.mapContainer} />
      <MapControls />
      <MapLegends />
      <MapLayout />
      {showParcelLayer &&
        !(props.configure.platlabsOnly || props.configure.marketplaceOnly) &&
        !props.currentMapLayerOptions.includes("zoning types") && (
          <ParcelLayer userGroup={props.user.userGroup} />
        )}
      {/* {props.serverType === "prod" && <ParcelBoundaryLayer />} */}
      {props.currentMapLayerOptions.includes("industry news") && <IndustryNewsLayer />}
      <SchoolDistrictLayer />
      <CharterSchoolLayer />
      <AdminCityLayer />
      <CBSALayer />
      <CountyLayer />
      <ZIPCodeLayer />
      <TaxLayer />
      <ActivityCenterLayer />
      <OpportunityZoneLayer />
      <FloodZoneLayer />

      {props.currentMapLayerOptions.includes("new construction") && <NewHomesLayer />}
      {props.currentMapLayerOptions.includes("school zones") && <SchoolAttendanceLayer />}
      {props.currentMapLayerOptions.includes("arcgis transit line") && <TransitLineLayer />}
      {props.currentMapLayerOptions.includes("arcgis bus stop") && <BusStopsLayer />}
      {props.currentMapLayerOptions.includes("arcgis amtrak") && <AmtrakStationsLayer />}
      {props.currentMapLayerOptions.includes("arcgis ca rail") && <CARailStationsLayer />}
      {props.currentMapLayerOptions.includes("qualified census tracts") && (
        <QualifiedCensusTractLayer />
      )}

      <Subdivision />
      <Neighborhoods />
      <WaterDistrict />
      <WetLands />
      <PowerLinesLayer />
      <AirBnBLayer />
      <OSMResidentialLayer />
      <ParcelOwnerHeatmap />
      <OwnedProperty />
      <BlueRiverPRPLayer />
      <MajorEmployerLayer />
      <PrologisLayer />
      <BFRClusterLayer />
      <MobileHomeParkLayer />
      {/* Public Housing Layers */}
      <HUDPublicHousingLayer />
      <PublicHousingAuthoritiesLayer />
      <AssistedMultifamilyPropertiesLayer />
      <AffordableHousingLayer />
      <HUDInsuredLayer />
      <PHDLayer />
      <EHASALayer />
      <VoucherTractLayer />

      {/* Demo Layers */}
      {props.user &&
        props.user.userGroup &&
        ["AlliedDev", "demo-users"].includes(props.user.userGroup[0]) && (
          <>
            <SanitarySewerLayer />
            <FutureLandUseLayer />
          </>
        )}
      {props.user &&
        props.user.userGroup &&
        ["SecondAvenue", "demo-users"].includes(props.user.userGroup[0]) && (
          <>
            <PublicHousingPropertyLayer />
          </>
        )}
      {props.user && props.user.userGroup && "demo-CMA-DFW-only" === props.user.userGroup[0] && (
        <CoreSpacesPortfolioLayer />
      )}
      {props.currentMapLayerOptions.includes("industrial parcels") && (
        <IndustrialParcelLayer map={props.map} serverType={props.serverType} />
      )}

      {props.currentMapLayerOptions.includes("land bank") && (
        <LandBankLayer map={props.map} serverType={props.serverType} />
      )}
      {props.user && props.user.userGroup && ["BlueRiver"].includes(props.user.userGroup[0]) && (
        <BlueRiverMarkettingDeals />
      )}
      {/* 
      {props.currentMapLayerOptions.includes("demo land owned") && <DemoOwnedLandLayer />}
      {props.currentMapLayerOptions.includes("demo land underwritten") && <DemoUnderwrittenLayer />} */}
      {props.user &&
        props.user.userGroup &&
        ["demo-users", "demo-CMA-DFW-only"].includes(props.user.userGroup[0]) && (
          <>
            <DemoOwnedLandLayer />
            <DemoUnderwrittenLandLayer />
          </>
        )}

      {props.user && props.user.userGroup && ["Truehold"].includes(props.user.userGroup[0]) && (
        <TrueholdUnderwrittenLayer />
      )}
      {props.user &&
        props.user.userGroup &&
        ["demo-users", "demo-CMA-DFW-only"].includes(props.user.userGroup[0]) && (
          <DemoUnderwrittenLayer />
        )}

      {props.user &&
        props.user.userGroup &&
        ["Truehold", "demo-users"].includes(props.user.userGroup[0]) && <TrueholdTargetLayer />}

      {/* demo/bridge investment/homebound */}
      {props.user &&
        props.user.userGroup &&
        ["demo-users", "Homebound", "BridgeFull", "BridgeAdmin"].includes(
          props.user.userGroup[0]
        ) && <GNLayer />}
      {props.user && props.user.userGroup && ["Greystar"].includes(props.user.userGroup[0]) && (
        <MudTaxLayer />
      )}
      {props.user && props.user.userGroup && ["Greystar"].includes(props.user.userGroup[0]) && (
        <HoustonCompsLayer />
      )}
      <RailwayNetworkLayer />
      {props.user &&
        props.user.userGroup &&
        props.user.userGroup.length > 0 &&
        MULTIFAMILY_USER_ACCESS_LIST.includes(props.user.userGroup[0]) && <MultiFamily />}

      {props.currentMapLayerOptions.includes("multi family apartments") && (
        <MultiFamilyApartments />
      )}

      {/* {(props.serverType === "exp" || props.serverType === "canary") && ( */}
      {showParcelBoundaryLayer && props.configure.showParcelBoundaryLayer && <Regrid />}

      <RegridZoning />

      {props.currentMapLayerOptions.includes("historical zoning") && <HistoricalZoningLayers />}
      {props.currentMapLayerOptions.includes("split parcels") && <NewlySplitParcels />}

      {/* )} */}
      {/* {props.user &&
        props.user.userGroup &&
        props.user.userGroup.length > 0 &&
        ["Avanta", "demo-users"].includes(props.user.userGroup[0]) && (
          <Heatmap />
        )} */}
      {/* {props.user &&
        props.user.userGroup &&
        props.user.userGroup.length > 0 &&
        HEATMAP_USER_ACCESS_LIST.includes(props.user.userGroup[0]) && (
          <HeatmapLayer />
        )} */}
      {props.user &&
        props.user.userGroup &&
        props.user.userGroup.length > 0 &&
        HEATMAP_USER_ACCESS_LIST.includes(props.user.userGroup[0]) && <Heatmap />}
      {props.currentMapLayerOptions.includes("drive time") &&
        props.eventCoordinates.length === 2 && (
          <DriveTimeLayer lng={props.eventCoordinates[0]} lat={props.eventCoordinates[1]} />
        )}
      {props.children}
      {props.configure.streetview && props.configure.streetview.enabled && <MapStreetview />}
      <ContextMenu map={props.map} />
      <SitePlanGenerator
        longitude={props.eventCoordinates[0]}
        latitude={props.eventCoordinates[1]}
      />
    </div>
  );
}

export default connect(({ Map }) => ({
  map: Map.map,
  currentMapThemeOption: Map.currentMapThemeOption,
  currentMapLayerOptions: Map.currentMapLayerOptions,
  eventCoordinates: Map.eventCoordinates,
  heatmapType: Map.heatmapType,
}))(Map);
