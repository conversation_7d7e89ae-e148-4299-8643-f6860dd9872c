import { useState, createContext, useContext, useMemo } from "react";

const MapContext = createContext(undefined);

export const MapProvider = ({ children }) => {
  const [map, setMap] = useState(null);
  const [activeLayers, setActiveLayers] = useState([]);
  const [nearbyChainStores, setNearbyChainStores] = useState({
    data: null,
    isLoading: false,
    isError: false,
  });
  const [openSitePlanModal, setOpenSitePlanModal] = useState(false);

  const context = useMemo(() => {
    return {
      map,
      setMap,
      nearbyChainStores,
      setNearbyChainStores,
      activeLayers,
      setActiveLayers,
      openSitePlanModal,
      setOpenSitePlanModal,
    };
  }, [map, nearbyChainStores, activeLayers, openSitePlanModal]);

  return <MapContext.Provider value={context}>{children}</MapContext.Provider>;
};

export const useMap = () => {
  const context = useContext(MapContext);
  if (context === undefined) {
    throw new Error("useMap must be used within a MapProvider");
  }
  return context;
};

/**
 * Get nearby chain stores with distance value.
 * Must be used within MapProvider. Works with ChainStoresLayerRadius component.
 *
 * @returns nearby chain stores with distance value
 */
export const useNearbyChainStore = () => {
  const context = useContext(MapContext);
  if (context === undefined) {
    throw new Error("useNearbyChainStore must be used within a MapProvider");
  }
  return context.nearbyChainStores;
};
