import mapboxgl from "mapbox-gl";

export const initPopup = (props) => {
  return new mapboxgl.Popup(
    !props
      ? {
          closeButton: false,
          closeOnClick: false,
          offset: 15,
        }
      : props
  );
};

// Convert scalable svg to png (Mapbox does not allow svg)
export const rasterize = async (svgPath, size = 100) => {
  return new Promise(async function (resolve, reject) {
    const svgString = await (await fetch(svgPath)).text();
    var parser = new DOMParser();
    var svg = parser.parseFromString(svgString, "image/svg+xml");
    svg.firstElementChild.setAttribute("width", size);
    svg.firstElementChild.setAttribute("height", size);

    const serializedSVG = new XMLSerializer().serializeToString(
      svg.firstElementChild
    );
    const base64Data = window.btoa(serializedSVG);
    const imgsrc = `data:image/svg+xml;base64,${base64Data}`;

    const image = new Image();
    image.src = imgsrc;
    image.onload = () => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      canvas.width = size;
      canvas.height = size;
      ctx.drawImage(image, 0, 0, size, size);

      resolve(ctx.canvas.toDataURL("image/png"));
    };
  });
};

export const setPaintPropertySafely = (
  map,
  layerId,
  name,
  value,
  options = {}
) => {
  if (!map.getLayer(layerId)) return;

  // isStyleLoaded is more for the base layers.
  if (map.isStyleLoaded()) {
    map.setPaintProperty(layerId, name, value, options);
  } else {
    // style.load does not work for some reason.
    const setPaintWhenStyleReady = () => {
      if (map.isStyleLoaded()) {
        map.setPaintProperty(layerId, name, value, options);
      }
    };

    map.once("style.load", setPaintWhenStyleReady);
  }
};
