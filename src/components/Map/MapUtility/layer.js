import { mapToken } from "../Map";
import moment from "moment";
import { rasterize } from "./general";
import asset1 from "../../../assets/images/mapbox/patterns/asset-1.svg";
import asset2 from "../../../assets/images/mapbox/patterns/asset-2.svg";
import asset4 from "../../../assets/images/mapbox/patterns/asset-4.svg";
import asset5 from "../../../assets/images/mapbox/patterns/asset-5.svg";
import asset6 from "../../../assets/images/mapbox/patterns/asset-6.svg";

export const swapStyle = async (map, styleId) => {
  // https://github.com/mapbox/mapbox-gl-js/issues/4006#issuecomment-1114095622

  const response = await fetch(
    `https://api.mapbox.com/styles/v1/${styleId}?access_token=${mapToken}`
  );
  const responseJson = await response.json();
  const newStyle = responseJson;

  const currentStyle = map.getStyle();

  // ensure any sources from the current style are copied across to the new style
  newStyle.sources = Object.assign({}, currentStyle.sources, newStyle.sources);

  // find the index of where to insert our layers to retain in the new style
  let labelIndex = newStyle.layers.findIndex((el) => {
    return el.id === "waterway-label";
  });

  // default to on top
  if (labelIndex === -1) {
    labelIndex = newStyle.layers.length;
  }
  const appLayers = currentStyle.layers.filter((el) => {
    // app layers are the layers to retain, and these are any layers which have a different source set
    return (
      el.source &&
      el.source !== "mapbox://mapbox.satellite" &&
      el.source !== "mapbox" &&
      el.source !== "composite"
    );
  });
  newStyle.layers = [
    ...newStyle.layers.slice(0, labelIndex),
    ...appLayers,
    ...newStyle.layers.slice(labelIndex, -1),
  ];

  map.setStyle(newStyle);

  await addFloodPatters(map);
};
const addFloodPatters = async (map) => {
  if (!map) return;

  const assetImg1 = await rasterize(asset1);
  map.loadImage(assetImg1, (err, img) => {
    if (!map.hasImage("undetermined risk areas")) {
      map.addImage("undetermined risk areas", img);
    }
  });

  const assetImg2 = await rasterize(asset2);
  map.loadImage(assetImg2, (err, img) => {
    if (!map.hasImage("moderate to low risk areas")) {
      map.addImage("moderate to low risk areas", img);
    }
  });

  const assetImg4 = await rasterize(asset4);
  map.loadImage(assetImg4, (err, img) => {
    if (!map.hasImage("other")) {
      map.addImage("other", img);
    }
  });

  const assetImg5 = await rasterize(asset5);
  map.loadImage(assetImg5, (err, img) => {
    if (!map.hasImage("high risk coastal areas")) {
      map.addImage("high risk coastal areas", img);
    }
  });

  const assetImg6 = await rasterize(asset6);
  map.loadImage(assetImg6, (err, img) => {
    if (!map.hasImage("high risk areas")) {
      map.addImage("high risk areas", img);
    }
  });
};

// might be better to pass map to function instead of importing in file above
export const showVisibility = (map, layerName) => {
  if (!map || !map.style || !map.style._loaded) return;
  if (!map.getLayer(layerName)) return;

  map.setLayoutProperty(layerName, "visibility", "visible");
};
export const hideVisibility = (map, layerName) => {
  if (!map || !map.style || !map.style._loaded) return;
  if (!map.getLayer(layerName)) return;

  map.setLayoutProperty(layerName, "visibility", "none");
};
