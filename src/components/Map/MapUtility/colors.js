import { zoomLevelToShowParcelAVM, zoomLevelToChangeBasemap } from "../Map";
import {
  OWNER_COLOR,
  HOA_FEE_COLOR,
  MAP_LAYER_NAME_BASE,
} from "../../../constants";
import { setPaintPropertySafely } from "./general";

// give parcel AVM markers in each subdivision a different color
// prettier-ignore
export const setParcelColorBySubdivision = (
  map,
  allUniqueSubdivisions,
  prevFillColor,
  leaseMode
) => {
  if (
    !map
    //  ||
    // !parcelData ||
    // !parcelData.features ||
    // parcelData.features.length === 0
  )
    return;

  // // get all unique subdivision names
  // const allSubdivisions = parcelData.features.map((item) =>
  //   item.properties.subdivisionWithoutPhase
  //     ? item.properties.subdivisionWithoutPhase
  //     : "-"
  // );
  // const allUniqueSubdivisions = allSubdivisions.filter(
  //   (value, index, array) => array.indexOf(value) === index
  // );

  const colorArray = [
    "#e41a1c",
    "#377eb8",
    "#4daf4a",
    "#984ea3",
    "#ff7f00",
    "#a8a802",
    "#a65628",
    "#f781bf",
    "#999999",
  ];

  // // ignore phase
  // let fillColor = ["match", ["get", "subdivisionWithoutPhase"]];
  // for (let i = 0; i < allUniqueSubdivisions.length; i++) {
  //   if (prevFillColor && prevFillColor.length > 2) {
  //     // after map moves, we want the subdivisions that has already been on the map keep their colors, instead of being assigned new colors
  //     // if a subdivision was on the map before
  //     // we push its name and color to current fillColor
  //     // if not, we assign a new color to it
  //     const prevColorIndex = prevFillColor.indexOf(allUniqueSubdivisions[i]);
  //     if (prevColorIndex !== -1) {
  //       fillColor.push(
  //         allUniqueSubdivisions[i],
  //         prevFillColor[prevColorIndex + 1]
  //       );
  //     } else {
  //       fillColor.push(
  //         allUniqueSubdivisions[i],
  //         colorArray[i % colorArray.length]
  //       );
  //     }
  //   } else {
  //     // if this is the first time showing parcels
  //     fillColor.push(
  //       allUniqueSubdivisions[i],
  //       colorArray[i % colorArray.length]
  //     );
  //   }
  // }

  // fillColor.push("#000");

  const fillColor = ["case"];
  for (let i = 0; i < allUniqueSubdivisions.length; i++) {
    if (prevFillColor && prevFillColor.length > 2) {
      // after map moves, we want the subdivisions that has already been on the map keep their colors, instead of being assigned new colors
      // if a subdivision was on the map before
      // we push its name and color to current fillColor
      // if not, we assign a new color to it
      // const prevColorIndex = prevFillColor.indexOf(allUniqueSubdivisions[i]);
      const prevColorString = JSON.stringify(prevFillColor);
      const prevColorIndex = prevColorString.indexOf(allUniqueSubdivisions[i]);
      if (prevColorIndex !== -1) {
        const prevStartColor = prevColorString.indexOf(`"#`, prevColorIndex) + 1;
        const prevEndColor = prevColorString.indexOf('"', prevStartColor);
        const prevColor = prevColorString.substring(prevStartColor, prevEndColor);
        fillColor.push(
          ["==", ["get", "subdivisionWithoutPhase"], allUniqueSubdivisions[i]], prevColor,
        )
        // fillColor.push(
        //   allUniqueSubdivisions[i],
        //   prevFillColor[prevColorIndex + 1]
        // );
      } else {
        fillColor.push(
          ["==", ["get", "subdivisionWithoutPhase"], allUniqueSubdivisions[i]], colorArray[i % colorArray.length],
        );
        // fillColor.push(
        //   allUniqueSubdivisions[i],
        //   colorArray[i % colorArray.length]
        // );
      }
    } else {
      // if this is the first time showing parcels
      fillColor.push(
        ["==", ["get", "subdivisionWithoutPhase"], allUniqueSubdivisions[i]], colorArray[i % colorArray.length],
      );
      // fillColor.push(
      //   allUniqueSubdivisions[i],
      //   colorArray[i % colorArray.length]
      // );
    }
  }

  
  fillColor.push("transparent");
  
  const nullAVM = [
    "case",
    ["!=", ["get", leaseMode ? "rent" : "sales"], null], "transparent",
    ...fillColor.slice(1),
  ];
  // regular parcel dot

  setPaintPropertySafely(map, "parcelMapScopeLayer", "circle-color", fillColor);
  setPaintPropertySafely(map, "parcelMapScopeLayer", "circle-radius", [
    "interpolate",
    ["linear"],
    ["zoom"],
    zoomLevelToChangeBasemap,
    3,
    zoomLevelToShowParcelAVM,
    6,
  ]);
  setPaintPropertySafely(map, "parcelMapScopeLayer", "circle-stroke-width", 1);
  // parcel avm
  setPaintPropertySafely(
    map,
    "parcelMapScopeLayerRentAVM",
    "text-halo-color",
    fillColor
  );
  setPaintPropertySafely(
    map,
    "parcelMapScopeLayerRentNullAVM",
    "circle-color",
    nullAVM
  );
  // setPaintPropertySafely(
  //   map,
  //   "parcelMapScopeLayerRentNullAVM",
  //   "circle-radius",
  //   [
  //     "interpolate",
  //     ["linear"],
  //     ["zoom"],
  //     zoomLevelToChangeBasemap,
  //     3,
  //     zoomLevelToShowParcelAVM,
  //     6,
  //   ]
  // );
  // setPaintPropertySafely(
  //   map,
  //   "parcelMapScopeLayerRentNullAVM",
  //   "circle-stroke-width",
  //   1
  // );

  return fillColor;
};

export const setParcelColorByOwnerAndInstitution = (
  map,
  selectedOwnerParcel
) => {
  let fillColor = ["match", ["get", "ownerColor"]];

  console.log("selectedOwnerParcel", selectedOwnerParcel);
  fillColor.push(
    OWNER_COLOR.ownerOccupiedColor,
    selectedOwnerParcel.includes("Owner Occupied")
      ? "rgba(0,0,0,.1)"
      : "transparent"
  );

  fillColor.push(
    OWNER_COLOR.AH4RColor,
    selectedOwnerParcel.includes("AH4R") ? OWNER_COLOR.AH4RColor : "transparent"
  );

  fillColor.push(
    OWNER_COLOR.amherstColor,
    selectedOwnerParcel.includes("Amherst")
      ? OWNER_COLOR.amherstColor
      : "transparent"
  );

  fillColor.push(
    OWNER_COLOR.cerberusColor,
    selectedOwnerParcel.includes("Cerberus")
      ? OWNER_COLOR.cerberusColor
      : "transparent"
  );

  fillColor.push(
    OWNER_COLOR.invitationHomes,
    selectedOwnerParcel.includes("Invitation Homes")
      ? OWNER_COLOR.invitationHomes
      : "transparent"
  );

  fillColor.push(
    OWNER_COLOR.progressResColor,
    selectedOwnerParcel.includes("Progress Residential")
      ? OWNER_COLOR.progressResColor
      : "transparent"
  );

  fillColor.push(
    OWNER_COLOR.triconColor,
    selectedOwnerParcel.includes("Tricon")
      ? OWNER_COLOR.triconColor
      : "transparent"
  );

  fillColor.push(
    OWNER_COLOR.momAndPopColor,
    selectedOwnerParcel.includes("Mom & Pop")
      ? OWNER_COLOR.momAndPopColor
      : "transparent"
  );

  fillColor.push(
    OWNER_COLOR.othersColor,
    selectedOwnerParcel.includes("Other Funds")
      ? OWNER_COLOR.othersColor
      : "transparent"
  );

  fillColor.push("#000");

  // regular parcel dot

  setPaintPropertySafely(map, "parcelMapScopeLayer", "circle-color", fillColor);
  setPaintPropertySafely(map, "parcelMapScopeLayer", "circle-radius", [
    "interpolate",
    ["linear"],
    ["zoom"],
    zoomLevelToChangeBasemap,
    3.5,
    zoomLevelToShowParcelAVM,
    8,
  ]);
  // parcel avm
  setPaintPropertySafely(
    map,
    "parcelMapScopeLayerRentAVM",
    "text-halo-color",
    fillColor
  );
  // parcel null avm dot
  setPaintPropertySafely(
    map,
    "parcelMapScopeLayerRentNullAVM",
    "circle-color",
    fillColor
  );
  setPaintPropertySafely(
    map,
    "parcelMapScopeLayerRentNullAVM",
    "circle-radius",
    [
      "interpolate",
      ["linear"],
      ["zoom"],
      zoomLevelToChangeBasemap,
      3.5,
      zoomLevelToShowParcelAVM,
      8,
    ]
  );
};

// prettier-ignore
export const setParcelColorByOwnerAndInstitutionV2 = (
  map,
  selectedOwnerParcel,
  leaseMode,
) => {
  let fillColor = [
    "case", 
  ];

  if (selectedOwnerParcel.includes("Owner Occupied")) {
    fillColor.push(["==", ["get", "owner_occupied_sl"], "Yes"], "rgba(0,0,0,.1)");
  }

  if (selectedOwnerParcel.includes("AH4R")) {
    fillColor.push(["all",["==", ["get", "owner_occupied_sl"], "No"], ["==", ["get", "institution"], "AH4R"]], OWNER_COLOR.AH4RColor);
  }

  if (selectedOwnerParcel.includes("Amherst")) {
    fillColor.push(["all",["==", ["get", "owner_occupied_sl"], "No"], ["==", ["get", "institution"], "Amherst"]], OWNER_COLOR.amherstColor);
  }

  if (selectedOwnerParcel.includes("Cerberus")) {
    fillColor.push(["all",["==", ["get", "owner_occupied_sl"], "No"], ["==", ["get", "institution"], "Cerberus"]], OWNER_COLOR.cerberusColor);
  }

  if (selectedOwnerParcel.includes("Invitation Homes")) {
    fillColor.push(["all",["==", ["get", "owner_occupied_sl"], "No"], ["==", ["get", "institution"], "Invitation Homes"]], OWNER_COLOR.invitationHomes);
  }

  if (selectedOwnerParcel.includes("Progress Residential")) {
    fillColor.push(["all",["==", ["get", "owner_occupied_sl"], "No"], ["==", ["get", "institution"], "Progress Residential"]], OWNER_COLOR.progressResColor);
  }

  if (selectedOwnerParcel.includes("Tricon")) {
    fillColor.push(["all",["==", ["get", "owner_occupied_sl"], "No"], ["==", ["get", "institution"], "Tricon"]], OWNER_COLOR.triconColor);
  }
  
  if (selectedOwnerParcel.includes("Other Funds")) {
    fillColor.push(["all",["==", ["get", "owner_occupied_sl"], "No"], ["==", ["get", "institution"], "Other"]], OWNER_COLOR.othersColor);
  }

  if (selectedOwnerParcel.includes("Mom & Pop")) {
    fillColor.push(["all",["==", ["get", "owner_occupied_sl"], "No"], ["==", ["get", "institution"], null]], OWNER_COLOR.momAndPopColor);
  }

  fillColor.push("transparent");

  const nullAVM = [
    "case",
    ["!=", ["get", leaseMode ? "rent" : "sales"], null], "transparent",
    ...fillColor.slice(1),
  ];

  // regular parcel dot
  setPaintPropertySafely(map, "parcelMapScopeLayer", "circle-color", fillColor);
  setPaintPropertySafely(map, "parcelMapScopeLayer", "circle-radius", [
    "interpolate",
    ["linear"],
    ["zoom"],
    zoomLevelToChangeBasemap,
    3.5,
    zoomLevelToShowParcelAVM,
    8,
  ]);
  // parcel avm
  setPaintPropertySafely(
    map,
    "parcelMapScopeLayerRentAVM",
    "text-halo-color",
    fillColor
  );
  // parcel null avm dot
  setPaintPropertySafely(
    map,
    "parcelMapScopeLayerRentNullAVM",
    "circle-color",
    nullAVM
  );
  // setPaintPropertySafely(
  //   map,
  //   "parcelMapScopeLayerRentNullAVM",
  //   "circle-radius",
  //   [
  //     "interpolate",
  //     ["linear"],
  //     ["zoom"],
  //     zoomLevelToChangeBasemap,
  //     3.5,
  //     zoomLevelToShowParcelAVM,
  //     8,
  //   ]
  // );
};

// prettier-ignore
export const setParcelColorByHOAFeesV2 = (map, selectedHOAFeeParcel, leaseMode) => {
  let fillColor = ["case"];

  if (selectedHOAFeeParcel.includes("$0")) {
    fillColor.push(["any", ["==", ["get", "hoa_fees"], 0], ["==", ["get", "hoa_fees"], null]], HOA_FEE_COLOR.haoFeeRange1Color);
  }

  if (selectedHOAFeeParcel.includes("$1-200")) {
    fillColor.push(["all", [">", ["get", "hoa_fees"], 0], ["<=", ["get", "hoa_fees"], 200]], HOA_FEE_COLOR.haoFeeRange2Color);
  }

  if (selectedHOAFeeParcel.includes("$201-600")) {
    fillColor.push(["all", [">", ["get", "hoa_fees"], 200], ["<=", ["get", "hoa_fees"], 600]], HOA_FEE_COLOR.haoFeeRange3Color);
  }

  if (selectedHOAFeeParcel.includes("$601-1000")) {
    fillColor.push(["all", [">", ["get", "hoa_fees"], 600], ["<=", ["get", "hoa_fees"], 1000]], HOA_FEE_COLOR.haoFeeRange4Color);
  }

  if (selectedHOAFeeParcel.includes("$1001+")) {
    fillColor.push([">", ["get", "hoa_fees"], 1000], HOA_FEE_COLOR.haoFeeRange5Color);
  }

  fillColor.push("transparent");

  const nullAVM = [
    "case",
    ["!=", ["get", leaseMode ? "rent" : "sales"], null], "transparent",
    ...fillColor.slice(1),
  ];

  // regular parcel dot

  setPaintPropertySafely(map, "parcelMapScopeLayer", "circle-color", fillColor);
  setPaintPropertySafely(map, "parcelMapScopeLayer", "circle-radius", [
    "interpolate",
    ["linear"],
    ["zoom"],
    zoomLevelToChangeBasemap,
    3.5,
    zoomLevelToShowParcelAVM,
    8,
  ]);
  // parcel avm
  setPaintPropertySafely(
    map,
    "parcelMapScopeLayerRentAVM",
    "text-halo-color",
    fillColor
  );
  // parcel null avm dot
  setPaintPropertySafely(
    map,
    "parcelMapScopeLayerRentNullAVM",
    "circle-color",
    nullAVM
  );
  // setPaintPropertySafely(
  //   map,
  //   "parcelMapScopeLayerRentNullAVM",
  //   "circle-radius",
  //   [
  //     "interpolate",
  //     ["linear"],
  //     ["zoom"],
  //     zoomLevelToChangeBasemap,
  //     3.5,
  //     zoomLevelToShowParcelAVM,
  //     8,
  //   ]
  // );
};

export const setParcelColorByHOAFees = (map, selectedHOAFeeParcel) => {
  let fillColor = ["match", ["get", "hoaColor"]];
  fillColor.push(
    HOA_FEE_COLOR.haoFeeRange1Color,
    selectedHOAFeeParcel.includes("$0")
      ? HOA_FEE_COLOR.haoFeeRange1Color
      : "transparent"
  );

  fillColor.push(
    HOA_FEE_COLOR.haoFeeRange2Color,
    selectedHOAFeeParcel.includes("$1-200")
      ? HOA_FEE_COLOR.haoFeeRange2Color
      : "transparent"
  );

  fillColor.push(
    HOA_FEE_COLOR.haoFeeRange3Color,
    selectedHOAFeeParcel.includes("$201-600")
      ? HOA_FEE_COLOR.haoFeeRange3Color
      : "transparent"
  );

  fillColor.push(
    HOA_FEE_COLOR.haoFeeRange4Color,
    selectedHOAFeeParcel.includes("$601-1000")
      ? HOA_FEE_COLOR.haoFeeRange4Color
      : "transparent"
  );

  fillColor.push(
    HOA_FEE_COLOR.haoFeeRange5Color,
    selectedHOAFeeParcel.includes("$1001+")
      ? HOA_FEE_COLOR.haoFeeRange5Color
      : "transparent"
  );

  fillColor.push("#000");

  // regular parcel dot

  setPaintPropertySafely(map, "parcelMapScopeLayer", "circle-color", fillColor);
  setPaintPropertySafely(map, "parcelMapScopeLayer", "circle-radius", [
    "interpolate",
    ["linear"],
    ["zoom"],
    zoomLevelToChangeBasemap,
    3.5,
    zoomLevelToShowParcelAVM,
    8,
  ]);
  // parcel avm
  setPaintPropertySafely(
    map,
    "parcelMapScopeLayerRentAVM",
    "text-halo-color",
    fillColor
  );
  // parcel null avm dot
  setPaintPropertySafely(
    map,
    "parcelMapScopeLayerRentNullAVM",
    "circle-color",
    fillColor
  );
  setPaintPropertySafely(
    map,
    "parcelMapScopeLayerRentNullAVM",
    "circle-radius",
    [
      "interpolate",
      ["linear"],
      ["zoom"],
      zoomLevelToChangeBasemap,
      3.5,
      zoomLevelToShowParcelAVM,
      8,
    ]
  );
};

export const setDistrictColor = (map, districtData, prevFillColor) => {
  if (districtData.features.length === 0) return;

  const colorArray = [
    "#e41a1c",
    "#377eb8",
    "#4daf4a",
    "#984ea3",
    "#ff7f00",
    "#a8a802",
    "#a65628",
    "#f781bf",
    "#999999",
  ];

  const replacingColor = [
    "#EFA94A",
    "#C35831",
    "#CF3476",
    "#26252D",
    "#79553D",
  ];

  let fillColor = ["match", ["get", "obj_name"]];
  let newColors = [];
  const districts = districtData.features;

  for (let i = 0; i < districts.length; i++) {
    if (prevFillColor && prevFillColor.length > 0) {
      const prevColorIndex = prevFillColor.indexOf(
        districts[i].properties["obj_name"]
      );
      if (prevColorIndex !== -1) {
        fillColor.push(
          districts[i].properties["obj_name"],
          prevFillColor[prevColorIndex + 1]
        );
      } else {
        fillColor.push(
          districts[i].properties["obj_name"],
          colorArray[i % colorArray.length]
        );
        newColors.push([
          fillColor.length - 1,
          i,
          colorArray[i % colorArray.length],
        ]);
      }
    } else {
      fillColor.push(
        districts[i].properties["obj_name"],
        colorArray[i % colorArray.length]
      );
    }
  }

  for (let i = 0; i < newColors.length; i++) {
    if (fillColor.filter((color) => color === newColors[i][2]).length > 1) {
      // duplicates color exist from newly added- replace with diff color array
      fillColor[newColors[i][0]] =
        replacingColor[newColors[i][1] % replacingColor.length];
    }
  }

  fillColor.push("#000");

  setPaintPropertySafely(
    map,
    `${MAP_LAYER_NAME_BASE.district}LayerFill`,
    "fill-color",
    fillColor
  );

  return fillColor;
};

export const setAttendanceZoneColor = (map, attendanceData) => {
  if (attendanceData.features.length === 0) return;

  const ratingColorArray = [
    "#d7191c",
    "#e85b3a",
    "#ffb987",
    "#fec981",
    "#ffedab",
    "#ecf7ad",
    "#c4e687",
    "#97d265",
    "#58b453",
    "#1a9641",
  ];

  let fillColor = ["match", ["get", "rating"]];

  for (let i = 0; i < ratingColorArray.length; i++) {
    fillColor.push(i + 1, ratingColorArray[i]);
  }

  // fillColor.push('#000');
  fillColor.push("transparent");

  setPaintPropertySafely(
    map,
    `${MAP_LAYER_NAME_BASE.attendance}FillLayer`,
    "fill-color",
    fillColor
  );
};

export const setTaxZoneColor = (map) => {
  const ratingColorArray = [
    "#1a9641",
    "#97d265",
    "#c4e687",
    "rgb(255, 237, 171)",
    "rgb(255, 185, 135)",
    "rgb(232, 91, 58)",
    "rgb(215, 25, 28)",
  ];

  let fillColor = ["match", ["get", "tax_rate"]];

  for (let i = 0; i < ratingColorArray.length; i++) {
    fillColor.push(i + 1, ratingColorArray[i]);
  }

  // fillColor.push('#000');
  fillColor.push("transparent");

  console.log("test fill Color", fillColor);
  setPaintPropertySafely(
    map,
    `${MAP_LAYER_NAME_BASE.tax}-fill`,
    "fill-color",
    fillColor
  );
};

export const setCBSAColor = (map, cbsaData, prevFillColor) => {
  if (cbsaData.features.length === 0) return;

  const colorArray = [
    "#e41a1c",
    "#377eb8",
    "#4daf4a",
    "#984ea3",
    "#ff7f00",
    "#a8a802",
    "#a65628",
    "#f781bf",
    "#999999",
  ];

  const replacingColor = [
    "#EFA94A",
    "#C35831",
    "#CF3476",
    "#26252D",
    "#79553D",
  ];

  let fillColor = ["match", ["get", "name"]];
  let newColors = [];
  const data = cbsaData.features;

  for (let i = 0; i < data.length; i++) {
    if (data[i].geometry.type === "Point") continue;

    if (prevFillColor && prevFillColor.length > 0) {
      const prevColorIndex = prevFillColor.indexOf(data[i].properties["name"]);
      if (prevColorIndex !== -1) {
        fillColor.push(
          data[i].properties["name"],
          prevFillColor[prevColorIndex + 1]
        );
      } else {
        fillColor.push(
          data[i].properties["name"],
          colorArray[i % colorArray.length]
        );
        newColors.push([
          fillColor.length - 1,
          i,
          colorArray[i % colorArray.length],
        ]);
      }
    } else {
      fillColor.push(
        data[i].properties["name"],
        colorArray[i % colorArray.length]
      );
    }
  }

  for (let i = 0; i < newColors.length; i++) {
    if (fillColor.filter((color) => color === newColors[i][2]).length > 1) {
      // duplicates color exist from newly added- replace with diff color array
      fillColor[newColors[i][0]] =
        replacingColor[newColors[i][1] % replacingColor.length];
    }
  }

  fillColor.push("#000");

  setPaintPropertySafely(
    map,
    `${MAP_LAYER_NAME_BASE.cbsa}LayerFill`,
    "fill-color",
    fillColor
  );

  return fillColor;
};

export const setCountyColor = (map, countyData, prevFillColor) => {
  if (countyData.features.length === 0) return;

  const colorArray = [
    "#e41a1c",
    "#377eb8",
    "#4daf4a",
    "#984ea3",
    "#ff7f00",
    "#a8a802",
    "#a65628",
    "#f781bf",
    "#999999",
  ];

  const replacingColor = [
    "#EFA94A",
    "#C35831",
    "#CF3476",
    "#26252D",
    "#79553D",
  ];

  let fillColor = ["match", ["get", "name"]];
  let newColors = [];
  const counties = countyData.features;

  for (let i = 0; i < counties.length; i++) {
    if (counties[i].geometry.type === "Point") continue;

    if (prevFillColor && prevFillColor.length > 0) {
      const prevColorIndex = prevFillColor.indexOf(
        counties[i].properties["name"]
      );
      if (prevColorIndex !== -1) {
        fillColor.push(
          counties[i].properties["name"],
          prevFillColor[prevColorIndex + 1]
        );
      } else {
        fillColor.push(
          counties[i].properties["name"],
          colorArray[i % colorArray.length]
        );
        newColors.push([
          fillColor.length - 1,
          i,
          colorArray[i % colorArray.length],
        ]);
      }
    } else {
      fillColor.push(
        counties[i].properties["name"],
        colorArray[i % colorArray.length]
      );
    }
  }

  for (let i = 0; i < newColors.length; i++) {
    if (fillColor.filter((color) => color == newColors[i][2]).length > 1) {
      // duplicates color exist from newly added- replace with diff color array
      fillColor[newColors[i][0]] =
        replacingColor[newColors[i][1] % replacingColor.length];
    }
  }

  fillColor.push("#000");

  setPaintPropertySafely(
    map,
    `${MAP_LAYER_NAME_BASE.county}LayerFill`,
    "fill-color",
    fillColor
  );

  return fillColor;
};

export const setZipCodeColor = (map, zipCodeData, prevFillColor) => {
  if (zipCodeData.features.length === 0) return;

  const colorArray = [
    "#e41a1c",
    "#377eb8",
    "#4daf4a",
    "#984ea3",
    "#ff7f00",
    "#a8a802",
    "#a65628",
    "#f781bf",
    "#999999",
  ];

  const replacingColor = [
    "#EFA94A",
    "#C35831",
    "#CF3476",
    "#26252D",
    "#79553D",
  ];

  let fillColor = ["match", ["get", "key"]];
  let newColors = [];
  const zipcode = zipCodeData.features;

  for (let i = 0; i < zipcode.length; i++) {
    if (zipcode[i].geometry.type === "Point") continue;

    if (prevFillColor && prevFillColor.length > 0) {
      const prevColorIndex = prevFillColor.indexOf(
        zipcode[i].properties["key"]
      );
      if (prevColorIndex !== -1) {
        fillColor.push(
          zipcode[i].properties["key"],
          prevFillColor[prevColorIndex + 1]
        );
      } else {
        fillColor.push(
          zipcode[i].properties["key"],
          colorArray[i % colorArray.length]
        );
        newColors.push([
          fillColor.length - 1,
          i,
          colorArray[i % colorArray.length],
        ]);
      }
    } else {
      fillColor.push(
        zipcode[i].properties["key"],
        colorArray[i % colorArray.length]
      );
    }
  }

  for (let i = 0; i < newColors.length; i++) {
    if (fillColor.filter((color) => color == newColors[i][2]).length > 1) {
      // duplicates color exist from newly added- replace with diff color array
      fillColor[newColors[i][0]] =
        replacingColor[newColors[i][1] % replacingColor.length];
    }
  }

  fillColor.push("#000");

  setPaintPropertySafely(
    map,
    `${MAP_LAYER_NAME_BASE.zipcode}LayerFill`,
    "fill-color",
    fillColor
  );

  return fillColor;
};
