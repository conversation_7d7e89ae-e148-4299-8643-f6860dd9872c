import { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Tooltip } from "antd";
import styles from "./MapStreetview.module.css";

const disableUIControls = () => {
  document.querySelector("#TopRightControl").style.display = "none";
  document.querySelector("#TopLeftControl").style.display = "none";
  document.querySelector("#MapTheme").style.visibility = "hidden";
  document.querySelector("#MapLayers").style.display = "none";
  document.querySelector("#MapSentinelLayerControl").style.display = "none";
  document.querySelector("#radiusSelectWrapper").style.display = "none";
};

const enableUIControls = () => {
  document.querySelector("#TopRightControl").style.display = "flex";
  document.querySelector("#TopLeftControl").style.display = "flex";
  document.querySelector("#MapTheme").style.visibility = "visible";
  document.querySelector("#MapLayers").style.display = "block";
  document.querySelector("#MapSentinelLayerControl").style.display = "block";
  document.querySelector("#radiusSelectWrapper").style.display = "flex";
};

function MapStreetview() {
  const eventCoordinates = useSelector((state) => state.Map.eventCoordinates);
  const mapViewMode = useSelector((state) => state.Map.mapViewMode);
  const dispatch = useDispatch();

  const [streetViewAvailable, setStreetViewAvailable] = useState(false);

  // useEffect(() => {
  //   if (eventCoordinates.length > 0) {
  //     if (mapViewMode !== "Map") {
  //       dispatch({ type: "Map/saveState", payload: { mapViewMode: "Map" } });
  //     }
  //   }
  // }, [eventCoordinates]);

  useEffect(() => {
    if (eventCoordinates.length > 0) {
      const streetViewAvailable = async () => {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/streetview/metadata?size=600x300&location=${eventCoordinates[1]},${eventCoordinates[0]}&heading=-45&pitch=42&fov=110&key=AIzaSyB72GqI_rbe0EOYu27FGU5naCDrqMZBkzs`
        );
        const data = await response.json();
        if (data && data.status && data.status === "OK") {
          setStreetViewAvailable(true);
        } else {
          setStreetViewAvailable(false);
        }
      };

      streetViewAvailable();
    }
  }, [eventCoordinates]);

  const changeMapViewMode = () => {
    if (mapViewMode === "Map") {
      dispatch({ type: "Map/saveState", payload: { mapViewMode: "Street" } });
      document.querySelector("#mapContainer").style.display = "none";
      document.querySelector("#streetview").style.display = "block";
      disableUIControls();
    } else {
      dispatch({ type: "Map/saveState", payload: { mapViewMode: "Map" } });
      document.querySelector("#mapContainer").style.display = "block";
      document.querySelector("#streetview").style.display = "none";
      enableUIControls();
    }
  };

  if (eventCoordinates.length > 0) {
    return (
      <div id="MapStreetview">
        {streetViewAvailable ? (
          <button
            className={`${styles.streetBtn} ${
              mapViewMode === "Map"
                ? styles.streetBtnMapMode
                : styles.streetBtnStreetMode
            }`}
            onClick={changeMapViewMode}
          >
            <span>{mapViewMode === "Map" ? "Street" : "Map"}</span>
          </button>
        ) : (
          <Tooltip
            title="Streetview not available in current location"
            placement="left"
          >
            <button
              className={`${styles.streetBtn} ${
                mapViewMode === "Map"
                  ? styles.streetBtnMapMode
                  : styles.streetBtnStreetMode
              }`}
              style={{ opacity: 0.5, cursor: "not-allowed" }}
              onClick={null}
            >
              <span>{mapViewMode === "Map" ? "Street" : "Map"}</span>
            </button>
          </Tooltip>
        )}
      </div>
    );
  } else {
    return null;
  }
}

export default MapStreetview;
