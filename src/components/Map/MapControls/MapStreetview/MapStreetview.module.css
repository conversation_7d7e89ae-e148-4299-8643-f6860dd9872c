.streetBtn {
  border: 2px solid white;
  background: transparent;
  padding: 0;
  width: 55px;
  height: 55px;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);

  position: relative;
}
.streetBtnMapMode {
  background-size: cover;
  background-image: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0) 25%,
      rgba(0, 0, 0, 0.5) 80%,
      rgba(0, 0, 0, 0.75)
    ),
    /* url("/images/mapbox/controls/automatic.png"); */
      url("../../../../assets/images/mapbox/controls/streetview_temp.png");
}

.streetBtnStreetMode {
  background-size: cover;
  background-image: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0) 25%,
      rgba(0, 0, 0, 0.5) 80%,
      rgba(0, 0, 0, 0.75)
    ),
    url("../../../../assets/images/mapbox/controls/automatic.png");
}

.streetBtn:hover {
  cursor: pointer;
}

.streetBtn span {
  position: absolute;
  bottom: 5px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: white;
  font-weight: 600;
  font-family: Arial, "helvetica", "sans-serif";
}
