import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
// import mapboxgl from "!mapbox-gl"; // eslint-disable-line import/no-webpack-loader-syntax
import styles from "../MapControls.module.css";

// const zoomControlButtons = new mapboxgl.NavigationControl({
//   showCompass: false,
//   showZoom: true,
// });

function MapZoom() {
  const [zoomInDisable, setZoomInDisable] = useState(false);
  const [zoomOutDisable, setZoomOutDisable] = useState(false);

  const map = useSelector((state) => state.Map.map);

  const zoomInHandler = () => {
    map.zoomIn();
    if (map.getZoom() + 1 >= 22 && !zoomInDisable) {
      setZoomInDisable(true);
    }
    if (zoomOutDisable) {
      setZoomOutDisable(false);
    }
  };

  const zoomOutHandler = () => {
    map.zoomOut();
    if (map.getZoom() - 1 <= -2 && !zoomOutDisable) {
      setZoomOutDisable(true);
    }
    if (zoomInDisable) {
      setZoomInDisable(false);
    }
  };

  return (
    <div
      id="MapZoom"
      className={`mapboxgl-ctrl mapboxgl-ctrl-group ${styles.zoomCtrlStyle}`}
    >
      <button
        onClick={zoomInHandler}
        className="mapboxgl-ctrl-zoom-in"
        type="button"
        aria-label="Zoom in"
        aria-disabled={zoomInDisable}
        disabled={zoomInDisable}
      >
        <span
          className="mapboxgl-ctrl-icon"
          aria-hidden="true"
          title="Zoom in"
        ></span>
      </button>
      <button
        onClick={zoomOutHandler}
        className="mapboxgl-ctrl-zoom-out"
        type="button"
        aria-label="Zoom out"
        aria-disabled={zoomOutDisable}
        disabled={zoomOutDisable}
      >
        <span
          className="mapboxgl-ctrl-icon"
          aria-hidden="true"
          title="Zoom out"
        ></span>
      </button>
    </div>
  );
}

export default MapZoom;
