import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Radio } from "antd";

const RangeAndColorSettings = ({ type }) => {
  const heatmapRangeColorType = useSelector(
    (state) => state.Map.heatmapRangeColorType
  );
  const dispatch = useDispatch();

  useEffect(() => {
    if (!heatmapRangeColorType[type]) {
      dispatch({
        type: "Map/saveState",
        payload: {
          heatmapRangeColorType: {
            ...heatmapRangeColorType,
            [type]: {
              rangeType: "country",
              colorType: "default",
            },
          },
        },
      });
    }
  });

  const onChangeRangeType = (e) => {
    dispatch({
      type: "Map/saveState",
      payload: {
        heatmapRangeColorType: {
          ...heatmapRangeColorType,
          [type]: {
            ...heatmapRangeColorType[type],
            rangeType: e.target.value,
          },
        },
      },
    });
  };

  const onChangeColorType = (e) => {
    dispatch({
      type: "Map/saveState",
      payload: {
        heatmapRangeColorType: {
          ...heatmapRangeColorType,
          [type]: {
            ...heatmapRangeColorType[type],
            colorType: e.target.value,
          },
        },
      },
    });
  };

  const benchmarkRadio = [];
  if (type === "crime_score" ) {
    benchmarkRadio.push(<Radio value={"country"}>Metro</Radio>);
  }else if (type === "combinedFilterScore") {
    benchmarkRadio.push(<Radio value={"country"}>Country</Radio>);
  } else {
    benchmarkRadio.push(<Radio value={"country"}>Country</Radio>);
    benchmarkRadio.push(<Radio value={"metro"}>Metro</Radio>);
  }

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "row",
        justifyContent: "space-between",
      }}
    >
      <div style={{ display: "flex", flexDirection: "column", width: "100%" }}>
        <strong>Benchmark</strong>
        <Radio.Group
          value={
            heatmapRangeColorType[type]
              ? heatmapRangeColorType[type].rangeType
              : "country"
          }
          onChange={onChangeRangeType}
        >
          {/* <Radio value={"metro"}>Metro</Radio>
          <Radio value={"country"}>Country</Radio> */}
          {benchmarkRadio}
        </Radio.Group>
      </div>
      <div style={{ display: "flex", flexDirection: "column", width: "100%" }}>
        <strong>Color</strong>
        <Radio.Group
          value={
            heatmapRangeColorType[type]
              ? heatmapRangeColorType[type].colorType
              : "default"
          }
          onChange={onChangeColorType}
        >
          <Radio value={"default"}>Default</Radio>
          <Radio value={"blues"}>Blues</Radio>
        </Radio.Group>
      </div>
    </div>
  );
};

export default RangeAndColorSettings;
