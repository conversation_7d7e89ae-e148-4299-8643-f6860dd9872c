import { useEffect, useRef, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { <PERSON>ton, Popover, Tooltip } from "antd";
import { formatStudyValue } from "./utils";
import "./heatmapMenu.css";
import { IoCloseSharp } from "@react-icons/all-files/io5/IoCloseSharp";
import { FiSettings } from "@react-icons/all-files/fi/FiSettings";
import { IoInformationOutline } from "@react-icons/all-files/io5/IoInformationOutline";
import { FaFilter } from "@react-icons/all-files/fa/FaFilter";
import Filter from "./Filter";
import RangeAndColorSettings from "./RangeAndColorSettings";
import RangeAndColorInfo from "./RangeAndColorInfo";

const MenuRow = ({
  title,
  studyType,
  summary,
  selected,
  changeStudyType,
  heatmapType,
  ranges,
  popupAlign,
  popupOpen,
  setPopupOpen,
}) => {
  const heatmapBGFilters = useSelector((state) => state.Map.heatmapBGFilters);
  const submarketBGEnabled = useSelector(
    (state) => state.Map.submarketBGEnabled
  );

  return (
    <section
      className={`heatmapMenu-row ${selected ? "heatmapMenu-selected" : ""}`}
      onClick={(e) => {
        e.stopPropagation();
        changeStudyType();
      }}
    >
      <div
        style={{
          padding: selected ? "5px 0" : "2.5px 0px 0px",
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "flex-end",
        }}
      >
        <div
          style={{
            display: "flex",
            flexDirection: "row",
            gap: "10px",
            alignItems: "center",
          }}
        >
          <strong>{title}</strong>
          {(heatmapType === "demographics" ||
            (heatmapType == "submarket" && submarketBGEnabled)) &&
            heatmapBGFilters[studyType]?.active && (
              <Tooltip title={"Filter is active"}>
                <FaFilter size={12} />
              </Tooltip>
            )}
        </div>
        {selected && (
          <Popover
            getPopupContainer={(trigger) => trigger.parentElement}
            popupAlign={{ offset: popupAlign }}
            title={
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "space-between",
                }}
              >
                <span>{`${title} ${
                  heatmapType === "demographics" ? "Settings" : "Info"
                }`}</span>
                <Button
                  className="heatmapMenu-settingsCloseBtn"
                  type="text"
                  shape="circle"
                  size="small"
                  onClick={() => setPopupOpen(false)}
                >
                  <IoCloseSharp size={20} />
                </Button>
              </div>
            }
            content={
              <div
                style={{
                  width: "300px",
                  display: "flex",
                  flexDirection: "column",
                  gap: "10px",
                }}
              >
                <RangeAndColorSettings type={studyType} />
                <RangeAndColorInfo type={studyType} ranges={ranges} />
                {(heatmapType === "demographics" ||
                  (heatmapType == "submarket" && submarketBGEnabled)) && (
                  <Filter
                    type={studyType}
                    min={summary.min}
                    max={summary.max}
                  />
                )}
              </div>
            }
            placement="right"
            open={popupOpen}
            trigger={"click"}
            style={{ zIndex: 500 }}
          >
            <Button
              className="heatmapMenu-settingsBtn"
              shape="circle"
              onClick={() => setPopupOpen(true)}
            >
              {heatmapType === "demographics" ||
              (heatmapType == "submarket" && submarketBGEnabled) ? (
                <FiSettings />
              ) : (
                <IoInformationOutline />
              )}
            </Button>
          </Popover>
        )}
      </div>
      <div
        style={{
          height: "2px",
          backgroundColor: "#bbb",
        }}
      ></div>
      <div
        style={{
          padding: "0px 0px 2.5px",
          display: "flex",
          flexDirection: "row",
          gap: "10px",
          justifyContent: "space-between",
        }}
      >
        <span style={{ color: "gray", width: "100px", whiteSpace: "nowrap" }}>
          <span style={{ fontSize: "12px" }}>Min: </span>
          <strong style={{ color: "black" }}>
            {formatStudyValue(studyType, summary.min)}
          </strong>
        </span>
        <span style={{ color: "gray", width: "100px", whiteSpace: "nowrap" }}>
          <span style={{ fontSize: "12px" }}>Median: </span>
          <strong style={{ color: "black" }}>
            {formatStudyValue(studyType, summary.median)}
          </strong>
        </span>
        <span style={{ color: "gray", width: "100px", whiteSpace: "nowrap" }}>
          <span style={{ fontSize: "12px" }}>Max: </span>

          <strong style={{ color: "black" }}>
            {formatStudyValue(studyType, summary.max)}
          </strong>
        </span>
      </div>
    </section>
  );
};

export default MenuRow;
