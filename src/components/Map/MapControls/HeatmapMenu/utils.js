import {
  submarketRanges,
  demographicsRanges,
} from "../../../../utils/heatmapRanges";
export const heatmapSubmarketStudyTypes = [
  "vacancy_rate",
  "market_effective_rent_growth_12_months",
  "market_effective_rent_sf",
  "market_effective_rent_unit",
  "rental_growth_5_years",
  "combinedFilterScore",
];

export const heatmapDemographicsStudyTypes = [
  "median_hh_income",
  "five_year_pop_growth",
  // "five_year_income_growth",
  "bachelors_and_above",
  "median_age",
  "household_size",
  "household_growth",
  "rental_growth_5_years",
  "rent_vs_owner_percentage",
  "population_density",
  "fifty_five_plus",
  "median_home_value",
  "median_rent",
  "rent_vs_own",
  "crime_score",
  "combinedFilterScore",
];

export const formatStudyValue = (studyType, value, decimal = false) => {
  if (value == null || !isFinite(value)) {
    return "--";
  }
  const formatDollar = (value) =>
    value.toLocaleString("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });

  const formatDollarDecimal = (value) =>
    value.toLocaleString("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

  if (
    [
      // "market_effective_rent_sf",
      "market_effective_rent_unit",
      "median_hh_income",
      "median_home_value",
      "median_rent",
      "rent_vs_own",
    ].includes(studyType)
  ) {
    if (decimal) {
      return formatDollarDecimal(value || 0);
    } else {
      return formatDollar(value || 0);
    }
  } else if (["market_effective_rent_sf"].includes(studyType)) {
    return formatDollarDecimal(value || 0);
  } else if (["bachelors_and_above"].includes(studyType)) {
    return `${Math.round(value) || 0}%`;
  } else if (
    [
      "vacancy_rate",
      "market_effective_rent_growth_12_months",
      "five_year_pop_growth",
      "five_year_income_growth",
      "household_growth",
      "rental_growth_5_years",
      "rent_vs_owner_percentage",
    ].includes(studyType)
  ) {
    return `${Math.round(value * 10) / 10 || 0}%`;
  } else if (["household_size", "fifty_five_plus"].includes(studyType)) {
    return `${Math.round(value * 10) / 10 || 0}`;
  } else if (
    [
      "median_age",
      "population_density",
      "crime_score",
      "combinedFilterScore",
    ].includes(studyType)
  ) {
    return `${
      value.toLocaleString("en-us", {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }) || 0
    }`;
  }
};

export const getTitleForStudyType = (type) => {
  switch (type) {
    case "vacancy_rate":
      return "Vacancy Rate (stabilized)";
    case "market_effective_rent_growth_12_months":
      return "Market Effective Rent Growth 12 Months";
    case "market_effective_rent_sf":
      return "Market Effective Rent/Sqft";
    case "market_effective_rent_unit":
      return "Market Effective Rent/Unit";
    case "median_hh_income":
      return "Median HH Income";
    case "five_year_pop_growth":
      return "5 Year Pop Growth";
    case "five_year_income_growth":
      return "5 Year Income Growth";
    case "bachelors_and_above":
      return "Bachelors and Above";
    case "median_age":
      return "Median Age";
    case "household_size":
      return "Household Size";
    case "household_growth":
      return "Household Growth";
    case "rental_growth_5_years":
      // return "Rental Growth 5 Years";
      return "Rent CAGR trailing 5 years";
    case "rent_vs_owner_percentage":
      return "Renter vs Owner Percentage (census)";
    case "population_density":
      return "Population Density (per mile)";
    case "fifty_five_plus":
      return "55+";
    case "median_home_value":
      return "Median Home Value";
    case "median_rent":
      return "Median Home Rent";
    case "rent_vs_own":
      return "Mtg Payment - Rent";
    case "crime_score":
      return "Crime Score";
    case "combinedFilterScore":
      return "Combined Filter Score";
    default:
      return "";
  }
};

// if (value === null) return value;
//       for (let i = 0; i < range.length; i++) {
//         if (i + 1 < range.length) {
//           if (value >= range[i].value && value < range[i + 1].value)
//             return color ? color[i] : range[i].color;
//         } else {
//           if (value >= range[i].value) return color ? color[i] : range[i].color;
//         }
//       }
//       // if below range
//       return color ? color[0] : range[0].color;

export const getColorScoreForStudyType = (type, value) => {
  let range = [];
  if (heatmapSubmarketStudyTypes.includes(type)) {
    range = submarketRanges[type];
  } else if (heatmapDemographicsStudyTypes.includes(type)) {
    range = demographicsRanges[type];
  }
  if (range) {
    if (value < range[0].value) return 1;
    for (let i = 0; i < range.length; i++) {
      if (i + 1 < range.length) {
        if (value >= range[i].value && value < range[i + 1].value) return i + 1;
      } else {
        if (value >= range[i].value) return i + 1;
      }
    }
  }

  return "N/A";
};

export const getStudySummary = (data, type) => {
  let keys = [];
  if (type === "submarket") {
    keys = heatmapSubmarketStudyTypes;
  } else if (type === "demographics") {
    keys = heatmapDemographicsStudyTypes;
  }

  const extactedValues = keys.reduce((acc, key) => {
    acc[key] = [];
    return acc;
  }, {});

  for (let i = 0; i < data.length; i++) {
    for (let j = 0; j < keys.length; j++) {
      if (data[i].hasOwnProperty(keys[j])) {
        let number;
        if (type === "submarket") {
          if (typeof data[i][keys[j]] === "string") {
            number = parseFloat(
              data[i][keys[j]]
                ?.match(/\d{1,3}(,\d{3})*(\.\d+)?|\.\d+/)?.[0]
                ?.replace(/,/g, "") ?? NaN
            );
          } else {
            number = data[i][keys[j]];
          }
        } else if (type === "demographics") {
          number = data[i][keys[j]];
        }
        if (!isNaN(number) && number !== null) {
          extactedValues[keys[j]].push(number);
        }
      }
    }
  }

  const result = keys.reduce((acc, key) => {
    // const min = Math.min(...extactedValues[key]);
    const min = Math.min.apply(null, extactedValues[key].filter(Boolean));;
    const max = Math.max(...extactedValues[key]);
    let median = null;
    if (min != null && max != null && isFinite(min) && isFinite(max)) {
      median = calculateMedian(extactedValues[key]);
    }
    acc[key] = {
      min: min,
      max: max,
      median: median,
    };
    return acc;
  }, {});

  return result;
};

const calculateMedian = (values) => {
  if (values.length === 0) {
    return 0;
  } else {
    values.sort(function (a, b) {
      return a - b;
    });

    var half = Math.floor(values.length / 2);

    if (values.length % 2) return values[half];

    return (values[half - 1] + values[half]) / 2.0;
  }
};
