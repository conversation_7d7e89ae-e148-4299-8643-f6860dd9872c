import React, { useEffect, useState } from "react";
import { formatStudyValue } from "./utils";
import invert from "invert-color";

const RangeAndColorInfo = ({ type, ranges = [] }) => {
  const [hoveredRange, setHoveredRange] = useState(null);
  const [rangeText, setRangeText] = useState("");

  useEffect(() => {
    if (hoveredRange) {
      const { idx, range } = hoveredRange;

      let text;
      if (type === "crime_score") {
        text = (
          <p style={{ margin: 0 }}>
            The color <strong>{range.color}</strong> represents crime score of{" "}
            <strong>{idx + 1}</strong>
          </p>
        );
      } else {
        if (idx === 0) {
          text = (
            <p style={{ margin: 0 }}>
              The color <strong>{range.color}</strong> represents values less
              than{" "}
              <strong>{formatStudyValue(type, ranges[idx + 1].value)}</strong>
            </p>
          );
        } else if (idx === ranges.length - 1) {
          text = (
            <p style={{ margin: 0 }}>
              The color <strong>{range.color}</strong> represents values greater
              than <strong>{formatStudyValue(type, ranges[idx].value)}</strong>
            </p>
          );
        } else {
          text = (
            <p style={{ margin: 0 }}>
              The color <strong>{range.color}</strong> represents values greater
              than or equal to{" "}
              <strong>{formatStudyValue(type, range.value)}</strong> but less
              than{" "}
              <strong>{formatStudyValue(type, ranges[idx + 1].value)}</strong>
            </p>
          );
        }
      }
      setRangeText(text);
    } else {
      setRangeText("");
    }
  }, [hoveredRange]);

  if (ranges.length === 0) return null;
  return (
    <div style={{ width: "100%" }}>
      <strong>Color and Score Ranges</strong>
      <div style={{ display: "flex", flexDirection: "row" }}>
        {ranges.map((range, idx) => {
          return (
            <div
              key={idx}
              className="heatmapMenu-rangeColor"
              style={{
                backgroundColor: range.color,
                width: "100%",
                height: "30px",
                position: "relative",
              }}
              onMouseEnter={() => setHoveredRange({ idx, range })}
              onMouseLeave={() => setHoveredRange(null)}
            >
              <span
                style={{
                  position: "absolute",
                  top: "50%",
                  left: "50%",
                  transform: "translate(-50%,-50%)",
                  color: invert(range.color, true),
                  pointer: "default",
                }}
              >
                {range.score}
              </span>
            </div>
          );
        })}
      </div>
      <div
        style={{
          height: "66px",
          overflow: "visible",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
        }}
      >
        {!hoveredRange ? (
          <p style={{ margin: 0 }}>Hover on color to see range values</p>
        ) : (
          rangeText
        )}
      </div>
    </div>
  );
};

export default RangeAndColorInfo;
