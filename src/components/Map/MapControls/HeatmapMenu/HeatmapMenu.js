import React, { useCallback, useEffect, useRef, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Button, Modal, Popover, Checkbox, Radio, Slider, Switch } from "antd";
import Draggable from "react-draggable";
import {
  getStudySummary,
  heatmapSubmarketStudyTypes,
  heatmapDemographicsStudyTypes,
  getTitleForStudyType,
  formatStudyValue,
} from "./utils";
import {
  submarketRanges,
  demographicsRanges,
} from "../../../../utils/heatmapRanges";
import { rgbToHexString } from "../../../../utils/color";
import invert from "invert-color";
import "./heatmapMenu.css";
import MenuRow from "./MenuRow";

const popupAlign = {
  0: [0, 144],
  1: [0, 62],
  2: [0, -29],
  3: [0, -110],
};

const HeatmapMenu = () => {
  const map = useSelector((state) => state.Map.map);
  const heatmapType = useSelector((state) => state.Map.heatmapType);
  const heatmapSubmarketStudyType = useSelector(
    (state) => state.Map.heatmapSubmarketStudyType
  );
  const heatmapDemographicsStudyType = useSelector(
    (state) => state.Map.heatmapDemographicsStudyType
  );
  const heatmapDemographicsData = useSelector(
    (state) => state.Map.heatmapDemographicsData
  );
  const heatmapSubmarketData = useSelector(
    (state) => state.Map.heatmapSubmarketData
  );
  const heatmapBGFilters = useSelector((state) => state.Map.heatmapBGFilters);
  const combineAllDemographicsFilters = useSelector(
    (state) => state.Map.combineAllDemographicsFilters
  );
  const currentDemographicColorScale = useSelector(
    (state) => state.Map.currentDemographicColorScale
  );
  const currentSubmarketColorScale = useSelector(
    (state) => state.Map.currentSubmarketColorScale
  );
  const submarketBGEnabled = useSelector(
    (state) => state.Map.submarketBGEnabled
  );
  const heatmapBlockGroupMap = useSelector(
    (state) => state.Map.heatmapBlockGroupMap
  );
  const heatmapOpacity = useSelector((state) => state.Map.heatmapOpacity);

  const dispatch = useDispatch();

  const [submarketSummary, setSubmarketSummary] = useState({});
  const [demographicsSummary, setDemographicsSummary] = useState({});

  const [popupOpen, setPopupOpen] = useState(false);
  const [open, setOpen] = useState(false);
  const [disabled, setDisabled] = useState(false);
  const [bounds, setBounds] = useState({
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  });
  const draggleRef = useRef(null);

  useEffect(() => {
    if (heatmapSubmarketData.length > 0) {
      getStudySummary(heatmapSubmarketData, heatmapType);
    }
  }, [heatmapSubmarketData]);

  useEffect(() => {
    getSubmarketSummary();
  }, [heatmapSubmarketData, heatmapBlockGroupMap]);

  useEffect(() => {
    getDemographicsSummary();
  }, [
    // heatmapDemographicsData,
    heatmapBlockGroupMap,
  ]);

  const getSubmarketSummary = useCallback(() => {
    if (heatmapSubmarketData.length > 0) {
      if (submarketBGEnabled) {
        const features = map.queryRenderedFeatures({
          layers: ["heatmapSubmarketStyle"],
        });
        if (features.length > 0) {
          const ids = features.map((f) => f.properties.Name);
          // const viewedBlocks = heatmapSubmarketData.filter((d) =>
          //   ids.includes(d.id)
          // );
          const viewedBlocks = [];
          for (let i = 0; i < ids.length; i++) {
            if (heatmapBlockGroupMap[ids[i]]) {
              viewedBlocks.push({
                id: ids[i],
                ...heatmapBlockGroupMap[ids[i]].getSubmarketData(),
                combinedFilterScore:
                  heatmapBlockGroupMap[ids[i]].combinedFilterScore,
              });
            }
          }

          setSubmarketSummary(getStudySummary(viewedBlocks, heatmapType));
        }
      } else {
        setSubmarketSummary(getStudySummary(heatmapSubmarketData, heatmapType));
      }
    }
  }, [heatmapSubmarketData, heatmapBlockGroupMap]);

  const getDemographicsSummary = useCallback(() => {
    if (heatmapDemographicsData.length > 0) {
      const features = map.queryRenderedFeatures({
        layers: ["heatmapDemographicStyle"],
      });
      if (features.length > 0) {
        const ids = features.map((f) => f.properties.Name);
        // const viewedBlocks = heatmapDemographicsData.filter((d) =>
        //   ids.includes(d.id)
        // );

        const viewedBlocks = [];
        for (let i = 0; i < ids.length; i++) {
          if (heatmapBlockGroupMap[ids[i]]) {
            viewedBlocks.push({
              id: ids[i],
              ...heatmapBlockGroupMap[ids[i]].getDemographicData(),
              combinedFilterScore:
                heatmapBlockGroupMap[ids[i]].combinedFilterScore,
            });
          }
        }
        setDemographicsSummary(getStudySummary(viewedBlocks, heatmapType));
      }
    }
  }, [
    // heatmapDemographicsData,
    heatmapBlockGroupMap,
    heatmapType,
  ]);

  useEffect(() => {
    if (!map) return;

    const moveEnd = () => {
      if (!heatmapType) return;
      if (heatmapType === "demographics") {
        getDemographicsSummary();
      } else if (heatmapType === "submarket" && submarketBGEnabled) {
        getSubmarketSummary();
      }
    };

    map.on("moveend", moveEnd);
    return () => {
      map.off("moveend", moveEnd);
    };
  }, [map, heatmapType, submarketBGEnabled]);

  useEffect(() => {
    if (
      Object.keys(heatmapBGFilters).filter(
        (key) => heatmapBGFilters[key].active
      ).length === 0
    ) {
      dispatch({
        type: "Map/saveState",
        payload: {
          combineAllDemographicsFilters: false,
        },
      });
    }
  }, [heatmapBGFilters]);

  useEffect(() => {
    if (heatmapType) {
      showModal();
    }
  }, [heatmapType]);

  const showModal = () => {
    setOpen(true);
  };
  const handleOk = (e) => {
    setOpen(false);
  };
  const handleCancel = (e) => {
    // console.log(e);
    setOpen(false);
    dispatch({
      type: "Map/saveState",
      payload: {
        heatmapType: null,
      },
    });
  };
  const onStart = (_event, uiData) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    if (!targetRect) {
      return;
    }
    setBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };

  return (
    <>
      {/* <Button onClick={showModal}>Open Draggable Modal</Button> */}
      <Modal
        title={
          <div
            style={{
              width: "100%",
              cursor: "move",
            }}
            onMouseOver={() => {
              if (disabled) {
                setDisabled(false);
              }
            }}
            onMouseOut={() => {
              setDisabled(true);
            }}
            // fix eslintjsx-a11y/mouse-events-have-key-events
            // https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/master/docs/rules/mouse-events-have-key-events.md
            onFocus={() => {}}
            onBlur={() => {}}
            // end
          >
            {heatmapType === "demographics" && "Demographics Heatmap Menu"}
            {heatmapType === "submarket" && "MF Submarket Heatmap Menu"}
          </div>
        }
        open={open}
        onCancel={handleCancel}
        footer={null}
        style={{ padding: "10px" }}
        modalRender={(modal) => (
          <Draggable
            disabled={disabled}
            bounds={bounds}
            onStart={(event, uiData) => onStart(event, uiData)}
          >
            <div ref={draggleRef}>{modal}</div>
          </Draggable>
        )}
        mask={false}
        wrapClassName={"heatmapMenu"}
        width={400}
      >
        <div
          style={{
            position: "relative",
            display: "flex",
            flexDirection: "column",
            gap: "5px",
          }}
        >
          <div>
            <span>Opacity:</span>
            <Slider
              min={0}
              max={100}
              value={heatmapOpacity * 100}
              onChange={(value) =>
                dispatch({
                  type: "Map/saveState",
                  payload: { heatmapOpacity: value / 100 },
                })
              }
            />
          </div>
          {(heatmapType === "demographics" ||
            (heatmapType === "submarket" && submarketBGEnabled)) &&
            Object.keys(heatmapBGFilters).filter(
              (key) => heatmapBGFilters[key].active
            ).length > 0 && (
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Checkbox
                  checked={combineAllDemographicsFilters}
                  onChange={(e) => {
                    dispatch({
                      type: "Map/saveState",
                      payload: {
                        combineAllDemographicsFilters: e.target.checked,
                      },
                    });
                  }}
                >
                  <strong>Combine all active filters</strong>
                </Checkbox>
                <Button
                  danger
                  onClick={() =>
                    dispatch({
                      type: "Map/saveState",
                      payload: {
                        heatmapBGFilters: {},
                      },
                    })
                  }
                >
                  Clear Filters
                </Button>
              </div>
            )}
          {heatmapType === "submarket" && (
            <>
              <div>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    alignItems: "center",
                    gap: "5px",
                  }}
                >
                  <Switch
                    size="small"
                    checked={submarketBGEnabled}
                    onChange={(checked) =>
                      dispatch({
                        type: "Map/saveState",
                        payload: {
                          submarketBGEnabled: checked,
                          heatmapSubmarketData: [],
                        },
                      })
                    }
                  />
                  <span>Block Group</span>
                </div>
              </div>

              {heatmapSubmarketStudyTypes.reduce((acc, studyType, idx) => {
                if (submarketBGEnabled && studyType === "rental_growth_5_years")
                  return acc;

                if (
                  !combineAllDemographicsFilters &&
                  studyType === "combinedFilterScore"
                ) {
                  return acc;
                }

                const changeStudyType = () => {
                  dispatch({
                    type: "Map/saveState",
                    payload: {
                      heatmapSubmarketStudyType: studyType,
                    },
                  });
                };

                const summary =
                  structuredClone(submarketSummary[studyType]) || {};

                // summary.min = formatStudyValue(studyType, summary.min);
                // summary.max = formatStudyValue(studyType, summary.max);
                // summary.median = formatStudyValue(studyType, summary.median);

                let ranges = submarketRanges[studyType];
                if (currentSubmarketColorScale) {
                  ranges = [];
                  const colorArray = currentSubmarketColorScale.range();
                  const valueArray = currentSubmarketColorScale.quantiles();
                  valueArray.unshift(null);
                  for (let i = 0; i < colorArray.length; i++) {
                    let color = colorArray[i];
                    if (colorArray[i].includes("rgb")) {
                      color = rgbToHexString(colorArray[i]);
                    }
                    let score;
                    if (
                      ["vacancy_rate", "market_effective_rent_sf"].includes(
                        studyType
                      )
                    ) {
                      score = 10 - i;
                    } else {
                      score = i + 1;
                    }

                    ranges.push({
                      color: color,
                      value: valueArray[i],
                      score,
                    });
                  }
                }

                acc.push(
                  <MenuRow
                    key={idx}
                    title={getTitleForStudyType(studyType)}
                    studyType={studyType}
                    summary={summary}
                    selected={studyType === heatmapSubmarketStudyType}
                    changeStudyType={changeStudyType}
                    heatmapType={heatmapType}
                    ranges={ranges}
                    popupAlign={popupAlign[idx]}
                    popupOpen={popupOpen}
                    setPopupOpen={setPopupOpen}
                  />
                );
                return acc;
              }, [])}
            </>
          )}

          {/* {heatmapType === "demographics" &&
            Object.keys(heatmapBGFilters).filter(
              (key) => heatmapBGFilters[key].active
            ).length > 0 && (
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Checkbox
                  checked={combineAllDemographicsFilters}
                  onChange={(e) => {
                    dispatch({
                      type: "Map/saveState",
                      payload: {
                        combineAllDemographicsFilters: e.target.checked,
                      },
                    });
                  }}
                >
                  <strong>Combine all active filters</strong>
                </Checkbox>
                <Button
                  danger
                  onClick={() =>
                    dispatch({
                      type: "Map/saveState",
                      payload: {
                        heatmapBGFilters: {},
                      },
                    })
                  }
                >
                  Clear Filters
                </Button>
              </div>
            )} */}
          {heatmapType === "demographics" &&
            heatmapDemographicsStudyTypes.map((studyType, idx) => {
              const changeStudyType = () => {
                dispatch({
                  type: "Map/saveState",
                  payload: {
                    heatmapDemographicsStudyType: studyType,
                  },
                });
              };

              const summary =
                structuredClone(demographicsSummary[studyType]) || {};

              // summary.min = formatStudyValue(studyType, summary.min);
              // summary.max = formatStudyValue(studyType, summary.max);
              // summary.median = formatStudyValue(studyType, summary.median);

              let ranges = demographicsRanges[studyType];
              if (currentDemographicColorScale) {
                ranges = [];
                const colorArray = currentDemographicColorScale.range();
                const valueArray = currentDemographicColorScale.quantiles();
                valueArray.unshift(null);
                for (let i = 0; i < colorArray.length; i++) {
                  let color = colorArray[i];
                  if (colorArray[i].includes("rgb")) {
                    color = rgbToHexString(colorArray[i]);
                  }
                  ranges.push({
                    color: color,
                    value: valueArray[i],
                    score: i + 1,
                  });
                }
              }

              if (
                !combineAllDemographicsFilters &&
                studyType === "combinedFilterScore"
              ) {
                return null;
              } else {
                return (
                  <MenuRow
                    key={idx}
                    title={getTitleForStudyType(studyType)}
                    studyType={studyType}
                    summary={summary}
                    selected={studyType === heatmapDemographicsStudyType}
                    changeStudyType={changeStudyType}
                    heatmapType={heatmapType}
                    ranges={ranges}
                    popupAlign={popupAlign[idx]}
                    popupOpen={popupOpen}
                    setPopupOpen={setPopupOpen}
                  />
                );
              }
            })}
        </div>
      </Modal>
    </>
  );
};
export default HeatmapMenu;
