.heatmapMenu {
  pointer-events: none;
}

.heatmapMenu .ant-modal-header {
  padding: 10px;
}
.heatmapMenu .ant-modal-content .ant-modal-close {
  /* top: -5px;
  right: -5px; */
}
.heatmapMenu .ant-modal-body {
  /* height: 425px;
  overflow-y: auto; */
}

.heatmapMenu-row {
  display: flex;
  flex-direction: column;
  background-color: #f4f4f4;
  padding: 5px 10px;
  cursor: pointer;
}

.heatmapMenu-row:hover {
  background-color: #e0e0e0;
}

.heatmapMenu-selected {
  background-color: #e0e0e0;
}

.heatmapMenu-settingsBtn,
.heatmapMenu-settingsCloseBtn {
  position: relative;
}

.heatmapMenu-settingsBtn svg,
.heatmapMenu-settingsCloseBtn svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ant-collapse-header {
  padding: 0 !important;
}

.heatmapMenu-rangeColor:hover {
  border: 5px solid black;
}
