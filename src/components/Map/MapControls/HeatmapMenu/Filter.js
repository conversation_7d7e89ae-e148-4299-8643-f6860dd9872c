import { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { InputNumber, Slider, Checkbox } from "antd";
import isEmpty from "lodash.isempty";
import usePrevious from "../../../hooks/usePrevious";

const Filter = ({ type, min, max }) => {
  const defaultFilter = {
    active: false,
    min: min,
    max: max,
  };

  const [filter, setFilter] = useState(defaultFilter);
  const heatmapBGFilters = useSelector((state) => state.Map.heatmapBGFilters);
  const prevDemographicFilters = usePrevious(heatmapBGFilters);

  const dispatch = useDispatch();

  useEffect(() => {
    if (heatmapBGFilters[type]) {
      setFilter({
        ...heatmapBGFilters[type],
      });
    } else {
      saveToDemographicsFilters(filter);
    }
    return () => {
      console.log("filter unmounted");
    };
  }, []);

  useEffect(() => {
    if (!isEmpty(prevDemographicFilters) && isEmpty(heatmapBGFilters)) {
      setFilter(defaultFilter);
      saveToDemographicsFilters(defaultFilter);
    }
  }, [heatmapBGFilters]);

  const saveToDemographicsFilters = (updatedFilter) => {
    dispatch({
      type: "Map/saveState",
      payload: {
        heatmapBGFilters: {
          ...heatmapBGFilters,
          [type]: {
            ...updatedFilter,
          },
        },
      },
    });
  };

  const minInputChange = (value) => {
    const updatedState = { ...filter, min: value };
    setFilter(updatedState);
    saveToDemographicsFilters(updatedState);
  };

  const maxInputChange = (value) => {
    const updatedState = { ...filter, max: value };
    setFilter(updatedState);
    saveToDemographicsFilters(updatedState);
  };

  const sliderChange = (value) => {
    const updatedState = { ...filter, min: value[0], max: value[1] };
    setFilter(updatedState);
    saveToDemographicsFilters(updatedState);
  };

  const activeChange = (e) => {
    const updatedState = { ...filter, active: e.target.checked };
    setFilter(updatedState);
    saveToDemographicsFilters(updatedState);
  };

  return (
    <div>
      <div
        style={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          width: "100%",
        }}
      >
        <div>
          <Checkbox checked={filter.active} onChange={activeChange}>
            <strong>Filter</strong>
          </Checkbox>
        </div>
        <div style={{ display: "flex", flexDirection: "row", gap: "10px" }}>
          <div style={{ display: "flex", flexDirection: "column" }}>
            <span>Min:</span>
            <InputNumber
              disabled={!filter.active}
              value={filter.min}
              min={Math.min(
                min,
                heatmapBGFilters[type] ? heatmapBGFilters[type].min : min
              )}
              max={Math.max(
                max,
                heatmapBGFilters[type] ? heatmapBGFilters[type].max : max
              )}
              onChange={minInputChange}
            />
          </div>
          <div style={{ display: "flex", flexDirection: "column" }}>
            <span>Max:</span>
            <InputNumber
              disabled={!filter.active}
              value={filter.max}
              min={Math.min(
                min,
                heatmapBGFilters[type] ? heatmapBGFilters[type].min : min
              )}
              max={Math.max(
                max,
                heatmapBGFilters[type] ? heatmapBGFilters[type].max : max
              )}
              onChange={maxInputChange}
            />
          </div>
        </div>
      </div>
      <div>
        <Slider
          disabled={!filter.active}
          range={{ draggableTrack: true }}
          defaultValue={[min, max]}
          min={Math.min(
            min,
            heatmapBGFilters[type] ? heatmapBGFilters[type].min : min
          )}
          max={Math.max(
            max,
            heatmapBGFilters[type] ? heatmapBGFilters[type].max : max
          )}
          value={[filter.min, filter.max]}
          onChange={sliderChange}
        />
      </div>
    </div>
  );
};

export default Filter;
