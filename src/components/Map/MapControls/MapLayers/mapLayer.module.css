.mapLayerBtnPos {
  position: absolute;
  left: 10px;
  bottom: 70px;
}
.mapLayerBtn {
  background-color: var(--antd-active-blue);
  border-radius: 100%;
  outline: none;
  border: none;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  width: 45px;
  height: 45px;
  z-index: 200;
  cursor: pointer;
  position: relative;
}
.mapLayerBtn:hover {
  background-color: #46a3fb;
}

.mapLayerBtn > svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.mapLayerMenuPopup {
  position: absolute;
  /* bottom: calc(100% + 10px);
  left: 0px; */
  bottom: 5px;
  left: calc(100% + 5px);
  width: 270px;
  /* height: 200px;
  overflow-y: auto; */
  z-index: 250;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  transition: all 250ms ease-out;
  transform-origin: bottom left;
  padding: 10px;
}
.mapLayerMenuPopupClose {
  scale: 0;
}
.mapLayerMenuPopupOpen {
  scale: 1;
}

.layerMenuSection {
  width: 100%;
  /* padding: 5px 10px; */
}

.layerMenuItemContainer {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  row-gap: 10px;
}

.layerMenuSectionTitle {
  font-size: 14px;
  font-weight: 600;
  margin-left: 10px;
}
.layerMenuItemLabel {
  font-size: 12px;
  width: 100%;
  text-align: center;
}

.layerMenuImgItemActive {
  border: 2px solid var(--antd-active-blue) !important;
}

.layerMenuImgItemActive > img {
  border: 4px solid white;
}

.themeImgContainer,
.typeImgContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  /* width: 85px; */
  width: 80px;
}

.themeImgBtn,
.typeImgBtn {
  overflow: hidden;
  outline: none;
  padding: 0;
  margin: 0;
  background-color: transparent;
  border: transparent;
  border-radius: 8px;
  width: 42.5px;
  height: 42.5px;
  cursor: pointer;
  pointer-events: auto;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2), 0 3px 12px 0 rgba(0, 0, 0, 0.19);
}

.themeImgBtn img,
.typeImgBtn img {
  height: 100%;
  width: 100%;
}
