import { connect } from "react-redux";
import styles from "./mapLayer.module.css";
import { capitalize } from "../../../../utils/strings";
import schoolDistrictsImg from "../../../../assets/images/mapbox/controls/schooldistricts.png";
import schoolAttendanceImg from "../../../../assets/images/mapbox/controls/schoolattendance.png";
import cbsaImg from "../../../../assets/images/mapbox/controls/cbsa.png";
import countyImg from "../../../../assets/images/mapbox/controls/county.png";
import zipcodeImg from "../../../../assets/images/mapbox/controls/ZIPcode.png";
import activityCentersImg from "../../../../assets/images/mapbox/controls/activitycenters.png";
import opportunityZonesImg from "../../../../assets/images/mapbox/controls/opportunityzones.png";
import floodZoneImg from "../../../../assets/images/mapbox/controls/floodzone.png";
import chainLocationsImg from "../../../../assets/images/mapbox/controls/chainlocations.png";
import hudPublicHousingImg from "../../../../assets/images/mapbox/controls/hudpublichousing.png";
import newHomesImg from "../../../../assets/images/mapbox/controls/newhomes.png";
import subdivisionImg from "../../../../assets/images/mapbox/controls/subdivision.png";
import neighborhoodImg from "../../../../assets/images/mapbox/controls/neighborhood.png";
import wetlandsImg from "../../../../assets/images/mapbox/controls/wetlands.png";
import powerlinesImg from "../../../../assets/images/mapbox/controls/powerlines.png";
import honlayerImg from "../../../../assets/images/mapbox/controls/honlayer.png";
import gorlickImg from "../../../../assets/images/mapbox/controls/gorlicklayer.png";
import trueholdImg from "../../../../assets/images/mapbox/controls/truehold-icon.jpg";
import drivetimeImg from "../../../../assets/images/mapbox/controls/drivetime.png";
import airbnbImg from "../../../../assets/images/mapbox/controls/airbnb.png";
import prologisImg from "../../../../assets/images/mapbox/controls/prologis.png";
import osmResidentialImg from "../../../../assets/images/mapbox/controls/osm_icon.png";
import institutionalOwnerImg from "../../../../assets/images/mapbox/controls/institutionalowners.png";
import multiFamilyImg from "../../../../assets/images/mapbox/controls/multi_family_icon.png";
import fundriseIcon from "../../../../assets/images/mapbox/controls/fundrise-icon.png";
import waterDistrictIcon from "../../../../assets/images/mapbox/controls/waterdistrict.png";
import railNetworkIcon from "../../../../assets/images/mapbox/controls/railnetwork.png";
import BridgeTowerIcon from "../../../../assets/images/mapbox/controls/BridgeTower-icon.png";
import CityIcon from "../../../../assets/images/mapbox/controls/city_icon.png";
import prpIcon from "../../../../assets/images/mapbox/controls/prp.png";
import gnImg from "../../../../assets/images/mapbox/controls/prologis.png";
const SCHOOL_DISTRICTS = "school districts";
const CHARTER_SCHOOL = "charter school";
const SCHOOL_ATTENDANTS = "school zones";
const ACTIVITY_CENTERS = "activity centers";
const COUNTY = "county";
const ZIPCODE = "ZIP code";
const TAX = "tax";
const OPPORTUNITY_ZONES = "opportunity zones";
const CBSA = "cbsa";
const CITY = "city";
const FLOOD_ZONES = "flood zone";
const WETLANDS = "wetlands";
const CHAIN_LOCATIONS = "chain stores";
const NEW_HOMES = "new construction";
const HUD_PUBLIC_HOUSING = "public housing";
const SUBDIVISION = "subdivision";
const NEIGHBORHOOD = "neighborhood";
const POWER_LINES = "power lines";
const HON_OWNED = "hon owned";
const GORLICK_OWNED = "gorlick owned";
const FUNDRISE_PROPERTIES = "fundrise properties";
const FUNDRISE_OWNED_COMMUNITY = "fundrise owned community";
const TRUEHOLD_OWNED = "truehold owned";
const TRUEHOLD_UNDERWRITTEN = "truehold underwritten";
const TRUEHOLD_TARGET = "truehold target";
const DRIVE_TIME = "drive time";
const AIRBNB = "airbnb";
const PROLOGIS = "prologis";
const RESIDENTIAL_DEVELOPMENT = "residential development";
const INSTITUTIONAL_OWNERS = "institutional owners";
const MULTI_FAMILY = "multi family";
const WATER_DISTRICT = "municipal water district";
const RAIL_NETWORK = "rail network";
const BRIDGE_TOWER_ICON = "Bridge Tower BTR";
const BLUERIVER_PRP = "public record prospect";
const GN = "gn";
const MAJOR_EMPLOYER = "major employer";

const imageSrc = {};
imageSrc[SCHOOL_DISTRICTS] = schoolDistrictsImg;
imageSrc[CHARTER_SCHOOL] = schoolDistrictsImg;
imageSrc[SCHOOL_ATTENDANTS] = schoolAttendanceImg;
imageSrc[ACTIVITY_CENTERS] = activityCentersImg;
imageSrc[COUNTY] = countyImg;
imageSrc[ZIPCODE] = zipcodeImg;
imageSrc[OPPORTUNITY_ZONES] = opportunityZonesImg;
imageSrc[CBSA] = cbsaImg;
imageSrc[CITY] = CityIcon;
imageSrc[TAX] = CityIcon;
imageSrc[FLOOD_ZONES] = floodZoneImg;
imageSrc[WETLANDS] = wetlandsImg;
imageSrc[CHAIN_LOCATIONS] = chainLocationsImg;
imageSrc[HUD_PUBLIC_HOUSING] = hudPublicHousingImg;
imageSrc[NEW_HOMES] = newHomesImg;
imageSrc[SUBDIVISION] = subdivisionImg;
imageSrc[NEIGHBORHOOD] = neighborhoodImg;
imageSrc[POWER_LINES] = powerlinesImg;
imageSrc[HON_OWNED] = honlayerImg;
imageSrc[GORLICK_OWNED] = gorlickImg;
imageSrc[TRUEHOLD_OWNED] = trueholdImg;
imageSrc[TRUEHOLD_UNDERWRITTEN] = trueholdImg;
imageSrc[TRUEHOLD_TARGET] = trueholdImg;
imageSrc[DRIVE_TIME] = drivetimeImg;
imageSrc[AIRBNB] = airbnbImg;
imageSrc[PROLOGIS] = prologisImg;
imageSrc[RESIDENTIAL_DEVELOPMENT] = osmResidentialImg;
imageSrc[INSTITUTIONAL_OWNERS] = institutionalOwnerImg;
imageSrc[MULTI_FAMILY] = multiFamilyImg;
imageSrc[FUNDRISE_PROPERTIES] = fundriseIcon;
imageSrc[WATER_DISTRICT] = waterDistrictIcon;
imageSrc[RAIL_NETWORK] = railNetworkIcon;
imageSrc[BRIDGE_TOWER_ICON] = BridgeTowerIcon;
imageSrc[BLUERIVER_PRP] = prpIcon;
imageSrc[GN] = gnImg;
imageSrc[MAJOR_EMPLOYER] = gnImg;

const MapLayerTypes = connect(({ Map, Configure }) => ({
  currentMapLayerOptions: Map.currentMapLayerOptions,
  user: Configure.user,

  // selectedAttendanceCategory: Map.selectedAttendanceCategory, // used in fetchLayerTypesData props
  // selectedChainIds: Map.selectedChainIds, // used in fetchLayerTypesData props
  // chainOpenedMonthNumber: Map.chainOpenedMonthNumber, // used in fetchLayerTypesData props
}))(function (props) {
  const schoolType = [SCHOOL_DISTRICTS, SCHOOL_ATTENDANTS, CHARTER_SCHOOL];
  const adminType = [CBSA, COUNTY, TAX, CITY, ZIPCODE, SUBDIVISION, NEIGHBORHOOD];
  const zoneType = [ACTIVITY_CENTERS, OPPORTUNITY_ZONES, DRIVE_TIME];
  const floodType = [FLOOD_ZONES, WETLANDS, WATER_DISTRICT];
  const poiType = [
    CHAIN_LOCATIONS,
    NEW_HOMES,
    AIRBNB,
    PROLOGIS,
    MAJOR_EMPLOYER,
    RESIDENTIAL_DEVELOPMENT,
    INSTITUTIONAL_OWNERS,
    MULTI_FAMILY,
    GN,
  ];
  const hudType = [HUD_PUBLIC_HOUSING, POWER_LINES, RAIL_NETWORK];
  const ownedType = [];

  if (props.user.userGroup.includes("BlueRiver")) {
    poiType.push(BLUERIVER_PRP);
  }

  if (props.user.userGroup.includes("HON")) {
    ownedType.push(HON_OWNED);
  } else if (
    props.user.userGroup.includes("BridgeAdmin") ||
    props.user.userGroup.includes("BridgeFull")
  ) {
    ownedType.push(GORLICK_OWNED);
  } else if (props.user.userGroup.includes("Fundrise")) {
    ownedType.push(FUNDRISE_PROPERTIES);
  } else if (props.user.userGroup.includes("Truehold")) {
    ownedType.push(TRUEHOLD_OWNED);
    ownedType.push(TRUEHOLD_UNDERWRITTEN);
    ownedType.push(TRUEHOLD_TARGET);
  } else if (props.user.userGroup.includes("BridgeTower")) {
    ownedType.push(BRIDGE_TOWER_ICON);
  }

  const handleSelectLayerImg = (e) => {
    e.stopPropagation();
    handleLayerOptions(e.currentTarget.value);
  };

  const handleLayerOptions = (clickedLayer) => {
    if (
      props.currentMapLayerOptions instanceof Array &&
      !props.currentMapLayerOptions.includes(clickedLayer)
    ) {
      // Determine which layer type and get related layers
      const relatedLayers = findWhichLayerType(clickedLayer);

      // Remove any previous layer of same type
      // const selectedLayer = [
      //   ...props.currentMapLayerOptions.filter(
      //     (layer) => !relatedLayers.includes(layer)
      //   ),
      // ];
      const selectedLayer = [...props.currentMapLayerOptions];

      selectedLayer.push(clickedLayer);

      props.dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: selectedLayer,
        },
      });

      // fetchLayerTypesData(props, clickedLayer);
    } else {
      // if icon layer clicked itself
      props.dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: [
            ...props.currentMapLayerOptions.filter((layer) => layer !== clickedLayer),
          ],
        },
      });
    }
  };

  const findWhichLayerType = (clickedLayer) => {
    if (schoolType.includes(clickedLayer)) return schoolType;
    if (adminType.includes(clickedLayer)) return adminType;
    if (zoneType.includes(clickedLayer)) return zoneType;
    if (floodType.includes(clickedLayer)) return floodType;
    if (poiType.includes(clickedLayer)) return poiType;
    if (hudType.includes(clickedLayer)) return hudType;
    if (ownedType.includes(clickedLayer)) return ownedType;
  };

  const getTypeIcons = (typesData) => {
    return typesData.map((layer) => {
      let layerName = capitalize(layer);
      if (layer === "cbsa") layerName = layer.toUpperCase();
      if (layer === "hon owned") layerName = "HON Owned";
      if (layer === GORLICK_OWNED) layerName = "Bridge Owned";
      if (layer === "Bridge Tower BTR") layerName = "BTR";

      return (
        <div key={layer} className={styles.typeImgContainer}>
          <button
            className={`${styles.typeImgBtn} ${
              props.currentMapLayerOptions instanceof Array &&
              props.currentMapLayerOptions.includes(layer)
                ? styles.layerMenuImgItemActive
                : ""
            }`}
            value={layer}
            onClick={handleSelectLayerImg}
          >
            <img
              // src={`/images/mapbox/controls/${layer.replace(/\s/g, "")}.png`}
              src={imageSrc[layer]}
            />
          </button>
          <span className={styles.layerMenuItemLabel}>{layerName}</span>
        </div>
      );
    });
  };

  return (
    <section className={styles.layerMenuSection}>
      <h3 className={styles.layerMenuSectionTitle}>School</h3>
      <div className={styles.layerMenuItemContainer}>{getTypeIcons(schoolType)}</div>
      <h3 className={styles.layerMenuSectionTitle}>Admin</h3>
      <div className={styles.layerMenuItemContainer}>{getTypeIcons(adminType)}</div>
      <h3 className={styles.layerMenuSectionTitle}>Zone</h3>
      <div className={styles.layerMenuItemContainer}>{getTypeIcons(zoneType)}</div>
      <h3 className={styles.layerMenuSectionTitle}>Flood</h3>
      <div className={styles.layerMenuItemContainer}>{getTypeIcons(floodType)}</div>
      <h3 className={styles.layerMenuSectionTitle}>POI</h3>
      <div className={styles.layerMenuItemContainer}>{getTypeIcons(poiType)}</div>
      <h3 className={styles.layerMenuSectionTitle}>Housing & Urban Development</h3>
      <div className={styles.layerMenuItemContainer}>{getTypeIcons(hudType)}</div>
      {ownedType.length > 0 && (
        <>
          <h3 className={styles.layerMenuSectionTitle}>Owned Properties</h3>
          <div className={styles.layerMenuItemContainer}>{getTypeIcons(ownedType)}</div>
        </>
      )}
    </section>
  );
});

export default MapLayerTypes;
