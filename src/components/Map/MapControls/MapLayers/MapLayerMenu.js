import { connect } from "react-redux";
import styles from "./mapLayer.module.css";
import MapLayerTypes from "./MapLayerTypes";

const controllerName = "MapLayer";

const MapLayerMenu = connect(({ Map }) => ({
  openedMapControllerOption: Map.openedMapControllerOption,
}))(function (props) {
  const closeOptions = () => {
    props.dispatch({
      type: "Map/saveState",
      payload: {
        openedMapControllerOption: "",
      },
    });
  };

  return (
    <div
      className={`${styles.mapLayerMenuPopup} ${
        props.openedMapControllerOption === controllerName
          ? styles.mapLayerMenuPopupOpen
          : styles.mapLayerMenuPopupClose
      }`}
      onMouseLeave={closeOptions}
      onClick={(e) => e.stopPropagation()}
    >
      <MapLayerTypes />
    </div>
  );
});

export default MapLayerMenu;
