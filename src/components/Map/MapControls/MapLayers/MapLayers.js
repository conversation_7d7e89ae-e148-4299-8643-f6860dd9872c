import { useEffect, useCallback } from "react";
import { connect } from "react-redux";
import styles from "./mapLayer.module.css";
import { IoLayersOutline } from "@react-icons/all-files/io5/IoLayersOutline";
import { IoClose } from "@react-icons/all-files/io5/IoClose";
import MapLayerMenu from "./MapLayerMenu";

const controllerName = "MapLayer";

const MapLayers = connect(({ Map }) => ({
  openedMapControllerOption: Map.openedMapControllerOption,
}))(function (props) {
  const closeOptions = useCallback(() => {
    props.dispatch({
      type: "Map/saveState",
      payload: {
        openedMapControllerOption: "",
      },
    });
  }, []);

  useEffect(() => {
    document.addEventListener("click", closeOptions);
    return () => {
      document.removeEventListener("click", closeOptions);
    };
  }, [closeOptions]);

  const handleLayerBtnClick = (e) => {
    e.stopPropagation();
    if (props.openedMapControllerOption != controllerName) {
      props.dispatch({
        type: "Map/saveState",
        payload: {
          openedMapControllerOption: controllerName,
        },
      });
    } else {
      props.dispatch({
        type: "Map/saveState",
        payload: {
          openedMapControllerOption: "",
        },
      });
    }
  };

  return (
    <div id="MapLayers" className={styles.mapLayerBtnPos}>
      <button className={styles.mapLayerBtn} onClick={handleLayerBtnClick}>
        {props.openedMapControllerOption === controllerName ? (
          <IoClose size={25} color="white" />
        ) : (
          <IoLayersOutline size={25} color="white" />
        )}
      </button>
      <MapLayerMenu />
    </div>
  );
});

export default MapLayers;
