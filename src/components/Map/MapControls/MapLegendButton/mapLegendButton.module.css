.buttonContainer {
  /* position: absolute;
  right: 10px;
  top: 119px; */
  overflow: hidden;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  border-radius: 4px;
  pointer-events: auto;
  z-index: 200;
  /* width: 29px; */
}

.button {
  width: 55px;
  height: 55px;
  background-color: white;
  border: none;
  cursor: pointer;
  position: relative;
  display: block;
}

.button:hover {
  background-color: rgb(240, 240, 240);
}

.iconContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.iconContainer {
  font-size: 12px;
}
/* .icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
} */
