import { useDispatch } from "react-redux";
import { BsFillMenuButtonWideFill } from "@react-icons/all-files/bs/BsFillMenuButtonWideFill";
import styles from "./mapLegendButton.module.css";

function MapLegendButton() {
  const dispatch = useDispatch();

  const showLegend = () => {
    dispatch({
      type: "Map/saveState",
      payload: {
        minimzedMapLegends: false,
      },
    });
  };
  return (
    <div className={styles.buttonContainer}>
      <button className={styles.button} onClick={showLegend}>
        <div className={styles.iconContainer}>
          <BsFillMenuButtonWideFill className={styles.icon} size={20} />
          <span>Legend</span>
        </div>
      </button>
    </div>
  );
}

export default MapLegendButton;
