import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { FaPencilAlt } from "@react-icons/all-files/fa/FaPencilAlt";
import { FaTrash } from "@react-icons/all-files/fa/FaTrash";
import { default as turf_area } from "@turf/area";
import styles from "./mapDraw.module.css";
import { geojsonTemplate } from "../../../../constants";
import { Source, Layer } from "../../MapLayers/index";
import geojsonValidation from "geojson-validation";
import mapCoordinates from "geojson-apply-right-hand-rule";

const getElements = () => {
  return [
    document.querySelector("#MapExpander"),
    document.querySelector("#MapZoom"),
    document.querySelector("#MapToImageDownload"),
    document.querySelector("#MapTilt"),
    document.querySelector("#MapRuler"),
    document.querySelector("#MapTheme"),
    document.querySelector("#MapLayers"),
    document.querySelector("#radiusSelectWrapper"),
    document.querySelector("#MapNavigation"),
    document.querySelector("#MapSentinelLayerControl"),
    document.querySelector("#PropertyParcelLegend"),
    document.querySelector("#AttendanceZoneLegend"),
    document.querySelector("#ActivityCenterLegend"),
    document.querySelector("#FloodZoneLegend"),
    document.querySelector("#ChainLocationLegend"),
  ];
};

const hideScreenElements = () => {
  const elements = getElements();

  if (!elements || !elements.length === 0) return;

  for (let i = 0; i < elements.length; i++) {
    if (!elements[i]) continue;
    elements[i].style.display = "none";
  }
  document.querySelector("#TopRightControl").style.position = "static";
};

const showScreenElements = () => {
  const elements = getElements();

  if (!elements || !elements.length === 0) return;

  for (let i = 0; i < elements.length; i++) {
    if (!elements[i]) continue;
    elements[i].style.display = "";
  }
  document.querySelector("#TopRightControl").style.position = "absolute";
};

function MapDraw() {
  const currentDrawLine = useRef([]);
  const tempSavedDrawings = useRef([]);
  const [warningMessage, setWarningMessage] = useState(null);
  const [drawnLineData, setDrawnLineData] = useState(geojsonTemplate);
  const [drawnFillData, setDrawnFillData] = useState(geojsonTemplate);
  const [tempDrawnLineData, setTempDrawnLineData] = useState(geojsonTemplate);

  const map = useSelector((state) => state.Map.map);
  const drawingMode = useSelector((state) => state.Map.drawingMode);
  const drawnCustomPolygons = useSelector(
    (state) => state.Map.drawnCustomPolygons
  );
  const dispatch = useDispatch();

  const drawingModeRef = useRef(drawingMode);
  drawingModeRef.current = drawingMode;

  const drawnCustomPolygonsRef = useRef(drawnCustomPolygons);
  drawnCustomPolygonsRef.current = drawnCustomPolygons;

  useEffect(() => {
    if (!map) return;

    map.on("mousedown", () => {
      if (currentDrawLine.current.length != 0) {
        currentDrawLine.current = [];
      }

      map.on("mousemove", mapDraw);

      map.once("mouseup", () => {
        map.off("mousemove", mapDraw);
        if (drawingModeRef.current) {
          setTempDrawnLineData(geojsonTemplate);

          const completedPolygon = [
            ...currentDrawLine.current,
            currentDrawLine.current[0],
          ];

          if (!completedPolygon[0]) return;

          // reduce the size of polygon to 1/3rd size
          const minifiedPolygon = [];
          for (let i = 1; i < completedPolygon.length - 2; i = i + 3) {
            minifiedPolygon.push(completedPolygon[i]);
          }
          minifiedPolygon.push(minifiedPolygon[0]);

          // check if polygon is valid
          // const isValid = gjv.valid(minifiedPolygon

          let area = 0;
          try {
            area = turf_area({
              type: "Feature",
              geometry: {
                type: "Polygon",
                coordinates: [minifiedPolygon],
              },
            });
          } catch (e) {
            console.log(e);
            return;
          }

          // 10 miles squared
          const maxArea = 10 * 1609.34 * (10 * 1609.34);

          if (area > maxArea) {
            const mileSquared = Math.sqrt(area) / 1609.34;
            const msg = (
              <span>
                Max area is 10 mi<sup>2</sup>.{" "}
                {`Your polygon was ${mileSquared.toFixed(2)}`} mi<sup>2</sup>.
              </span>
            );
            setWarningMessage(msg);
            setTimeout(() => {
              setWarningMessage(null);
            }, 2500);

            return;
          }

          const newTempSavedDrawings = [
            ...tempSavedDrawings.current,
            minifiedPolygon,
          ];

          const lineGeojson = {
            type: "FeatureCollection",
            features: [
              {
                type: "Feature",
                geometry: {
                  type: "MultiLineString",
                  coordinates: newTempSavedDrawings,
                },
                properties: {},
              },
            ],
          };

          const savedPolygons = [];
          for (let i = 0; i < newTempSavedDrawings.length; i++) {
            const drawingLine = newTempSavedDrawings[i];

            savedPolygons.push({
              type: "Feature",
              geometry: mapCoordinates(drawingLine),
              properties: {},
            });
          }

          const polygonGeojson = {
            type: "FeatureCollection",
            features: savedPolygons,
          };

          if (
            geojsonValidation.valid(lineGeojson) &&
            geojsonValidation.valid(polygonGeojson)
          ) {
            currentDrawLine.current = minifiedPolygon;
            tempSavedDrawings.current = newTempSavedDrawings;

            setDrawnLineData(lineGeojson);
            setDrawnFillData(polygonGeojson);
          } else {
            const msg = (
              <span>Your polygon was invalid. Please try again.</span>
            );
            setWarningMessage(msg);
            setTimeout(() => {
              setWarningMessage(null);
            }, 2500);
          }
        }
      });
    });

    map.on("mapDraw.clear", () => {
      if (drawnCustomPolygonsRef.current.length > 0) {
        dispatch({
          type: "Map/saveState",
          payload: {
            drawnCustomPolygons: [],
          },
        });

        setDrawnLineData(geojsonTemplate);
        setDrawnFillData(geojsonTemplate);
      }
    });
  }, [map]);

  useEffect(() => {
    if (!map) return;

    if (drawnCustomPolygons.length == 0) {
      setDrawnLineData(geojsonTemplate);
      setDrawnFillData(geojsonTemplate);
    }
  }, [drawnCustomPolygons]);

  const mapDraw = (e) => {
    if (drawingModeRef.current) {
      currentDrawLine.current = [
        ...currentDrawLine.current,
        [e.lngLat.lng, e.lngLat.lat],
      ];

      setTempDrawnLineData({
        type: "FeatureCollection",
        features: [
          {
            type: "Feature",
            geometry: {
              type: "LineString",
              coordinates: currentDrawLine.current,
            },
          },
        ],
      });
    }
  };

  const enterDrawingMode = (e) => {
    e.preventDefault();

    dispatch({
      type: "Map/saveState",
      payload: { drawingMode: true },
    });
    document
    .getElementById("mapContainer")
    ?.classList.add("[&>div.mapboxgl-popup]:!hidden");
    map.dragPan.disable();
    map.scrollZoom.disable();
    map.doubleClickZoom.disable();
    map.getCanvas().style.cursor = "crosshair";

    if (tempSavedDrawings.current.length != 0) {
      tempSavedDrawings.current = drawnCustomPolygons;
    }
    // console.log("Entered drawing mode");

    hideScreenElements();
    map.fire("mapDraw.enter");
  };

  const exitDrawingMode = () => {
    dispatch({
      type: "Map/saveState",
      payload: { drawingMode: false },
    });

    document
    .getElementById("mapContainer")
    ?.classList.remove("[&>div.mapboxgl-popup]:!hidden");
    
    map.dragPan.enable();
    map.scrollZoom.enable();
    map.doubleClickZoom.enable();
    map.getCanvas().style.cursor = "";

    // console.log("Exit drawing mode");
    showScreenElements();
    map.fire("mapDraw.exit");
  };

  const cancelDrawing = (e) => {
    e.preventDefault();

    setDrawnLineData({
      type: "FeatureCollection",
      features: [
        {
          type: "Feature",
          geometry: {
            type: "MultiLineString",
            coordinates: drawnCustomPolygons,
          },
          properties: {},
        },
      ],
    });

    const savedPolygons = [];
    for (let i = 0; i < drawnCustomPolygons.length; i++) {
      const drawingLine = drawnCustomPolygons[i];
      savedPolygons.push({
        type: "Feature",
        geometry: mapCoordinates(drawingLine),
        properties: {},
      });
    }

    setDrawnFillData({
      type: "FeatureCollection",
      features: savedPolygons,
    });

    // console.log("Cancel drawing");

    map.fire("mapDraw.cancel");
    exitDrawingMode();
  };

  const applyDrawing = (e) => {
    e.preventDefault();

    dispatch({
      type: "Map/saveState",
      payload: {
        drawnCustomPolygons: tempSavedDrawings.current,
      },
    });

    // console.log("Apply drawing");

    map.fire("mapDraw.apply", {
      payload: {
        drawnCustomPolygons: tempSavedDrawings.current,
      },
    });

    exitDrawingMode();
  };

  const clearDrawings = (e) => {
    e.preventDefault();

    dispatch({
      type: "Map/saveState",
      payload: {
        drawnCustomPolygons: [],
      },
    });

    setDrawnLineData(geojsonTemplate);
    setDrawnFillData(geojsonTemplate);

    map.fire("mapDraw.clear");
  };

  return (
    <>
      {drawingMode ? (
        <>
          {warningMessage && (
            <div className={styles.warningMessageContainer}>
              {/* <span>
                Max area is 10 mi<sup>2</sup>. {warningMessage} mi<sup>2</sup>.
              </span> */}
              {warningMessage}
            </div>
          )}
          <div className={styles.drawBarMenuContainer}>
            <div className={styles.drawMenuLabel}>
              <p>Draw custom polygons on the map.</p>
            </div>
            <div className={styles.drawMenuBtnContainer}>
              <div className={styles.cancelBtnContainer}>
                <button onClick={cancelDrawing}>Cancel</button>
              </div>
              <div className={styles.applyBtnContainer}>
                <button onClick={applyDrawing}>Apply</button>
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          <div id="mapDrawControl" className={styles.drawBtnContainer}>
            <button className={styles.drawBtn} onClick={enterDrawingMode}>
              <FaPencilAlt size={16} className={styles.drawIcon} />
            </button>
          </div>
          {drawnCustomPolygons.length > 0 && (
            <div className={styles.trashBtnContainer}>
              <button className={styles.trashBtn} onClick={clearDrawings}>
                <FaTrash size={16} className={styles.trashIcon} />
              </button>
            </div>
          )}
        </>
      )}
      <Source id="drawLineSource" type="geojson" data={drawnLineData}>
        <Layer
          {...{
            id: "drawLineLayer",
            type: "line",
            paint: {
              "line-color": "#F7455D",
              "line-width": 3,
            },
          }}
        />
      </Source>
      <Source id="drawFillSource" type="geojson" data={drawnFillData}>
        <Layer
          {...{
            id: "drawFillLayer",
            type: "fill",
            paint: {
              "fill-color": "#F7455D",
              "fill-opacity": 0.2,
            },
          }}
        />
      </Source>
      <Source id="tempDrawLineSource" type="geojson" data={tempDrawnLineData}>
        <Layer
          {...{
            id: "tempDrawLineLayer",
            type: "line",
            paint: {
              "line-color": "#F7455D",
              "line-width": 3,
            },
          }}
        />
      </Source>
    </>
  );
}

export default MapDraw;
