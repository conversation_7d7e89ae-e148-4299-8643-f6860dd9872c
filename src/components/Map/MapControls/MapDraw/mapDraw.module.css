.drawBtnContainer,
.trashBtnContainer {
  /* position: absolute; */
  /* right: 10px; */

  overflow: hidden;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  border-radius: 4px;
  pointer-events: auto;
  z-index: 150;
  width: 29px;
}
/* .drawBtnContainer {
  top: 158px;
}
.trashBtnContainer {
  top: 197px;
} */

.drawBtn,
.trashBtn {
  width: 29px;
  height: 29px;
  background-color: white;
  border: none;
  cursor: pointer;
  position: relative;
  display: block;
}

.drawBtn:hover,
.trashBtn:hover {
  background-color: rgb(240, 240, 240);
}

.drawIcon,
.trashIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.drawBarMenuContainer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 10px;
}

.drawMenuLabel {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.drawMenuLabel p {
  margin: 0;
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.drawMenuBtnContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}

.cancelBtnContainer button,
.applyBtnContainer button {
  font-size: 16px;
  color: white;
  font-weight: 600;
  background-color: transparent;
  border: none;
  cursor: pointer;
  padding: 5px 10px;
}

.cancelBtnContainer button:hover,
.applyBtnContainer button:hover {
  background-color: var(--antd-active-blue);
}

.warningMessageContainer {
  position: absolute;
  top: 70px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 150;
  background-color: white;
  padding: 10px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  text-align: center;
}
