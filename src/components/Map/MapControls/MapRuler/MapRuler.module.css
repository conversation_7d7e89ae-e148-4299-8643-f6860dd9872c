.buttonContainer {
  overflow: hidden;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  border-radius: 4px;
  pointer-events: auto;
  z-index: 200;
  width: 29px;
}

.button {
  width: 29px;
  height: 29px;
  background-color: white;
  border: none;
  cursor: pointer;
  position: relative;
  display: block;
}

.button:hover {
  background-color: rgb(240, 240, 240);
}

.button svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.distanceContainer {
  /* width: 200px; */
  /* height: 50px; */
  background-color: white;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  padding: 5px;
  position: relative;
}
.popupHeader {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.popupHeader span:nth-child(1) {
  font-weight: 600;
}
.popupHeader span:nth-child(2) {
  font-size: 11px;
}

.popupContent {
  display: flex;
  flex-direction: row;
  justify-content: center;
  padding: 5px;
}

.popupCloseBtn {
  width: 20px;
  height: 20px;
  margin: 5px 5px 0 0;
  background-color: white;
  border: none;
  cursor: pointer;
  display: block;
  position: absolute;
  top: 0;
  right: 0;
}

.popupCloseBtn svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
