import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { FaRuler } from "@react-icons/all-files/fa/FaRuler";
import { FaTimes } from "@react-icons/all-files/fa/FaTimes";
import styles from "./MapRuler.module.css";
import { Source, Layer } from "../../MapLayers/index";
import { geojsonTemplate } from "../../../../constants";
import { default as turf_length } from "@turf/length";

// const linestring = {
//   type: "Feature",
//   geometry: {
//     type: "LineString",
//     coordinates: [],
//   },
// };

const circleStyle = {
  id: "measure-points",
  type: "circle",
  paint: {
    "circle-radius": 5,
    "circle-color": "#000",
  },
  filter: ["in", "$type", "Point"],
};

const lineStyle = {
  id: "measure-lines",
  type: "line",
  layout: {
    "line-cap": "round",
    "line-join": "round",
  },
  paint: {
    "line-color": "#000",
    "line-width": 2.5,
  },
  filter: ["in", "$type", "LineString"],
};

function MapRuler() {
  const map = useSelector((state) => state.Map.map);
  const measureMode = useSelector((state) => state.Map.measureMode);
  const measureGeoJSON = useSelector((state) => state.Map.measureGeoJSON);
  const dispatch = useDispatch();

  const measureModeRef = useRef(measureMode);
  measureModeRef.current = measureMode;
  const measureGeoJSONRef = useRef(measureGeoJSON);
  measureGeoJSONRef.current = measureGeoJSON;

  useEffect(() => {
    if (!map) return;

    map.on("click", (e) => {
      if (!measureModeRef.current) return;

      const features = map.queryRenderedFeatures(e.point, {
        layers: ["measure-points"],
      });

      const geojsonClone = structuredClone(measureGeoJSONRef.current);

      if (geojsonClone.features.length > 1) geojsonClone.features.pop();

      if (features.length) {
        const id = features[0].properties.id;
        geojsonClone.features = geojsonClone.features.filter(
          (point) => point.properties.id !== id
        );
      } else {
        const point = {
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [e.lngLat.lng, e.lngLat.lat],
          },
          properties: {
            id: String(new Date().getTime()),
          },
        };

        geojsonClone.features.push(point);
      }

      let distance = 0;

      if (geojsonClone.features.length > 1) {
        const linestring = {
          type: "Feature",
          geometry: {
            type: "LineString",
            coordinates: [],
          },
        };

        linestring.geometry.coordinates = geojsonClone.features.map(
          (point) => point.geometry.coordinates
        );

        geojsonClone.features.push(linestring);
        distance = turf_length(linestring, { units: "miles" });
      }

      dispatch({
        type: "Map/saveState",
        payload: { measureGeoJSON: geojsonClone, measureDistance: distance },
      });
    });

    map.on("mousemove", (e) => {
      if (!measureModeRef.current) return;

      const features = map.queryRenderedFeatures(e.point, {
        layers: ["measure-points"],
      });
      // Change the cursor to a pointer when hovering over a point on the map.
      // Otherwise cursor is a crosshair.
      map.getCanvas().style.cursor = features.length ? "pointer" : "crosshair";
    });
  }, [map]);

  const enterMeasureMode = () => {
    dispatch({
      type: "Map/saveState",
      payload: { measureMode: true },
    });
    map.fire("mapRuler.enter");
  };

  return (
    <>
      <div id="MapRuler" className={styles.buttonContainer}>
        <button className={styles.button} onClick={enterMeasureMode}>
          <FaRuler />
        </button>
      </div>
      <Source id="geojson" type="geojson" data={measureGeoJSON}>
        <Layer {...circleStyle} />
        <Layer {...lineStyle} />
      </Source>
    </>
  );
}

export const DistancePopup = () => {
  const map = useSelector((state) => state.Map.map);
  const measureMode = useSelector((state) => state.Map.measureMode);
  const measureDistance = useSelector((state) => state.Map.measureDistance);
  const dispatch = useDispatch();

  const exitMeasureMode = () => {
    dispatch({
      type: "Map/saveState",
      payload: {
        measureMode: false,
        measureGeoJSON: geojsonTemplate,
        measureDistance: 0,
      },
    });
    map.getCanvas().style.cursor = "";
    map.fire("mapRuler.exit");
  };

  if (measureMode) {
    return (
      <div className={styles.distanceContainer}>
        <div className={styles.popupHeader}>
          <span>Measure Distance</span>
          <span>(Click on map to add path)</span>
          <button className={styles.popupCloseBtn} onClick={exitMeasureMode}>
            <FaTimes />
          </button>
        </div>
        <div className={styles.popupContent}>
          <span>Total Distance: {measureDistance.toFixed(2)} miles</span>
        </div>
      </div>
    );
  }
  return null;
};

export default MapRuler;
