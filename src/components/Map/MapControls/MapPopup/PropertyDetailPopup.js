import styles from "./propertyDetailPopup.module.css";
import { formatter } from "../../../../utils/money";
import { MAP_LAYER_NAME_BASE } from "../../../../constants";

let propertyType;

function PropertyDetailPopup({ hoverPropertyDetails }) {
  const propertyDetails = convertProperties(hoverPropertyDetails);

  let popupContent;
  propertyType = hoverPropertyDetails.type;

  switch (propertyType) {
    case MAP_LAYER_NAME_BASE.parcel:
      popupContent = createParcelPopup(propertyDetails);
      break;
  }

  return <div>{popupContent}</div>;
}

export default PropertyDetailPopup;

const createBasePropertyContent = (propertyDetails) => {
  return (
    <>
      <div className={styles.popupStat}>
        <div>
          <p>
            <span className={styles.popupValue}>{propertyDetails.Rent}</span>{" "}
            {propertyType != "parcel"
              ? "rent"
              : propertyDetails.Status === "Closed" && propertyType != "parcel"
              ? "sold"
              : propertyType != "parcel"
              ? "sales"
              : "rent"}{" "}
            -{" "}
            {propertyType != "parcel" ? (
              propertyDetails.Status
            ) : (
              <>
                <span className={styles.popupValue}>
                  {propertyDetails.Sales}
                </span>{" "}
                sales
              </>
            )}
          </p>
        </div>
        <div>
          <p>
            <span className={styles.popupValue}>
              {propertyDetails.Beds || "-"}
            </span>{" "}
            Bds ·{" "}
            <span className={styles.popupValue}>
              {propertyDetails.Baths || "-"}
            </span>{" "}
            Ba ·{" "}
            <span className={styles.popupValue}>
              {propertyDetails.Sqft || "-"}
            </span>{" "}
            Sqft
          </p>
        </div>
      </div>
      <div className={styles.popupAddress}>
        <div>
          <p className={styles.popupValue}>{propertyDetails.Address[0]}</p>
        </div>
        <div>
          <p>{propertyDetails.Address[1]}</p>
        </div>
      </div>
    </>
  );
};

const createParcelPopup = (propertyDetails) => {
  return (
    <div className={styles.popupContent}>
      {createBasePropertyContent(propertyDetails)}

      <div className={styles.popupStat}>
        <span className={styles.popupValue}>
          {propertyDetails["Yr. Built"]}
        </span>
        <span>Yr. Built</span>
      </div>
      <div className={styles.popupStat}>
        <span className={styles.popupValue}>{propertyDetails.Subdivision}</span>
        <span>Subdivision</span>
      </div>
    </div>
  );
};

const convertProperties = (record) => {
  let propertyDetails = {};
  switch (record.type) {
    case MAP_LAYER_NAME_BASE.parcel:
      propertyDetails.Address = [
        record.street_number +
          (record.street_prefix ? " " + record.street_prefix : "") +
          " " +
          record.street_name +
          (record.street_suffix ? " " + record.street_suffix : ""),
        record.city + ", " + record.state + " " + record.zip_code,
      ];
      propertyDetails.Rent = "$" + formatter(parseFloat(record.rent).toFixed());
      propertyDetails.Sales =
        "$" + formatter(parseFloat(record.sales).toFixed());
      propertyDetails["Yr. Built"] = record.year_built;
      propertyDetails.Beds = record.beds_count;
      propertyDetails.Baths = record.baths;
      propertyDetails.Sqft = record.total_area_sq_ft
        ? formatter(+record.total_area_sq_ft)
        : "-"; // ensure size is of type number
      propertyDetails.Subdivision = record.subdivision;

      break;
  }
  return propertyDetails;
};
