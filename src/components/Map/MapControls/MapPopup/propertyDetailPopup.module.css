.popupContainer {
  max-width: 240px;
  display: flex;
  flex-direction: column;
}

.popupContent {
  display: flex;
  flex-direction: column;
  /* gap: 10px; */
  /* gap: 4px; */
  gap: 4px;
  padding: 10px;
  font-size: 11px !important;
}

.MLS_popupImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.popupStat,
.popupAddress {
  display: flex;
  flex-direction: column;
}

.popupStatOther {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  gap: 2px;
}

.popupStat p,
.popupStat span,
.popupAddress p,
.popupStatOther span {
  margin: 0;
  padding: 0;
  line-height: 14px;
}

.popupValue {
  /* font-size: 13px; */
  font-size: 12px !important;
  font-weight: 500;
}

.popupDotContainer {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
