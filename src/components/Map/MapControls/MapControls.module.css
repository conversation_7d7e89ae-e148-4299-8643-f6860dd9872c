/* @import '../../globalVars.css'; */

:root {
  --bottom-right-control-position: 23px;
  /* --map-padding: 32px;
  --card-border-radius: 8px;
  --logo-height: 48px;
  --header-margin-bottom: 24px;
  --color-deep-blue: rgb(32, 71, 122);
  --color-BT-blue: rgb(22, 70, 133);
  --color-BT-blue-50: rgba(22, 70, 133, 0.5); */
  --antd-active-blue: #1890ff;
}

:global(.flexCenter) {
  display: flex;
  align-items: center;
  justify-content: center;
}

.mapExpanderControl {
  /* position: absolute;
  top: 10px;
  right: 10px;
  z-index: 150; */
  font-size: 21px;
  font-weight: bold;
  background-color: white;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  border-radius: 4px;
  pointer-events: auto;
  width: 29px;
}
.mapExpanderControl button {
  overflow: hidden;
  width: 29px;
  height: 29px;
  display: block;
  padding: 0;
  outline: none;
  border: 0;
  box-sizing: border-box;
  background-color: transparent;
  cursor: pointer;
  pointer-events: auto;
  position: relative;
}
.mapExpanderControl button:hover {
  background: rgba(0, 0, 0, 0.05);
}
.mapExpanderControl button svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.mapThemeControl,
.mapLayerControl {
  position: absolute;
  right: 10px;
  z-index: 100;
  font-size: 21px;
  font-weight: bold;
  background-color: white;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  border-radius: 4px;
  pointer-events: auto;
}

/* Position of the map theme button */
.mapThemeControl {
  /* bottom: 200px; */
  /* bottom: 40px; */
  bottom: calc(var(--bottom-right-control-position) + 40px);
}

/* Position of the map layer button */
.mapLayerControl {
  /* bottom: 240px; */
  /* bottom: 80px; */
  bottom: calc(var(--bottom-right-control-position) + 80px);
}

/* Position of the expanded map layer button */
.mapLayerControlExpanded {
  /* bottom: 260px; */
  /* bottom: 100px; */
  bottom: calc(var(--bottom-right-control-position) + 100px);
}

.mapThemeControlButton,
.mapLayerControlButton {
  /* overflow: hidden; */
  width: 29px;
  height: 29px;
  display: block;
  padding: 0;
  outline: none;
  border: 0;
  box-sizing: border-box;
  background-color: transparent;
  cursor: pointer;
  pointer-events: auto;
  line-height: 18px;
}

.mapThemeControl span,
.mapLayerControl span {
  display: none;
  font-weight: 400;
  font-size: 12px;
}

.mapThemeControlButtonExpanded,
.mapLayerControlButtonExpanded {
  width: 50px;
  height: 50px;
}

.mapThemeControlButtonExpanded span,
.mapLayerControlButtonExpanded span {
  display: block;
}

.mapThemeControl button:hover,
.mapLayerControl button:hover {
  background: rgba(0, 0, 0, 0.05);
}

.mapThemeOptions,
.mapLayerOptions {
  position: absolute;
  right: 50px;
  z-index: 200;
  display: flex;
  flex-direction: column;
  background-color: white;
  padding: 10px;
  padding-right: 0px;
  border-radius: 8px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

/* Position of the popup theme options */
.mapThemeOptions {
  /* bottom: 165px; */
  /* bottom: 5px; */
  bottom: calc(var(--bottom-right-control-position) + 5px);
}

/* Position of the popup layer options */
.mapLayerOptions {
  /* bottom: 235px; */
  /* bottom: calc(var(--bottom-right-control-position) + 74px); */
  bottom: calc(var(--bottom-right-control-position) + 25px);
}

.mapThemeOptionsExpanded,
.mapLayerOptionsExpanded {
  position: absolute;
  right: 70px;
  z-index: 200;
  background-color: white;
  padding: 10px 15px;
  border-radius: 8px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

/* Position of the expanded popup theme options */
.mapThemeOptionsExpanded {
  /* bottom: 164px; */
  bottom: calc(var(--bottom-right-control-position) + 4px);
}

/* Position of the expanded layer theme options */
.mapLayerOptionsExpanded {
  /* bottom: 225px; */
  /* bottom: calc(var(--bottom-right-control-position) + 65px); */
  bottom: calc(var(--bottom-right-control-position) + 30px);
}

.mapThemeOptionsExpanded > span,
.mapLayerOptionsExpanded > span {
  font-weight: bold;
  display: inline-block;
  padding-bottom: 8px;
}

.themeImgOptionsContainer,
.layerImgOptionsContainer {
  /* display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 10px; */
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  row-gap: 10px;
}

.themeImgContainer,
.layerImgContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 0 2px;
  width: 85px;
}

/* .layerImgContainer {
  max-width: 86.6px;
} */

.themeImgBtn,
.layerImgBtn {
  overflow: hidden;
  outline: none;
  padding: 0;
  margin: 0;
  background-color: transparent;
  border: transparent;
  border-radius: 8px;
  width: 50px;
  height: 50px;
  cursor: pointer;
  pointer-events: auto;
}

.themeImgBtn img,
.layerImgBtn img {
  height: 100%;
  width: 100%;
}

.themeImgOptionsContainer span,
.layerImgOptionsContainer span {
  font-size: 12px;
}

.themeImgBtnActive,
.layerImgBtnActive {
  border: 2px solid var(--antd-active-blue);
}

.themeImgBtnActive > img,
.layerImgBtnActive > img {
  border: 4px solid white;
}

.mapSentinelLayerControlWrapper {
  display: block;
  position: absolute;
  top: 8px;
  right: 50px;
  background-color: #fff;
  text-align: center;
  font-size: 12px;
  padding: 8px;
  border: none;
  border-radius: 4px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  z-index: 200;
  cursor: pointer;
}

.parcelSwitch2Wrapper {
  display: block;
  position: absolute;
  top: 60px;
  right: 50px;
  background-color: #fff;
  text-align: center;
  font-size: 12px;
  padding: 8px;
  border: none;
  border-radius: 4px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  z-index: 200;
  cursor: pointer;
}

.controlSelect {
  color: var(--antd-active-blue);
  /* font-weight: 500; */
  /* font-size: 16px; */
  text-align: center;
  /* padding: 5.5px 0 4.5px; */
  border: none;
  /* border-bottom: 1px solid var(--antd-active-blue); */
  cursor: pointer;
  /* margin: 0 8px; */
}

.mspSentinelLayerControlButton {
  font-size: 13px;
  /* font-weight: 500; */
  /* background: var(--antd-active-blue); */
  color: var(--antd-active-blue);
  border: none;
  border-radius: 2px;
  padding: 0 4px;
  margin-left: 6px;
  cursor: pointer;
}

.infoButton {
  padding: 0;
  background: transparent;
  border: none;
  width: 18px;
  height: 18px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: black;
}

.layerLabelContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.pushTopRightControllerDown {
  margin-top: 40px;
  z-index: 200 !important;
}

.zoomCtrlStyle {
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19) !important;
  width: 29px;
}

.topRightControl {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding-top: 10px;
  padding-right: 10px;
  align-items: end;
  width: 39px;
  z-index: 200;
}

.topLeftControl {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding-top: 10px;
  padding-left: 10px;
  align-items: flex-start;
  /* width: 39px; */
  z-index: 200;
}

.bottomRightControl {
  position: absolute;
  bottom: 70px;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding-right: 10px;
  align-items: end;
  width: 39px;
  z-index: 200;
}
