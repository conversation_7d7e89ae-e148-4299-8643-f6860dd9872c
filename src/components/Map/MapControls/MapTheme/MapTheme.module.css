.btnPosition {
  /* position: absolute;
  right: 10px;
  bottom: 70px; */
  display: flex;
  flex-direction: row;
  align-items: center;
}

.themeBtn {
  border: 2px solid white;
  background: transparent;
  padding: 0;
  width: 55px;
  height: 55px;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  background-size: cover;
  background-image: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0) 25%,
      rgba(0, 0, 0, 0.5) 80%,
      rgba(0, 0, 0, 0.75)
    ),
    /* url("/images/mapbox/controls/automatic.png"); */
      url("../../../../assets/images/mapbox/controls/automatic.png");
  position: relative;
}

.themeBtn span {
  position: absolute;
  bottom: 5px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: white;
  font-weight: 600;
  font-family: Arial, "helvetica", "sans-serif";
}

.themeMenuContainer {
  position: relative;
}

.themeMenu {
  background-color: white;
  border-radius: 5px;
  transition: all 300ms ease-out;
  display: flex;
  flex-direction: row;
  gap: 10px;
  overflow: hidden;
  position: absolute;
  top: 0;
  right: 0;
  transform: translateY(-50%);
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  z-index: 300;
}

.themeMenu button {
  height: 50px;
  width: 50px;
  border: none;
  background: transparent;
  padding: 0;
  border-radius: 5px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2), 0 3px 12px 0 rgba(0, 0, 0, 0.19);
  overflow: hidden;
  cursor: pointer;
}

.themeMenu button img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  box-sizing: border-box;
}

.btnActive {
  border: 2px solid #1890ff !important;
}
.btnActive img {
  border: 4px solid white;
}

.themeMenuClose {
  max-width: 0;
  padding: 0;
}
.themeMenuOpen {
  max-width: 400px;
  padding: 10px 5px 5px;
}

.optionBtnContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  width: 65px;
  text-align: center;
  line-height: 12px;
  font-family: Arial, "helvetica", "sans-serif";
}
