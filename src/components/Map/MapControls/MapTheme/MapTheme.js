import { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import styles from "./MapTheme.module.css";
import autoImg from "../../../../assets/images/mapbox/controls/automatic.png";
import satelliteImg from "../../../../assets/images/mapbox/controls/satellite.png";
import streetImg from "../../../../assets/images/mapbox/controls/street.png";
import monochromeImg from "../../../../assets/images/mapbox/controls/monochrome.png";
import terrainImg from "../../../../assets/images/mapbox/controls/terrain.png";

const themesData = [
  { name: "Automatic", imgSrc: autoImg },
  { name: "Satellite", imgSrc: satelliteImg },
  { name: "Street", imgSrc: streetImg },
  { name: "Monochrome", imgSrc: monochromeImg },
  { name: "Terrain", imgSrc: terrainImg },
];

function MapTheme() {
  const [openMenu, setOpenMenu] = useState(false);

  const map = useSelector((state) => state.Map.map);
  const currentMapThemeOption = useSelector(
    (state) => state.Map.currentMapThemeOption
  );
  const dispatch = useDispatch();

  useEffect(() => {
    if (!map) return;
    
    map.fire("map.themeOption", {
      payload: { currentMapThemeOption: currentMapThemeOption },
    });

    map.on("map.setThemeOption", (e) => {
      const { payload } = e;
      const { currentMapThemeOption } = payload;

      console.log("map.setThemeOption called", currentMapThemeOption);
      dispatch({
        type: "Map/saveState",
        payload: { currentMapThemeOption: currentMapThemeOption },
      });
    });
  }, [map]);

  const openThemeMenu = () => {
    setOpenMenu(true);
  };

  const closeThemeMenu = () => {
    setOpenMenu(false);
  };

  const changeMapTheme = (e) => {
    e.stopPropagation();
    const themeName = e.currentTarget.value;

    dispatch({
      type: "Map/saveState",
      payload: { currentMapThemeOption: themeName },
    });

    map.fire("map.themeOption", {
      payload: { currentMapThemeOption: themeName },
    });
  };

  const parseThemeName = (name) => {
    if (name === "Monochrome") {
      return "Mono-chrome";
    }
    return name;
  };

  const themeValue = themesData.find(
    (theme) => theme.name === currentMapThemeOption
    // (theme) => theme.name === "Automatic"
  ).value;

  return (
    <div
      id="MapTheme"
      className={styles.btnPosition}
      onMouseEnter={openThemeMenu}
      onMouseLeave={closeThemeMenu}
    >
      <div className={styles.themeMenuContainer}>
        <div
          className={`${styles.themeMenu} ${
            openMenu ? styles.themeMenuOpen : styles.themeMenuClose
          }`}
        >
          {themesData.map((theme, idx) => (
            <div key={idx} className={styles.optionBtnContainer}>
              <button
                value={theme.name}
                onClick={changeMapTheme}
                className={`${
                  currentMapThemeOption === theme.name ? styles.btnActive : ""
                }`}
              >
                <img src={theme.imgSrc} alt={`${theme.name} icon`} />
              </button>
              <span style={{ fontSize: "12px" }}>
                {parseThemeName(theme.name)}
              </span>
            </div>
          ))}
        </div>
      </div>
      <button className={styles.themeBtn}>
        <span>Theme</span>
      </button>
    </div>
  );
}

export default MapTheme;
