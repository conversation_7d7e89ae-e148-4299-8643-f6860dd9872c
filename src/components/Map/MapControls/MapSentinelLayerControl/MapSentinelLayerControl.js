import { useEffect, useState } from "react";
import { connect } from "react-redux";
import { Select } from "antd";
import styles from "../MapControls.module.css";
import { zoomLevelToChangeBasemap } from "../../Map";
import { dateFormat } from "../../../../constants";
import moment from "moment";
import { useSentinelAPIKey } from "../../../hooks/useSentinelAPIKey";

const MapSentinelLayerControl = (props) => {
  const sentinelAPIKey = useSentinelAPIKey();

  const onChangeDaysWithinSentinel = (e) => {
    props.dispatch({
      type: "Map/saveState",
      payload: {
        daysWithinSentinel: e.target.value,
      },
    });
  };

  useEffect(() => {
    if (!props.map) return;

    const moveEnd = () => {
      const currentZoomLevel = props.map.getZoom();

      if (currentZoomLevel >= zoomLevelToChangeBasemap) {
        props.dispatch({
          type: "Map/saveState",
          payload: {
            showSentinelControl: true,
          },
        });
      } else {
        props.dispatch({
          type: "Map/saveState",
          payload: {
            showSentinelControl: false,
          },
        });
      }
    };

    props.map.on("moveend", moveEnd);

    return () => {
      props.map.off("moveend", moveEnd);
    };
  }, [props.map]);

  const addSentinel = () => {
    // remove source and layer if existing
    if (props.map.getLayer("SentinelWMSLayer")) {
      props.map.removeLayer("SentinelWMSLayer");
    }
    if (props.map.getSource("SentinelWMSSource")) {
      props.map.removeSource("SentinelWMSSource");
    }
    // generate wms url

    const mapWidth = props.map.getContainer().clientWidth;
    const mapHeight = props.map.getContainer().clientHeight;
    const fromDate = moment()
      .subtract(props.daysWithinSentinel, "days")
      .format(dateFormat);
    const toDate = moment().format(dateFormat);
    // add Sentinel satellite wms source
    // use BBOX={bbox-epsg-3857}
    // see the example in Mapbox documentation
    props.map.addSource("SentinelWMSSource", {
      type: "raster",
      tiles: [
        `https://services.sentinel-hub.com/ogc/wms/${sentinelAPIKey}?REQUEST=GetMap&BBOX={bbox-epsg-3857}&LAYERS=1_TRUE_COLOR&MAXCC=10&WIDTH=${mapWidth}&HEIGHT=${mapHeight}&FORMAT=image/jpeg&TIME=${fromDate}/${toDate}`,
      ],
      tileSize: 512,
      minzoom: zoomLevelToChangeBasemap,
    });
    console.log("wms source", props.map.getSource("SentinelWMSSource"));
    // add Sentinel satellite wms layer
    // below circleFill layer, at the bottom of all added layers
    const layers = props.map.getStyle().layers;
    let firstLayerId;
    for (const layer of layers) {
      if (
        layer.source &&
        layer.source !== "composite" &&
        !layer.source.includes("mapbox")
      ) {
        firstLayerId = layer.id;
        break;
      }
    }

    props.map.addLayer(
      {
        id: "SentinelWMSLayer",
        type: "raster",
        source: "SentinelWMSSource",
      },
      // 'circleFill',
      // "parcelMapScopeLayer",
      firstLayerId
    );
  };

  const removeSentinel = () => {
    if (props.map.getLayer("SentinelWMSLayer")) {
      props.map.removeLayer("SentinelWMSLayer");
    }
    if (props.map.getSource("SentinelWMSSource")) {
      props.map.removeSource("SentinelWMSSource");
    }
  };

  useEffect(() => {
    if (!props.map) return;

    if (props.showSentinelLayer) {
      addSentinel();
    } else {
      removeSentinel();
    }
  }, [props.showSentinelLayer, props.daysWithinSentinel]);

  return (
    <div
      id="MapSentinelLayerControl"
      key="sentinel control wrapper"
      className={styles.mapSentinelLayerControlWrapper}
      style={{
        visibility: props.showSentinelControl ? "visible" : "hidden",
      }}
    >
      <span>
        {!props.showSentinelLayer
          ? `Satellite Image within ${props.daysWithinSentinel} days`
          : "Satellite Image within"}
      </span>
      {props.showSentinelLayer && (
        <select
          value={props.daysWithinSentinel}
          onChange={onChangeDaysWithinSentinel}
          className={styles.controlSelect}
        >
          <option key="30 days" value={30}>
            30 days
          </option>
          <option key="60 days" value={60}>
            60 days
          </option>
          <option key="90 days" value={90}>
            90 days
          </option>
        </select>
      )}
      <button
        className={styles.mspSentinelLayerControlButton}
        onClick={() => {
          props.dispatch({
            type: "Map/saveState",
            payload: {
              showSentinelLayer: !props.showSentinelLayer,
            },
          });
        }}
      >
        {props.showSentinelLayer ? "Close" : "Show"}
      </button>
    </div>
  );
};

export default connect(({ Map }) => ({
  map: Map.map,
  showSentinelLayer: Map.showSentinelLayer,
  showSentinelControl: Map.showSentinelControl,
  daysWithinSentinel: Map.daysWithinSentinel,
}))(MapSentinelLayerControl);
