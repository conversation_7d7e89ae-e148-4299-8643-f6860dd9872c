import { useState, useEffect, useRef } from "react";
import { connect } from "react-redux";
import { capitalize } from "../../../../utils/strings";
import isEmpty from "lodash.isempty";

const zoomLevelToCBSA = 8;
const zoomLevelToLayerTypes = 9;
const zoomLevelToFlood = 14;

const MapZoomTooltip = connect(({ Map }) => ({
  map: Map.map,
  currentMapLayerOptions: Map.currentMapLayerOptions,
  heatmapType: Map.heatmapType,
  mapZoomLevel: Map.mapZoomLevel,
}))(function (props) {
  const [show, setShow] = useState(false);
  const [message, setMessage] = useState({});

  const propsRef = useRef({ ...props, show, message });
  propsRef.current = { ...props, show, message };

  const layerOptions = [
    { type: "school districts", minZoom: zoomLevelToLayerTypes },
    { type: "activity centers", minZoom: zoomLevelToLayerTypes },
    { type: "county", minZoom: zoomLevelToLayerTypes },
    { type: "ZIP code", minZoom: zoomLevelToLayerTypes },
    { type: "opportunity zones", minZoom: zoomLevelToLayerTypes },
    { type: "school zones", minZoom: zoomLevelToLayerTypes },
    { type: "cbsa", minZoom: zoomLevelToCBSA },
    { type: "flood zone", minZoom: 7 },
    { type: "HUD public housing", minZoom: zoomLevelToLayerTypes },
    { type: "new construction", minZoom: 7 },
    { type: "chain locations", minZoom: 0 },
    { type: "submarket", minZoom: 9 },
    { type: "demographics", minZoom: 9 },
    { type: "subdivision", minZoom: 9 },
    { type: "neighborhood", minZoom: 9 },
    { type: "wetlands", minZoom: 9 },
    // { type: "power lines", minZoom: 9 },
    { type: "residential development", minZoom: 9 },
    { type: "chain stores", minZoom: 8 },
    // { type: "institutional owners", minZoom: 9 },
    { type: "multi family", minZoom: 8 },
    { type: "municipal water district", minZoom: 6 },
    { type: "city", minZoom: 8 },
    { type: "zoning types", minZoom: 12 },
    { type: "public housing voucher", minZoom: 8 },
    { type: "public housing ehasa", minZoom: 8 },
    { type: "industrial parcels", minZoom: 9 },
    { type: "split parcels", minZoom: 8 },
    { type: "arcgis bus stop", minZoom: 8 },
    { type: "arcgis transit line", minZoom: 8 },
    { type: "qualified census tracts", minZoom: 8 },
    { type: "multi family apartments", minZoom: 4 },
  ];

  const setZoomTooltipMessage = () => {
    if (
      (propsRef.current.currentMapLayerOptions.length > 0 ||
        propsRef.current.heatmapType != null) &&
      props.map
    ) {
      const currentZoomLevel = props.map.getZoom();

      let layerIndex = -1;

      for (let i = 0; i < layerOptions.length; i++) {
        if (
          propsRef.current.currentMapLayerOptions.includes(
            layerOptions[i].type
          ) ||
          layerOptions[i].type === propsRef.current.heatmapType
        ) {
          if (layerIndex !== -1) {
            if (
              // layerOptions[i].minZoom > layerOptions[layerIndex].minZoom &&
              layerOptions[i].minZoom > currentZoomLevel &&
              layerOptions[i].minZoom < layerOptions[layerIndex].minZoom
            ) {
              layerIndex = i;
            }
          } else {
            if (layerOptions[i].minZoom > currentZoomLevel) {
              layerIndex = i;
            }
          }
        }
      }

      if (layerIndex !== -1) {
        if (layerOptions[layerIndex].type === "cbsa") {
          setMessage({
            layerName: layerOptions[layerIndex].type.toUpperCase(),
            minZoom: layerOptions[layerIndex].minZoom,
          });
        } else if (layerOptions[layerIndex].type.includes("osm")) {
          setMessage({
            layerName: "OSM Residential",
            minZoom: layerOptions[layerIndex].minZoom,
          });
        } else if (
          layerOptions[layerIndex].type.includes("multi family apartments")
        ) {
          setMessage({
            layerName: "Multifamily Apartments",
            minZoom: layerOptions[layerIndex].minZoom,
          });
        } else {
          setMessage({
            layerName: capitalize(layerOptions[layerIndex].type),
            minZoom: layerOptions[layerIndex].minZoom,
          });
        }

        setShow(true);
      } else {
        if (!isEmpty(propsRef.current.message)) {
          setMessage({});
          setShow(false);
        }
      }
    } else {
      if (!isEmpty(propsRef.current.message)) {
        setMessage({});
        setShow(false);
      }
    }
  };

  useEffect(() => {
    if (![props.map]) return;

    setZoomTooltipMessage();
    // eslint-disable-next-line
  }, [props.currentMapLayerOptions, props.heatmapType]);

  useEffect(() => {
    if (!props.map) return;

    const moveEnd = () => {
      setZoomTooltipMessage();
    };

    props.map.on("zoomend", moveEnd);
    return () => {
      props.map.off("zoomend", moveEnd);
    };
    // eslint-disable-next-line
  }, [props.map]);

  const jumpZoomHandler = () => {
    props.map.setZoom(message.minZoom + 0.1);
    setShow(false);
  };

  return (
    <div
      id="MapZoomTooltip"
      style={{
        position: "absolute",
        top: "130px",
        left: "50%",
        backgroundColor: "rgba(255,255,255,0.85)",
        textAlign: "center",
        transform: "translate(-50%)",
        width: "300px",
        padding: "15px 20px",
        display: show ? "flex" : "none",
        flexDirection: "column",
        zIndex: 300,
        boxShadow:
          "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
      }}
    >
      <span>
        Current zoom level is {props.map ? props.map.getZoom().toFixed(2) : 0}
        {/* TODO: if layerName ends in s, remove it. eg. Wetlands's layer */}.{" "}
        {message.layerName} layer starts at zoom level {message.minZoom}.
      </span>
      <span>
        <a onClick={jumpZoomHandler}>Click to jump zoom.</a>
      </span>
    </div>
  );
});

export default MapZoomTooltip;
