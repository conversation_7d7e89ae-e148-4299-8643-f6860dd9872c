import { useSelector, useDispatch } from "react-redux";
import { AiOutlineFullscreen } from "@react-icons/all-files/ai/AiOutlineFullscreen";
import { AiOutlineFullscreenExit } from "@react-icons/all-files/ai/AiOutlineFullscreenExit";
import mapControlStyles from "../MapControls.module.css";
import { useEffect } from "react";

function MapExpander() {
  const map = useSelector((state) => state.Map.map);
  const mapExpander = useSelector((state) => state.Configure.mapExpander);
  const mapExpandedView = useSelector((state) => state.Map.mapExpandedView);
  const dispatch = useDispatch();

  useEffect(() => {
    if (mapExpander && mapExpander.enabled) {
      const expanded = mapExpander.init.mapExpandedView;
      dispatch({
        type: "Map/saveState",
        payload: {
          mapExpandedView: expanded,
        },
      });
    }
  }, [mapExpander]);

  const onClickHandle = (e) => {
    e.stopPropagation();
    if (!map) return;

    dispatch({
      type: "Map/saveState",
      payload: {
        mapExpandedView: !mapExpandedView,
      },
    });

    console.log("expanded in map: ", !mapExpandedView);
    map.fire("mapExpandedView", {
      payload: { mapExpandedView: !mapExpandedView },
    });
  };

  return (
    <div id="MapExpander" className={mapControlStyles.mapExpanderControl}>
      <button onClick={onClickHandle}>
        {mapExpandedView ? (
          <AiOutlineFullscreenExit />
        ) : (
          <AiOutlineFullscreen />
        )}
      </button>
    </div>
  );
}

export default MapExpander;
