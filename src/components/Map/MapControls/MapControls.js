import { useSelector, useDispatch } from "react-redux";
import MapTheme from "./MapTheme/MapTheme";
import MapLayers from "./MapLayers/MapLayers";
import MapSentinelLayerControl from "./MapSentinelLayerControl/MapSentinelLayerControl";
import MapZoomTooltip from "./MapZoomTooltip/MapZoomTooltip";
import MapZoom from "./MapZoom/MapZoom";
import MapToImageDownload from "./MapToImageDownload/MapToImageDownload";
import MapExpander from "./MapExpander/MapExpander";
import MapDraw from "./MapDraw/MapDraw";
import MapTilt from "./MapTilt/MapTilt";
import SelectRadius from "./SelectRadius/SelectRadius";
import MapNavigation from "./MapNavigation/MapNavigation";
import styles from "./MapControls.module.css";
import MapRuler from "./MapRuler/MapRuler";
import PropertyParcelLegend from "../MapLegends/PropertyParcelLegend/PropertyParcelLegend";
import MapStreetview from "./MapStreetview/MapStreetview";
import NewHomeLegend from "../MapLegends/NewHomeLegend/NewHomeLegend";
import HeatmapMenu from "./HeatmapMenu/HeatmapMenu";
import MapLegendButton from "./MapLegendButton/MapLegendButton";
import { PolygonExport } from "../../features/parcel-output/PolygonExport";
import ParcelSwitch2 from "./SelectRadius/ParcelSwitch2";

function MapControls() {
  const sentinalHub = useSelector((state) => state.Configure.sentinelHub);
  const platlabsOnly = useSelector((state) => state.Configure.platlabsOnly);
  const marketplaceOnly = useSelector((state) => state.Configure.marketplaceOnly);
  const mapExpander = useSelector((state) => state.Configure.mapExpander);
  const mapToImageDownload = useSelector((state) => state.Configure.mapToImageDownload);
  const mapDraw = useSelector((state) => state.Configure.mapDraw);
  const mapRuler = useSelector((state) => state.Configure.mapRuler);
  const selectRadius = useSelector((state) => state.Configure.selectRadius);
  const mapNavigation = useSelector((state) => state.Configure.mapNavigation);
  const mapControlsMapLayers = useSelector((state) => state.Configure.mapControlsMapLayers);
  const streetview = useSelector((state) => state.Configure.streetview);
  const drawingMode = useSelector((state) => state.Map.drawingMode);
  const drawnCustomPolygons = useSelector((state) => state.Map.drawnCustomPolygons);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const newBuildClusterType = useSelector((state) => state.Map.newBuildClusterType);
  const minimzedMapLegends = useSelector((state) => state.Map.minimzedMapLegends);
  const heatmapType = useSelector((state) => state.Map.heatmapType);

  const dispatch = useDispatch();
  console.log("platlabsOnly", platlabsOnly);
  return (
    <>
      <div id="TopRightControl" className={styles.topRightControl}>
        {mapExpander.enabled && <MapExpander />}
        <MapZoom />
        {mapToImageDownload && <MapToImageDownload />}
        <PolygonExport />
        {mapDraw && <MapDraw />}
        {mapRuler && <MapRuler />}
        <MapTilt />
      </div>

      <div id="TopLeftControl" className={styles.topLeftControl}>
        {mapNavigation.enabled && <MapNavigation />}
        {!(platlabsOnly || marketplaceOnly) && <PropertyParcelLegend />}
        {/* {currentMapLayerOptions.includes("new construction") &&
          newBuildClusterType === "Clustered By Status" && <NewHomeLegend />} */}
      </div>

      <div id="BottomRightControl" className={styles.bottomRightControl}>
        {(currentMapLayerOptions.length > 0 || heatmapType) && minimzedMapLegends && (
          <MapLegendButton />
        )}
        {streetview.enabled && <MapStreetview />}
        <MapTheme />
      </div>

      {mapControlsMapLayers && <MapLayers />}
      {sentinalHub && <MapSentinelLayerControl />}
      <MapZoomTooltip />
      {!(platlabsOnly || marketplaceOnly) && <ParcelSwitch2 />}
    </>
  );
}

export default MapControls;


