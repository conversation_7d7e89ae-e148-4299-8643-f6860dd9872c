import { useEffect } from "react";
import styles from "./mapTilt.module.css";
import { Md3DRotation } from "@react-icons/all-files/md/Md3DRotation";
import { useSelector, useDispatch } from "react-redux";

function MapTilt() {
  const map = useSelector((state) => state.Map.map);
  const drawnCustomPolygons = useSelector(
    (state) => state.Map.drawnCustomPolygons
  );
  const currentMapThemeOption = useSelector(
    (state) => state.Map.currentMapThemeOption
  );

  const dispatch = useDispatch();

  useEffect(() => {
    if (!map) return;
    if (currentMapThemeOption === "Terrain") {
      map.dragRotate.enable();
      map.touchZoomRotate.enable();
    } else {
      map.dragRotate.disable();
      map.touchZoomRotate.disable();
      map.easeTo({ pitch: 0, bearing: 0 });
      dispatch({ type: "Map/saveState", payload: { tiltMode: false } });
    }
  }, [map, currentMapThemeOption]);

  const tiltBtnHandler = () => {
    if (map.getPitch() == 0) {
      map.easeTo({ pitch: 80 });
      dispatch({ type: "Map/saveState", payload: { tiltMode: true } });
    } else {
      map.easeTo({ pitch: 0, bearing: 0 });
      dispatch({ type: "Map/saveState", payload: { tiltMode: false } });
    }
  };

  if (currentMapThemeOption === "Terrain") {
    return (
      <div
        id="MapTilt"
        className={`${styles.buttonContainer} ${
          drawnCustomPolygons.length > 0
            ? styles.shiftDownPosition
            : styles.defaultPosition
        }`}
      >
        <button className={styles.button} onClick={tiltBtnHandler}>
          <Md3DRotation size={20} />
        </button>
      </div>
    );
  } else {
    return null;
  }
}

export default MapTilt;
