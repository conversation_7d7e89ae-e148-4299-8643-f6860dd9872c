.buttonContainer {
  /* position: absolute;
  right: 10px; */
  overflow: hidden;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  border-radius: 4px;
  pointer-events: auto;
  z-index: 200;
  width: 29px;
}

.defaultPosition {
  top: 197px;
}

.shiftDownPosition {
  top: 236px;
}

.button {
  width: 29px;
  height: 29px;
  background-color: white;
  border: none;
  cursor: pointer;
  position: relative;
  display: block;
}

.button:hover {
  background-color: rgb(240, 240, 240);
}

.button svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
