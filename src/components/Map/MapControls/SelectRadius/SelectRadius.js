import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Select } from "antd";
import { geojsonTemplate } from "../../../../constants";
import { FaTrash } from "@react-icons/all-files/fa/FaTrash";
import { FaEye } from "@react-icons/all-files/fa/FaEye";
import { FaEyeSlash } from "@react-icons/all-files/fa/FaEyeSlash";
import { MdOutlineCenterFocusStrong } from "@react-icons/all-files/md/MdOutlineCenterFocusStrong";
import styles from "./SelectRadius.module.css";
import { default as turf_circle } from "@turf/circle";
import { default as turf_bbox } from "@turf/bbox";
import { Source, Layer } from "../../MapLayers/index";
import mapboxgl from "mapbox-gl"; // eslint-disable-line import/no-webpack-loader-syntax
import { Switch } from "antd";
import ParcelSwitch from "./ParcelSwitch";

// const dateFormat = "YYYY-MM-DD";
const options = [
  // { label: "0.1 Miles", value: 0.1 },
  // { label: "0.3 Miles", value: 0.3 },
  { label: "0.5 Miles", value: 0.5 },
  { label: "1 Mile", value: 1 },
  // { label: "1.5 Miles", value: 1.5 },
  { label: "2 Miles", value: 2 },
  { label: "3 Miles", value: 3 },
  { label: "5 Miles", value: 5 },
  { label: "10 Miles", value: 10 },
  { label: "15 Miles", value: 15 },
];

let subjectPropertyMarker = null;

const setSubjectMarker = (map, coordinates, userGroup) => {
  console.log('setSubjectMarker - userGroup', userGroup);
  if (!map || !coordinates) return;
  if (coordinates.length === 0) return;

  if (subjectPropertyMarker) {
    subjectPropertyMarker.remove();
  }
  if (coordinates.length > 0) {
    let el = document.createElement("div");
    // size svg using viewBox and width/height
    el.innerHTML = `<svg viewBox="0 0 244 232" width="32" height="32" preserveAspectRatio="xMidYMid meet" xmlns="http://www.w3.org/2000/svg"><path d="m122 181.996-75.237 49.558 23.884-86.868L.265 88.446l89.997-4.129L122 0l31.738 84.317 89.997 4.129-70.382 56.24 23.884 86.868z" opacity="1" fill-rule="evenodd"/></svg>`;
    el.className = userGroup.includes('Lennar') ? styles.subjectPropertyMarkerLennar : styles.subjectPropertyMarker;
    subjectPropertyMarker = new mapboxgl.Marker(el);
    subjectPropertyMarker.setLngLat(coordinates).addTo(map);
  }
};

const setCircleRadius = (map, coordinates, currentRadius) => {
  if (!map || !coordinates || !currentRadius) return;
  if (coordinates.length === 0) return;

  const drawCircle = turf_circle(coordinates, currentRadius, {
    units: "miles",
  });
  map.fitBounds(turf_bbox(drawCircle), { padding: 32 });
  return drawCircle;
};

function SelectRadius() {
  const [showCircle, setShowCircle] = useState(true);
  const [circleData, setCircleData] = useState(geojsonTemplate);

  const map = useSelector((state) => state.Map.map);
  const eventCoordinates = useSelector((state) => state.Map.eventCoordinates);
  const currentRadiusMile = useSelector((state) => state.Map.currentRadiusMile);
  const currentMapThemeOption = useSelector(
    (state) => state.Map.currentMapThemeOption
  );
  const drawnCustomPolygons = useSelector(
    (state) => state.Map.drawnCustomPolygons
  );
  const CMAscorecardModalOpen = useSelector(
    (state) => state.Map.CMAscorecardModalOpen
  );
  const parcelMode = useSelector((state) => state.Map.parcelMode);
  const selectRadius = useSelector((state) => state.Configure.selectRadius);
  const defaultRadius = useSelector((state) => state.Configure.defaultRadius);
  const serverType = useSelector((state) => state.Configure.serverType);

  const user = useSelector((state) => state.Configure.user);

  const dispatch = useDispatch();

  const currentRadiusMileRef = useRef(currentRadiusMile);
  currentRadiusMileRef.current = currentRadiusMile;

  const eventCoordinatesRef = useRef(eventCoordinates);
  eventCoordinatesRef.current = eventCoordinates;

  const currentMapThemeOptionRef = useRef(currentMapThemeOption);
  currentMapThemeOptionRef.current = currentMapThemeOption;

  const drawnCustomPolygonsRef = useRef(drawnCustomPolygons);
  drawnCustomPolygonsRef.current = drawnCustomPolygons;

  const CMAscorecardModalOpenRef = useRef(CMAscorecardModalOpen);
  CMAscorecardModalOpenRef.current = CMAscorecardModalOpen;

  const showCircleRef = useRef(showCircle);
  showCircleRef.current = showCircle;

  const toggleHideCircleRef = useRef(null);
  const hasInitialized = useRef(false);

  // Initialize currentRadiusMile with defaultRadius from configuration (only once)
  useEffect(() => {
    if (!hasInitialized.current && selectRadius && selectRadius.init && selectRadius.init.defaultRadius !== undefined) {
      // Set if currentRadiusMile is null (initial state) or still the old default 0.5
      if (currentRadiusMile === null || currentRadiusMile === 0.5) {
        dispatch({
          type: "Map/saveState",
          payload: {
            currentRadiusMile: selectRadius.init.defaultRadius,
          },
        });
      }
      hasInitialized.current = true;
    }
  }, [selectRadius, dispatch, currentRadiusMile]);

  useEffect(() => {
    if (!map) return;

    const mapClick = (e) => {
      if (parcelMode) return;
      const coordinates = [e.lngLat.lng, e.lngLat.lat];
      const features = map.queryRenderedFeatures(e.point);
      if (
        features.some(
          (f) =>
            f.source.includes("fundrise") ||
            f.source.includes("water-district-source-geojson")
        )
      )
        return;

      // add circle
      if (
        drawnCustomPolygonsRef.current.length === 0 &&
        !CMAscorecardModalOpenRef.current
      ) {
        const drawCircle = setCircleRadius(
          map,
          coordinates,
          currentRadiusMileRef.current
        );
        setCircleData(drawCircle);
      }

      // add star
      setSubjectMarker(map, coordinates, user.userGroup);

      dispatch({
        type: "Map/saveState",
        payload: {
          eventCoordinates: coordinates,
        },
      });
    };

    const zoomEnd = () => {
      if (
        currentMapThemeOptionRef.current === "Automatic" &&
        eventCoordinatesRef.current.length > 0
      ) {
        map.on("style.load", () => {
          // add star
          setSubjectMarker(map, eventCoordinatesRef.current, user.userGroup);
        });
      }
    };

    const setRadius = (e) => {
      const radius = e.payload.currentRadiusMile;
      const propertyModalTabKey = e.payload?.propertyModalTabKey;
      dispatch({
        type: "Map/saveState",
        payload: {
          currentRadiusMile: radius,
        },
      });
      // don't draw circle if proForma tab is selected
      if (eventCoordinatesRef.current.length > 0 && (!propertyModalTabKey || (propertyModalTabKey && propertyModalTabKey !== 'proForma'))) {
        if (drawnCustomPolygonsRef.current.length === 0) {
          const drawCircle = setCircleRadius(
            map,
            eventCoordinatesRef.current,
            radius
          );
          setCircleData(drawCircle);
        }

        setSubjectMarker(map, eventCoordinatesRef.current, user.userGroup);
      }
    };

    const setEventCoordinates = (e) => {
      const coordinates = e.payload.eventCoordinates;
      const propertyModalTabKey = e.payload?.propertyModalTabKey;
      dispatch({
        type: "Map/saveState",
        payload: {
          eventCoordinates: coordinates,
        },
      });
      if (coordinates.length > 0) {
        // add circle
        if (
          drawnCustomPolygonsRef.current.length === 0 &&
          !CMAscorecardModalOpenRef.current && (
            !propertyModalTabKey || (
            propertyModalTabKey &&
            propertyModalTabKey !== 'proForma'
          ))
        ) {
          const drawCircle = setCircleRadius(
            map,
            coordinates,
            currentRadiusMileRef.current
          );
          setCircleData(drawCircle);
        }

        // add star
        setSubjectMarker(map, coordinates, user.userGroup);
      } else {
        // remove star
        if (subjectPropertyMarker != null) {
          subjectPropertyMarker.remove();
          subjectPropertyMarker = null;
        }
        setCircleData(geojsonTemplate);
        dispatch({
          type: "Map/saveState",
          payload: {
            eventCoordinates: [],
          },
        });
      }
    };

    // Only fire the initial radius event if currentRadiusMile is properly set (not null)
    if (currentRadiusMile !== null) {
      map.fire("selectRadius.radius", {
        payload: { currentRadiusMile: currentRadiusMile },
      });
    }
    
    map.on("map.click", mapClick);
    map.on("zoomend", zoomEnd);
    map.on("selectRadius.setRadius", setRadius);
    map.on("selectRadius.setEventCoordinates", setEventCoordinates);

    map.on("selectRadius.hideCircle", () => {
      if (!toggleHideCircleRef.current) return;
      if (showCircleRef.current) toggleHideCircleRef.current();
    });
    map.on("selectRadius.showCircle", () => {
      if (!toggleHideCircleRef.current) return;
      if (!showCircleRef.current) toggleHideCircleRef.current();
    });
    map.on("selectRadius.toggleCircle", () => {
      if (!toggleHideCircleRef.current) return;
      toggleHideCircleRef.current();
    });

    // from navigation menu
    map.on("selectRadius.navigationClear", clearCMA);

    return () => {
      map.off("map.click", mapClick);
      map.off("zoomend", zoomEnd);
      map.off("selectRadius.setRadius", setRadius);
      map.off("selectRadius.setEventCoordinates", setEventCoordinates);
      map.off("selectRadius.navigationClear", clearCMA);
    };
  }, [map, parcelMode, currentRadiusMile]); // Added currentRadiusMile to dependencies

  useEffect(() => {
    // When custom drawn polygon is cleared, add radius circle back to subject marker
    if (
      drawnCustomPolygons.length === 0 &&
      eventCoordinates.length > 0 &&
      subjectPropertyMarker
    ) {
      // remove star
      if (subjectPropertyMarker) {
        subjectPropertyMarker.remove();
        subjectPropertyMarker = null;
      }
      dispatch({
        type: "Map/saveState",
        payload: {
          eventCoordinates: [],
        },
      });
      setCircleData(geojsonTemplate);
    }
  }, [drawnCustomPolygons]);

  useEffect(() => {
    if (!map || !map.style || !map.style._loaded || !map.getLayer("circleFill"))
      return;

    if (CMAscorecardModalOpen) {
      map.setLayoutProperty("circleFill", "visibility", "none");
    } else {
      map.setLayoutProperty("circleFill", "visibility", "visible");
      if (eventCoordinates.length > 0) {
        const drawCircle = setCircleRadius(
          map,
          eventCoordinates,
          currentRadiusMile
        );
        setCircleData(drawCircle);
      }
    }
  }, [CMAscorecardModalOpen]);

  const onChangeRadius = (value) => {
    // change circle
    if (eventCoordinates && eventCoordinates.length > 0) {
      const drawCircle = setCircleRadius(map, eventCoordinates, value);
      setCircleData(drawCircle);
    }

    dispatch({
      type: "Map/saveState",
      payload: {
        currentRadiusMile: value,
      },
    });

    map.fire("selectRadius.radius", {
      payload: { currentRadiusMile: value },
    });
  };

  const centerMap = () => {
    map.fitBounds(turf_bbox(circleData), { padding: 32 });
  };

  const toggleHideCircle = () => {
    if (!map || !map.style || !map.style._loaded || !map.getLayer("circleFill")) 
      return;
    
    if (showCircle) {
      map.setLayoutProperty("circleFill", "visibility", "none");
    } else {
      map.setLayoutProperty("circleFill", "visibility", "visible");
    }
    setShowCircle(!showCircle);
  };
  toggleHideCircleRef.current = toggleHideCircle;

  const clearCMA = () => {
    if (!map || !map.style || !map.style._loaded || !map.getLayer("circleFill"))
      return;

    setCircleData(geojsonTemplate);
    setShowCircle(true);
    map.setLayoutProperty("circleFill", "visibility", "visible");
    // remove star
    if (subjectPropertyMarker) {
      subjectPropertyMarker.remove();
      subjectPropertyMarker = null;
    }
    dispatch({
      type: "Map/saveState",
      payload: {
        eventCoordinates: [],
      },
    });
    map.fire("selectRadius.clear");
  };

  if (drawnCustomPolygons.length > 0 || CMAscorecardModalOpen) {
    return null;
  } else {
    return (
      <>
        <div id="radiusSelectWrapper" className={styles.selectStyle}>
          <span
            key="radius label"
            style={{ lineHeight: "32px", marginRight: 0, whiteSpace: "nowrap" }}
          >
            Select a radius: 
          </span>
          <Select
            key="radius select"
            id="radiusSelect" // not working
            defaultValue={defaultRadius}
            options={options}
            value={currentRadiusMile}
            onChange={onChangeRadius}
            style={{ width: "100px" }}
          />
          {/* {props.circleBbox.length > 0 &&  */}
          {eventCoordinates.length > 0 && (
            <div style={{ display: "flex", flexDirection: "row" }}>
              <button
                onClick={centerMap}
                className={styles.selectRadiusHideClearBtns}
              >
                <MdOutlineCenterFocusStrong />
              </button>
              <button
                onClick={toggleHideCircle}
                className={styles.selectRadiusHideClearBtns}
              >
                {showCircle ? <FaEyeSlash /> : <FaEye />}
              </button>
              {selectRadius.init.showClearButton && (
                <button
                  onClick={clearCMA}
                  className={styles.selectRadiusHideClearBtns}
                >
                  <FaTrash />
                </button>
              )}
            </div>
          )}
          <ParcelSwitch />
        </div>
        <Source id="circle" type="geojson" data={circleData}>
          <Layer
            {...{
              id: "circleFill",
              type: "fill",
              paint: {
                "fill-color": "#c4edff",
                "fill-opacity": 0.5,
              },
            }}
          />
        </Source>
      </>
    );
  }
}
export default SelectRadius;
