import { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Switch } from "antd";
import { minZoom } from "../../MapLayers/Regrid";
import { LockIcon, UnlockIcon } from "lucide-react";

const ParcelSwitch = () => {
  const map = useSelector((state) => state.Map.map);
  const parcelMode = useSelector((state) => state.Map.parcelMode);
  const parcelLock = useSelector((state) => state.Map.parcelLock);
  const user = useSelector((state) => state.Configure.user);
  const [parcelSwitch, setParcelSwitch] = useState(false);
  const dispatch = useDispatch();

  const isEvergreenUser = user?.userGroup?.includes('Evergreen');

  useEffect(() => {
    if (!map) return;

    const zoomEnd = () => {
      const zoom = map.getZoom();
      if (!parcelLock || !isEvergreenUser) {
        if (zoom >= minZoom) {
          if (!parcelSwitch) setParcelSwitch(true);
        } else {
          if (parcelSwitch) setParcelSwitch(false);
          if (parcelMode) {
            dispatch({
              type: "Map/saveState",
              payload: {
                parcelMode: false,
              },
            });
            map.fire("selectRadius.parcelMode", {
              payload: { parcelMode: false },
            });
          }
        }
      } else {
        if (!parcelSwitch) setParcelSwitch(true);
      }
    };

    const handleParcelModeSwitch = (e) => {
      const { payload } = e;
      if (parcelMode !== payload.parcelMode) {
        dispatch({
          type: "Map/saveState",
          payload: {
            parcelMode: payload.parcelMode,
          },
        });
      }
    };

    map.on("zoomend", zoomEnd);
    map.on("selectRadius.parcelMode", handleParcelModeSwitch);
    return () => {
      map.off("zoomend", zoomEnd);
      map.off("selectRadius.parcelMode", handleParcelModeSwitch);
    };
  }, [map, parcelSwitch, parcelMode, parcelLock, isEvergreenUser]);

  if (!parcelSwitch) return null;
  return (
    <div className="h-full mr-[10px] translate-y-[4px] flex flex-row gap-[5px] items-center">
      <Switch
        disabled={parcelLock}
        size="small"
        checked={parcelMode}
        onChange={(checked) => {
          dispatch({
            type: "Map/saveState",
            payload: {
              parcelMode: checked,
            },
          });
          map.fire("selectRadius.parcelMode", {
            payload: { parcelMode: checked },
          });
        }}
      />
      <span>Parcel</span>
      {isEvergreenUser && (
        <span
          className="cursor-pointer"
          onClick={() => {
            dispatch({
              type: "Map/saveState",
              payload: {
                parcelLock: !parcelLock,
              },
            });
          }}
        >
          {parcelLock ? <UnlockIcon width={15} /> : <LockIcon width={15} />}
        </span>
      )}
    </div>
  );
};

export default ParcelSwitch;