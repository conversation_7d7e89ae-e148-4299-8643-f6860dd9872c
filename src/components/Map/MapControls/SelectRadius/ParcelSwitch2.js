import React, { useEffect, useState } from "react";
import styles from "../MapControls.module.css";
import { minZoom } from "../../MapLayers/Regrid";
import { useDispatch, useSelector } from "react-redux";
import { Switch } from "antd";
const ParcelSwitch2 = () => {
  const map = useSelector((state) => state.Map.map);
  const parcelMode = useSelector((state) => state.Map.parcelMode);
  const [parcelSwitch, setParcelSwitch] = useState(false);

  const dispatch = useDispatch();
  useEffect(() => {
    if (!map) return;

    const zoomEnd = () => {
      const zoom = map.getZoom();
        if (zoom >= minZoom) {
          if (!parcelSwitch) setParcelSwitch(true);
        } else {
          if (parcelSwitch) setParcelSwitch(false);
          if (parcelMode) {
            dispatch({
              type: "Map/saveState",
              payload: {
                parcelMode: false,
              },
            });
            map.fire("selectRadius.parcelMode", {
              payload: { parcelMode: false },
            });
          }
        }
    };

    const handleParcelModeSwitch = (e) => {
      const { payload } = e;
      if (parcelMode !== payload.parcelMode) {
        dispatch({
          type: "Map/saveState",
          payload: {
            parcelMode: payload.parcelMode,
          },
        });
      }
    };

    map.on("zoomend", zoomEnd);
    map.on("selectRadius.parcelMode", handleParcelModeSwitch);
    return () => {
      map.off("zoomend", zoomEnd);
      map.off("selectRadius.parcelMode", handleParcelModeSwitch);
    };
  }, [map, parcelSwitch, parcelMode]);
  
  if (!parcelSwitch) return null;
  
  return (
    <div
      id="parcelSwitch2"
      key="parcel switch 2 wrapper"
      className={styles.parcelSwitch2Wrapper}
      
    >
      <Switch
        size="small"
        checked={parcelMode}
        onChange={(checked) => {
          dispatch({
            type: "Map/saveState",
            payload: {
              parcelMode: checked,
            },
          });
          map.fire("selectRadius.parcelMode", {
            payload: { parcelMode: checked },
          });
        }}
      />
      <span className="pl-2">Parcel</span>
    </div>
  );
};

export default ParcelSwitch2;
