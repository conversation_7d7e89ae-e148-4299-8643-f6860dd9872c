.selectStyle {
  /* position: absolute;
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%); */
  padding: 0px 4px 0px 16px;
  border-radius: 16px;
  z-index: 102;
  background: rgba(255, 255, 255, 0.9);
  text-align: center;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  display: flex;
  flex-direction: row;
}

.selectRadiusHideClearBtns {
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  padding: 0;
  cursor: pointer;
}

.selectRadiusHideClearBtns:hover {
  color: var(--antd-active-blue);
}

.subjectPropertyMarker {
  z-index: 101;
  fill: #e200ff;
  stroke: #fff;
  stroke-width: 12px;
  filter: drop-shadow(0px 0px 2px rgb(0 0 0 / 0.4));
}

.subjectPropertyMarker:hover {
  fill: orange;
}

.subjectPropertyMarkerLennar {
  z-index: 101;
  fill: #006d2c;
  stroke: #fff;
  stroke-width: 12px;
  filter: drop-shadow(0px 0px 2px rgb(0 0 0 / 0.4));
}

.subjectPropertyMarkerLennar:hover {
  fill: #00441b;
}