import React from "react";
import { useSelector } from "react-redux";
import { toPng } from "html-to-image";
import { MdFileDownload } from "@react-icons/all-files/md/MdFileDownload";
import styles from "./mapToImageDownload.module.css";

function MapToImageDownload() {
  const map = useSelector((state) => state.Map.map);

  const downloadBtnHandler = async () => {
    if (!map) return;
    const screenshotData = await takeMapBoxScreenshot(map);
    const download = document.createElement("a");
    download.href = screenshotData;
    download.download = "CMA Map Screenshot";
    download.click();
  };

  return (
    <div id="MapToImageDownload" className={styles.buttonContainer}>
      <button onClick={downloadBtnHandler} className={styles.button}>
        <MdFileDownload size={20} className={styles.icon} />
      </button>
    </div>
  );
}

const takeMapBoxScreenshot = (map) => {
  // https://github.com/mapbox/mapbox-gl-js/issues/2766
  return new Promise(function (resolve, reject) {
    map.once("render", async function () {
      const zoomBtns = document.querySelector(`.mapboxgl-ctrl-top-right`);
      zoomBtns.style.visibility = "hidden";

      const screenshotData = await toPng(
        document.querySelector(`.mapboxgl-map`)
      );
      // const screenshotData = map.getCanvas().toDataURL();

      zoomBtns.style.visibility = "visible";
      resolve(screenshotData);
    });
    /* trigger render */
    map.setBearing(map.getBearing());
  });
};

export default MapToImageDownload;
