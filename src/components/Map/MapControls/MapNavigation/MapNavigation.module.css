.btnPos {
  /* position: absolute; */
  left: 10px;
  /* bottom: 140px; */
  top: 10px;
  z-index: 200;
}

.menuContainer {
  /* height: 250px; */
  overflow-y: auto;
  height: 100%;
}

.menuRow {
  cursor: pointer;
  height: 42px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 0 0 20px;
  color: #1890ff;
}

.menuRow:hover {
  /* background-color: #46a3fb; */
  background-color: #e5e5e5;
}

.menuRowWithoutMLS {
  composes: menuRow;
  color: #333;
}

.mapNav {
  background-color: white;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  overflow: hidden;
  border-radius: 5px;
  width: 200px;
}

.mapNavContainer {
  background-color: white;
  width: 200px;
  padding: 9px 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.mapNavExpander {
  background: none;
  border: none;
  padding: 0;
  font-size: 20px;
  width: 20px;
  height: 20px;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: #007aff;
}

.mapNavMenuContainer {
  height: 0px;
  width: 100%;
  transition: height 150ms ease-out;
}

.mapNavMenuOpen {
  height: 200px;
  border-top: 1px solid lightgray;
}

.disabledRow {
  cursor: not-allowed;
  height: 42px;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 5px;
  padding: 0 0 0 20px;
  background-color: #e6e6e6;
  color: #9d9d9d;
}
