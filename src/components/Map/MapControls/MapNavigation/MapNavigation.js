import { useState, useEffect, useCallback } from "react";
import { connect } from "react-redux";
import { BiNavigation } from "@react-icons/all-files/bi/BiNavigation";
import { IoClose } from "@react-icons/all-files/io5/IoClose";
import { HiOutlineChevronDown } from "@react-icons/all-files/hi/HiOutlineChevronDown";
import { HiOutlineChevronUp } from "@react-icons/all-files/hi/HiOutlineChevronUp";
import styles from "./MapNavigation.module.css";
import MapNavigationMenu from "./MapNavigationMenu";

const controllerName = "MapNavigation";

const MapNavigation = connect(({ Map }) => ({
  openedMapControllerOption: Map.openedMapControllerOption,
}))(function (props) {
  const [menuOpen, setMenuOpen] = useState(false);

  const expanderBtnHandler = () => {
    setMenuOpen(!menuOpen);
  };

  const closeMenu = () => {
    if (menuOpen) {
      setMenuOpen(false);
    }
  };

  return (
    <div id="MapNavigation" className={styles.btnPos}>
      <div
        className={`${styles.mapNav} ${menuOpen ? styles.mapNavOpen : ""}`}
        onMouseLeave={closeMenu}
      >
        <div className={styles.mapNavContainer}>
          <span>Metro Navigation</span>
          <button
            className={styles.mapNavExpander}
            onClick={expanderBtnHandler}
          >
            {menuOpen ? <HiOutlineChevronUp /> : <HiOutlineChevronDown />}
          </button>
        </div>
        <div
          className={`${styles.mapNavMenuContainer} ${
            menuOpen ? styles.mapNavMenuOpen : ""
          }`}
        >
          <MapNavigationMenu />
        </div>
      </div>
    </div>
  );
});

export default MapNavigation;
