const cbsa_bbox = [
  {
    name: "<PERSON>",
    bbox: [-98.068545, 32.052085, -95.858638, 33.434064],
  },
  {
    name: "<PERSON>",
    bbox: [-96.621998, 28.825049, -94.353378, 30.630279],
  },
  {
    name: "<PERSON>",
    bbox: [-98.297578, 29.630709, -97.024462, 30.906179],
  },
  {
    name: "San Antonio",
    bbox: [-99.603323, 28.612659, -97.630974, 30.138955],
  },
  {
    name: "Atlanta",
    bbox: [-85.386582, 32.844591, -83.269196, 34.617918],
  },
  {
    name: "<PERSON>",
    bbox: [-81.537862, 34.457791, -80.182014, 36.057878],
  },
  {
    name: "Nashville",
    bbox: [-87.743778, 35.408176, -85.778681, 36.652495],
  },
  {
    name: "Phoenix",
    bbox: [-113.334436, 32.50121, -110.448355, 34.048203],
  },
  {
    name: "Jacksonville",
    bbox: [-82.459544, 29.622428, -81.212941, 30.830121],
  },
  {
    name: "Tampa-Orlando",
    bbox: [-82.852376, 27.5762, -80.86146, 29.276992],
  },
  {
    name: "Minneapolis",
    bbox: [-94.629464, 44.195837, -92.134796, 46.247159],
  },
  {
    name: "Detroit",
    bbox: [-84.1582, 42.033399, -82.418396, 43.327081],
  },
  {
    name: "Miami",
    bbox: [-80.886232,25.13748,-80.031101,26.970983],
  },
  {
    name: "Bakersfield",
    bbox: [-120.194153,34.789181,-117.616196,35.798402],
  },
  {
    name: "St. Louis",
    bbox: [-91.833957, 37.9643, -89.098843, 39.727857],
  },
  {
    name: "Cincinnati",
    bbox: [-84.820159, 38.403423, -83.627386, 39.569531],
  },
  {
    name: "Indianapolis",
    bbox: [-87.014727, 39.048355, -85.575491, 40.379802],
  },
  {
    name: "Oklahoma City",
    bbox: [-98.313616, 34.681149, -96.619096, 36.164777],
  },
  {
    name: "Columbia",
    bbox: [-82.010272, 33.466014, -80.284088, 34.614778],
  },
  {
    name: "Baltimore",
    bbox: [-77.311651, 38.712718, -75.747675, 39.721493],
  },
  {
    name: "Las Vegas",
    bbox: [-115.896925, 35.002086, -114.045766, 36.853474],
  },
  {
    name: "Raleigh-Durham-Cary",
    bbox: [-79.555902, 35.256379, -78.006617, 36.542154],
  },
  {
    name: "Cleveland",
    bbox: [-82.348037,40.98819,-81.002205,41.85338],
  },
  {
    name: "Tulsa",
    bbox: [-97.064885,35.376633,-95.207921,36.999399],
  },
  {
    name: "Columbus",
    bbox: [-83.653217,39.361606,-82.02422,40.712735],
  },
  {
    name: "Kansas City",
    bbox: [-95.188008,38.025952,-93.477231,39.788978],
  },
  {
    name: "Akron",
    bbox: [-81.688493,40.90651,-81.001691,41.351326],
  },
  {
    name: "Dayton",
    bbox: [-84.485707,39.550289,-83.646678,40.200082],
  },
  {
    name: "Pittsburgh",
    bbox: [-80.51928,39.720151,-78.97384,41.173469],
  }
];

cbsa_bbox.sort((a, b) => {
  return a.name.localeCompare(b.name);
});

export default cbsa_bbox;
