import { useSelector } from "react-redux";
import styles from "./MapNavigation.module.css";
import cbsa_bbox from "./cbsa_bbox";
import { Toolt<PERSON>, Modal } from "antd";
import { IoInformationCircle } from "@react-icons/all-files/io5/IoInformationCircle";

let access = {
  Avanta: [
    "Dallas",
    "Austin",
    "Houston",
    "San Antonio",
    "Charlotte",
    "Tampa-Orlando",
    "Atlanta",
    "Jacksonville",
    "Miami"
  ],
  BridgeAdmin: ["Dallas", "Houston", "Charlotte", "Atlanta", "Tampa-Orlando", "Jacksonville", "Indianapolis", "Columbia"],
  BridgeFull: ["Dallas", "Houston", "Charlotte", "Atlanta", "Tampa-Orlando", "Jacksonville", "Indianapolis", "Columbia"],
  BridgeWithoutMLS: ["Minneapolis", "Oklahoma City", "Baltimore", "Las Vegas", "Raleigh-Durham-Cary", "Phoenix"],
  BridgeTower: [
    "Dallas",
    "Austin",
    "Houston",
    "San Antonio",
    "Charlotte",
    "Tampa-Orlando",
    "Atlanta",
    "Nashville",
    "Jacksonville",
    "Raleigh-Durham-Cary",
  ],
  CommonGroundCapital: ["Dallas"],
  // "demo-CMA-DFW-only": ["Dallas"],
  // "demo-users": [
  //   "Dallas",
  //   "Austin",
  //   "Houston",
  //   "San Antonio",
  //   "Charlotte",
  //   "Tampa-Orlando",
  //   "Atlanta",
  //   "Nashville",
  //   "Phoenix",
  //   "Jacksonville",
  //   "Minneapolis",
  //   "Detroit",
  //   "Bakersfield",
  //   "Miami",
  //   "Indianapolis",
  //   "Columbia",
  //   "Raleigh-Durham-Cary",
  // ],
  // dev: [
  //   "Dallas",
  //   "Austin",
  //   "Houston",
  //   "San Antonio",
  //   "Charlotte",
  //   "Tampa-Orlando",
  //   "Atlanta",
  //   "Nashville",
  //   "Phoenix",
  //   "Jacksonville",
  //   "Minneapolis",
  //   "Detroit",
  //   "Bakersfield",
  //   "Miami",
  //   "Indianapolis",
  //   "Columbia",
  //   "Raleigh-Durham-Cary",
  // ],
  MarketplaceHomes: [
    "Dallas",
    "Austin",
    "Houston",
    "San Antonio",
    "Charlotte",
    "Tampa-Orlando",
    "Atlanta",
    "Nashville",
    "Jacksonville",
    "Detroit",
  ],
  Nhimble: ["Atlanta"],
  TrellyGroup: ["Dallas", "Houston"],
  HON: ["Dallas", "Houston", "Tampa-Orlando", "Jacksonville", "Minneapolis"],
  AvenueOne: ["Dallas"],
  Transwestern: ["Dallas"],
  // VentureREI has access to all metros
  // VentureREI: [
  //   "Dallas",
  //   "Austin",
  //   "Houston",
  //   "San Antonio",
  //   "Charlotte",
  //   "Tampa-Orlando",
  //   "Atlanta",
  //   "Nashville",
  //   "Phoenix",
  //   "Jacksonville",
  //   "Minneapolis",
  //   "Detroit",
  //   "Bakersfield",
  //   "Miami",
  //   "Indianapolis",
  //   "Columbia",
  //   "Raleigh-Durham-Cary",
  // ],
  // without this setting
  // SVN will have access to all metros
  // including metros without MLS
  // SVN: [
  //   "Dallas",
  //   "Austin",
  //   "Houston",
  //   "San Antonio",
  //   "Charlotte",
  //   "Tampa-Orlando",
  //   "Atlanta",
  //   "Nashville",
  //   "Phoenix",
  //   "Jacksonville",
  //   "Minneapolis",
  //   "Detroit",
  //   "Bakersfield",
  //   "Miami"
  // ],
  Darwin: [
    "Dallas",
    "Austin",
    "Houston",
    "San Antonio",
    "Charlotte",
    "Tampa-Orlando",
    "Atlanta",
    "Nashville",
    "Phoenix",
    "Jacksonville",
    "Minneapolis",
    "Detroit",
    "Bakersfield",
    "Miami"
  ],
  BigStateBuyers: ["Houston"],
  FHFS: ["Tampa-Orlando"],
  Truehold: [
    "St. Louis",
    "Cincinnati",
    "Cleveland",
    "Oklahoma City",
    "Tulsa",
    "Indianapolis",
    "Columbus",
    "Kansas City",
    "Akron",
    "Dayton",
    "Pittsburgh"
  ],
  "UrbanRowGroup": [
    "Houston"
  ],
  "LendingOne": [
    "Dallas"
  ]
};

function MapNavigationMenu() {
  const map = useSelector((state) => state.Map.map);
  const mapNavigation = useSelector((state) => state.Configure.mapNavigation);
  const user = useSelector((state) => state.Configure.user);
  const eventCoordinates = useSelector((state) => state.Map.eventCoordinates);

  const moveToCBSA = (bbox) => {
    if (eventCoordinates.length > 0) {
      Modal.confirm({
        title: "Do you want to clear comps?",
        okText: "Yes",
        cancelText: "No",
        getContainer: "#Map",
        centered: true,
        maskStyle: { position: "absolute" },
        onOk: () => {
          map.fire("selectRadius.navigationClear");
          map.fitBounds(bbox, { padding: 20 });
        },
        onCancel: () => {
          map.fitBounds(bbox, { padding: 20 });
        },
      });
    } else {
      map.fitBounds(bbox, { padding: 20 });
    }
  };

  let menu = [];

  if (mapNavigation.enabled && user.userGroup.length > 0) {
    const userName = user.userGroup[0];

    // if userName is "demo-CMA-DFW-only", add its metros to the access list from Cognito user profile
    if (userName === "demo-CMA-DFW-only") {
      access[userName] = user.metrosAllowedForCurrentTrialUser;
    }

    if (access[userName]) {
      const allowed = cbsa_bbox.map((cbsa) => {
        if (access[userName].includes(cbsa.name)) {
          return (
            <div
              key={cbsa.name}
              className={styles.menuRow}
              onClick={() => moveToCBSA(cbsa.bbox)}
            >
              <span>{cbsa.name}</span>
            </div>
          );
        }
      });

      const notAllowed = cbsa_bbox.map((cbsa) => {
        if (
          (!['BridgeAdmin', 'BridgeFull'].includes(userName) && !access[userName].includes(cbsa.name)) ||
          (['BridgeAdmin', 'BridgeFull'].includes(userName) && !access[userName].includes(cbsa.name) && !access['BridgeWithoutMLS'].includes(cbsa.name))
        ) {
          return (
            <div
              key={cbsa.name}
              className={styles.disabledRow}
              // onClick={() => moveToCBSA(cbsa.bbox)}
            >
              <span>{cbsa.name}</span>
              <Tooltip title="Not subscribed">
                <IoInformationCircle />
              </Tooltip>
            </div>
          );
        }
      });

      let allowedWithoutMLS;
      if (['BridgeAdmin', 'BridgeFull'].includes(userName)) {
        allowedWithoutMLS = cbsa_bbox.map((cbsa) => {
          if (access['BridgeWithoutMLS'].includes(cbsa.name)) {
            return (
              <div
                key={cbsa.name}
                className={styles.menuRowWithoutMLS}
                onClick={() => moveToCBSA(cbsa.bbox)}
              >
                <span>{cbsa.name}</span>
              </div>
            );
          }
        });
        menu.push(...allowed, ...allowedWithoutMLS, ...notAllowed);
      } else {
        menu.push(...allowed, ...notAllowed);
      }
    } else {
      menu = cbsa_bbox.map((cbsa, idx) => {
        return (
          <div
            key={idx}
            className={styles.menuRow}
            onClick={() => moveToCBSA(cbsa.bbox)}
          >
            <span>{cbsa.name}</span>
          </div>
        );
      });
    }
  } else {
    menu = cbsa_bbox.map((cbsa, idx) => {
      return (
        <div
          key={idx}
          className={styles.menuRow}
          onClick={() => moveToCBSA(cbsa.bbox)}
        >
          <span>{cbsa.name}</span>
        </div>
      );
    });
  }

  return (
    <div className={styles.menuContainer}>
      {menu}
      {/* <Modal
        open={true}
        getContainer="#Map"
        centered={true}
        maskStyle={{ position: "absolute" }}
      >
        <span>Do you want to clear comps</span>
      </Modal> */}
    </div>
  );
}

export default MapNavigationMenu;
