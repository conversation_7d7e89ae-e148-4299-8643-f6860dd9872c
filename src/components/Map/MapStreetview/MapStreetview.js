import { useSelector } from "react-redux";

function MapStreetview() {
  const eventCoordinates = useSelector((state) => state.Map.eventCoordinates);

  let src = ``;
  if (eventCoordinates.length > 0) {
    src = `https://www.google.com/maps/embed/v1/streetview?key=AIzaSyB72GqI_rbe0EOYu27FGU5naCDrqMZBkzs&location=${eventCoordinates[1]},${eventCoordinates[0]}`;
  }

  return (
    <iframe
      id="streetview"
      src={src}
      style={{ border: 0, display: "none" }}
      width="100%"
      height="100%"
      allowFullScreen=""
      loading="lazy"
      referrerPolicy="no-referrer-when-downgrade"
    ></iframe>
  );
}

export default MapStreetview;
