import React from "react";
import { useSelector, useDispatch } from "react-redux";
import SelectRadius from "../../MapControls/SelectRadius/SelectRadius";
import { DistancePopup } from "../../MapControls/MapRuler/MapRuler";

function BottomMiddle() {
  const selectRadius = useSelector((state) => state.Configure.selectRadius);
  const mapRuler = useSelector((state) => state.Configure.mapRuler);
  return (
    <div
      style={{
        position: "absolute",
        bottom: 0,
        left: "50%",
        transform: "translateX(-50%)",
        // height: "0px",
        // width: "0px",
        overflow: "visible",
        display: "flex",
        flexDirection: "column",
        gap: "10px",
        alignItems: "center",
        paddingBottom: "10px",
        zIndex: 200,
      }}
    >
      {mapRuler && <DistancePopup />}
      {selectRadius.enabled && <SelectRadius  />}
    </div>
  );
}

export default BottomMiddle;
