import { connect } from "react-redux";
import styles from "./floodZoneLegend.module.css";
import { IoInformationCircle } from "@react-icons/all-files/io5/IoInformationCircle";
import { Switch } from "antd";

import {
  EFFECTIVE_COLOR_LOMR,
  INCORPORATED_COLOR_LOMR,
  SUPERSEDED_COLOR_LOMR,
  OTHER_COLOR_LOMR,
} from "../../MapLayers/FloodZoneLayer";
import { geojsonTemplate } from "../../../../constants";

const FloodZoneLegend = connect(({ Map }) => ({
  map: Map.map,
  currentMapLayerOptions: Map.currentMapLayerOptions,
  enableFloodZoneLOMRLayer: Map.enableFloodZoneLOMRLayer,
}))(function (props) {
  const legendItems = [
    {
      name: "High Risk",
      types: "A, AE, AH, AO, A99",
      src: "https://sl-img-client.s3.amazonaws.com/map/legend-flood-pattern-6.svg",
    },
    {
      name: "Moderate to Low Risk",
      types: "X",
      src: "https://sl-img-client.s3.amazonaws.com/map/legend-flood-pattern-2.svg",
    },
    {
      name: "High Risk - Coastal",
      types: "V, VE",
      src: "https://sl-img-client.s3.amazonaws.com/map/legend-flood-pattern-5.svg",
    },
    {
      name: "Undetermined Risk",
      types: "D",
      src: "https://sl-img-client.s3.amazonaws.com/map/legend-flood-pattern-1.svg",
    },
    {
      name: "Others",
      src: "https://sl-img-client.s3.amazonaws.com/map/legend-flood-pattern-4.svg",
    },
  ];

  const legendItemsLOMR = [
    {
      name: "Effective",
      color: EFFECTIVE_COLOR_LOMR,
    },
    {
      name: "Incorporated",
      color: INCORPORATED_COLOR_LOMR,
    },
    {
      name: "Superseded",
      color: SUPERSEDED_COLOR_LOMR,
    },
    {
      name: "Others",
      color: OTHER_COLOR_LOMR,
    },
  ];

  const onFloodZoneLOMRSwitch = (checked) => {
    props.dispatch({
      type: "Map/saveState",
      payload: { enableFloodZoneLOMRLayer: checked },
    });
  };

  if (props.currentMapLayerOptions.includes("flood zone")) {
    return (
      <div
        id="FloodZoneLegend"
        className="flex flex-col bg-white box-content rounded-[5px] py-[10px] px-0 min-w-[225px]"
      >
        <div className="flex flex-row justify-center">
          <span className="font-bold">Flood Zone Types</span>
          <a
            className="text-black"
            href="https://spat-batchfiles.s3.amazonaws.com/fema-flood-zone-definitions.pdf"
            target="_blank"
          >
            <IoInformationCircle size={18} />
          </a>
        </div>
        <div className="flex flex-col">
          {legendItems.map((item, index) => (
            <div
              key={index}
              className="flex flex-row items-center gap-[5px] py-[2.5px] px-[15px]"
            >
              <div className="w-[25px] h-[25px] overflow-hidden">
                <img
                  className="object-cover w-[100px] h-[100px]"
                  src={item.src}
                />
              </div>
              <div className="flex flex-col">
                <span>{item.name}</span>
                {item.types && (
                  <span className="font-[12px]">{item.types}</span>
                )}
              </div>
            </div>
          ))}
        </div>
        <div className="flex flex-row justify-center relative">
          <span className="font-bold">LOMR Status</span>
          <a
            className="text-black"
            href="https://www.fema.gov/glossary/letter-map-revision-lomr"
            target="_blank"
          >
            <IoInformationCircle size={18} />
          </a>
          <Switch
            checked={props.enableFloodZoneLOMRLayer}
            onChange={onFloodZoneLOMRSwitch}
            size="small"
            style={{ position: "absolute", right: "15px" }}
          />
        </div>
        <div className="flex flex-col">
          {legendItemsLOMR.map((item, index) => (
            <div
              key={index}
              className="flex flex-row items-center gap-[5px] py-[2.5px] px-[15px]"
            >
              <div
                className="w-[25px] h-[25px] overflow-hidden"
                style={{ backgroundColor: item.color }}
              />
              <div className="flex flex-col">
                <span>{item.name}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  } else {
    return null;
  }
});

export default FloodZoneLegend;
