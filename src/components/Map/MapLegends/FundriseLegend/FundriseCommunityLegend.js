import { <PERSON>, Button, Switch } from "antd";
import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";

// Custom event name for communicating with external applications
const COMMUNITY_SELECTED_EVENT = "FUNDRISE_COMMUNITY_SELECTED";

const FundriseCommunitiesLegend = () => {
  const [communities, setCommunities] = useState([]);
  const [treeData, setTreeData] = useState([]);
  const [displayedTreeData, setDisplayedTreeData] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [showAll, setShowAll] = useState(false);
  const [expandAll, setExpandAll] = useState(false);
  
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const fundriseCommunities = useSelector((state) => state.Map.fundriseCommunities || []);
  const dispatch = useDispatch();

  // Extract bed and bath count from floorplanType (e.g., "3x2" means 3 bedrooms, 2 bathrooms)
  const extractBedBath = (floorplanType) => {
    if (!floorplanType) return { bed: '?', bath: '?' };
    const parts = floorplanType.split('x');
    return {
      bed: parts[0] || '?',
      bath: parts[1] || '?'
    };
  };

  // Process the raw data into tree structure
  const processData = (data) => {
    if (!data || !data.length) return [];

    // Group by community
    const communitiesMap = data.reduce((acc, item) => {
      if (!acc[item.community]) {
        acc[item.community] = {
          community: item.community,
          city: item.city,
          state: item.state,
          msa: item.msa,
          floorplans: [],
          geom: item.geom,
          // Save original data to pass to external applications
          originalData: item
        };
      }
      
      const { bed, bath } = extractBedBath(item.floorplanType);
      
      acc[item.community].floorplans.push({
        floorplanName: item.floorplanName,
        floorplanType: item.floorplanType,
        livingAreaSqft: item.livingAreaSqft,
        bedrooms: bed,
        bathrooms: bath,
        geom: item.geom,
        // Save original data to pass to external applications
        originalData: item
      });
      
      return acc;
    }, {});

    // Convert to array and sort
    return Object.values(communitiesMap)
      .sort((a, b) => a.community.localeCompare(b.community))
      .map((community, idx) => {
        // Sort floorplans by size
        const floorplans = community.floorplans.sort((a, b) => 
          (a.livingAreaSqft || 0) - (b.livingAreaSqft || 0)
        );
        
        return {
          title: (
            <div className="flex flex-row items-center">
              <strong>{community.community}</strong>
              <span className="text-gray-500 text-xs ml-1">
                {community.city}, {community.state}
              </span>
            </div>
          ),
          key: `community-${idx}`,
          meta: {
            community: community,
            isLeaf: false
          },
          children: floorplans.map((floorplan, fpIdx) => ({
            title: (
              <div className="pl-2 flex flex-row items-center gap-2">
                <span className="font-medium">{floorplan.floorplanName}</span>
                <div className="flex gap-1 text-xs text-gray-600">
                  <span>{floorplan.bedrooms} bed</span>
                  <span>{floorplan.bathrooms} bath</span>
                  <span>{Math.round(floorplan.livingAreaSqft || 0)} sqft</span>
                </div>
              </div>
            ),
            key: `community-${idx}-floorplan-${fpIdx}`,
            meta: {
              floorplan: floorplan,
              community: community,
              isLeaf: true
            }
          }))
        };
      });
  };

  // Handle data update
  useEffect(() => {
    if (!currentMapLayerOptions.includes("fundrise owned community")) return;
    
    setCommunities(fundriseCommunities);
    const processed = processData(fundriseCommunities);
    setTreeData(processed);
    
    // Preserve showAll state when data refreshes
    if (!showAll && processed.length > 0) {
      setDisplayedTreeData(processed.slice(0, 5));
      setShowAll(processed.length <= 5);
    } else {
      setDisplayedTreeData(processed);
    }
    
    // Preserve expanded state when data refreshes
    if (expandAll && processed.length > 0) {
      const newExpandedKeys = processed.map((_, idx) => `community-${idx}`);
      setExpandedKeys(newExpandedKeys);
    }
    // Don't reset expandAll state here
  }, [currentMapLayerOptions, fundriseCommunities, showAll, expandAll]);

  // Effect to handle expand all toggle
  useEffect(() => {
    if (expandAll) {
      // Get all community keys to expand them
      const allCommunityKeys = displayedTreeData.map((item, idx) => `community-${idx}`);
      setExpandedKeys(allCommunityKeys);
    } else {
      // Only clear keys if we're explicitly toggling the switch to off
      // This prevents auto-collapse when map moves
      if (expandedKeys.length > 0 && expandedKeys.length === displayedTreeData.length) {
        setExpandedKeys([]);
      }
    }
  }, [expandAll, displayedTreeData]);

  // Method to dispatch the selected community/floorplan to external applications
  const sendToExternalApp = (data) => {
    try {
      // 1. Save to Redux store for other components in this app
    //   dispatch({
    //     type: "Map/setSelectedFundriseCommunity",
    //     payload: data
    //   });
      
      // 2. Dispatch a custom event for external applications to listen for
      const event = new CustomEvent(COMMUNITY_SELECTED_EVENT, { 
        detail: data,
        bubbles: true 
      });
      document.dispatchEvent(event);
      
      
      console.log("Community data sent to external applications:", data);
    } catch (error) {
      console.error("Error sending community data:", error);
    }
  };

  const onSelect = (selectedKeys, info) => {
    if (!map || !info.node || !info.node.meta) return;
    
    const node = info.node.meta;
    
    // Determine if it's a community or floorplan
    if (node.isLeaf) {
      // It's a floorplan
      const floorplan = node.floorplan;
      const community = node.community;
      
      // Send floorplan data to external app
      sendToExternalApp({
        type: 'floorplan',
        community: community.community,
        city: community.city,
        state: community.state,
        msa: community.msa,
        floorplanName: floorplan.floorplanName,
        floorplanType: floorplan.floorplanType,
        bedrooms: floorplan.bedrooms,
        bathrooms: floorplan.bathrooms,
        livingAreaSqft: floorplan.livingAreaSqft,
        coordinates: community.geom.coordinates,
        // Send the original data for complete access
        originalData: floorplan.originalData
      });
    } else {
      // It's a community
      const community = node.community;
      
      // Send community data to external app
      sendToExternalApp({
        type: 'community',
        community: community.community,
        city: community.city,
        state: community.state,
        msa: community.msa,
        floorplans: community.floorplans.map(fp => ({
          floorplanName: fp.floorplanName,
          floorplanType: fp.floorplanType,
          bedrooms: fp.bedrooms,
          bathrooms: fp.bathrooms,
          livingAreaSqft: fp.livingAreaSqft
        })),
        coordinates: community.geom.coordinates,
        // Send the original data for complete access
        originalData: community.originalData
      });
    }
    
    // Fly to the community location
    if (node.community && node.community.geom && node.community.geom.coordinates) {
      map.flyTo({
        center: node.community.geom.coordinates,
      });
    }
  };
  
  const onExpand = (expandedKeys) => {
    setExpandedKeys(expandedKeys);
    // Check if all communities are expanded or not
    const allCommunityKeys = displayedTreeData.map((item, idx) => `community-${idx}`);
    const allExpanded = allCommunityKeys.every(key => expandedKeys.includes(key));
    // Only update expandAll if all are expanded or none are expanded
    // This prevents the switch from toggling during individual expand/collapse actions
    if (allExpanded || expandedKeys.length === 0) {
      setExpandAll(allExpanded);
    }
  };

  const handleExpandToggle = (checked) => {
    setExpandAll(checked);
  };

  const handleShowMore = () => {
    setDisplayedTreeData(treeData);
    setShowAll(true);
  };

  if (!currentMapLayerOptions.includes("fundrise owned community")) return null;

  return (
    <div className="bg-white p-[10px] min-w-[300px] max-w-[400px]">
      <div className="text-center mb-2">
        <strong>Fundrise Communities</strong>
      </div>
      <div className="text-center mb-2">
        <p>Click to use the data in comping</p>
      </div>
      
      <div className="flex justify-between items-center mb-2 px-1">
        <span className="text-sm">Expand All</span>
        <Switch 
          size="small" 
          checked={expandAll} 
          onChange={handleExpandToggle} 
        />
      </div>
      
      <div className="h-[150px] overflow-y-auto mb-2">
        {displayedTreeData.length > 0 ? (
          <Tree
            checkable={false}
            onExpand={onExpand}
            expandedKeys={expandedKeys}
            autoExpandParent={false}
            onSelect={onSelect}
            treeData={displayedTreeData}
          />
        ) : (
          <div className="text-center text-gray-500">
            No communities found in this area
          </div>
        )}
      </div>
      
      {!showAll && treeData.length > 5 && (
        <div className="text-center">
          <Button 
            type="primary" 
            onClick={handleShowMore}
            className="w-full"
          >
            Show More ({treeData.length - 5} more)
          </Button>
        </div>
      )}
    </div>
  );
};

// Export a way for external applications to listen for community selection
export const listenForCommunitySelection = (callback) => {
  const handler = (event) => {
    callback(event.detail);
  };
  
  document.addEventListener(COMMUNITY_SELECTED_EVENT, handler);
  
  // Return cleanup function
  return () => {
    document.removeEventListener(COMMUNITY_SELECTED_EVENT, handler);
  };
};

export default FundriseCommunitiesLegend;