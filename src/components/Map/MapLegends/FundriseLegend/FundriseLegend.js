import { Tree } from "antd";
import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import blueMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-blue.png";
import grayMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-gray.png";
import greenMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-green.png";
import orangeMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-orange.png";
import pinkMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-pink.png";
import purpleMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-purple.png";
import redMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-red.png";
import yellowMarker from "../../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-yellow.png";
import fundriseCW from "../../../../assets/images/mapbox/controls/fundrise-cw.png";
import fundriseProgress from "../../../../assets/images/mapbox/controls/fundrise-progress.png";

const icons = {
  "Fundrise CW": fundriseCW,
  "Fundrise Progress": fundriseProgress,
  AMH: yellowMarker,
  "DHI Residential": blueMarker,
  "Invitation Homes": greenMarker,
  Miscellaneous: purpleMarker,
  "NexMetro Communities": pinkMarker,
  "Quinn Residences": redMarker,
  "Tricon Residential": grayMarker,
  "Wan Bridge": orangeMarker,
};

const reducer = (data) => {
  const features = data.features;
  if (features && features.length > 0) {
    const branches = [
      ...features
        .reduce((acc, feature) => {
          if (
            feature.properties.group != "Miscellaneous" &&
            !acc.includes(feature.properties.brand)
          )
            acc.push(feature.properties.brand);
          if (
            feature.properties.group === "Miscellaneous" &&
            !acc.includes(feature.properties.group)
          )
            acc.push(feature.properties.group);

          return acc;
        }, [])
        .sort((a, b) => {
          if (a.includes("Fundrise")) return -1;
          if (b.includes("Fundrise")) return 1;
          if (a.includes("Miscellaneous")) return 1;
          if (b.includes("Miscellaneous")) return -1;

          return a.localeCompare(b);
        })
        .map((brand, brandIdx) => {
          return {
            title: (
              <div className="flex flex-row gap-1 items-center">
                <div className="w-[20px] h-[20px] flex flex-row justify-center items-center">
                  <img src={icons[brand]} width={"auto"} height={20} />
                </div>
                <strong>{brand}</strong>
              </div>
            ),
            meta: { title: brand },
            // key: `${brandIdx}`,
            key: `${brand}`,
            children: [
              ...features
                .filter((feature) => {
                  if (
                    brand === "Miscellaneous" &&
                    feature.properties.group === brand
                  ) {
                    return true;
                  } else if (
                    brand != "Miscellaneous" &&
                    brand === feature.properties.brand
                  ) {
                    return true;
                  }
                  return false;
                })
                .map((feature, featureIdx) => {
                  return {
                    title: feature.properties.community_name,
                    checkable: false,
                    key: `${brandIdx}-${featureIdx}`,
                    meta: {
                      feature,
                    },
                    // children: [],
                  };
                }),
            ],
          };
        }),
    ];

    const ownedBranches = [];
    const len = Math.min(branches.length, 2);
    for (let i = 0; i < len; i++) {
      console.log("branches[0]: ", branches[0]);
      if (branches[0].meta.title.includes("Fundrise")) {
        ownedBranches.push(branches.shift());
      }
    }

    console.log("ownedBranches: ", ownedBranches);
    console.log("branches: ", branches);

    return {
      owned: ownedBranches,
      competitor: branches,
    };
  }
  return {
    owned: [],
    competitor: [],
  };
};

const checkedKeysHistory = {};

const FundriseLegend = () => {
  const fundrisePropertyGeoJSON = useSelector(
    (state) => state.Map.fundrisePropertyGeoJSON
  );
  const map = useSelector((state) => state.Map.map);
  const fundrisePropertiesLoading = useSelector(
    (state) => state.Map.fundrisePropertiesLoading
  );
  const dispatch = useDispatch();

  const [treeData, setTreeData] = useState([]);
  const [ownedTreeData, setOwnedTreeData] = useState([]);
  const [competitorTreeData, setCompetitorTreeData] = useState([]);
  const [checkedKeys, setCheckedKeys] = useState([]);
  // const [checkedKeys, setCheckedKeys] = useState([
  //   {
  //     owned: [],
  //     competitor: [],
  //   },
  // ]);

  useEffect(() => {
    const newTreeData = reducer(fundrisePropertyGeoJSON);

    console.log("newTreeData: ", newTreeData);
    setOwnedTreeData(newTreeData.owned);
    setCompetitorTreeData(newTreeData.competitor);

    setCheckedKeys((prevState) => {
      const newCheckedKeys = [];
      const types = Object.keys(newTreeData);
      for (let i = 0; i < types.length; i++) {
        for (let j = 0; j < newTreeData[types[i]].length; j++) {
          const d = newTreeData[types[i]][j];
          if (checkedKeysHistory[d.key] === undefined) {
            checkedKeysHistory[d.key] = true;
            newCheckedKeys.push(d.key);
          } else {
            if (checkedKeysHistory[d.key]) {
              newCheckedKeys.push(d.key);
            }
          }
        }
      }
      return newCheckedKeys;
    });
  }, [fundrisePropertyGeoJSON]);

  const onCheckHandler = (keys, type) => {
    const validOwnedKeys = ownedTreeData.map((d) => d.key);
    const validCompetitorKeys = competitorTreeData.map((d) => d.key);

    let newKeys = [...checkedKeys];
    if (type === "owned") {
      const filterKeys = newKeys.filter((key) => !validOwnedKeys.includes(key));
      newKeys = [...filterKeys, ...keys];
    } else if (type === "competitor") {
      const filterKeys = newKeys.filter(
        (key) => !validCompetitorKeys.includes(key)
      );
      newKeys = [...filterKeys, ...keys];
    }

    setCheckedKeys(newKeys);

    Object.keys(checkedKeysHistory).forEach((key) => {
      if (newKeys.includes(key)) {
        checkedKeysHistory[key] = true;
      } else {
        checkedKeysHistory[key] = false;
      }
    });

    const unChecked = [
      ...ownedTreeData
        .filter((d) => !newKeys.includes(d.key))
        .map((d) => d.meta.title),
      ...competitorTreeData
        .filter((d) => !newKeys.includes(d.key))
        .map((d) => d.meta.title),
    ];

    // console.log("unChecked: ", unChecked);

    dispatch({
      type: "Map/saveState",
      payload: {
        unCheckedFundrise: unChecked,
      },
    });
  };

  const onSelectHandler = (keys, e) => {
    if (e.node && e.node.meta) {
      const feature = e.node.meta.feature;
      moveToProperty(feature);
    }
  };

  const moveToProperty = (feature) => {
    if (!map) return;

    const coordinates = feature.geometry.coordinates;
    map.flyTo({
      center: coordinates,
      zoom: 15,
    });
  };

  // console.log("competitorTreeData: ", competitorTreeData);
  // console.log("checkedKeys: ", checkedKeys);

  return (
    <div className="bg-white p-[10px] min-w-[200px] max-h-[800px] overflow-y-auto">
      <div className="text-center">
        <strong>Fundrise Properties</strong>
      </div>
      <div>
        <Tree
          checkable
          treeData={ownedTreeData}
          checkedKeys={checkedKeys}
          onSelect={(keys, e) => onSelectHandler(keys, e)}
          onCheck={(keys) => onCheckHandler(keys, "owned")}
        />

        {competitorTreeData && competitorTreeData.length > 0 && (
          <div className="h-[2px] w-full my-1 px-8 bg-gray-300 bg-clip-content" />
        )}
        <Tree
          checkable
          treeData={competitorTreeData}
          checkedKeys={checkedKeys}
          onSelect={(keys, e) => onSelectHandler(keys, e)}
          onCheck={(keys) => onCheckHandler(keys, "competitor")}
        />

        {!fundrisePropertiesLoading &&
          ownedTreeData.length === 0 &&
          competitorTreeData.length === 0 && (
            <div className="text-center text-gray-500">
              No Fundrise properties found in this area
            </div>
          )}

        {/* <Tree
          checkable
          treeData={treeData}
          checkedKeys={checkedKeys}
          onSelect={(keys, e) => onSelectHandler(keys, e)}
          onCheck={(keys) => onCheckHandler(keys)}
        /> */}
      </div>
    </div>
  );
};

export default FundriseLegend;
