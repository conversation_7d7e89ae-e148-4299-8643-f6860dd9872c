.legendContainer {
  background-color: rgb(236, 236, 236);
  border-radius: 5px;
  position: absolute;
  bottom: 70px;
  right: 80px;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  z-index: 1000;
}

.legendHeader {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 5px 10px;
  /* border-bottom: 1px solid black; */
  cursor: move;
}

.headerBtnContainer {
  display: flex;
  gap: 5px;
  align-items: center;
}

.headerButton {
  padding: 0;
  background: transparent;
  /* background-color: rgb(199, 198, 198); */
  border: none;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  position: relative;
  cursor: pointer;
}

.headerButton:hover {
  /* background-color: rgb(225, 162, 81); */
  background-color: rgb(199, 198, 198);
}

.headerButton svg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
