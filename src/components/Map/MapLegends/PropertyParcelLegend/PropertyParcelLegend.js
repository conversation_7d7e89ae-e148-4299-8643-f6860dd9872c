import { useState, useEffect, useRef } from "react";
import { connect } from "react-redux";
import { Switch, Checkbox, Radio } from "antd";
import styles from "./propertyParcelLegend.module.css";
import { HiOutlineChevronDown } from "@react-icons/all-files/hi/HiOutlineChevronDown";
import { HiOutlineChevronUp } from "@react-icons/all-files/hi/HiOutlineChevronUp";
import { FaBars } from "@react-icons/all-files/fa/FaBars";
import { FaTimes } from "@react-icons/all-files/fa/FaTimes";
import { capitalize } from "../../../../utils/strings";
import { FaEye } from "@react-icons/all-files/fa/FaEye";
import { FaEyeSlash } from "@react-icons/all-files/fa/FaEyeSlash";
import { showVisibility, hideVisibility } from "../../MapUtility/layer";

const PropertyParcelLegend = connect(({ Map, Configure }) => ({
  map: Map.map,
  parcelTypeShown: Map.parcelTypeShown,
  selectedOwnerParcel: Map.selectedOwnerParcel,
  selectedHOAFeeParcel: Map.selectedHOAFeeParcel,
  serverType: Configure.serverType,
}))(function (props) {
  const [expandedLegend, setExpandedLegend] = useState(true);
  const [expandedMenuLegend, setExpandedMenuLegend] = useState(false);
  const [hideParcel, sethideParcel] = useState(false);
  const [hideParcelBoundary, sethideParcelBoundary] = useState(false);
  const [displayLegend, setDisplayLegend] = useState(false);

  const displayLegendRef = useRef(displayLegend);
  displayLegendRef.current = displayLegend;

  const optionsParcelTypeShown = [
    {
      label: "Subdivision",
      value: "subdivision",
    },
    {
      label: "Owners",
      value: "owners",
    },
    {
      label: "HOA Fees",
      value: "hoa_fees",
    },
  ];

  let legendContentType = [];
  let selectLegendType = [];

  const owners = [
    { name: "Owner Occupied", symbolClass: "ownerOccupiedSymbol" },
    { name: "AH4R", symbolClass: "AH4RSymbol" },
    { name: "Amherst", symbolClass: "AMHERSTSymbol" },
    { name: "Cerberus", symbolClass: "CERBERUSSymbol" },
    { name: "Invitation Homes", symbolClass: "INVITATIONSymbol" },
    { name: "Progress Residential", symbolClass: "PROGRESSSymbol" },
    { name: "Tricon", symbolClass: "TRICONSymbol" },
    { name: "Other Funds", symbolClass: "OTHERSSymbol" },
    { name: "Mom & Pop", symbolClass: "MOMANDPOPSymbol" },
  ];

  const hoaFees = [
    { name: "$0", symbolClass: "symbolFeeRange1" },
    { name: "$1-200", symbolClass: "symbolFeeRange2" },
    { name: "$201-600", symbolClass: "symbolFeeRange3" },
    { name: "$601-1000", symbolClass: "symbolFeeRange4" },
    { name: "$1001+", symbolClass: "symbolFeeRange5" },
  ];

  useEffect(() => {
    if (!props.map) return;
    const zoomLevelToParcel = 14.5;

    const zoomEnd = () => {
      const currentZoomLevel = props.map.getZoom();

      if (currentZoomLevel >= zoomLevelToParcel) {
        if (!displayLegendRef.current) {
          setDisplayLegend(true);
        }
      } else {
        if (displayLegendRef.current) {
          setDisplayLegend(false);
        }
      }
    };

    props.map.on("zoomend", zoomEnd);

    return () => {
      props.map.off("zoomend", zoomEnd);
    };
  }, [props.map]);

  const toggleLegendExpand = () => {
    if (expandedMenuLegend) {
      setExpandedMenuLegend(false);
    }
    setExpandedLegend(!expandedLegend);
  };

  const headerSwitchChange = (e) => {
    if (hideParcel) return;
    props.dispatch({
      type: "Map/saveState",
      payload: {
        parcelTypeShown: e.currentTarget.getAttribute("value"),
      },
    });
    setExpandedLegend(true);
    setExpandedMenuLegend(false);
  };

  const legendSelectChange = (e) => {
    let payload = {};

    if (e.target.checked) {
      if (e.target.name === "owners") {
        payload.selectedOwnerParcel = [
          ...props.selectedOwnerParcel,
          e.target.value,
        ];
      } else if (e.target.name === "hoa_fees") {
        payload.selectedHOAFeeParcel = [
          ...props.selectedHOAFeeParcel,
          e.target.value,
        ];
      }

      props.dispatch({
        type: "Map/saveState",
        payload: payload,
      });
    } else {
      if (e.target.name === "owners") {
        payload.selectedOwnerParcel = props.selectedOwnerParcel.filter(
          (parcelType) => parcelType !== e.target.value
        );
      } else if (e.target.name === "hoa_fees") {
        payload.selectedHOAFeeParcel = props.selectedHOAFeeParcel.filter(
          (parcelType) => parcelType !== e.target.value
        );
      }

      props.dispatch({
        type: "Map/saveState",
        payload: payload,
      });
    }
  };

  const parcelMenuBtnHandler = (e) => {
    if (e.currentTarget.classList.contains(styles.menuOpen)) {
      e.currentTarget.classList.remove(styles.menuOpen);
      setExpandedMenuLegend(false);
    } else {
      e.currentTarget.classList.add(styles.menuOpen);
      setExpandedMenuLegend(true);
      setExpandedLegend(false);
    }
  };

  const hideParcelChange = (e) => {
    sethideParcel(e.target.checked);
    if (e.target.checked) {
      hideVisibility(props.map, "parcelMapScopeLayer");
      hideVisibility(props.map, "parcelMapScopeLayerRentAVM");
      hideVisibility(props.map, "parcelMapScopeLayerRentNullAVM");
    } else {
      showVisibility(props.map, "parcelMapScopeLayer");
      showVisibility(props.map, "parcelMapScopeLayerRentAVM");
      showVisibility(props.map, "parcelMapScopeLayerRentNullAVM");
    }
  };

  const hideBoundaryChange = (e) => {
    sethideParcelBoundary(e.target.checked);
    if (e.target.checked) {
      hideVisibility(props.map, "parcelsStyle");
    } else {
      showVisibility(props.map, "parcelsStyle");
    }
  };

  return (
    <>
      <div
        id="PropertyParcelLegend"
        style={{ display: displayLegend ? "block" : "none" }}
        className={styles.parcelLegendContainer}
      >
        <div className={styles.parcelLegendHeader}>
          <div>
            <button
              className={`${styles.legendMenuButton} ${
                expandedMenuLegend && styles.menuOpen
              }`}
              onClick={parcelMenuBtnHandler}
            >
              {expandedMenuLegend ? <FaTimes /> : <FaBars />}
            </button>
          </div>
          <div className={styles.legendMenuTitle}>
            <span style={{ color: hideParcel ? "lightgray" : "" }}>
              {
                optionsParcelTypeShown.filter(
                  (parcel) => parcel.value == props.parcelTypeShown
                )[0].label
              }
            </span>
          </div>
          <div style={{ width: "20px" }}>
            {props.parcelTypeShown != "subdivision" && !hideParcel && (
              <button
                className={styles.legendExpanderBtn}
                onClick={toggleLegendExpand}
              >
                {expandedLegend ? (
                  <HiOutlineChevronUp />
                ) : (
                  <HiOutlineChevronDown />
                )}
              </button>
            )}
          </div>
        </div>

        <div
          className={`${styles.parcelLegendContent} ${
            (expandedMenuLegend || expandedLegend) && styles.contentExpanded
          }`}
        >
          {expandedMenuLegend && (
            <div>
              {optionsParcelTypeShown
                .filter((parcel) => parcel.value != props.parcelTypeShown)
                .map((parcel) => (
                  <div
                    key={parcel.value}
                    value={parcel.value}
                    onClick={headerSwitchChange}
                    className={styles.legendMenuItem}
                    style={{
                      cursor: hideParcel ? "not-allowed" : "default",
                    }}
                  >
                    <span style={{ color: hideParcel ? "lightgray" : "" }}>
                      {parcel.label}
                    </span>
                  </div>
                ))}
            </div>
          )}

          {expandedLegend &&
            props.parcelTypeShown != "subdivision" &&
            (props.parcelTypeShown === "owners"
              ? (legendContentType = owners) &&
                (selectLegendType = props.selectedOwnerParcel)
              : (legendContentType = hoaFees) &&
                (selectLegendType = props.selectedHOAFeeParcel)) && (
              <ul className={styles.legendList}>
                {legendContentType.map((legendItem, index) => (
                  <li key={index}>
                    <div>
                      <span className={styles[legendItem.symbolClass]}></span>
                      <span>
                        {legendItem.name.includes("AH4R")
                          ? "AMH"
                          : legendItem.name}
                      </span>
                    </div>
                    <div>
                      <Checkbox
                        name={props.parcelTypeShown}
                        value={legendItem.name}
                        onChange={legendSelectChange}
                        checked={selectLegendType.includes(legendItem.name)}
                      />
                    </div>
                  </li>
                ))}
              </ul>
            )}
          {expandedMenuLegend && (
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                padding: "10px 10px",
                gap: "10px",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Checkbox checked={hideParcel} onChange={hideParcelChange} />
              <span style={{ paddingRight: "20px" }}>Hide Parcel Dots</span>
            </div>
          )}
          {expandedMenuLegend && (
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                padding: "10px 10px",
                gap: "10px",
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Checkbox
                checked={hideParcelBoundary}
                onChange={hideBoundaryChange}
              />
              <span style={{ paddingRight: "20px" }}>
                Hide Parcel Boundaries
              </span>
            </div>
          )}
        </div>
      </div>
    </>
  );
});

export default PropertyParcelLegend;
