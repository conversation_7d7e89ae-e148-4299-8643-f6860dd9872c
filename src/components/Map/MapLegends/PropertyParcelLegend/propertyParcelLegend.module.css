.legendMenuButton {
  width: 20px;
  height: 30px;
  position: relative;
  cursor: pointer;
  background: transparent;
  border: none;
  outline: 0;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-size: 20px;
  color: #007aff;
}
.menuOpen {
  color: rgb(100, 100, 100);
}

.legendExpanderBtn {
  display: flex;
  border: none;
  background: transparent;
  cursor: pointer;
  color: #007aff;
  font-size: 20px;
  padding: 0;
  width: 20px;
}

.legendMenuTitle {
  font-weight: 500;
}

.legendMenuItem {
  display: flex;
  flex-direction: row;
  justify-content: center;
  padding: 10px;
  cursor: pointer;
}
.legendMenuItem:hover {
  background-color: #007bff35;
}

.parcelLegendContainer {
  /* position: absolute; */
  left: 10px;
  /* top: 10px; */
  top: 60px;
  background-color: white;
  z-index: 150;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  border-radius: 5px;
  /* min-width: 215.83px; */
  min-width: 200px;
}

.parcelLegendHeader {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 4px 10px;
  width: 100%;
}

.parcelLegendContent {
  overflow: hidden;
  max-height: 0px;
  transition: max-height 150ms ease-in-out;
}

.contentExpanded {
  max-height: 240px;
}

.legendList {
  list-style-type: none;
  padding: 0 12px 10px;
  margin: 0;
}

.legendList li {
  padding: 2px 0px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.parcelBaseSymbol {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 100%;
  margin-right: 10px;
}

.ownerOccupiedSymbol {
  composes: parcelBaseSymbol;
  background-color: rgba(0, 0, 0, 0.1);
}

.AH4RSymbol {
  composes: parcelBaseSymbol;
  background-color: #f5222d;
}
.CERBERUSSymbol {
  composes: parcelBaseSymbol;
  background-color: #1890ff;
}
.INVITATIONSymbol {
  composes: parcelBaseSymbol;
  background-color: #52c41a;
}
.PROGRESSSymbol {
  composes: parcelBaseSymbol;
  background-color: #08979c;
}
.MOMANDPOPSymbol {
  composes: parcelBaseSymbol;
  background-color: #8c8c8c;
}
.OTHERSSymbol {
  composes: parcelBaseSymbol;
  background-color: #faad14;
}

.AMHERSTSymbol {
  composes: parcelBaseSymbol;
  /* background-color: #1c2e5a; */
  background-color: #136170;
}
.TRICONSymbol {
  composes: parcelBaseSymbol;
  /* background-color: #051f4a; */
  background-color: #0749b5;
}

.symbolFeeRange1 {
  composes: parcelBaseSymbol;
  background-color: #4daf4a;
}
.symbolFeeRange2 {
  composes: parcelBaseSymbol;
  background-color: #a8a802;
}
.symbolFeeRange3 {
  composes: parcelBaseSymbol;
  background-color: #faad14;
}
.symbolFeeRange4 {
  composes: parcelBaseSymbol;
  background-color: #ff7f00;
}
.symbolFeeRange5 {
  composes: parcelBaseSymbol;
  background-color: #e41a1c;
}
