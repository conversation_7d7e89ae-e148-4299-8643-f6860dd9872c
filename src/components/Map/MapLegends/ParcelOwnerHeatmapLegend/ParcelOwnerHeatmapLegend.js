import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Checkbox, Switch } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { Spin } from "antd";

const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

const labelMap = (key) =>
  ({
    ah4r: "AMH",
    cerberus: "Cerberus",
    invitationHomes: "Invitation Homes",
    progressResidential: "Progress Residential",
    tricon: "Tricon",
    amherst: "Amherst",
  }[key]);

const reverseLabelMap = (key) =>
  ({
    AMH: "ah4r",
    Cerberus: "cerberus",
    Invitation: "invitationHomes",
    Progress: "progressResidential",
    Tricon: "tricon",
    Amherst: "amherst",
  }[key]);

const getOwnerColor = (key) =>
  ({
    ah4r: "#f5222d",
    cerberus: "#1890ff",
    invitationHomes: "#52c41a",
    progressResidential: "#08979c",
    tricon: "#5047b9",
    amherst: "#3d9146",
  }[key]);

const ParcelOwnerHeatmapLegend = () => {
  const map = useSelector((state) => state.Map.map);

  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const parcelHeatmapOptions = useSelector(
    (state) => state.Map.parcelHeatmapOptions
  );
  const parcelHeatmapLoading = useSelector(
    (state) => state.Map.parcelHeatmapLoading
  );
  const parcelShowHeatmap = useSelector((state) => state.Map.parcelShowHeatmap);

  const dispatch = useDispatch();
  const [showPercentage, setShowPercentage] = useState(false);
  const [percentages, setPercentages] = useState({});

  useEffect(() => {
    if (!map) return;

    const getPercentage = () => {
      if (
        showPercentage &&
        currentMapLayerOptions.includes("institutional owners")
      ) {
        const features = map.queryRenderedFeatures({
          layers: ["parcelOwnerHeatmapCircleLayer"],
        });
        const newPercentages = {
          ah4r: { count: 0, percent: 0 },
          cerberus: { count: 0, percent: 0 },
          invitationHomes: { count: 0, percent: 0 },
          progressResidential: { count: 0, percent: 0 },
          tricon: { count: 0, percent: 0 },
          amherst: { count: 0, percent: 0 },
        };

        let ownerSum = 0;
        for (let i = 0; i < features.length; i++) {
          const { institution } = features[i].properties;

          let owner = institution.includes("AH4R") ? "AMH" : institution;

          if (newPercentages[reverseLabelMap(owner)]) {
            newPercentages[reverseLabelMap(owner)].count++;
            ownerSum += 1;
          }
        }
        Object.keys(newPercentages).forEach((key) => {
          if (!newPercentages[key]) return;
          if (ownerSum === 0) {
            newPercentages[key].percent = 0;
          } else {
            newPercentages[key].percent =
              (newPercentages[key].count / ownerSum) * 100;
          }
        });

        setPercentages(newPercentages);
      } else {
        if (Object.keys(percentages).length > 0) setPercentages({});
      }
    };
    getPercentage();
    map.on("moveend", getPercentage);
    return () => {
      map.off("moveend", getPercentage);
    };
  }, [map, showPercentage]);

  if (!currentMapLayerOptions.includes("institutional owners")) return null;
  return (
    <div className="py-[10px] flex flex-col bg-white">
      <div className="py-[2.5px] px-[15px] text-center">
        <span className="font-bold relative">
          {parcelHeatmapLoading && (
            <div className="absolute -left-[30px] -top-[4px]">
              <Spin indicator={antIcon} />
            </div>
          )}
          Institutional Owners
        </span>
      </div>
      <div>
        <div className="flex flex-col">
          <span className="px-[15px] font-semibold">View</span>
          <div className="px-[15px] flex flex-col gap-[5px]">
            {/* <div className="flex flex-row gap-[5px] items-center">
              <Switch
                checked={parcelShowHeatmap}
                onChange={(checked) => {
                  dispatch({
                    type: "Map/saveState",
                    payload: { parcelShowHeatmap: checked },
                  });
                }}
                size="small"
              />
              <span>Heatmap</span>
            </div> */}
            <div className="flex flex-row gap-[5px] items-center">
              <Switch
                checked={showPercentage}
                onChange={setShowPercentage}
                size="small"
              />
              <span>Percentage and count</span>
            </div>
          </div>
        </div>
        <div>
          <span className="px-[15px] font-semibold">Owners</span>
          {Object.keys(parcelHeatmapOptions).map((key, index) => (
            <div
              key={index}
              className="flex flex-row items-center py-[2.5px] px-[15px]"
            >
              <Checkbox
                checked={parcelHeatmapOptions[key]}
                onChange={(e) => {
                  dispatch({
                    type: "Map/saveState",
                    payload: {
                      parcelHeatmapOptions: {
                        ...parcelHeatmapOptions,
                        [key]: e.target.checked,
                      },
                    },
                  });
                }}
              >
                <div className="flex flex-row gap-[5px] items-center">
                  <div
                    className="rounded w-[10px] h-[10px]"
                    style={{ backgroundColor: getOwnerColor(key) }}
                  ></div>
                  <div>
                    {showPercentage && Object.keys(percentages).length > 0 ? (
                      <div className="flex flex-row gap-[5px]">
                        <span>{labelMap(key)}</span>
                        <strong>
                          {` (${Math.round(percentages[key].percent)}%)`} -{" "}
                          {percentages[key].count}
                        </strong>
                      </div>
                    ) : (
                      <span>{labelMap(key)}</span>
                    )}
                  </div>
                </div>
              </Checkbox>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ParcelOwnerHeatmapLegend;
