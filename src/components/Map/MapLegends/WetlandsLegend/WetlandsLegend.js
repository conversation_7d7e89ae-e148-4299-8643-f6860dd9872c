import { useSelector } from "react-redux";
import styles from "./wetlandsLegend.module.css";
import { IoInformationCircle } from "@react-icons/all-files/io5/IoInformationCircle";

// import asset1 from "../../../../assets/images/mapbox/patterns/asset-1.svg";
// import asset2 from "../../../../assets/images/mapbox/patterns/asset-2.svg";
// import asset4 from "../../../../assets/images/mapbox/patterns/asset-4.svg";
// import asset5 from "../../../../assets/images/mapbox/patterns/asset-5.svg";
// import asset6 from "../../../../assets/images/mapbox/patterns/asset-6.svg";

const WetlandsLegend = function () {
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );

  const legendItems = [
    {
      name: "Estuarine and Marine Deepwater",
      color: "#17707c",
      code: "E1/M1",
    },
    {
      name: "Estuarine and Marine Wetland",
      color: "#60ba9a",
      code: "E2/M2",
    },
    {
      name: "Freshwater Emergent Wetland",
      color: "#76bb0c",
      code: "PEM",
    },
    {
      name: "Freshwater Forested/Shrub Wetland",
      color: "#177b2e",
      code: "PFO/PSS",
    },
    {
      name: "Freshwater Pond",
      color: "#5f80b9",
      code: "P",
    },
    {
      name: "Lake",
      color: "#140071",
      code: "L1/L2",
    },
    {
      name: "Other",
      color: "#a87d49",
    },
    {
      name: "Riverine",
      color: "#1c83b7",
      code: "R1/R2/R3/R4",
    },
  ];

  if (currentMapLayerOptions.includes("wetlands")) {
    return (
      <div id="WetlandsLegend" className={styles.LegendContainer}>
        <div className={styles.HeaderContainer}>
          <span style={{ fontWeight: "bold" }}>
            Wetlands{" "}
            <a
              className={styles.infoButton}
              href="https://www.fws.gov/node/264584"
              target="_blank"
            >
              <IoInformationCircle size={18} />
            </a>
          </span>
        </div>
        <div className={styles.ItemContainer}>
          {legendItems.map((item, index) => (
            <div key={index} className={styles.LabelContainer}>
              <span className={styles.FillColorContainer}>
                <div
                  style={{
                    width: 100,
                    height: 100,
                    display: "inline-block",
                    backgroundColor: item.color,
                  }}
                />
              </span>
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  width: "180px",
                }}
              >
                <span>
                  {item.name} {item.code && <strong>({item.code})</strong>}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  } else {
    return null;
  }
};

export default WetlandsLegend;
