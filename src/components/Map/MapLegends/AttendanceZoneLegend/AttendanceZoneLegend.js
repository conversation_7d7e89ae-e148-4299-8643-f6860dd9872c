import { connect } from "react-redux";
import { Radio, Select } from "antd";
import styles from "./attendanceZoneLegend.module.css";

const AttendanceZoneLegend = connect(({ Map }) => ({
  map: Map.map,
  currentMapLayerOptions: Map.currentMapLayerOptions,
  selectedAttendanceCategory: Map.selectedAttendanceCategory,
}))(function (props) {
  const ratingColorArray = [
    "#d7191c",
    "#e85b3a",
    "#ffb987",
    "#fec981",
    "#ffedab",
    "#ecf7ad",
    "#c4e687",
    "#97d265",
    "#58b453",
    "#1a9641",
  ];

  const handleChange = (value) => {
    if (!props.map) return;
    console.log(`selected ${value}`);
    props.dispatch({
      type: "Map/saveState",
      payload: {
        selectedAttendanceCategory: value,
      },
    });

    const mapScopeBBox = props.map.getBounds().toArray();
    props.dispatch({
      type: "Map/getAttendanceZone",
      payload: {
        // lng/lat switched
        category: value,
        lng1: mapScopeBBox[0][1],
        lat1: mapScopeBBox[0][0],
        lng2: mapScopeBBox[1][1],
        lat2: mapScopeBBox[1][0],
      },
    });
  };

  if (props.currentMapLayerOptions.includes("school zones")) {
    return (
      <div
        id="AttendanceZoneLegend"
        className={styles.attendanceZoneLegendContainer}
      >
        <div
          className={styles.attendanceZoneHeaderContainer}
          style={{ marginBottom: "5px" }}
        >
          <span style={{ fontWeight: "bold" }}>School Zones</span>
        </div>
        <div className={styles.attendanceZoneHeaderContainer}>
          <Select
            style={{
              width: "100%",
            }}
            defaultValue={props.selectedAttendanceCategory}
            onChange={handleChange}
            options={[
              {
                value: "elementary",
                label: "Elementary",
              },
              {
                value: "intermediate",
                label: "Intermediate",
              },
              {
                value: "middle",
                label: "Middle",
              },
              {
                value: "junior high",
                label: "Junior High",
              },
              {
                value: "high",
                label: "High",
              },
              {
                value: "senior high",
                label: "Senior High",
              },
              {
                value: "other",
                label: "Other",
              },
            ]}
          />
        </div>
        {/* <div style={{ display: 'flex', flexDirection: 'column' }}> */}
        <div style={{ display: "flex", flexDirection: "row" }}>
          {/* <div> */}
          {ratingColorArray.map((color, index) => (
            <div key={color} className={styles.attendanceZoneLabelContainer}>
              <span
                className={styles.attendanceZoneColorContainer}
                style={{
                  backgroundColor: color,
                }}
              ></span>
              <span>{index + 1}</span>
            </div>
          ))}
        </div>
      </div>
    );
  } else {
    return null;
  }
});

export default AttendanceZoneLegend;
