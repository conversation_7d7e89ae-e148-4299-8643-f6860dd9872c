import { useSelector, useDispatch } from "react-redux";
import styles from "./subdivisionLegend.module.css";
import { Switch } from "antd";

const SubdivisionLegend = function () {
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const fiftyFivePlusSubdivisionEnabled = useSelector(
    (state) => state.Map.fiftyFivePlusSubdivisionEnabled
  );

  const dispatch = useDispatch();

  if (currentMapLayerOptions.includes("subdivision")) {
    return (
      <div
        id="SubdivisionLegend"
        className={styles.LegendContainer}
        style={{ bottom: "200px" }}
      >
        <div className={styles.HeaderContainer}>
          <span style={{ fontWeight: "bold" }}>Subdivisions</span>
        </div>
        <div className={styles.ItemContainer}>
          <div className={styles.LabelContainer}>
            <div
              style={{
                display: "flex",
                flexDirection: "row",
                gap: "5px",
                alignItems: "center",
              }}
            >
              <span
                className={styles.FillColorContainer}
                style={{
                  backgroundColor: "#737373",
                }}
              ></span>
              <span>55+ Communities</span>
              <Switch
                size="small"
                checked={fiftyFivePlusSubdivisionEnabled}
                onChange={() => {
                  dispatch({
                    type: "Map/saveState",
                    payload: {
                      fiftyFivePlusSubdivisionEnabled:
                        !fiftyFivePlusSubdivisionEnabled,
                    },
                  });
                }}
              />
            </div>
          </div>
        </div>
      </div>
    );
  } else {
    return null;
  }
};

export default SubdivisionLegend;
