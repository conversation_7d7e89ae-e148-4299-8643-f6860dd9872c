import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import RangeAndColorInfo from "../../MapControls/HeatmapMenu/RangeAndColorInfo";
import { getTitleForStudyType } from "../../MapControls/HeatmapMenu/utils";
import {
  demographicsRanges,
  submarketRanges,
} from "../../../../utils/heatmapRanges";
import { rgbToHexString } from "../../../../utils/color";

function HeatmapLegend() {
  const heatmapType = useSelector((state) => state.Map.heatmapType);
  const heatmapSubmarketStudyType = useSelector(
    (state) => state.Map.heatmapSubmarketStudyType
  );
  const currentSubmarketColorScale = useSelector(
    (state) => state.Map.currentSubmarketColorScale
  );
  const heatmapDemographicsStudyType = useSelector(
    (state) => state.Map.heatmapDemographicsStudyType
  );
  const currentDemographicColorScale = useSelector(
    (state) => state.Map.currentDemographicColorScale
  );

  const [type, setType] = useState(null);
  const [ranges, setRanges] = useState([]);

  useEffect(() => {
    if (heatmapType != "demographics" && currentDemographicColorScale === null)
      return;

    let ranges = demographicsRanges[heatmapDemographicsStudyType];
    if (currentDemographicColorScale) {
      ranges = [];
      const colorArray = currentDemographicColorScale.range();
      const valueArray = currentDemographicColorScale.quantiles();
      valueArray.unshift(null);
      for (let i = 0; i < colorArray.length; i++) {
        let color = colorArray[i];
        if (colorArray[i].includes("rgb")) {
          color = rgbToHexString(colorArray[i]);
        }
        ranges.push({
          color: color,
          value: valueArray[i],
          score: i + 1,
        });
      }
    }

    setType(heatmapDemographicsStudyType);
    setRanges(ranges);
  }, [currentDemographicColorScale]);

  useEffect(() => {
    if (heatmapType != "submarket" && currentSubmarketColorScale === null)
      return;

    let ranges = submarketRanges[heatmapSubmarketStudyType];
    if (currentSubmarketColorScale) {
      ranges = [];
      const colorArray = currentSubmarketColorScale.range();
      const valueArray = currentSubmarketColorScale.quantiles();
      valueArray.unshift(null);
      for (let i = 0; i < colorArray.length; i++) {
        let color = colorArray[i];
        if (colorArray[i].includes("rgb")) {
          color = rgbToHexString(colorArray[i]);
        }
        let score;
        if (
          ["vacancy_rate", "market_effective_rent_sf"].includes(
            heatmapSubmarketStudyType
          )
        ) {
          score = 10 - i;
        } else {
          score = i + 1;
        }

        ranges.push({
          color: color,
          value: valueArray[i],
          score,
        });
      }
    }
    setType(heatmapSubmarketStudyType);
    setRanges(ranges);
  }, [currentSubmarketColorScale]);

  return (
    <div
      style={{
        width: "300px",
        padding: "5px 10px",
        backgroundColor: "white",
        borderRadius: "0 0 5px 5px",
      }}
    >
      <div style={{ textAlign: "center" }}>
        <span style={{ fontWeight: "bold" }}>
          Heatmap: {getTitleForStudyType(type)}
        </span>
      </div>
      <RangeAndColorInfo type={type} ranges={ranges} />
    </div>
  );
}

export default HeatmapLegend;
