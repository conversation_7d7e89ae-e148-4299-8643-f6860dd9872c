import { connect } from "react-redux";
import { Select } from "antd";
import { Loader2 } from "lucide-react";

const MobileHomeParkLegend = connect(({ Map }) => ({
  map: Map.map,
  currentMapLayerOptions: Map.currentMapLayerOptions,
  selectedMobileHomeParkType: Map.selectedMobileHomeParkType,
  selectedMobileHomeParkLoading: Map.selectedMobileHomeParkLoading,
}))(function (props) {
  const handleTypeChange = (value) => {
    if (!props.map) return;
    console.log(`selected ${value}`);
    props.dispatch({
      type: "Map/saveState",
      payload: {
        selectedMobileHomeParkType: value,
      },
    });
  };

  if (props.currentMapLayerOptions.includes("mobile home park")) {
    return (
      <div className="bg-white flex flex-col px-4 pb-6 w-96">
        <div className="flex justify-center items-center py-2 gap-2">
          <span className="font-bold">Mobile Home Parks</span>
          {props.selectedMobileHomeParkLoading && (
            <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
          )}
        </div>

        {/* Type Selection */}
        <div className="mb-4">
          <Select
            className="w-full"
            defaultValue="all"
            onChange={handleTypeChange}
            disabled={props.selectedMobileHomeParkLoading}
            options={[
              {
                value: "all",
                label: "All",
              },
              {
                value: "MOBILE HOME PARK",
                label: "Mobile Home Park",
              },
              {
                value: "RECREATIONAL VEHICLE PARK",
                label: "RV Park",
              },
              {
                value: "MHP/RV/MIGRANT HOUSING",
                label: "MHP/RV/Migrant Housing",
              },
            ]}
          />
        </div>
      </div>
    );
  } else {
    return null;
  }
});

export default MobileHomeParkLegend;