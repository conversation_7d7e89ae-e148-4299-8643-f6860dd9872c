import { connect } from "react-redux";
import styles from "./driveTimeLegend.module.css";
import { ACTIVITY_CENTER_COLOR } from "../../../../constants";
import { IoInformationCircle } from "@react-icons/all-files/io5/IoInformationCircle";
import { Checkbox, Segmented } from "antd";
import { useCallback } from "react";

const DriveTimeLegend = connect(({ Map }) => ({
  currentMapLayerOptions: Map.currentMapLayerOptions,
  driveTimeOptions: Map.driveTimeOptions,
  driveTimeTraffic: Map.driveTimeTraffic,
}))(function (props) {
  const legendItems = [
    { name: "5 Minutes" },
    { name: "10 Minutes" },
    { name: "15 Minutes" },
    { name: "20 Minutes" },
    { name: "30 Minutes" },
    { name: "60 Minutes" },
  ];

  const changeTrafficOption = useCallback((value) => {
    props.dispatch({
      type: "Map/saveState",
      payload: {
        driveTimeTraffic: value,
      },
    });
  }, []);

  if (props.currentMapLayerOptions.includes("drive time")) {
    return (
      <div
        id="DriveTimeLegend"
        className="bg-white flex flex-col gap-2 px-4 py-2"
      >
        <div>
          <div className={styles.HeaderContainer}>
            <span style={{ fontWeight: "bold" }}>Traffic</span>
          </div>
          <div className="flex flex-row justify-center">
            <Segmented
              size={"small"}
              options={[
                { label: "Yes", value: true },
                { label: "No", value: false },
              ]}
              value={props.driveTimeTraffic}
              onChange={changeTrafficOption}
            />
          </div>
        </div>
        <div>
          <div className={styles.HeaderContainer}>
            <span style={{ fontWeight: "bold" }}>Drive Times</span>
          </div>
          <div className={styles.ItemContainer}>
            {legendItems.map((item, index) => (
              <div key={index} className={styles.LabelContainer}>
                <Checkbox
                  checked={props.driveTimeOptions[item.name]}
                  onChange={(e) => {
                    props.dispatch({
                      type: "Map/saveState",
                      payload: {
                        driveTimeOptions: {
                          ...props.driveTimeOptions,
                          [item.name]: e.target.checked,
                        },
                      },
                    });
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      gap: "5px",
                    }}
                  >
                    <span>{item.name}</span>
                  </div>
                </Checkbox>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  } else {
    return null;
  }
});

export default DriveTimeLegend;
