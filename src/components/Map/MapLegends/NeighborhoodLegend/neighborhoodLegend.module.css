.neighborhoodLegendContainer {
  /* position: absolute; */
  right: 10px;
  /* bottom: 60px; */
  bottom: 150px;
  z-index: 150;
  display: flex;
  flex-direction: column;
  background-color: white;
  box-sizing: content-box;
  border-radius: 5px;
  /* box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); */
  padding: 10px 0;
}

.neighborhoodHeaderContainer {
  font-weight: 500;
  padding: 2.5px 15px;
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.neighborhoodItemContainer {
  display: flex;
  flex-direction: column;
}

.neighborhoodLabelContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 2.5px 15px;
}

.neighborhoodColorContainer {
  display: inline-block;
  width: 15px;
  height: 15px;
  transform: translateY(2px);
}

.infoButton {
  padding: 0;
  background: transparent;
  border: none;
  /* width: 18px;
  height: 18px; */
  /* display: flex; */
  flex-direction: column;
  justify-content: center;
  color: black;
}
