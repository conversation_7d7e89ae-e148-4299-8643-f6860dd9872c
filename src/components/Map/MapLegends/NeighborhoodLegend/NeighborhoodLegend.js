import { connect } from "react-redux";
import styles from "./neighborhoodLegend.module.css";
import { ACTIVITY_CENTER_COLOR } from "../../../../constants";
import { IoInformationCircle } from "@react-icons/all-files/io5/IoInformationCircle";
import { Checkbox } from "antd";

const NeighborhoodLegend = connect(({ Map }) => ({
  currentMapLayerOptions: Map.currentMapLayerOptions,
  neighborhoodType: Map.neighborhoodType,
}))(function NeighborhoodLegend(props) {
  const legendItems = [
    {
      name: "Macro Neighborhoods",
      color: "#841d4e",
      type: "M",
    },
    {
      name: "Neighborhoods",
      color: "#a82265",
      type: "N",
    },
    {
      name: "Sub Neighborhoods",
      color: "#d55aa0",
      type: "S",
    },
  ];

  if (props.currentMapLayerOptions.includes("neighborhood")) {
    return (
      <div
        id="NeighborhoodLegend"
        className={styles.neighborhoodLegendContainer}
      >
        <div className={styles.neighborhoodHeaderContainer}>
          <span style={{ fontWeight: "bold" }}>
            Neighborhoods{" "}
            {/* <span style={{ whiteSpace: "nowrap" }}>
              Centers{" "}
              <a
                className={styles.infoButton}
                href="https://www.brookings.edu/research/activity-centers/"
                target="_blank"
              >
                <IoInformationCircle size={18} />
              </a>
            </span> */}
          </span>
        </div>
        <div className={styles.neighborhoodItemContainer}>
          {legendItems.map((item, index) => (
            <div key={index} className={styles.neighborhoodLabelContainer}>
              <Checkbox
                checked={props.neighborhoodType === item.type}
                onChange={(e) => {
                  if (e.target.checked) {
                    props.dispatch({
                      type: "Map/saveState",
                      payload: {
                        neighborhoodType: item.type,
                      },
                    });
                  }
                }}
              >
                <div
                  style={{
                    display: "flex",
                    flexDirection: "row",
                    gap: "5px",
                  }}
                >
                  <span
                    className={styles.neighborhoodColorContainer}
                    style={{
                      backgroundColor: item.color,
                    }}
                  ></span>
                  <span>{item.name}</span>
                </div>
              </Checkbox>
            </div>
          ))}
        </div>
      </div>
    );
  } else {
    return null;
  }
});

export default NeighborhoodLegend;
