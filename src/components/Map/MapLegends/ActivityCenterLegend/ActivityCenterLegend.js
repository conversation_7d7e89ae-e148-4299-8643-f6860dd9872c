import { connect } from "react-redux";
import styles from "./activityCenterLegend.module.css";
import { ACTIVITY_CENTER_COLOR } from "../../../../constants";
import { IoInformationCircle } from "@react-icons/all-files/io5/IoInformationCircle";

const ActivityCenterLegend = connect(({ Map }) => ({
  currentMapLayerOptions: Map.currentMapLayerOptions,
}))(function ActivityCenterLegend(props) {
  const legendItems = [
    {
      name: "Major",
      color: ACTIVITY_CENTER_COLOR.majorColor,
    },
    {
      name: "Minor",
      color: ACTIVITY_CENTER_COLOR.minorColor,
    },
    {
      name: "Mono",
      color: ACTIVITY_CENTER_COLOR.monoColor,
    },
  ];

  if (props.currentMapLayerOptions.includes("activity centers")) {
    return (
      <div
        id="ActivityCenterLegend"
        className={styles.activityCenterLegendContainer}
      >
        <div className={styles.activityCenterHeaderContainer}>
          <span style={{ fontWeight: "bold" }}>
            Activity{" "}
            <span style={{ whiteSpace: "nowrap" }}>
              Centers{" "}
              <a
                className={styles.infoButton}
                href="https://www.brookings.edu/research/activity-centers/"
                target="_blank"
              >
                <IoInformationCircle size={18} />
              </a>
            </span>
          </span>
        </div>
        <div className={styles.activityCenterItemContainer}>
          {legendItems.map((item, index) => (
            <div key={index} className={styles.activityCenterLabelContainer}>
              <span
                className={styles.activityCenterColorContainer}
                style={{
                  backgroundColor: item.color,
                }}
              ></span>
              <span>{item.name}</span>
            </div>
          ))}
        </div>
      </div>
    );
  } else {
    return null;
  }
});

export default ActivityCenterLegend;
