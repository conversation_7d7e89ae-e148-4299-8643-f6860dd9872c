import React from "react";
import { useSelector } from "react-redux";
import styles from "./newHomeLegend.module.css";

function NewHomeLegend() {
  const newBuildLegendCount = useSelector(
    (state) => state.Map.newBuildLegendCount
  );
  return (
    <div className={styles.newBuildLegendContainer}>
      <div className={styles.newBuildLegendHeader}>
        <span style={{ fontWeight: "700" }}>New Construction Home Status</span>
      </div>
      <div>
        {newBuildLegendCount &&
          newBuildLegendCount.length > 0 &&
          newBuildLegendCount.map((item, index) => (
            <div
              key={`${index}-${item.label}-${item.count}`}
              className={styles.itemRow}
            >
              <div>
                <span>
                  {item.label} ({item.count || 0})
                </span>
              </div>
              <div
                className={`${styles.color}`}
                style={{ backgroundColor: item.color }}
              ></div>
            </div>
          ))}
      </div>
    </div>
  );
}

export default NewHomeLegend;
