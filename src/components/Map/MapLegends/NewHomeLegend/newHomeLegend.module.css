.newBuildLegendContainer {
  background-color: white;
  z-index: 200;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  border-radius: 5px;
  min-width: 200px;
  padding-bottom: 5px;
}

.newBuildLegendHeader {
  display: flex;
  flex-direction: row;
  justify-content: center;
  padding: 10px;
}

.itemRow {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 2.5px 10px;
}

.color {
  width: 20px;
  height: 20px;
  /* border-radius: 50%; */
}
.notSpecified {
  background-color: #a9bbc0;
}
.available,
.availableNow {
  background-color: #8ceb49;
}
.readyToBuild {
  background-color: #ebd849;
}
.nowBuilding {
  background-color: #eb6149;
}
.modelHome {
  background-color: #1282de;
}
