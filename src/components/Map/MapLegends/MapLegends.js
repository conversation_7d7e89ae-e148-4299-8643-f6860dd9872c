import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import AttendanceZoneLegend from "./AttendanceZoneLegend/AttendanceZoneLegend";
import PropertyParcelLegend from "./PropertyParcelLegend/PropertyParcelLegend";
import ActivityCenterLegend from "./ActivityCenterLegend/ActivityCenterLegend";
import FloodZoneLegend from "./FloodZoneLegend/FloodZoneLegend";
import NeighborhoodLegend from "./NeighborhoodLegend/NeighborhoodLegend";
import WetlandsLegend from "./WetlandsLegend/WetlandsLegend";
import SubdivisionLegend from "./SubdivisionLegend/SubdivisionLegend";
import { FaMinus } from "@react-icons/all-files/fa/FaMinus";
import { FaAngleLeft } from "@react-icons/all-files/fa/FaAngleLeft";
import { FaAngleRight } from "@react-icons/all-files/fa/FaAngleRight";
import styles from "./mapLegends.module.css";
import Draggable from "react-draggable";
import HeatmapLegend from "./HeatmapLegend/HeatmapLegend";
// import DriveTimeLegend from "./DriveTimeLegend/DriveTimeLegend";
import ParcelOwnerHeatmapLegend from "./ParcelOwnerHeatmapLegend/ParcelOwnerHeatmapLegend";
import NewHomeLegend from "./NewHomeLegend/NewHomeLegend";
import { BridgeOwnedPropertyLegend } from "../MapLayers/OwnedProperty/BridgeProperty";
import { TrueholdOwnedPropertyLegend } from "../MapLayers/OwnedProperty/TrueholdProperty";
import { MultiFamilyLegend } from "../MapLayers/MultiFamily";
import { FutureLandUseLegend } from "../MapLayers/ClientsData/FutureLandUserLayer";
import FundriseLegend from "./FundriseLegend/FundriseLegend";
import { PowerLinesLegend } from "../MapLayers/PowerLinesLayer";
import { TaxLayerLegend } from "../MapLayers/TaxLayer";
import { RegridZoningLegend } from "../MapLayers/RegridZoning";
import { LandBankLegend } from "../../features/land-bank/LandBankLayer";
import { IndustrialParcelLegend } from "../../features/industrial-parcels/legend";

import { DriveTimeLegend } from "../../features/drive-time/index";
import { HistoricalZoningLegend } from "../../features/historical-zoning/legend";

import { MajorEmployerLegend } from "../../features/major-employer/MajorEmployerLegend";
import { PublicHousingLegend } from "../../features/public-housing/Legend";
import { DemoOwnedPropertyLegend } from "../MapLayers/OwnedProperty/DemoOwnedProperty";
import { DemoUnderwrittenLegend } from "../MapLayers/DemoUnderwrittenLandLayer";
import { TransitLineLegend } from "../MapLayers/TransitLineLayer";
import MobileHomeParkLegend from "./MobileHomeParkLegend/MobileHomeParkLegend";
import FundriseCommunitiesLegend from "./FundriseLegend/FundriseCommunityLegend";
import { NewlySplitParcelsLegend } from "../../features/parcels/newly-split";
import { MudTaxLegend } from "../../features/greystar/mudTaxLayer";
import { HoustonCompsLegend } from "../../features/greystar/HoustonCompsLayer";
import IndustryNewsLegend from "../../features/industry-news/IndustryNewsLegend";
const VALID_LEGEND_LAYERS = [
  "school zones",
  "activity centers",
  "flood zone",
  "subdivision",
  "neighborhood",
  "wetlands",
  "drive time",
  "institutional owners",
  "new construction",
  "gorlick owned",
  // "truehold owned",
  "multi family",
  "fundrise properties",
  "fundrise owned community",
  "power lines",
  "tax",
  "zoning types",
  "land bank",
  "industrial parcels",
  "major employer",
  "public housing voucher",
  "public housing ah",
  "public housing assisted multifamily properties",
  "public housing insured",
  "public housing development",
  "public housing authorities",
  "public housing ehasa",
  "public housing",
  "demo owned",
  "demo land owned",
  "demo land underwritten",
  "historical zoning",
  "allied_mineola_future_land_use",
  "allied_sanitary_sewer",
  "arcgis transit line",
  "mobile home park",
  "split parcels",
  "greystar mudtax",
  "greystar houston comps",
  "industry news",
];
// {currentMapLayerOptions.includes("new construction") &&
//           newBuildClusterType === "Clustered By Status" && <NewHomeLegend />}

function MapLegends() {
  // const drawingMode = useSelector((state) => state.Map.drawingMode);
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector(
    (state) => state.Map.currentMapLayerOptions
  );
  const newBuildClusterType = useSelector(
    (state) => state.Map.newBuildClusterType
  );
  const bridgeOwnedClusterType = useSelector(
    (state) => state.Map.bridgeOwnedClusterType
  );
  const minimzedMapLegends = useSelector(
    (state) => state.Map.minimzedMapLegends
  );
  const heatmapType = useSelector((state) => state.Map.heatmapType);

  const dispatch = useDispatch();

  const [showMapLegend, setShowMapLegend] = useState(false);
  const [legendLayers, setLegendLayers] = useState([]);
  const [viewedLegend, setViewedLegend] = useState(null);

  const [disabled, setDisabled] = useState(true);
  const [bounds, setBounds] = useState({
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  });
  const draggleRef = useRef(null);

  useEffect(() => {
    if (currentMapLayerOptions.length === 0 && heatmapType === null) {
      dispatch({
        type: "Map/saveState",
        payload: {
          minimzedMapLegends: false,
        },
      });
      setShowMapLegend(false);
      setLegendLayers([]);
      setViewedLegend(null);
    } else {
      // use prev state and filter out any removed layer menu options
      let layers = [...legendLayers].filter((layer) => {
        if (
          currentMapLayerOptions.includes(layer) &&
          layer === "new construction" &&
          newBuildClusterType === "Clustered By Status"
        ) {
          return true;
        } else if (
          currentMapLayerOptions.includes(layer) &&
          layer === "gorlick owned" &&
          bridgeOwnedClusterType === "Clustered By Status"
        ) {
          return true;
        } else if (
          currentMapLayerOptions.some((item) => item.includes("truehold")) &&
          layer.includes("truehold")
          // && bridgeOwnedClusterType === "Clustered By Status"
        ) {
          return true;
        } else if (
          (currentMapLayerOptions.includes(layer) &&
            layer != "new construction" &&
            layer != "gorlick owned") || //&&
          // layer != "truehold owned") ||
          layer === "demographics" ||
          layer === "submarket"
        ) {
          return true;
        }
        return false;
      });

      // add new layer menu options
      for (let i = currentMapLayerOptions.length - 1; i >= 0; i--) {
        if (VALID_LEGEND_LAYERS.includes(currentMapLayerOptions[i])) {
          if (
            !layers.includes(currentMapLayerOptions[i]) &&
            currentMapLayerOptions[i] !== "new construction" &&
            currentMapLayerOptions[i] !== "gorlick owned"
            // && currentMapLayerOptions[i] !== "truehold owned"
          ) {
            layers.push(currentMapLayerOptions[i]);
            break;
          } else if (
            !layers.includes(currentMapLayerOptions[i]) &&
            ((currentMapLayerOptions[i] === "new construction" &&
              newBuildClusterType === "Clustered By Status") ||
              (currentMapLayerOptions[i] === "gorlick owned" &&
                bridgeOwnedClusterType === "Clustered By Status"))
            //    ||
            // (currentMapLayerOptions[i] === "truehold owned" &&
            //   bridgeOwnedClusterType === "Clustered By Status")
          ) {
            layers.push(currentMapLayerOptions[i]);
            break;
          }
        } else {
          if (
            currentMapLayerOptions[i].includes("truehold") &&
            !layers.some((item) => item.includes("truehold"))
          ) {
            layers.push("truehold");
          }
        }
      }

      // add or remove demographics and submarket layers
      // if (
      //   !heatmapType &&
      //   (layers.includes("demographics") || layers.includes("submarket"))
      // ) {
      //   layers = layers.filter(
      //     (layer) => layer !== "demographics" && layer !== "submarket"
      //   );
      // } else if (heatmapType) {
      //   if (!layers.includes(heatmapType)) {
      //     layers = layers.filter(
      //       (layer) => layer !== "demographics" && layer !== "submarket"
      //     );
      //     layers.push(heatmapType);
      //   }
      // }

      setShowMapLegend(layers.length > 0);
      setLegendLayers(layers);
      setViewedLegend(layers[layers.length - 1]);
    }
  }, [
    currentMapLayerOptions,
    heatmapType,
    newBuildClusterType,
    bridgeOwnedClusterType,
  ]);

  const minBtnHandler = () => {
    dispatch({
      type: "Map/saveState",
      payload: {
        minimzedMapLegends: true,
      },
    });
  };

  const handleNavigation = (direction) => {
    const index = legendLayers.indexOf(viewedLegend);
    if (direction === "prev") {
      if (index != 0) {
        setViewedLegend(legendLayers[index - 1]);
      } else {
        setViewedLegend(legendLayers[legendLayers.length - 1]);
      }
    } else {
      if (index != legendLayers.length - 1) {
        setViewedLegend(legendLayers[index + 1]);
      } else {
        setViewedLegend(legendLayers[0]);
      }
    }
  };

  const onStart = (_event, uiData) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    if (!targetRect) {
      return;
    }
    setBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };
  console.log("viewed legend", viewedLegend);
  return (
    <>
      {/* <PropertyParcelLegend /> */}
      {showMapLegend && !minimzedMapLegends && (
        <Draggable
          nodeRef={draggleRef}
          disabled={disabled}
          bounds={bounds}
          onStart={(event, uiData) => onStart(event, uiData)}
        >
          <div ref={draggleRef} className={styles.legendContainer}>
            <div
              className={styles.legendHeader}
              onMouseOver={() => {
                if (disabled) {
                  setDisabled(false);
                }
              }}
              onMouseOut={() => {
                setDisabled(true);
              }}
            >
              <div
                className={styles.headerBtnContainer}
                style={{
                  visibility: legendLayers.length > 1 ? "visible" : "hidden",
                }}
              >
                <button
                  className={styles.headerButton}
                  onClick={() => handleNavigation("prev")}
                >
                  <FaAngleLeft />
                </button>
                <button
                  className={styles.headerButton}
                  onClick={() => handleNavigation("next")}
                >
                  <FaAngleRight />
                </button>
              </div>
              <div className={styles.headerBtnContainer}>
                <button className={styles.headerButton} onClick={minBtnHandler}>
                  <FaMinus />
                </button>
              </div>
            </div>
            <div>
            {viewedLegend === "greystar mudtax" && <MudTaxLegend />}
            {viewedLegend === "greystar houston comps" && <HoustonCompsLegend />}
              {viewedLegend === "school zones" && <AttendanceZoneLegend />}
              {viewedLegend === "charter school" && <AttendanceZoneLegend />}
              {viewedLegend === "activity centers" && <ActivityCenterLegend />}
              {viewedLegend === "flood zone" && <FloodZoneLegend />}
              {viewedLegend === "subdivision" && <SubdivisionLegend />}
              {viewedLegend === "neighborhood" && <NeighborhoodLegend />}
              {viewedLegend === "wetlands" && <WetlandsLegend />}
              {/* {(viewedLegend === "demographics" ||
                viewedLegend === "submarket") && <HeatmapLegend />} */}
              {viewedLegend === "drive time" && <DriveTimeLegend />}
              {viewedLegend === "major employer" && <MajorEmployerLegend />}
              {viewedLegend === "arcgis transit line" && <TransitLineLegend />}
              {viewedLegend === "public housing" ||
              viewedLegend === "public housing voucher" ||
              viewedLegend === "public housing ah" ||
              viewedLegend ===
                "public housing assisted multifamily properties" ||
              viewedLegend === "public housing insured" ||
              viewedLegend === "public housing development" ||
              viewedLegend === "public housing authorities" ||
              viewedLegend === "public housing ehasa" ? (
                <PublicHousingLegend />
              ) : (
                <></>
              )}

              {viewedLegend === "institutional owners" && (
                <ParcelOwnerHeatmapLegend />
              )}
              {viewedLegend === "new construction" &&
                newBuildClusterType === "Clustered By Status" && (
                  <NewHomeLegend />
                )}

              {viewedLegend === "gorlick owned" &&
                bridgeOwnedClusterType === "Clustered By Status" && (
                  <BridgeOwnedPropertyLegend />
                )}
              {viewedLegend === "truehold" && (
                <TrueholdOwnedPropertyLegend
                  clusterType={bridgeOwnedClusterType}
                />
              )}
              {viewedLegend === "demo owned" && (
                <DemoOwnedPropertyLegend clusterType={bridgeOwnedClusterType} />
              )}
              {(viewedLegend === "demo land owned" ||
                viewedLegend === "demo land underwritten") && (
                <DemoUnderwrittenLegend />
              )}
              {(viewedLegend === "allied_mineola_future_land_use" ||
                viewedLegend === "allied_sanitary_sewer") && (
                <FutureLandUseLegend />
              )}

              {viewedLegend === "multi family" && <MultiFamilyLegend />}
              {viewedLegend === "fundrise properties" && <FundriseLegend />}
              {viewedLegend === "fundrise owned community" && (
                <FundriseCommunitiesLegend />
              )}
              {viewedLegend === "power lines" && <PowerLinesLegend />}
              {viewedLegend === "tax" && <TaxLayerLegend />}
              {viewedLegend === "zoning types" && <RegridZoningLegend />}
              {viewedLegend === "land bank" && <LandBankLegend map={map} />}
              {viewedLegend === "industrial parcels" && (
                <IndustrialParcelLegend />
              )}
              {viewedLegend === "historical zoning" && (
                <HistoricalZoningLegend />
              )}
              {viewedLegend === "mobile home park" && <MobileHomeParkLegend />}
              {viewedLegend === "split parcels" && <NewlySplitParcelsLegend />}
              {viewedLegend === "industry news" && <IndustryNewsLegend />}
              
            </div>
          </div>
        </Draggable>
      )}
    </>
  );
}

export default MapLegends;


