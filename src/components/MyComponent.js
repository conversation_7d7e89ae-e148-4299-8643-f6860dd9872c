import React, { useState } from "react";
import * as d3 from "d3";
import * as d3Scale from "d3-scale";

function MyComponent() {
  const [data, setData] = useState([
    -9, -6, -3, -3, -1, 1, 2, 2, 2, 3, 3, 4, 5, 5, 6, 6, 7, 8, 8, 9, 10, 11, 11,
    12, 12, 12, 12, 13, 15, 15, 15, 15, 16, 16, 17, 18, 18, 19, 19, 19, 21, 22,
    23, 23, 23, 23, 23, 26, 28, 28, 30, 31, 31, 32, 32, 33, 34, 34, 34, 34, 35,
    35, 35, 36, 36, 41, 41, 41, 42, 43, 43, 43, 44, 44, 45, 45, 46, 46, 47, 48,
    51, 51, 52, 53, 53, 54, 54, 56, 56, 58, 60, 61, 62, 64, 67, 70, 73, 74, 78,
    78, 78, 79, 83, 93,
  ]);

  const accent = d3.scaleOrdinal(d3.schemeAccent);
  var bluesequential = d3.scaleSequential(d3.interpolateBlues);

  function generateClassRange(num) {
    const step = 1 / (num - 1);
    const range = Array.from({ length: num }, (_, i) => i * step);
    return range;
  }
  console.log("generateClassRange(10): ", generateClassRange(10));

  const colorArray = generateClassRange(10).map((q) => bluesequential(q));

  console.log("colorArray: ", colorArray);
  const quantile = d3.scaleQuantile().domain(data).range(colorArray);

  console.log("quantile.quantiles():", quantile.quantiles());
  console.log("quantiles.range():", quantile.range());
  console.log("quantiles(12): ", quantile(12));

  // Define color scale using D3.js
  const colors = d3Scale
    .scaleSequential()
    .interpolator(d3.interpolateOrRd)
    .domain([0, data.length]);

  console.log(colors);

  return (
    // Your component code here
    null
  );
}

export default MyComponent;
