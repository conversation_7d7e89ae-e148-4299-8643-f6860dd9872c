import React from "react";
import { useMap } from "../../Map/MapProvider";
import { useQuery } from "react-query";
import {
  getHistoricalZoningPeriods,
  getHistoricalZoningPeriodSummary,
  getHistoricalZoningComparisonCounts,
} from "./api";
import { useHistoricalZoning } from "./context";
import { Button, Checkbox, Select, Tabs } from "antd";
import { FaMinus } from "@react-icons/all-files/fa/FaMinus";
import { FaPlus } from "@react-icons/all-files/fa/FaPlus";

const zoningTypes = {
  Residential: "#ebfd1f",
  Commercial: "#ac3c34",
  Special: "#e087b4",
  Mixed: "#8d6698",
  Planned: "#d8925f",
  Agriculture: "#67a25b",
  Industrial: "#4a6692",
  Overlay: "#8d5436",
  Others: "#c6c6c6",
};

const zoningSubTypes = {
  "Single Family": "#a6cee3",
  "Two Family": "#1f78b4",
  "Multi Family": "#b2df8a",
  "Mobile Home Park": "#33a02c",
  "General Commercial": "#fb9a99",
  "Core Commercial": "#e31a1c",
  "Retail Commercial": "#fdbf6f",
  "Neighborhood Commercial": "#ff7f00",
  Office: "#cab2d6",
  "Special Commercial": "#6a3d9a",
  "Mixed Use": "#ffff99",
  Industrial: "#b15928",
  "Light Industrial": "#dfc27d",
  Special: "#66bd63",
  Planned: "#80cdc1",
  Overlay: "#878787",
};

const ZoningLegendItems = ({ types }) => (
  <div className="flex flex-col">
    {Object.keys(types).map((key) => (
      <div key={key} className="flex flex-row gap-[10px]">
        <div
          className="w-[22px] h-[22px] min-w-[22px] min-h-[22px]"
          style={{ backgroundColor: types[key] }}
        ></div>
        <div className="text-nowrap">{key}</div>
      </div>
    ))}
  </div>
);

const useMapZoomEnd = ({ map }) => {
  const [zoomEnd, setZoomEnd] = React.useState(null);
  React.useEffect(() => {
    if (!map) return;
    const onZoomEnd = () => setZoomEnd(map.getZoom());
    onZoomEnd();
    map.on("zoomend", onZoomEnd);
    return () => map.off("zoomend", onZoomEnd);
  }, [map]);
  return zoomEnd;
};

const HistoricalPeriodSummary = ({ period }) => {
  const { data } = useQuery(
    ["getHistoricalZoningPeriodSummary", period],
    () => getHistoricalZoningPeriodSummary(period),
    { enabled: period !== undefined ? true : false }
  );

  return (
    <div className="flex flex-col my-2">
      <span className="underline underline-offset-4">{period} Summary</span>
      <div className="flex flex-col">
        <span>Date: {data ? data?.updated_at?.split("T")[0] : "-"}</span>
        <span>Total: {data ? Number(data?.count)?.toLocaleString() : "-"}</span>
      </div>
    </div>
  );
};

const periodFormatter = (period) => {
  return `${period.year}-${String(period.month).padStart(2, "0")}`;
};

const HistoricalPeriodSelector = (props) => {
  const { showCompare, setShowCompare } = props;
  const {
    state: { currentPeriod, comparePeriod },
    dispatch,
  } = useHistoricalZoning();
  const { data, isLoading } = useQuery(
    ["getHistoricalZoningPeriods"],
    () => getHistoricalZoningPeriods(),
    { enabled: true }
  );

  React.useEffect(() => {
    if (!data || currentPeriod) return;
    const { year, month } = data[0];
    dispatch({
      type: "UPDATE",
      payload: {
        currentPeriod: periodFormatter({ year, month }),
      },
    });
  }, [data]);

  return (
    <div className="flex flex-row gap-4 w-full">
      <div className="flex flex-col w-full">
        <div className="flex flex-col w-full">
          <span>Current</span>
          <div className="flex flex-row gap-1 items-center">
            <Select
              placeholder="Select period"
              value={currentPeriod}
              onChange={(value) => {
                dispatch({
                  type: "UPDATE",
                  payload: {
                    currentPeriod: value,
                  },
                });
              }}
              options={data
                ?.filter((d) => comparePeriod !== periodFormatter(d))
                .map((d) => ({
                  value: periodFormatter(d),
                  label: periodFormatter(d),
                }))}
              style={{ minWidth: "125px", width: "100%" }}
            />
            {!showCompare && (
              <Button
                type="default"
                shape="circle"
                size="small"
                onClick={() => setShowCompare(!showCompare)}
              >
                <FaPlus />
              </Button>
            )}
          </div>
        </div>
        {currentPeriod && <HistoricalPeriodSummary period={currentPeriod} />}
      </div>
      {showCompare && (
        <div className="flex flex-col w-full">
          <div className="flex flex-col w-full">
            <span>Compared to</span>
            <div className="flex flex-row gap-1 items-center">
              <Select
                placeholder="Select period"
                value={comparePeriod}
                onChange={(value) => {
                  dispatch({
                    type: "UPDATE",
                    payload: {
                      comparePeriod: value,
                    },
                  });
                }}
                options={data
                  ?.filter((d) => currentPeriod !== periodFormatter(d))
                  .map((d) => ({
                    value: periodFormatter(d),
                    label: periodFormatter(d),
                  }))}
                style={{ minWidth: "125px", width: "100%" }}
              />
              <Button
                type="default"
                shape="circle"
                size="small"
                onClick={() => {
                  setShowCompare(!showCompare);
                  dispatch({
                    type: "UPDATE",
                    payload: {
                      comparePeriod: undefined,
                    },
                  });
                }}
              >
                <FaMinus />
              </Button>
            </div>
          </div>
          {comparePeriod && <HistoricalPeriodSummary period={comparePeriod} />}
        </div>
      )}
    </div>
  );
};

const CHECKBOX_LABELS = {
  "hist-zoning-currrent": "Current",
  "hist-zoning-compare": "Compare",
};
export const LayerVisibilitySelector = () => {
  const {
    state: { layerVisibility },
    dispatch,
  } = useHistoricalZoning();

  return (
    <div className="flex flex-col my-2">
      <h4 className="font-bold">Layer Visibility</h4>
      <div className="flex flex-row gap-4">
        {Object.keys(layerVisibility).map((key) => (
          <Checkbox
            key={key}
            checked={layerVisibility[key]}
            onChange={(e) =>
              dispatch({
                type: "UPDATE",
                payload: {
                  layerVisibility: {
                    ...layerVisibility,
                    [key]: e.target.checked,
                  },
                },
              })
            }
          >
            {CHECKBOX_LABELS[key]}
          </Checkbox>
        ))}
      </div>
    </div>
  );
};

const ComparisonResults = (props) => {
  const {
    state: { currentPeriod, comparePeriod },
  } = useHistoricalZoning();
  const { data, isLoading } = useQuery(
    ["getHistoricalZoningComparisonCounts", currentPeriod, comparePeriod],
    () =>
      getHistoricalZoningComparisonCounts({
        currPeriod: currentPeriod,
        prevPeriod: comparePeriod,
      }),
    { enabled: currentPeriod && comparePeriod ? true : false }
  );
  const [showCompare, setShowCompare] = React.useState(false);
  const dispatch = useHistoricalZoning();

  const items = [
    {
      key: "1",
      label: `New`,
      children: (
        <div>
          {data && data.newZoning && (
            <p>{`${Number(
              data.newZoning
            ).toLocaleString()} new zoning records`}</p>
          )}
          {data && data.newZoningIntersectExisting && (
            <p>{`With ${Number(
              data.newZoningIntersectExisting
            ).toLocaleString()} intersecting existing zones`}</p>
          )}
        </div>
      ),
    },
    {
      key: "2",
      label: `Existing`,
      children: (
        <div>
          {data && data.existingZoning && (
            <p>{`${Number(
              data.existingZoning
            ).toLocaleString()} existing zoning records remain`}</p>
          )}
          {data && data.existingZoningChange && (
            <p>{`With ${Number(
              data.existingZoningChange
            ).toLocaleString()} having some changes`}</p>
          )}
          {data && data.existingZoningTypeChange && (
            <p>{`Of the changes, ${Number(
              data.existingZoningTypeChange
            ).toLocaleString()} had type related changes`}</p>
          )}
          {data && data.existingZoningGeometryChange && (
            <p>{`Of the changes, ${Number(
              data.existingZoningGeometryChange
            ).toLocaleString()} had area coverage related changes`}</p>
          )}
        </div>
      ),
    },
    {
      key: "3",
      label: `Deleted`,
      children: (
        <div>
          {data && data.zoningDeleted && (
            <p>{`${Number(
              data.zoningDeleted
            ).toLocaleString()} zoning records were removed`}</p>
          )}
        </div>
      ),
    },
  ];
  return (
    <div className="flex flex-col my-2">
      <h4 className="font-bold">Comparison Results</h4>
      <div className="flex flex-row gap-4">
        <Tabs items={items} style={{ width: "100%" }} />
      </div>
    </div>
  );
};

export const HistoricalZoningLegend = () => {
  const { map } = useMap();
  const zoom = useMapZoomEnd({ map });
  const [showCompare, setShowCompare] = React.useState(false);

  return (
    <div className="bg-white p-[10px]">
      <h4 className="font-bold">Historical Zoning</h4>
      <HistoricalPeriodSelector {...{ showCompare, setShowCompare }} />

      {showCompare && <ComparisonResults />}

      <LayerVisibilitySelector />
      <div className="flex flex-row gap-4 w-full">
        {(zoom < 14 || showCompare) && (
          <div className="min-w-[200px]">
            <div className="font-bold mb-1">Zoning Types</div>
            <ZoningLegendItems types={zoningTypes} />
          </div>
        )}
        {(zoom >= 14 || showCompare) && (
          <div className="min-w-[200px]">
            <div className="font-bold mb-1">Zoning Subtypes</div>
            <ZoningLegendItems types={zoningSubTypes} />
          </div>
        )}
      </div>
    </div>
  );
};
