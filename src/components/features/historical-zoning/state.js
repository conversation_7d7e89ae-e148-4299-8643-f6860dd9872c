const initialState = {
  isLoading: false,
  currentPeriod: undefined,
  comparePeriod: undefined,
  layerVisibility: {
    "hist-zoning-currrent": true,
    "hist-zoning-compare": false,
  },
};

const reducer = (state, action) => {
  const newState = { ...state };
  switch (action.type) {
    case "UPDATE":
      return {
        ...newState,
        ...action.payload,
      };
    case "LOADING":
      return {
        ...newState,
        isLoading: true,
      };
    case "LOADED":
      return {
        ...newState,
        isLoading: false,
      };
    default:
      return state;
  }
};

export { initialState, reducer };
