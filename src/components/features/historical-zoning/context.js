import { createContext, useContext, useReducer, useMemo } from "react";
import { initialState, reducer } from "./state";

const Context = createContext(undefined);

const HistoricalZoningProvider = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, initialState);

  const context = useMemo(() => ({ state, dispatch }), [state, dispatch]);

  return <Context.Provider value={context}>{children}</Context.Provider>;
};

const useHistoricalZoning = (selectorFn) => {
  const context = useContext(Context);
  if (context === undefined) {
    throw new Error(
      "useHistoricalZoning must be used within a HistoricalZoningProvider"
    );
  }

  if (selectorFn) {
    return selectorFn(context.state);
  }

  return context;
};

export { HistoricalZoningProvider, useHistoricalZoning };
