import { customRequest, getServerType } from "../../../services/data";

export const getHistoricalZoningPeriods = async () => {
  const serverType = getServerType();
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/historical-zoning/periods`,
    { method: "GET" }
  );
};

export const getHistoricalZoningPeriodSummary = async (period) => {
  const serverType = getServerType();
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/historical-zoning/periods/${period}`,
    { method: "GET" }
  );
};

export const getHistoricalZoningComparisonCounts = async (opts) => {
  const { currPeriod, prevPeriod } = opts;
  const serverType = getServerType();
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/historical-zoning/compare/${currPeriod}/${prevPeriod}`,
    { method: "GET" }
  );
};
