import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getGreystarHoustonCompsData } from "../../../services/data";
import { renderToString } from "react-dom/server";
import { initPopup } from "../../Map/MapUtility/general";
import { convertToGeoJSON } from "../../../utils/geography";
import { geojsonTemplate } from "../../../constants";
import { Loader2 } from "lucide-react";
import { Select } from "antd";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";

const sourceId = "graystar-houston-comps-source";

// Legend component
export const HoustonCompsLegend = () => {
  const dispatch = useDispatch();
  const greystarMudTaxLoading = useSelector((state) => state.Map.greystarMudTaxLoading);
  const selectedField = useSelector((state) => state.Map.houstonCompsSelectedField || "market_price_per_mo");
  const selectedColorScheme = useSelector((state) => state.Map.houstonCompsColorScheme || "blue-green");
  
  const fieldOptions = [
    { value: "market_price_per_mo", label: "Market Price/Month", unit: "$" },
    { value: "market_rate_per_sf_mo", label: "Market Rate/SF/Month", unit: "$" },
    { value: "eff_price_per_mo", label: "Effective Price/Month", unit: "$" },
    { value: "eff_rate_per_sf_mo", label: "Effective Rate/SF/Month", unit: "$" }
  ];

  const colorSchemeOptions = [
    { value: "blue-green", label: "Blue-Green" },
    { value: "purple-green", label: "Purple-Green" },
    { value: "pink-purple", label: "Pink-Purple" },
    { value: "blue-purple", label: "Blue-Purple" },
    { value: "viridis", label: "Viridis" }
  ];

  const getColorScheme = (scheme) => {
    switch (scheme) {
      case "blue-green":
        return ["#e0f3db", "#a8ddb5", "#7bccc4", "#2b8cbe", "#0868ac", "#084081"];
      case "purple-green":
        return ["#762a83", "#af8dc3", "#e7d4e8", "#d9f0d3", "#7fbf7b", "#1b7837"];
      case "pink-purple":
        return ["#fce7f3", "#f9a8d4", "#ec4899", "#d946ef", "#a855f7", "#7c3aed"];
      case "blue-purple":
        return ["#1e40af", "#6366f1", "#7c3aed", "#c026d3", "#ec4899", "#f97316"];
      case "viridis":
        return ["#440154", "#482677", "#3e4989", "#31688e", "#26828e", "#1f9e89"];
      default:
        return ["#e0f3db", "#a8ddb5", "#7bccc4", "#2b8cbe", "#0868ac", "#084081"];
    }
  };

  const getLegendData = () => {
    // Get colors based on selected scheme
    const colors = getColorScheme(selectedColorScheme);
    
    switch (selectedField) {
      case "market_price_per_mo":
      case "eff_price_per_mo":
        return [
          { value: "1000", color: colors[0] },
          { value: "1300", color: colors[1] },
          { value: "1600", color: colors[2] },
          { value: "1900", color: colors[3] },
          { value: "2200", color: colors[4] },
          { value: "2500", color: colors[5] }
        ];
      case "market_rate_per_sf_mo":
      case "eff_rate_per_sf_mo":
        return [
          { value: "1.00", color: colors[0] },
          { value: "1.25", color: colors[1] },
          { value: "1.50", color: colors[2] },
          { value: "1.75", color: colors[3] },
          { value: "2.00", color: colors[4] },
          { value: "2.25", color: colors[5] }
        ];
      default:
        return [];
    }
  };

  const legendData = getLegendData();
  const currentField = fieldOptions.find(field => field.value === selectedField);

  if (greystarMudTaxLoading) {
    return (
      <div className="flex flex-col p-[10px] gap-[10px] bg-white">
        <div className="flex flex-col text-center">
          <div className="flex justify-center items-center gap-2">
            <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
            <span className="font-semibold text-xs">
              Loading...
            </span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-[350px] flex flex-col p-[10px] gap-[10px] bg-white">
      <div className="flex flex-col text-center">
        <span className="font-semibold text-base">
          Houston Comps
        </span>
        <Select
          value={selectedField}
          onChange={(value) => dispatch({
            type: "Map/saveState",
            payload: { houstonCompsSelectedField: value }
          })}
          style={{ width: '100%', marginTop: '8px' }}
          size="small"
          placeholder="Select Field"
          options={fieldOptions.map(option => ({
            value: option.value,
            label: option.label
          }))}
        />
        <Select
          value={selectedColorScheme}
          onChange={(value) => dispatch({
            type: "Map/saveState",
            payload: { houstonCompsColorScheme: value }
          })}
          style={{ width: '100%', marginTop: '8px' }}
          size="small"
          placeholder="Select Color Scheme"
          options={colorSchemeOptions}
        />
      </div>
      
      <div>
        <div className="flex flex-row">
          {legendData.map((item, idx) => (
            <div key={idx} className="flex flex-row items-center flex-1">
              <div
                style={{
                  width: "100%",
                  height: "40px",
                  backgroundColor: item.color,
                }}
              ></div>
            </div>
          ))}
        </div>
        <div className="flex flex-row">
          {legendData.map((item, idx) => (
            <div key={idx} className="flex flex-row items-center flex-1">
              <div className="text-center w-full text-xs">
                {currentField?.unit}{item.value}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export const HoustonCompsLayer = () => {
  const dispatch = useDispatch();
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const selectedField = useSelector((state) => state.Map.houstonCompsSelectedField || "market_price_per_mo");
  const selectedColorScheme = useSelector((state) => state.Map.houstonCompsColorScheme || "blue-green");
  const [geojson, setGeojson] = useState(geojsonTemplate);

  const getColorScheme = (scheme) => {
    switch (scheme) {
      case "blue-green":
        return ["#e0f3db", "#a8ddb5", "#7bccc4", "#2b8cbe", "#0868ac", "#084081"];
      case "purple-green":
        return ["#762a83", "#af8dc3", "#e7d4e8", "#d9f0d3", "#7fbf7b", "#1b7837"];
      case "pink-purple":
        return ["#fce7f3", "#f9a8d4", "#ec4899", "#d946ef", "#a855f7", "#7c3aed"];
      case "blue-purple":
        return ["#1e40af", "#6366f1", "#7c3aed", "#c026d3", "#ec4899", "#f97316"];
      case "viridis":
        return ["#440154", "#482677", "#3e4989", "#31688e", "#26828e", "#1f9e89"];
      default:
        return ["#e0f3db", "#a8ddb5", "#7bccc4", "#2b8cbe", "#0868ac", "#084081"];
    }
  };

  const getColorConfiguration = (field) => {
    // Get colors based on selected scheme
    const colors = getColorScheme(selectedColorScheme);
    
    switch (field) {
      case "market_price_per_mo":
      case "eff_price_per_mo":
        return [
          "interpolate",
          ["linear"],
          ["get", field],
          1000, colors[0],
          1300, colors[1],
          1600, colors[2],
          1900, colors[3],
          2200, colors[4],
          2500, colors[5],
          3000, colors[5],
        ];
      case "market_rate_per_sf_mo":
      case "eff_rate_per_sf_mo":
        return [
          "interpolate",
          ["linear"],
          ["get", field],
          1.0, colors[0],
          1.25, colors[1],
          1.5, colors[2],
          1.75, colors[3],
          2.0, colors[4],
          2.25, colors[5],
          2.5, colors[5],
        ];
      default:
        return colors[0]; // fallback to solid color
    }
  };

  const [pointStyle, setPointStyle] = useState({
    id: "graystar-houston-comps-points",
    type: "circle",
    paint: {
      "circle-color": getColorConfiguration(selectedField),
      "circle-radius": 6,
      "circle-opacity": 1,
      "circle-stroke-color": "#000",
      "circle-stroke-width": 0.25,
    },
  });

  // Update point style when selectedField or selectedColorScheme changes
  useEffect(() => {
    setPointStyle(prevStyle => ({
      ...prevStyle,
      paint: {
        ...prevStyle.paint,
        "circle-color": getColorConfiguration(selectedField),
      },
    }));
  }, [selectedField, selectedColorScheme]);

  const getTooltipHTML = (properties) => {
    return renderToString(
      <div style={{ padding: "10px", display: "flex", flexDirection: "column" }}>
        <span><strong>Name:</strong> {properties.name}</span>
        <span><strong>Address:</strong> {properties.address}</span>
        <span><strong>City:</strong> {properties.city}, {properties.state}</span>
        <span><strong>Units:</strong> {properties.units}</span>
        <span><strong>Occupancy:</strong> {(properties.occupancy * 100).toFixed(1)}%</span>
        <span><strong>Market Rate:</strong> ${properties.market_rate_per_sf_mo?.toFixed(2)}/sf/mo</span>
        <span><strong>Market Price:</strong> ${properties.market_price_per_mo}/mo</span>
        <span><strong>Year Built:</strong> {properties.yoc?.trim()}</span>
        <span><strong>Management:</strong> {properties.management}</span>
      </div>
    );
  };

  let popup = initPopup();

  const onMouseMove = (e) => {
    if (e.features.length > 0) {
      const feature = e.features[0];
      const coordinates = [e.lngLat.lng, e.lngLat.lat];
      const properties = feature.properties;

      popup.setLngLat(coordinates).setHTML(getTooltipHTML(properties)).addTo(map);
    }
  };

  const onMouseLeave = () => {
    popup.remove();
  };

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("greystar houston comps")) return;

    const fetchAndSetData = async () => {
      try {
        dispatch({
          type: "Map/saveState",
          payload: {
            greystarMudTaxLoading: true,
          },
        });
        const data = await getGreystarHoustonCompsData();
        
        if (data && Array.isArray(data)) {
          dispatch({
            type: "Map/saveState",
            payload: {
              greystarMudTaxLoading: false,
            },
          });
          const geoJson = convertToGeoJSON({
            data,
            geomAccessor: (item) => item.geom,
            propertiesAccessor: (item) => {
              const { geom, ...properties } = item;
              return properties;
            },
          });
          setGeojson(geoJson);
        } else {
          setGeojson(geojsonTemplate);
        }
      } catch (error) {
        console.error("Error fetching Houston Comps data:", error);
        setGeojson(geojsonTemplate);
      }
    };

    map.fire("map.setThemeOption", {
      payload: { currentMapThemeOption: "Monochrome" },
    });

    fetchAndSetData();

    map.on("mousemove", "graystar-houston-comps-points", onMouseMove);
    map.on("mouseleave", "graystar-houston-comps-points", onMouseLeave);

    return () => {
      map.off("mousemove", "graystar-houston-comps-points", onMouseMove);
      map.off("mouseleave", "graystar-houston-comps-points", onMouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("greystar houston comps")) return null;

  return (
    <>
      <Source id={sourceId} type="geojson" data={geojson}>
        <Layer {...pointStyle} />
      </Source>
    </>
  );
};

