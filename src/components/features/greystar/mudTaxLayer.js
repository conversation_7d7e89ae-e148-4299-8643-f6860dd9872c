import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getGreystarMudTaxData } from "../../../services/data";
import { renderToString } from "react-dom/server";
import { initPopup } from "../../Map/MapUtility/general";
import { convertToGeoJSON } from "../../../utils/geography";
import { geojsonTemplate } from "../../../constants";
import { Loader2 } from "lucide-react";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";

const sourceId = "graystar-mudtax-source";

// Legend component
export const MudTaxLegend = () => {
  const greystarMudTaxLoading = useSelector((state) => state.Map.greystarMudTaxLoading);
  const legendData = [
    { rate: "1.25", color: "#006837" },
    { rate: "1.5", color: "#1a9850" },
    { rate: "1.75", color: "#66bd63" },
    { rate: "2", color: "#a6d96a" },
    { rate: "2.25", color: "#d9ef8b" },
    { rate: "2.5", color: "#ffffbf" },
    { rate: "2.75", color: "#fee08b" },
    { rate: "3", color: "#fdae61" },
    { rate: "3.25", color: "#f46d43" },
    { rate: "3.5", color: "#d73027" },
    { rate: "3.75", color: "#a50026" },
    { rate: "4", color: "#a50026" },
  ];
  if (greystarMudTaxLoading) {
    return (
      <div className="flex flex-col p-[10px] gap-[10px] bg-white">
        <div className="flex flex-col text-center">
          <div className="flex justify-center items-center gap-2">
            <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
            <span className="font-semibold text-xs">
              Loading...
            </span>
          </div>
        </div>
      </div>
    );
  }
  return (
    <div className="flex flex-col p-[10px] gap-[10px] bg-white">
      <div className="flex flex-col text-center">
        <span className="font-semibold text-base">
          MUD Tax Rate %
        </span>
      </div>
      
      <div>
        <div className="flex flex-row">
          {legendData.map((item, idx) => (
            <div key={idx} className="flex flex-row items-center">
              <div
                style={{
                  width: "40px",
                  height: "40px",
                  backgroundColor: item.color,
                }}
              ></div>
            </div>
          ))}
        </div>
        <div className="flex flex-row">
          {legendData.map((item, idx) => (
            <div key={idx} className="flex flex-row items-center">
              <div className="text-center w-[40px] text-xs">
                {item.rate}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export const MudTaxLayer = () => {
  const dispatch = useDispatch();
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const greystarMudTaxLoading = useSelector((state) => state.Map.greystarMudTaxLoading);
  const [geojson, setGeojson] = useState(geojsonTemplate);
  const [polygonStyle, setPolygonStyle] = useState({
    id: "graystar-mudtax-polygon",
    type: "fill",
    paint: {
      "fill-color": [
        "interpolate",
        ["linear"],
        ["get", "taxrate"],
        0.0125,  // 1.25%
        "#1a9850",
        0.015,   // 1.50%
        "#66bd63",
        0.0175,  // 1.75%
        "#a6d96a",
        0.02,    // 2.00%
        "#d9ef8b",
        0.0225,  // 2.25%
        "#ffffbf",
        0.025,   // 2.50%
        "#fee08b",
        0.0275,  // 2.75%
        "#fdae61",
        0.03,    // 3.00%
        "#f46d43",
        0.04,    // 4.00%
        "#d73027",
      ],
      "fill-opacity": 0.7,
      "fill-outline-color": "#000"
    },
  });

  const getTooltipHTML = (properties) => {
    return renderToString(
      <div style={{ padding: "10px", display: "flex", flexDirection: "column" }}>
        <span><strong>ID:</strong> {properties.id}</span>
        <span><strong>MUD ID:</strong> {properties.mudid}</span>
        <span><strong>Tax Rate:</strong> {properties.taxrate}</span>
        <span><strong>MUD Name:</strong> {properties.mudname}</span>
      </div>
    );
  };

  let popup = initPopup();

  const onMouseMove = (e) => {
    if (e.features.length > 0) {
      const feature = e.features[0];
      const coordinates = [e.lngLat.lng, e.lngLat.lat];
      const properties = feature.properties;

      popup.setLngLat(coordinates).setHTML(getTooltipHTML(properties)).addTo(map);
    }
  };

  const onMouseLeave = () => {
    popup.remove();
  };

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("greystar mudtax")) return;

    const fetchAndSetData = async () => {
      try {
        dispatch({
          type: "Map/saveState",
          payload: {
            greystarMudTaxLoading: true,
          },
        });
        const data = await getGreystarMudTaxData();
        
        if (data && Array.isArray(data)) {
          dispatch({
            type: "Map/saveState",
            payload: {
              greystarMudTaxLoading: false,
            },
          });
          const geoJson = convertToGeoJSON({
            data,
            geomAccessor: (item) => item.geom,
            propertiesAccessor: (item) => {
              const { geom, ...properties } = item;
              return properties;
            },
          });
          setGeojson(geoJson);
        } else {
          setGeojson(geojsonTemplate);
        }
      } catch (error) {
        console.error("Error fetching MUD tax data:", error);
        setGeojson(geojsonTemplate);
      }
    };

    map.fire("map.setThemeOption", {
      payload: { currentMapThemeOption: "Monochrome" },
    });

    fetchAndSetData();

    map.on("mousemove", "graystar-mudtax-polygon", onMouseMove);
    map.on("mouseleave", "graystar-mudtax-polygon", onMouseLeave);

    return () => {
      map.off("mousemove", "graystar-mudtax-polygon", onMouseMove);
      map.off("mouseleave", "graystar-mudtax-polygon", onMouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("greystar mudtax")) return null;

  return (
    <>
      <Source id={sourceId} type="geojson" data={geojson}>
        <Layer {...polygonStyle} />
      </Source>
    </>
  );
};

