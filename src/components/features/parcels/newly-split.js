import React from "react";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";
import { useSelector } from "react-redux";
import { useSLMapAccessToken } from "../../hooks/useSLMapAccessToken";
import { tileURLRoot } from "../../../services/data";
import { useQuery } from "react-query";
import { customRequest, getServerType } from "../../../services/data";

const SOURCE_ID = "newly-split-parcels";
const SOURCE_LAYER = SOURCE_ID;

const RECENTLY_NEW_COLOR = "#FF1744";
const NEW_COLOR = "#2979FF";

const countStyle = {
  id: `${SOURCE_ID}-count`,
  source: `${SOURCE_ID}-count`,
  "source-layer": `${SOURCE_LAYER}-count`,
  minzoom: 12,
  type: "symbol",
  layout: {
    "text-field": "{cluster_size}",
    "text-size": 18,
    "text-allow-overlap": true,
  },
};

const countCircleStyle = {
  id: `${SOURCE_ID}-count-circle`,
  source: `${SOURCE_ID}-count`,
  "source-layer": `${SOURCE_LAYER}-count`,
  minzoom: 12,
  type: "circle",
  paint: {
    "circle-color": "yellow",
    "circle-radius": 20,
  },
  beforeId: countStyle.id,
};

const [_, MONTH] = new Date()
  .toISOString()
  .split("-")
  .map((x) => x);

export const NewlySplitParcels = () => {
  const map = useSelector((state) => state.Map.map);
  const serverType = useSelector((state) => state.Configure.serverType);
  const token = useSLMapAccessToken({ map });

  const { data: latestSplitDate } = useQuery({
    queryKey: [`NewlySplitParcels-latest-date-${MONTH}`],
    queryFn: async () => {
      const serverType = getServerType();
      return await customRequest(
        `/api/sbs/${serverType}/api/v1/parcel/split/latest-updated-at`,
        { method: "GET" }
      );
    },
    staleTime: 5 * 60 * 1000,
  });

  const lineStyle = React.useMemo(() => {
    let lineColor = NEW_COLOR;
    if (latestSplitDate && latestSplitDate?.updated_at) {
      lineColor = [
        "case",
        ["==", ["get", "updated_at"], latestSplitDate?.updated_at],
        RECENTLY_NEW_COLOR,
        NEW_COLOR,
      ];
    }

    return {
      id: `${SOURCE_ID}-line`,
      source: SOURCE_ID,
      "source-layer": SOURCE_LAYER,
      minzoom: 8,
      type: "line",
      paint: {
        "line-color": lineColor,
        "line-width": ["interpolate", ["linear"], ["zoom"], 8, 1, 12, 5],
      },
    };
  }, [latestSplitDate]);

  return (
    <React.Fragment>
      <Source
        id={SOURCE_ID}
        type="vector"
        tiles={[
          `${tileURLRoot(
            "sbs",
            serverType
          )}/api/v1/parcel/split/tile/${SOURCE_LAYER}/{z}/{x}/{y}.mvt?access_token=${token}`,
        ]}
      >
        <Layer {...lineStyle} />
      </Source>
      <Source
        id={`${SOURCE_ID}-count`}
        type="vector"
        tiles={[
          `${tileURLRoot(
            "sbs",
            serverType
          )}/api/v1/parcel/split/count/tile/${SOURCE_LAYER}-count/{z}/{x}/{y}.mvt?access_token=${token}`,
        ]}
      >
        <Layer {...countCircleStyle} />
        <Layer {...countStyle} />
      </Source>
    </React.Fragment>
  );
};
// props.currentMapLayerOptions.includes("split parcels")

export const NewlySplitParcelsLegend = () => {
  const { data: latestSplitDate } = useQuery({
    queryKey: [`NewlySplitParcels-latest-date-${MONTH}`],
    queryFn: async () => {
      const serverType = getServerType();
      return await customRequest(
        `/api/sbs/${serverType}/api/v1/parcel/split/latest-updated-at`,
        { method: "GET" }
      );
    },
    staleTime: 5 * 60 * 1000,
  });

  return (
    <div
      id="FloodZoneLegend"
      className="flex flex-col bg-white box-content rounded-[5px] py-[10px] px-0 min-w-[225px]"
    >
      <div className="flex flex-row justify-center">
        <span className="font-bold">Newly Subdivided Parcels</span>
      </div>
      <div className="px-[15px]">
        <span>Updated date:</span>
      </div>
      <div className="flex flex-col">
        <div className="flex flex-row items-center gap-[5px] py-[2.5px] px-[15px]">
          <div
            className="w-[25px] h-[25px] overflow-hidden"
            style={{ backgroundColor: RECENTLY_NEW_COLOR }}
          ></div>
          <div className="flex flex-col">
            <span className="font-[12px]">
              {/* {`= ${latestSplitDate && latestSplitDate?.updated_at}`} */}
              Q2 2025
            </span>
          </div>
        </div>
        <div className="flex flex-row items-center gap-[5px] py-[2.5px] px-[15px]">
          <div
            className="w-[25px] h-[25px] overflow-hidden"
            style={{ backgroundColor: NEW_COLOR }}
          ></div>
          <div className="flex flex-col">
            <span className="font-[12px]">
              {/* {`< ${latestSplitDate && latestSplitDate?.updated_at}`} */}
              Q4 2024
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
