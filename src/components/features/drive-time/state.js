const initialState = {
  isLoading: false,
  time: {
    active: true,
    value: "now", // now, morning, noon, evening
  },
  traffic: true,
  duration: {
    "5 Minutes": false,
    "10 Minutes": false,
    "15 Minutes": false,
    "20 Minutes": false,
    "30 Minutes": true,
    "60 Minutes": false,
  },
  geojsonData: {},  // Store GeoJSON data for each duration
  visualizationMode: "filled", // "filled" or "outlined"
  opacity: 0.8, // New: opacity control (0.0 to 1.0)
};

const reducer = (state, action) => {
  const newState = { ...state };
  switch (action.type) {
    case "UPDATE":
      return {
        ...newState,
        ...action.payload,
      };
    case "LOADING":
      return {
        ...newState,
        isLoading: true,
      };
    case "LOADED":
      return {
        ...newState,
        isLoading: false,
      };
    case "UPDATE_GEOJSON":
      return {
        ...newState,
        geojsonData: {
          ...newState.geojsonData,
          ...action.payload
        }
      };
    default:
      return state;
  }
};

export { initialState, reducer };