// utils.js
export const getMinutes = (duration) => {
    // duration = "5 Minutes" | "10 Minutes" | "15 Minutes" | "20 Minutes" | "30 Minutes" | "60 Minutes"
    return parseInt(duration.split(" ")[0]);
  };
  
  export const getDepartAtTime = (time) => {
    // time = "now" | "morning" | "noon" | "evening"
    const today = new Date();
    if (time === "morning") {
      today.setHours(9, 0, 0, 0); // 9am
    } else if (time === "noon") {
      today.setHours(12, 0, 0, 0); // 12pm
    } else if (time === "evening") {
      today.setHours(18, 0, 0, 0); // 6pm
    }
  
    // convert iso's utc to iso's local time
    let year = today.getFullYear();
    let month = String(today.getMonth() + 1).padStart(2, "0");
    let day = String(today.getDate()).padStart(2, "0");
    let hours = String(today.getHours()).padStart(2, "0");
    let minutes = String(today.getMinutes()).padStart(2, "0");
    let seconds = String(today.getSeconds()).padStart(2, "0");
  
    let timezoneOffset = -today.getTimezoneOffset();
    let sign = timezoneOffset >= 0 ? "+" : "-";
    let offsetHours = String(Math.floor(Math.abs(timezoneOffset) / 60)).padStart(
      2,
      "0"
    );
    let offsetMinutes = String(Math.abs(timezoneOffset) % 60).padStart(2, "0");
  
    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}${sign}${offsetHours}:${offsetMinutes}`;
  };