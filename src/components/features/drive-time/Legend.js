import { useCallback } from "react";
import { useDriveTime } from "./context";
import { Checkbox, Segmented, Button, Switch, Slider } from "antd";
import { LoadingOutlined, DownloadOutlined } from "@ant-design/icons";
import { Spin } from "antd";
import { getDurationColor } from "./colors";

const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

const DriveTimeLegend = () => {
  const {
    state: { time, traffic, duration, isLoading, geojsonData, visualizationMode, opacity },
    dispatch,
  } = useDriveTime();

  const updateTimeOfDay = useCallback(
    (value) => {
      dispatch({
        type: "UPDATE",
        payload: { time: { ...time, value: value } },
      });
    },
    [time, dispatch]
  );

  const changeTrafficOption = useCallback((value) => {
    dispatch({
      type: "UPDATE",
      payload: { traffic: value },
    });
  }, [dispatch]);
  
  const changeVisualizationMode = useCallback((checked) => {
    dispatch({
      type: "UPDATE",
      payload: { visualizationMode: checked ? "outlined" : "filled" },
    });
  }, [dispatch]);

  const updatedriveTimeDuration = useCallback(
    (name, value) => {
      dispatch({
        type: "UPDATE",
        payload: { duration: { ...duration, [name]: value } },
      });
    },
    [duration, dispatch]
  );
  
  const updateOpacity = useCallback((value) => {
    dispatch({
      type: "UPDATE",
      payload: { opacity: value / 100 },
    });
  }, [dispatch]);
  
  // Function to download GeoJSON
  const downloadGeoJSON = (durationKey) => {
    if (!geojsonData[durationKey]) return;
    
    const blob = new Blob([JSON.stringify(geojsonData[durationKey])], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `drive_time_${durationKey.replace(' ', '_').toLowerCase()}.geojson`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="bg-white flex flex-col gap-2 px-4 py-2">
      <div className="py-[2.5px] px-[15px] flex flex-row justify-center relative">
        <span className="font-bold">Drive Time</span>
        {isLoading && (
          <Spin
            indicator={antIcon}
            className="absolute left-[40px] top-1/2 -translate-y-1/2"
          />
        )}
      </div>
      <div>
        <div className="py-[2.5px] px-[15px] flex flex-row justify-center">
          <span className="font-bold">Time of Day</span>
        </div>
        <div className="flex flex-row justify-center">
          <Segmented
            size={"small"}
            options={[
              { label: "Now", value: "now" },
              { label: "Morning", value: "morning" },
              { label: "Noon", value: "noon" },
              { label: "Evening", value: "evening" },
            ]}
            value={time.value}
            onChange={updateTimeOfDay}
          />
        </div>
      </div>
      <div>
        <div className="py-[2.5px] px-[15px] flex flex-row justify-center">
          <span className="font-bold">Traffic</span>
        </div>
        <div className="flex flex-row justify-center">
          <Segmented
            size={"small"}
            options={[
              { label: "Yes", value: true },
              { label: "No", value: false },
            ]}
            value={traffic}
            onChange={changeTrafficOption}
          />
        </div>
      </div>
      
      <div>
        <div className="py-[2.5px] px-[15px] flex flex-row justify-between">
          <span className="font-bold pr-2">Visualization </span>
          <div className="flex items-center">
            <span className="mr-2">Filled</span>
            <Switch 
              size="small" 
              checked={visualizationMode === "outlined"}
              onChange={changeVisualizationMode} 
            />
            <span className="ml-2">Outlined</span>
          </div>
        </div>
      </div>

      <div>
        <div className="py-[2.5px] px-[15px] flex flex-row justify-center">
          <span className="font-bold">Transparency</span>
        </div>
        <div className="px-[15px] pb-2">
          <Slider
            min={10}
            max={100}
            value={Math.round(opacity * 100)}
            onChange={updateOpacity}
            disabled={visualizationMode === "outlined"}
            tooltip={{
              formatter: (value) => `${value}%`
            }}
          />
        </div>
      </div>

      <div>
        <div className="py-[2.5px] px-[15px] flex flex-row justify-center">
          <span className="font-bold">Duration</span>
        </div>
        <div className="flex flex-col">
          {Object.keys(duration).map((minute, index) => (
            <div
              key={index}
              className="flex flex-row items-center justify-between py-[2.5px] px-[15px]"
            >
              <div className="flex items-center">
                <Checkbox
                  checked={duration[minute]}
                  onChange={(e) => {
                    updatedriveTimeDuration(minute, e.target.checked);
                  }}
                >
                  <div className="flex flex-row gap-[5px] items-center">
                    {visualizationMode === "outlined" && (
                      <div 
                        className="w-3 h-3 rounded-full mr-1" 
                        style={{ 
                          backgroundColor: duration[minute] ? getDurationColor(minute) : '#ccc',
                          border: '1px solid #999'
                        }} 
                      />
                    )}
                    <span>{minute}</span>
                  </div>
                </Checkbox>
              </div>
              
              {/* Add download button when checkbox is checked */}
              {duration[minute] && (
                <Button 
                  type="primary" 
                  size="small" 
                  icon={<DownloadOutlined />} 
                  onClick={() => downloadGeoJSON(minute)}
                  disabled={!geojsonData[minute]}
                  style={{ marginLeft: '8px' }}
                >
                  Download
                </Button>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export { DriveTimeLegend };