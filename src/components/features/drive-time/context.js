import { createContext, useContext, useReducer, useMemo } from "react";
import { initialState, reducer } from "./state";

const Context = createContext(undefined);

const DriveTimeProvider = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, initialState);

  const context = useMemo(() => ({ state, dispatch }), [state, dispatch]);

  return <Context.Provider value={context}>{children}</Context.Provider>;
};

const useDriveTime = (selectorFn) => {
  const context = useContext(Context);
  if (context === undefined) {
    throw new Error("useDriveTime must be used within a DriveTime");
  }

  if (selectorFn) {
    return selectorFn(context.state);
  }

  return context;
};

export { DriveTimeProvider, useDriveTime };