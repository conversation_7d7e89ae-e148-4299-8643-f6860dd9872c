import { useState, useEffect, memo, useCallback } from "react";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";
import { useMap } from "../../Map/MapProvider";
import { useDriveTime } from "./context";
import { getDriveTimeData } from "../../../services/data";
import { default as turf_bbox } from "@turf/bbox";
import { default as turf_difference } from "@turf/difference";
import isEqual from "lodash.isequal";
import usePrevious from "../../hooks/usePrevious";
import { getDepartAtTime, getMinutes } from "./utils";
import { getDurationColor } from "./colors";

const sourceId = "driveTime";

const getFillStyle = (idx, durationKey, opacity) => {
  // Use dynamic opacity from state
  return {
    id: `${sourceId}-${idx}-Layer`,
    type: "fill",
    paint: {
      "fill-color": getDurationColor(durationKey),
      "fill-opacity": opacity,
    },
  };
};

const getOutlineLayer = (idx, durationKey) => {
  return {
    id: `${sourceId}-${idx}-LayerOutline`,
    type: "line",
    paint: {
      "line-color": getDurationColor(durationKey),
      "line-width": 2,
    },
  };
};

const DriveTimeLayer = ({ lat, lng, outlined }) => {
  const { map, activeLayers } = useMap();
  const {
    state: { time, duration, traffic, visualizationMode, opacity },
    dispatch,
  } = useDriveTime();

  const [geojsonArr, setGeojsonArr] = useState(null);
  const [durationKeys, setDurationKeys] = useState([]);
  const prevLat = usePrevious(lat);
  const prevLng = usePrevious(lng);
  const prevDuration = usePrevious(duration);

  useEffect(() => {
    if (!map) return;
    if (!lat || !lng) return;

    const fetchDriveTime = async () => {
      try {
        const minutes = Object.keys(duration).reduce((acc, curr) => {
          if (duration[curr]) {
            acc.push({ key: curr, minutes: getMinutes(curr) });
          }
          return acc;
        }, []);

        // When no durations are selected, clear the data
        if (minutes.length === 0) {
          setGeojsonArr([]);
          setDurationKeys([]);
          // Clear the geojson data in context
          dispatch({ 
            type: "UPDATE_GEOJSON", 
            payload: {} 
          });
          return;
        }

        dispatch({ type: "LOADING" });
        
        // Sort by duration ascending for processing
        const sortedMinutes = minutes.sort((a, b) => a.minutes - b.minutes);
        
        // Create a mapping object to store GeoJSON data by duration key
        const geojsonByDuration = {};
        
        const results = await Promise.all(
          sortedMinutes.map(async ({ key, minutes: mins }) => {
            const result = await getDriveTimeData({
              lat,
              lng,
              minutes: mins,
              driveTimeTraffic: traffic,
              departAt: time.active ? getDepartAtTime(time.value) : null,
            });
            
            // Store the result in our mapping object
            geojsonByDuration[key] = result;
            
            return { key, result };
          })
        );
        
        // Create layered zones by subtracting smaller zones from larger ones
        const layeredZones = [];
        for (let i = 0; i < results.length; i++) {
          const currentResult = results[i];
          let currentZone = currentResult.result;
          
          // Subtract all smaller zones from the current zone
          for (let j = 0; j < i; j++) {
            try {
              currentZone = turf_difference(currentZone, results[j].result);
            } catch (error) {
              console.warn('Turf difference operation failed:', error);
              // If difference fails, use the original zone
            }
          }
          
          if (currentZone) {
            layeredZones.push(currentZone);
          }
        }
        
        // Update the context with the GeoJSON data for each duration
        dispatch({ 
          type: "UPDATE_GEOJSON", 
          payload: geojsonByDuration 
        });
        
        dispatch({ type: "LOADED" });

        // Handle map bounds
        if (
          prevLat != lat ||
          prevLng != lng ||
          !isEqual(prevDuration, duration)
        ) {
          if (layeredZones.length > 0) {
            map.fitBounds(turf_bbox(layeredZones[layeredZones.length - 1]), {
              padding: 32,
            });
          }
        }

        setGeojsonArr(layeredZones);
        setDurationKeys(sortedMinutes.map(item => item.key));
      } catch (error) {
        console.error(error);
        setGeojsonArr(null);
      }
    };

    fetchDriveTime();
  }, [map, time, duration, traffic, lat, lng, dispatch]);

  const showOutlined = useCallback(
    () => 
      visualizationMode === "outlined" || 
      activeLayers.some((layer) =>
        ["submarket", "demographics", "heatmap-demographics"].includes(layer)
      ),
    [activeLayers, visualizationMode]
  );

  if (!geojsonArr || geojsonArr.length === 0) return null;
  return (
    <ISOLayers 
      map={map} 
      geojsonArr={geojsonArr} 
      outlined={showOutlined()} 
      visualizationMode={visualizationMode}
      durationKeys={durationKeys}
      opacity={opacity}
    />
  );
};

const ISOLayers = memo(
  ({ map, geojsonArr, outlined, visualizationMode, durationKeys, opacity }) => {
    // Force proper layer ordering after all layers are added
    useEffect(() => {
      if (!map || !geojsonArr.length) return;
      
      // Wait for the next frame to ensure all layers are added
      requestAnimationFrame(() => {
        // Sort duration keys to get proper order (largest to smallest)
        const sortedDurationKeys = [...durationKeys].sort((a, b) => getMinutes(b) - getMinutes(a));
        
        // Find the indices in geojsonArr that correspond to each sorted duration
        const layerOrder = sortedDurationKeys.map(key => {
          const index = durationKeys.indexOf(key);
          return {
            fillLayerId: `${sourceId}-${index}-Layer`,
            outlineLayerId: `${sourceId}-${index}-LayerOutline`
          };
        });
        
        // Reorder layers - larger durations should be at the bottom, smaller at the top
        layerOrder.forEach(({ fillLayerId, outlineLayerId }) => {
          if (map.getLayer(fillLayerId)) {
            // Move layer to the top (which puts it above previously moved layers)
            map.moveLayer(fillLayerId);
          }
          if (map.getLayer(outlineLayerId)) {
            map.moveLayer(outlineLayerId);
          }
        });
      });
    }, [map, geojsonArr, durationKeys, visualizationMode]);

    // Update opacity of existing layers when opacity changes
    useEffect(() => {
      if (!map) return;
      
      geojsonArr.forEach((_, idx) => {
        const layerId = `${sourceId}-${idx}-Layer`;
        if (map.getLayer(layerId)) {
          map.setPaintProperty(layerId, 'fill-opacity', opacity);
        }
      });
    }, [map, opacity, geojsonArr]);

    // Data is already sorted correctly - render as-is
    return geojsonArr.map((geojson, idx) => {
      const durationKey = durationKeys[idx] || "";
      
      return (
        <Source
          key={`${sourceId}-${idx}`}
          id={`${sourceId}-${idx}`}
          type="geojson"
          data={geojson}
        >
          {visualizationMode === "filled" && (
            <Layer {...getFillStyle(idx, durationKey, opacity)} beforeId={"poi-label"} />
          )}
          {outlined && <Layer {...getOutlineLayer(idx, durationKey)} beforeId={"poi-label"} />}
        </Source>
      );
    });
  },
  (prevProps, nextProps) => {
    return isEqual(prevProps, nextProps);
  }
);

export { DriveTimeLayer };