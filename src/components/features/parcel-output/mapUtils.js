// Utility functions for map setup and interactions
import { CONSTANTS, STYLES } from './constants';

const {
  DRAW_SOURCE_ID,
  DRAW_LAYER_ID,
  PREVIEW_SOURCE_ID,
  PREVIEW_LAYER_ID,
  SELECTED_PARCELS_SOURCE_ID,
  SELECTED_PARCELS_LAYER_ID
} = CONSTANTS;

const {
  polygonDrawStyle,
  polygonPreviewStyle,
  selectedParcelsStyle
} = STYLES;

/**
 * Initialize GeoJSON sources for the map
 * @param {Object} map - Mapbox map instance
 */
export const initMapSources = (map) => {
  if (!map) return;
  
  // Add sources to map if they don't exist
  if (!map.getSource(DRAW_SOURCE_ID)) {
    map.addSource(DRAW_SOURCE_ID, {
      type: "geojson",
      data: {
        type: "Feature",
        geometry: {
          type: "LineString",
          coordinates: []
        }
      }
    });
  }
  
  if (!map.getSource(PREVIEW_SOURCE_ID)) {
    map.addSource(PREVIEW_SOURCE_ID, {
      type: "geojson",
      data: {
        type: "Feature",
        geometry: {
          type: "Polygon",
          coordinates: [[]]
        }
      }
    });
  }
  
  if (!map.getSource(SELECTED_PARCELS_SOURCE_ID)) {
    map.addSource(SELECTED_PARCELS_SOURCE_ID, {
      type: "geojson",
      data: {
        type: "FeatureCollection",
        features: []
      }
    });
  }
};

/**
 * Setup map layers for drawing and displaying parcels
 * @param {Object} map - Mapbox map instance
 */
export const setupMapLayers = (map) => {
  if (!map) return;
  
  // Add layers if they don't exist
  if (!map.getLayer(DRAW_LAYER_ID)) {
    map.addLayer({
      id: DRAW_LAYER_ID,
      type: "line",
      source: DRAW_SOURCE_ID,
      paint: polygonDrawStyle.paint
    });
  }
  
  if (!map.getLayer(PREVIEW_LAYER_ID)) {
    map.addLayer({
      id: PREVIEW_LAYER_ID,
      type: "fill",
      source: PREVIEW_SOURCE_ID,
      paint: polygonPreviewStyle.paint
    });
  }
  
  if (!map.getLayer(SELECTED_PARCELS_LAYER_ID)) {
    map.addLayer({
      id: SELECTED_PARCELS_LAYER_ID,
      type: "fill",
      source: SELECTED_PARCELS_SOURCE_ID,
      paint: selectedParcelsStyle.paint
    });
  }
};