/**
 * Escape special XML characters to ensure valid KML
 * @param {string} str - String to escape
 * @returns {string} - Escaped string
 */
const escapeXML = (str) => {
  if (str === null || str === undefined) return "";
  return String(str)
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&apos;");
};

/**
 * Export parcels to KML format and trigger download
 * @param {Array} parcels - Array of GeoJSON features to export
 */
export const exportToKML = (parcels) => {
  console.log("exportToKML called with:", parcels);

  // Check if parcels is valid
  if (!parcels) {
    console.error("Parcels is undefined or null");
    throw new Error("No valid parcels to export");
  }

  if (!Array.isArray(parcels)) {
    console.error("Parcels is not an array, type is:", typeof parcels);
    if (parcels.type === "FeatureCollection" && Array.isArray(parcels.features)) {
      console.log("Converting FeatureCollection to array of features");
      parcels = parcels.features;
    } else {
      throw new Error("No valid parcels to export");
    }
  }

  if (parcels.length === 0) {
    console.error("Parcels array is empty");
    throw new Error("No valid parcels to export");
  }

  console.log(`Processing ${parcels.length} parcels for KML export`);

  // Start KML document
  let kml = `<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
<Document>
  <name>Exported Parcels</name>
  <Style id="parcelStyle">
    <LineStyle>
      <color>ff0000ff</color>
      <width>2</width>
    </LineStyle>
    <PolyStyle>
      <color>7f0000ff</color>
      <outline>1</outline>
    </PolyStyle>
  </Style>`;

  // Count of successful parcels
  let successCount = 0;

  // Add each parcel as a Placemark
  parcels.forEach((parcel, index) => {
    try {
      if (!parcel || !parcel.geometry) {
        console.warn(`Skipping parcel ${index}: Missing geometry`);
        return;
      }

      const properties = parcel.properties || {};

      // Create a name for the placemark (escape XML characters)
      const name = escapeXML(properties.owner || `Parcel ${index + 1}`);

      // Create description with all properties (escape XML characters)
      let description = "<![CDATA[<table>";
      for (const [key, value] of Object.entries(properties)) {
        if (value !== null && value !== undefined) {
          description += `<tr><td>${escapeXML(key)}</td><td>${escapeXML(value)}</td></tr>`;
        }
      }
      description += "</table>]]>";

      // Ensure valid coordinates
      if (
        !parcel.geometry.coordinates ||
        !Array.isArray(parcel.geometry.coordinates) ||
        parcel.geometry.coordinates.length === 0
      ) {
        console.warn(`Skipping parcel ${index}: Invalid coordinates`);
        return;
      }

      let coordinates;

      // Handle different geometry types
      if (parcel.geometry.type === "Polygon") {
        if (!Array.isArray(parcel.geometry.coordinates[0])) {
          console.warn(`Skipping parcel ${index}: Invalid Polygon coordinates format`);
          return;
        }

        coordinates = parcel.geometry.coordinates[0]
          .map((coord) => `${coord[0]},${coord[1]},0`)
          .join(" ");
      } else if (parcel.geometry.type === "MultiPolygon") {
        if (
          !Array.isArray(parcel.geometry.coordinates[0]) ||
          !Array.isArray(parcel.geometry.coordinates[0][0])
        ) {
          console.warn(`Skipping parcel ${index}: Invalid MultiPolygon coordinates format`);
          return;
        }

        // Just use the first polygon for MultiPolygon
        coordinates = parcel.geometry.coordinates[0][0]
          .map((coord) => `${coord[0]},${coord[1]},0`)
          .join(" ");
      } else {
        console.warn(`Skipping parcel ${index}: Unsupported geometry type: ${parcel.geometry.type}`);
        return;
      }

      // Add placemark to KML
      kml += `
  <Placemark>
    <name>${name}</name>
    <description>${description}</description>
    <styleUrl>#parcelStyle</styleUrl>
    <Polygon>
      <outerBoundaryIs>
        <LinearRing>
          <coordinates>${coordinates}</coordinates>
        </LinearRing>
      </outerBoundaryIs>
    </Polygon>
  </Placemark>`;

      successCount++;
    } catch (error) {
      console.error(`Error processing parcel ${index}:`, error);
    }
  });

  // Close KML document
  kml += `
</Document>
</kml>`;

  console.log(`Successfully processed ${successCount} parcels for KML export`);

  // Create a download link
  const blob = new Blob([kml], { type: "application/vnd.google-earth.kml+xml" });
  const url = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = `Exported_Parcels_${new Date().toISOString().slice(0, 10)}.kml`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  return { kml, count: successCount };
};