// Utility functions for managing UI elements during drawing mode

/**
 * Get elements that need to be hidden/shown during drawing mode
 * @returns {Array} Array of DOM elements
 */
export const getElements = () => {
  return [
    document.querySelector("#MapExpander"),
    document.querySelector("#MapZoom"),
    document.querySelector("#MapToImageDownload"),
    document.querySelector("#MapTilt"),
    document.querySelector("#MapRuler"),
    document.querySelector("#MapTheme"),
    document.querySelector("#MapLayers"),
    document.querySelector("#radiusSelectWrapper"),
    document.querySelector("#MapNavigation"),
    document.querySelector("#MapSentinelLayerControl"),
    document.querySelector("#PropertyParcelLegend"),
    document.querySelector("#AttendanceZoneLegend"),
    document.querySelector("#ActivityCenterLegend"),
    document.querySelector("#FloodZoneLegend"),
    document.querySelector("#ChainLocationLegend"),
  ];
};

/**
 * Hide screen elements when drawing mode is active
 */
export const hideScreenElements = () => {
  const elements = getElements();

  if (!elements || elements.length === 0) return;

  for (let i = 0; i < elements.length; i++) {
    if (!elements[i]) continue;
    elements[i].style.display = "none";
  }

  const topRightControl = document.querySelector("#TopRightControl");
  if (topRightControl) {
    topRightControl.style.position = "static";
  }
};

/**
 * Show screen elements when drawing mode is inactive
 */
export const showScreenElements = () => {
  const elements = getElements();

  if (!elements || elements.length === 0) return;

  for (let i = 0; i < elements.length; i++) {
    if (!elements[i]) continue;
    elements[i].style.display = "";
  }

  const topRightControl = document.querySelector("#TopRightControl");
  if (topRightControl) {
    topRightControl.style.position = "absolute";
  }
};