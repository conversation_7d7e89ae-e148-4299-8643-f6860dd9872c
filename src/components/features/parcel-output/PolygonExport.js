import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Button, Modal, Spin, message } from "antd";
import { FaFileExport } from "@react-icons/all-files/fa/FaFileExport";
import * as turf from "@turf/turf";
import styles from "../../Map/MapControls/MapDraw/mapDraw.module.css";
import Draggable from "react-draggable";
// Import components and utilities
import { CONSTANTS, STYLES } from "./constants";
import { showScreenElements, getElements } from "./uiUtils";
import { initMapSources, setupMapLayers } from "./mapUtils";
import { exportToKML } from "./exportUtils";

const {
  DRAW_SOURCE_ID,
  DRAW_LAYER_ID,
  PREVIEW_SOURCE_ID,
  PREVIEW_LAYER_ID,
  SELECTED_PARCELS_SOURCE_ID,
  SELECTED_PARCELS_LAYER_ID,
} = CONSTANTS;

export const PolygonExport = () => {
  const dispatch = useDispatch();
  const map = useSelector((state) => state.Map.map);
  const serverType = useSelector((state) => state.Configure.serverType);
  const mapDrawActive = useSelector((state) => state.Map.drawingMode);
  const parcelOutputMode = useSelector((state) => state.Map.parcelOutputMode);

  const [drawingMode, setDrawingMode] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const [selectedParcels, setSelectedParcels] = useState([]);
  const [warningMessage, setWarningMessage] = useState(null);
  const [parcelCount, setParcelCount] = useState(null);
  
  // New state for area calculations
  const [totalArea, setTotalArea] = useState({
    squareFeet: 0,
    acres: 0
  });

  // Draggable modal states
  const [disabled, setDisabled] = useState(true);
  const [bounds, setBounds] = useState({
    left: 0,
    top: 0,
    bottom: 0,
    right: 0
  });
  const draggleRef = useRef(null);

  const polygonCoordinates = useRef([]);
  const drawingPolygon = useRef(null);
  const mousePositionRef = useRef(null);
  const parcelSourceLayer = useRef("c9320a846de802ef7936b0b9968f0cda2f240355");
  const token = useRef(null);

  // Handle modal draggable interactions
  const onStart = (_event, uiData) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    if (!targetRect) {
      return;
    }
    setBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };

  // Initialize map sources and event handlers
  useEffect(() => {
    if (!map) return;

    const initialize = async () => {
      // Get user token
      const userToken = await window.spatiallaser.getUserToken("access");
      token.current = userToken;

      // Initialize map sources and layers
      initMapSources(map);
      setupMapLayers(map);
    };

    initialize();

    // Function to handle mousedown event
    const handleMouseDown = () => {
      if (!drawingMode) return;

      // Reset current line when starting a new drawing
      if (polygonCoordinates.current.length !== 0) {
        polygonCoordinates.current = [];
      }

      // Enable mouse move tracking during drag
      map.on("mousemove", mapDraw);

      // When mouse is released, complete the dragging
      map.once("mouseup", () => {
        map.off("mousemove", mapDraw);

        if (drawingMode && polygonCoordinates.current.length > 2) {
          // Close the polygon by adding the first point
          const completedPolygon = [...polygonCoordinates.current, polygonCoordinates.current[0]];

          polygonCoordinates.current = completedPolygon;
          updateDrawPolygon();
          completePolygon();
        }
      });
    };
    
    // Add mousedown handler
    map.on("mousedown", handleMouseDown);

    const onKeyDown = (e) => {
      if (!drawingMode) return;

      if (e.key === "Escape") {
        // Cancel drawing
        cancelDrawing();
      }
    };

    document.addEventListener("keydown", onKeyDown);

    // Handle mapDraw.enter event - disable our tool when MapDraw is activated
    const handleMapDrawEnter = () => {
      // If MapDraw is activated, make sure our tool is deactivated
      if (drawingMode) {
        cancelDrawing();
      }
      
      // Also close modal if it's open
      if (modalVisible) {
        setModalVisible(false);
      }
      
      // Reset any in-progress drawing
      resetDrawing();
    };
    
    const handleActivatePolygonExport = () => {
      // Check if the original map draw feature is active
      if (mapDrawActive) {
        message.info("Please finish or cancel your current drawing first");
        return;
      }
      
      // Update parcelOutputMode to true in Redux store
      dispatch({
        type: "Map/saveState",
        payload: {
          parcelOutputMode: true,
        },
      });
      
      // Notify mapDraw.exit to ensure we don't interfere with other drawing tools
      map.fire("mapDraw.exit");
      
      resetDrawing();
      setDrawingMode(true);
      document
      .getElementById("mapContainer")
      ?.classList.add("[&>div.mapboxgl-popup]:!hidden");
      disableMapInteractions();
      message.info("Draw a shape to select parcels by dragging on the map");
      
      // Dispatch event that other map features can listen for
      const event = new CustomEvent("exportDrawingStarted", { detail: { isActive: true } });
      window.dispatchEvent(event);
      
      // Hide screen elements when drawing mode is active but don't hide the map draw button
      hideScreenElementsExceptDraw();
    };
    
    // Listen for custom events from MapDraw
    map.on("mapDraw.enter", handleMapDrawEnter);
    
    // Listen for the custom event to activate polygon export
    map.on("polygonExport.activate", handleActivatePolygonExport);

    // Cleanup all event listeners
    return () => {
      map.off("mousedown", handleMouseDown);
      map.off("mousemove", mapDraw);
      map.off("mapDraw.enter", handleMapDrawEnter);
      map.off("polygonExport.activate", handleActivatePolygonExport);
      document.removeEventListener("keydown", onKeyDown);
    };
  }, [map, drawingMode, modalVisible, mapDrawActive]);

  const mapDraw = (e) => {
    if (drawingMode) {
      polygonCoordinates.current = [...polygonCoordinates.current, [e.lngLat.lng, e.lngLat.lat]];

      updateDrawPolygon();
    }
  };

  // Update the drawing line
  const updateDrawPolygon = () => {
    if (!map) return;

    const source = map.getSource(DRAW_SOURCE_ID);
    if (source) {
      source.setData({
        type: "Feature",
        geometry: {
          type: "LineString",
          coordinates: polygonCoordinates.current,
        },
      });
    }
  };

  const completePolygon = async () => {
    if (polygonCoordinates.current.length < 3) {
      message.warning("Please draw a shape with at least three points");
      return;
    }

    // Close the shape by adding the first point if it's not already closed
    const coordinates = [...polygonCoordinates.current];
    const firstPoint = coordinates[0];
    const lastPoint = coordinates[coordinates.length - 1];

    // Only add closing point if not already closed
    if (firstPoint[0] !== lastPoint[0] || firstPoint[1] !== lastPoint[1]) {
      coordinates.push([...firstPoint]);
    }

    // Create turf polygon from the coordinates
    const shape = turf.polygon([coordinates]);

    // Check area constraints
    let area = 0;
    try {
      area = turf.area(shape);
    } catch (e) {
      console.log(e);
      return;
    }

    // 10 miles squared (matching your MapDraw constraint)
    const maxArea = 10 * 1609.34 * (10 * 1609.34);

    if (area > maxArea) {
      const mileSquared = Math.sqrt(area) / 1609.34;
      const msg = (
        <span>
          Max area is 10 mi<sup>2</sup>. {`Your selection was ${mileSquared.toFixed(2)}`} mi
          <sup>2</sup>.
        </span>
      );
      setWarningMessage(msg);
      setTimeout(() => {
        setWarningMessage(null);
      }, 2500);
      return;
    }

    // Store the drawn shape
    drawingPolygon.current = shape;

    // Exit drawing mode
    setDrawingMode(false);

    // Re-enable map interactions
    enableMapInteractions();

    // Show the modal
    setModalVisible(true);
  };

  // Calculate total area of selected parcels
  const calculateTotalArea = (parcels) => {
    let totalSquareFeet = 0;
    let validParcelCount = 0;

    parcels.forEach(parcel => {
      try {
        // Calculate area using turf.js
        const area = turf.area(parcel);
        
        // Convert from square meters to square feet (1 sq meter = 10.764 sq feet)
        const sqFeet = area * 10.764;
        
        totalSquareFeet += sqFeet;
        validParcelCount++;
      } catch (error) {
        console.error("Error calculating area for parcel:", error);
      }
    });

    // Convert square feet to acres (1 acre = 43,560 sq feet)
    const acres = totalSquareFeet / 43560;

    console.log(`Calculated area for ${validParcelCount} parcels: ${totalSquareFeet.toFixed(2)} sq ft, ${acres.toFixed(2)} acres`);
    
    return {
      squareFeet: totalSquareFeet,
      acres: acres
    };
  };

  // Query for parcels that are completely contained within the drawn shape
  const queryParcelsInShape = async () => {
    if (!map || !drawingPolygon.current) {
      message.error("No shape has been drawn");
      return;
    }

    setLoading(true);

    try {
      // Get current viewport bounds to limit the query
      const bounds = map.getBounds();

      console.log("Querying parcels within the drawn shape");

      // Looking for all parcel-related layers by checking all available layers
      const allLayers = map.getStyle().layers;
      const parcelLayers = allLayers
        .filter((layer) => {
          const id = layer.id.toLowerCase();
          return id.includes("parcel") || id.includes("regrid");
        })
        .map((layer) => layer.id);

      console.log("Found parcel layers:", parcelLayers);

      if (parcelLayers.length === 0) {
        console.warn("No parcel layers found in the map");
        message.warning("No parcel layers found in the map. Make sure parcels are visible.");
        setLoading(false);
        return;
      }

      // Get all parcels currently rendered within the bounds
      const features = map.queryRenderedFeatures(
        [map.project(bounds.getSouthWest()), map.project(bounds.getNorthEast())],
        {
          layers: parcelLayers, // Use all detected parcel layers
        }
      );

      console.log(`Found ${features.length} features in the viewport`);

      // Filter features that are completely contained within our shape (not just intersecting)
      const containedFeatures = [];

      // Set to track unique parcel IDs to avoid duplicates
      const uniqueParcelIds = new Set();

      for (const feature of features) {
        try {
          // Convert tile feature to GeoJSON if needed
          let geojson = feature;

          if (feature._vectorTileFeature) {
            const { _x, _y, _z } = feature;
            geojson = feature._vectorTileFeature.toGeoJSON(_x, _y, _z);
          }

          // Check if the parcel is completely contained within the shape
          if (turf.booleanContains(drawingPolygon.current, geojson)) {
            // Extract a unique identifier for the parcel
            // Look for common property IDs used in parcel data
            const properties = geojson.properties || {};
            const parcelId =
              properties.ll_uuid || // Regrid UUID
              properties.parcelnumb || // Parcel number
              properties.ogc_fid || // Object ID
              properties.id || // Generic ID
              JSON.stringify(geojson.geometry.coordinates[0][0]); // Use first coordinate as fallback ID

            // Only add if we haven't seen this parcel before
            if (!uniqueParcelIds.has(parcelId)) {
              uniqueParcelIds.add(parcelId);
              containedFeatures.push(geojson);
            }
          }
        } catch (error) {
          console.error("Error processing feature:", error);
        }
      }

      console.log(`Found ${containedFeatures.length} unique parcels completely inside the shape`);

      // Update selected parcels
      setSelectedParcels(containedFeatures);
      setParcelCount(containedFeatures.length);
      
      // Calculate and set total area
      const areaData = calculateTotalArea(containedFeatures);
      setTotalArea(areaData);

      // Update the selected parcels source
      const source = map.getSource(SELECTED_PARCELS_SOURCE_ID);
      if (source) {
        source.setData({
          type: "FeatureCollection",
          features: containedFeatures,
        });
      }

      // Show success or warning message
      if (containedFeatures.length > 0) {
        message.success(
          `Found ${containedFeatures.length} unique parcels completely inside your selection`
        );
      } else {
        message.warning(
          "No parcels found completely inside your selection. Try adjusting your shape or zoom level."
        );
      }
    } catch (error) {
      console.error("Error querying parcels:", error);
      message.error("Failed to query parcels: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Handle export KML button click
  const handleExportKML = () => {
    if (selectedParcels.length === 0) {
      message.info("No parcels to export");
      return;
    }

    setLoading(true);

    try {
      // Make sure we're passing an array to exportToKML
      if (!Array.isArray(selectedParcels)) {
        console.error("selectedParcels is not an array:", selectedParcels);
        message.error("Invalid parcel data format. Please try again.");
        setLoading(false);
        return;
      }

      console.log("Exporting parcels:", selectedParcels);
      exportToKML(selectedParcels);
      message.success(`Successfully exported ${selectedParcels.length} parcels`);
    } catch (error) {
      console.error("Error exporting to KML:", error);
      message.error("Failed to export parcels: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Reset drawing state
  const resetDrawing = () => {
    polygonCoordinates.current = [];
    drawingPolygon.current = null;

    if (map) {
      // Reset draw source
      const drawSource = map.getSource(DRAW_SOURCE_ID);
      if (drawSource) {
        drawSource.setData({
          type: "Feature",
          geometry: {
            type: "LineString",
            coordinates: [],
          },
        });
      }

      // Reset preview source
      const previewSource = map.getSource(PREVIEW_SOURCE_ID);
      if (previewSource) {
        previewSource.setData({
          type: "Feature",
          geometry: {
            type: "Polygon",
            coordinates: [[]],
          },
        });
      }

      // Reset selected parcels source
      const selectedParcelsSource = map.getSource(SELECTED_PARCELS_SOURCE_ID);
      if (selectedParcelsSource) {
        selectedParcelsSource.setData({
          type: "FeatureCollection",
          features: [],
        });
      }
    }
  };
  useEffect(() => {
    // If parcelOutputMode is turned off from outside this component
    if (!parcelOutputMode && drawingMode) {
      // Cancel any active drawing
      resetDrawing();
      setDrawingMode(false);
      enableMapInteractions();
      
      // Show screen elements when drawing mode is inactive
      showScreenElements();
    }
    
    // If parcelOutputMode is turned on from outside this component
    if (parcelOutputMode && !drawingMode && !modalVisible && map) {
      // If MapDraw is active, don't start
      if (mapDrawActive) {
        // Reset parcelOutputMode back to false
        dispatch({
          type: "Map/saveState",
          payload: {
            parcelOutputMode: false,
          },
        });
        return;
      }
      
      // Start drawing mode
      resetDrawing();
      setDrawingMode(true);
      disableMapInteractions();
      message.info("Draw a shape to select parcels by dragging on the map");
      
      // Hide screen elements when drawing mode is active
      hideScreenElementsExceptDraw();
    }
  }, [parcelOutputMode, drawingMode, modalVisible, mapDrawActive]);
  const handleCancel = () => {
    // Close the modal
    setModalVisible(false);
    
    // Reset drawing
    resetDrawing();
    
    // Clear selected parcels
    setSelectedParcels([]);
    setParcelCount(null);
    
    // Reset area calculation
    setTotalArea({
      squareFeet: 0,
      acres: 0
    });
    
    // Clear the selected parcels source
    if (map) {
      const source = map.getSource(SELECTED_PARCELS_SOURCE_ID);
      if (source) {
        source.setData({
          type: "FeatureCollection",
          features: [],
        });
      }
    }
    
    // Set parcelOutputMode to false when closing the modal
    dispatch({
      type: "Map/saveState",
      payload: {
        parcelOutputMode: false,
      },
    });
    
    // Make sure map interactions are enabled
    enableMapInteractions();
    
    // Show all other UI elements that might have been hidden
    showScreenElements();
    
    // Dispatch event that other map features can listen for
    const event = new CustomEvent("exportDrawingEnded", { detail: { isActive: false } });
    window.dispatchEvent(event);
    
    // Reset any other state as needed
    drawingPolygon.current = null;
  };

  // Enter drawing mode
  const enterDrawingMode = (e) => {
    e.preventDefault();
    
    // Check if the original map draw feature is active
    if (mapDrawActive) {
      message.info("Please finish or cancel your current drawing first");
      return;
    }
    
    // Notify mapDraw.exit to ensure we don't interfere with other drawing tools
    if (map) {
      map.fire("mapDraw.exit");
    }
    
    resetDrawing();
    setDrawingMode(true);
    document
    .getElementById("mapContainer")
    ?.classList.add("[&>div.mapboxgl-popup]:!hidden");
    disableMapInteractions();
    message.info("Draw a shape to select parcels by dragging on the map");
    
    // Dispatch event that other map features can listen for
    const event = new CustomEvent("exportDrawingStarted", { detail: { isActive: true } });
    window.dispatchEvent(event);
    
    // Hide screen elements when drawing mode is active 
    // But don't hide the original map draw button
    hideScreenElementsExceptDraw();
  };

  // Hide all screen elements except the map draw button
  const hideScreenElementsExceptDraw = () => {
    const elements = getElements();
  
    if (!elements || elements.length === 0) return;
  
    for (let i = 0; i < elements.length; i++) {
      // Skip hiding the map draw control
      if (elements[i] && elements[i].id !== "mapDrawControl") {
        elements[i].style.display = "none";
      }
    }
  
    const topRightControl = document.querySelector("#TopRightControl");
    if (topRightControl) {
      topRightControl.style.position = "static";
    }
  };

  const exitDrawingMode = () => {
    setDrawingMode(false);
    enableMapInteractions();
  
    // Set parcelOutputMode to false when exiting drawing mode
    dispatch({
      type: "Map/saveState",
      payload: {
        parcelOutputMode: false,
      },
    });
    document
      .getElementById("mapContainer")
      ?.classList.remove("[&>div.mapboxgl-popup]:!hidden");
      
    // Dispatch event that other map features can listen for
    const event = new CustomEvent("exportDrawingEnded", { detail: { isActive: false } });
    window.dispatchEvent(event);
  
    // Show screen elements when drawing mode is inactive
    showScreenElements();
  };

  const cancelDrawing = () => {
    resetDrawing();
    exitDrawingMode(); // This will also set parcelOutputMode to false
    
    // Also ensure modal is closed
    if (modalVisible) {
      setModalVisible(false);
      setSelectedParcels([]);
      setParcelCount(null);
      setTotalArea({
        squareFeet: 0,
        acres: 0
      });
    }
  };

  // Disable other map interactions when drawing is active
  const disableMapInteractions = () => {
    if (!map) return;

    // Save current interaction states
    map._previousInteractionsExport = {
      dragPan: map.dragPan && map.dragPan.isEnabled(),
      scrollZoom: map.scrollZoom && map.scrollZoom.isEnabled(),
      doubleClickZoom: map.doubleClickZoom && map.doubleClickZoom.isEnabled(),
    };

    // Disable map interactions
    map.dragPan.disable();
    map.scrollZoom.disable();
    map.doubleClickZoom.disable();
    map.getCanvas().style.cursor = "crosshair";
  };

  // Re-enable map interactions when drawing is complete
  const enableMapInteractions = () => {
    if (!map || !map._previousInteractionsExport) return;

    const { dragPan, scrollZoom, doubleClickZoom } = map._previousInteractionsExport;

    // Restore previous interaction states
    if (dragPan) map.dragPan.enable();
    if (scrollZoom) map.scrollZoom.enable();
    if (doubleClickZoom) map.doubleClickZoom.enable();
    map.getCanvas().style.cursor = "";

    // Clean up
    delete map._previousInteractionsExport;
  };

  return (
    <>
      {drawingMode ? (
        <>
          {warningMessage && <div className={styles.warningMessageContainer}>{warningMessage}</div>}
          <div className={styles.drawBarMenuContainer}>
            <div className={styles.drawMenuLabel}>
              <p>Draw a shape to select parcels for export.</p>
            </div>

            <div className={styles.drawMenuBtnContainer}>
              <div className={styles.cancelBtnContainer}>
                <button onClick={cancelDrawing}>Cancel</button>
              </div>
              <div className={styles.applyBtnContainer}>
                <button onClick={completePolygon}>Complete</button>
              </div>
            </div>
          </div>
        </>
      ) : null}

      {/* Modal for exporting parcels */}
      <Modal
        title={
          <div
            style={{ width: '100%', cursor: 'move' }}
            onMouseOver={() => {
              if (disabled) {
                setDisabled(false);
              }
            }}
            onMouseOut={() => {
              setDisabled(true);
            }}
            // fix eslintjsx-a11y/mouse-events-have-key-events
            onFocus={() => {}}
            onBlur={() => {}}
          >
            Export Parcels
          </div>
        }
        open={modalVisible}
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            Cancel
          </Button>,
          <Button
            key="export"
            type="primary"
            loading={loading}
            onClick={handleExportKML}
            disabled={selectedParcels.length === 0 || loading}
          >
            Export to KML
          </Button>,
        ]}
        modalRender={(modal) => (
          <Draggable
            disabled={disabled}
            bounds={bounds}
            nodeRef={draggleRef}
            onStart={(event, uiData) => onStart(event, uiData)}
          >
            <div ref={draggleRef}>{modal}</div>
          </Draggable>
        )}
      >
        {loading ? (
          <div style={{ textAlign: "center", padding: "20px" }}>
            <Spin size="large" />
            <p style={{ marginTop: "10px" }}>Processing parcels...</p>
          </div>
        ) : (
          <div>
            {parcelCount === null ? (
              <div style={{ textAlign: "center", padding: "20px" }}>
                <p>Click "Find Parcels" to identify parcels within your drawn area.</p>
                <Button type="primary" onClick={queryParcelsInShape} style={{ marginTop: "10px" }}>
                  Find Parcels
                </Button>
              </div>
            ) : (
              <>
                <p>
                  {parcelCount > 0
                    ? `Found ${parcelCount} parcels within your drawn shape.`
                    : "No parcels found within your drawn shape."}
                </p>
                
                {/* Display area information */}
                {parcelCount > 0 && (
                  <div style={{ margin: "15px 0", padding: "10px", backgroundColor: "#f5f5f5", borderRadius: "4px" }}>
                    <h4 style={{ marginTop: 0 }}>Total Area</h4>
                    <p style={{ margin: "5px 0" }}>
                      <strong>Square Feet:</strong> {totalArea.squareFeet.toLocaleString(undefined, {maximumFractionDigits: 0})}
                    </p>
                    <p style={{ margin: "5px 0" }}>
                      <strong>Acres:</strong> {totalArea.acres.toLocaleString(undefined, {maximumFractionDigits: 2})}
                    </p>
                  </div>
                )}
                
                <p>
                  Click "Export to KML" to download a KML file containing these parcels that can be
                  imported into Google Earth or other GIS software.
                </p>
              </>
            )}
          </div>
        )}
      </Modal>
    </>
  );
};