import React, { useRef, useState, useEffect } from "react";
import { <PERSON><PERSON>, Modal, Select } from "antd";
import Draggable from "react-draggable";
import GentrifyingSelect from "./GentrifyingSelect";
import GentrifyingTable from "./GentrifyingTable";
import { useDispatch } from "react-redux";
export const GentrifyingMenu = ({ onClose }) => {
  const dispatch = useDispatch()
  const [open, setOpen] = useState(true);
  const [disabled, setDisabled] = useState(true);

  const [bounds, setBounds] = useState({
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  });
  const draggleRef = useRef(null);
  const onStart = (_event, uiData) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    if (!targetRect) {
      return;
    }
    setBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };

  
  return (
    <>
      {/* <Modal
        title={
          <div
            style={{
              width: '100%',
              cursor: 'move',
            }}
          >
            Draggable Modal
          </div>
        }
        open={open}
        onOk={() => onClose()}
        onCancel={() => onClose()}
        modalRender={(modal) => (
          <Draggable
            disabled={disabled}
            bounds={bounds}
            nodeRef={draggleRef}
            onStart={(event, uiData) => onStart(event, uiData)}
          >
            <div ref={draggleRef}>{modal}</div>
          </Draggable>
        )}
      >
        <p>
          Just don&apos;t learn physics at school and your life will be full of magic and miracles.
        </p>
        <br />
        <p>Day before yesterday I saw a rabbit, and yesterday a deer, and today, you.</p>
      </Modal> */}
      <Modal
        title={
          <div
            style={{
              width: "100%",
              cursor: "move",
            }}
            onMouseOver={() => {
              if (disabled) {
                setDisabled(false);
              }
            }}
            onMouseOut={() => {
              setDisabled(true);
            }}
            // fix eslintjsx-a11y/mouse-events-have-key-events
            // https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/master/docs/rules/mouse-events-have-key-events.md
            onFocus={() => {}}
            onBlur={() => {}}
            // end
          >
            Gentrifying Neighborhoods
          </div>
        }
        width={600}
        open={open}
        onCancel={() => {
          onClose()
          dispatch({
            type: "Map/saveState",
            payload: {
              gentrifyingFilter: [],
            },
          });
        }}
        footer={false}
        modalRender={(modal) => (
          <Draggable
            disabled={disabled}
            bounds={bounds}
            nodeRef={draggleRef}
            onStart={(event, uiData) => onStart(event, uiData)}
          >
            <div ref={draggleRef}>{modal}</div>
          </Draggable>
        )}
        mask={false}
        wrapClassName="pointer-events-none"
      >
        <div className="flex flex-col gap-[6px]">
       <GentrifyingSelect />
       {/* <GentrifyingTable /> */}
        </div>
      </Modal>
    </>
  );
};

