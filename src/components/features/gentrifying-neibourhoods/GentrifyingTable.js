import React, { useEffect, useState } from "react";
import { Table } from "antd";
import { getGentrifyingNeibourhoodsCategories } from "../../../services/data";
const columns = [
  {
    title: "Primary Category",
    dataIndex: "category",
    key: "category",
    width: '150px'
  },

  {
    title: "Action",
    dataIndex: "",
    key: "x",
    width: '50px',
    render: () => <a>Add</a>,
  },
];

const GentrifyingTable = () => {
  const [data, setData] = useState([]);
  useEffect(() => {
    const fetchMenu = async () => {
      const result = await getGentrifyingNeibourhoodsCategories();
      console.log("getGentrifyingNeibourhoodsCategories", result);
      const optionList = result.data.sort();
      const options = optionList.map((element) => {
        return {
          key: element,
          category: element,
        };
      });
      console.log("options", options);
      setData(options);
    };

    fetchMenu();
  }, []);
  return (
    <div>

        <Table
        scroll={{y: 300}}
          columns={columns}
          dataSource={data}
          size="small"
          pagination={{
            position: ["bottomRight"],
            defaultPageSize: 100
          }}
        />
    </div>
  );
};
export default GentrifyingTable;
