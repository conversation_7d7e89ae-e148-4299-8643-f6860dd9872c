import React, { useEffect, useState } from "react";
import { getGentrifyingNeibourhoodsCategories } from "../../../services/data";
import { Select } from "antd";
import { useDispatch, useSelector } from "react-redux";
const GentrifyingSelect = () => {
  const [options, setOptions] = useState([]);
  const dispatch = useDispatch();
  // Global States
  const gentrifyingFilter = useSelector((state) => state.Map.gentrifyingFilter);

  // Getting Categories from API
  useEffect(() => {
    const fetchMenu = async () => {
      const result = await getGentrifyingNeibourhoodsCategories();
      console.log("getGentrifyingNeibourhoodsCategories", result);
      const optionList = result.data.sort();
      const options = optionList.map((element) => {
        return {
          label: element,
          value: element,
        };
      });
      console.log("options", gentrifyingFilter);
      setOptions(options);
    };

    fetchMenu();
  }, []);
  const handleChange = (value) => {
    dispatch({
      type: "Map/saveState",
      payload: {
        gentrifyingFilter: value,
      },
    });
  };

  return (
    <>
      <p>Showing all categories in default, please select any category to make it more specific.</p>
      <Select
        mode="multiple"
        allowClear
        showSearch
        style={{
          width: "100%",
        }}
        placeholder="Please select"
        onChange={handleChange}
        options={options}
      />
    </>
  );
};

export default GentrifyingSelect;
