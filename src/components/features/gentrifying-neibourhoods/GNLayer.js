import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { tileURLRoot } from "../../../services/data";
import { renderToString } from "react-dom/server";
import { initPopup } from "../../Map/MapUtility/general";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";



const sourceId = "gn-source";
const sourceLayer = "chainstore_lower_income";

const getTileURL = (serverType, token) =>
  `${tileURLRoot(
    "cma",
    serverType
  )}/map/tile/chain-low-income/{z}/{x}/{y}.mvt?access_token=${token}`;

export const GNLayer = () => {
  const serverType = useSelector((state) => state.Configure.serverType);
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const gentrifyingFilter = useSelector((state) => state.Map.gentrifyingFilter);

  const [token, setToken] = useState(null);
  const [pointStyle, setPointStyle] = useState({
    id: "gn-point",
    type: "circle",
    "source-layer": sourceLayer,
    paint: {
      "circle-radius": 6,
      "circle-stroke-color": "#000",
      "circle-stroke-width": 0.5,
      "circle-color": [
        "interpolate",
        ["linear"],
        ["get", "delta_hhi"],
        -90,
        "#24703b",
        -45,
        "#31a354",
        0,
        "#e5f5e0",
      ],
    },
  });

  useEffect(() => {
    if (gentrifyingFilter.length > 0) {
      const newFilter = ["any"];
      gentrifyingFilter.forEach((element) => {
        newFilter.push(["==", ["get", "primarycat"], element]);
      });
      setPointStyle((prevStyle) => ({
        ...prevStyle,
        filter: newFilter,
      }));
    } else {
      setPointStyle((prevStyle) => {
        const newStyle = { ...prevStyle };
        delete newStyle.filter;
        return newStyle;
      });
    }
  }, [gentrifyingFilter]);

  const getTooltipHTML = (properties) => {
    return renderToString(
      <div style={{ padding: "10px", display: "flex", flexDirection: "column" }}>
        <span>Primary Category: {properties.primarycat}</span>
        <span>Chain Name: {properties.chainname}</span>
        <span>
          Address: {properties.address}, {properties.state}, {properties.postalcode}
        </span>
        <span>First Appears Date: {properties.firstappea}</span>
        <span>Delta HHI: {properties.delta_hhi}</span>
      </div>
    );
  };

  let popup = initPopup();

  const onMouseMove = (e) => {
    if (e.features.length > 0) {
      const feature = e.features[0];
      const coordinates = feature.geometry.coordinates.slice();
      const properties = feature.properties;

      while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
        coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
      }

      popup.setLngLat(coordinates).setHTML(getTooltipHTML(properties)).addTo(map);
    }
  };

  const onMouseLeave = () => {
    popup.remove();
  };

  useEffect(() => {
    if (!map) return;
    const checkForLatestToken = async () => {
      const newToken = await window.spatiallaser.getUserToken("access");
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    checkForLatestToken();
    map.on("movestart", checkForLatestToken);
    return () => {
      map.off("movestart", checkForLatestToken);
    };
  }, [map, token]);

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("gn")) return;

    map.fire("map.setThemeOption", {
      payload: { currentMapThemeOption: "Monochrome" },
    });

    map.on("mousemove", "gn-point", onMouseMove);
    map.on("mouseleave", "gn-point", onMouseLeave);

    return () => {
      map.off("mousemove", "gn-point", onMouseMove);
      map.off("mouseleave", "gn-point", onMouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!token || !currentMapLayerOptions.includes("gn")) return null;

  return (
    <>
      <Source id={sourceId} type="vector" tiles={[getTileURL(serverType, token)]}>
        <Layer {...pointStyle} />
      </Source>
    </>
  );
};
