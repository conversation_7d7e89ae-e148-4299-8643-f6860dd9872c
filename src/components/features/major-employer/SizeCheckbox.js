import React, { useEffect, useState } from "react";
import { Checkbox, Typography, Row, Col } from "antd";
import { useDispatch, useSelector } from "react-redux";
const { Title } = Typography;
const SizeCheckboxGroup = () => {
  const dispatch = useDispatch();
  // Global States
  const majorEmployerFilter = useSelector((state) => state.Map.majorEmployerFilter);

  // Getting Categories from API
  const plainOptions = ["Apple", "Pear", "Orange"];
  const handleChange = (value) => {
    console.log("value", value);
    dispatch({
      type: "Map/saveState",
      payload: {
        majorEmployerFilter: {
          category: majorEmployerFilter.category,
          size: value,
        },
      },
    });
  };

  return (
    <>
      <p className="text-center font-semibold">Employee Size</p>

      <Checkbox.Group
        style={{
          width: "100%",
        }}
        onChange={handleChange}
        value={majorEmployerFilter.size}
      >
        <Row>
          <Col span={12}>
            <Checkbox value="100 to 249">100 - 249</Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox value="250 to 499">250 - 499</Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox value="500 to 999">500 - 999</Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox value="1000 to 4999">1000 - 4999</Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox value="5000 to 9999">5000 - 9999</Checkbox>
          </Col>
          <Col span={12}>
            <Checkbox value="10000+">10000+</Checkbox>
          </Col>
        </Row>
      </Checkbox.Group>
    </>
  );
};

export default SizeCheckboxGroup;
