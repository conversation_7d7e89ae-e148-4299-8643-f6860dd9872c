import { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";
import { tileURLRoot } from "../../../services/data";
import { initPopup } from "../../Map/MapUtility/general";
import { renderToString } from "react-dom/server";

const sourceId = "major-employer-source";
const sourceLayer = "major_employer";

const getTileURL = (serverType, token) =>
  `${tileURLRoot(
    "cma",
    serverType
  )}/map/tile/major-employer/{z}/{x}/{y}.mvt?fields=location_type,location_employee_size_actual,location_employee_size_range,category,company_name,address,city,state,zip_code&access_token=${token}`;

const MajorEmployerLayer = () => {
  const dispatch = useDispatch();
  const serverType = useSelector((state) => state.Configure.serverType);
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const majorEmployerFilter = useSelector((state) => state.Map.majorEmployerFilter);
  const [token, setToken] = useState(null);
  const [pointStyle, setPointStyle] = useState({
    id: "major-employer-point",
    type: "circle",
    "source-layer": sourceLayer,
    paint: {
      "circle-radius": [
        "interpolate",
        ["linear"],
        ["log10", ["max", ["get", "location_employee_size_actual"], 1]],
        0,
        0,
        1,
        3,
        2,
        6,
        3,
        9,
        4,
        12,
        5,
        15,
        6,
        18,
        7,
        21,
        8,
        24,
        9,
        27,
        10,
        30,
      ],

      "circle-stroke-color": "#FFF",
      "circle-stroke-width": 1,
      "circle-color": "#2289c9",
    },
  });
  useEffect(() => {
    const createFilter = (type, key) => {
      const filterPart = ["any"];
      type.forEach((element) => {
        filterPart.push(["==", ["get", key], element]);
      });
      return filterPart;
    };

    const categoryFilter =
      majorEmployerFilter.category.length > 0
        ? createFilter(majorEmployerFilter.category, "category")
        : null;

    const sizeFilter =
      majorEmployerFilter.size.length > 0
        ? createFilter(majorEmployerFilter.size, "location_employee_size_range")
        : null;

    const combinedFilter = ["all"];
    if (categoryFilter) combinedFilter.push(categoryFilter);
    if (sizeFilter) combinedFilter.push(sizeFilter);

    setPointStyle((prevStyle) => ({
      ...prevStyle,
      filter: combinedFilter.length > 1 ? combinedFilter : ["any"],
    }));
  }, [majorEmployerFilter, setPointStyle]);

  const getTooltipHTML = (properties) => {
    console.log("properties",properties)
    return renderToString(
      <div style={{ padding: "10px", display: "flex", flexDirection: "column" }}>
        <span>Company Name: {properties.company_name}</span>
        <span>Location Type: {properties.location_type === 'Single Loc' ? 'Single Location' : properties.location_type}</span>
        <span>Category: {properties.category}</span>
        <span>
          Address: {properties.address}, {properties.state}, {properties.zip_code}
        </span>
        <span>Actual Employee Size: {properties.location_employee_size_actual}</span>
        
      </div>
    );
  };

  let popup = initPopup();

  const onMouseMove = (e) => {
    if (e.features.length > 0) {
      const feature = e.features[0];
      const coordinates = feature.geometry.coordinates.slice();
      const properties = feature.properties;

      while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
        coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
      }

      popup.setLngLat(coordinates).setHTML(getTooltipHTML(properties)).addTo(map);
    }
  };

  const onMouseLeave = () => {
    popup.remove();
  };

  useEffect(() => {
    if (!map) return;
    const checkForLatestToken = async () => {
      const newToken = await window.spatiallaser.getUserToken("access");
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    checkForLatestToken();
    map.on("movestart", checkForLatestToken);
    return () => {
      map.off("movestart", checkForLatestToken);
    };
  }, [map, token]);

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("major employer")) return;

    // map.fire("map.setThemeOption", {
    //   payload: { currentMapThemeOption: "Monochrome" },
    // });

    map.on("mousemove", "major-employer-point", onMouseMove);
    map.on("mouseleave", "major-employer-point", onMouseLeave);

    return () => {
      map.off("mousemove", "major-employer-point", onMouseMove);
      map.off("mouseleave", "major-employer-point", onMouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!token || !currentMapLayerOptions.includes("major employer")) return null;

  return (
    <>
      <Source id={sourceId} type="vector" tiles={[getTileURL(serverType, token)]}>
        <Layer {...pointStyle} />
      </Source>
    </>
  );
};

export { MajorEmployerLayer };
