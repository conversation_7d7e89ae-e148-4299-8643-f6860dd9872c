import React, { useEffect, useState } from "react";
import { getMajorEmployerCategories } from "../../../services/data";
import { Select } from "antd";
import { useDispatch, useSelector } from "react-redux";
const MajorEmployerSelect = () => {
  const [options, setOptions] = useState([]);
  const dispatch = useDispatch();
  // Global States
  const majorEmployerFilter = useSelector((state) => state.Map.majorEmployerFilter);

  // Getting Categories from API
  useEffect(() => {
    const fetchMenu = async () => {
      const result = await getMajorEmployerCategories();
      console.log("getGentrifyingNeibourhoodsCategories", result);
      let optionList = result.data.sort();
      // If "N/A" is found, remove it from its current position and push it to the end
      optionList = optionList.filter((option) => option !== "N/A");
      optionList.push("N/A");
      console.log("optionList", optionList);

      const options = optionList.map((element) => {
        return {
          label: element,
          value: element,
        };
      });
      console.log("options", majorEmployerFilter);
      setOptions(options);
    };

    fetchMenu();
  }, []);
  const handleChange = (value) => {
    dispatch({
      type: "Map/saveState",
      payload: {
        majorEmployerFilter: {
          category: value,
          size: majorEmployerFilter.size,
        },
      },
    });
  };

  return (
    <>
      <p className="text-center font-semibold">Category</p>
      <Select
        mode="multiple"
        allowClear
        showSearch
        style={{
          width: "100%",
        }}
        placeholder="Please select"
        onChange={handleChange}
        options={options}
      />
    </>
  );
};

export default MajorEmployerSelect;
