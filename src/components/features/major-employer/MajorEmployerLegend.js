import { LoadingOutlined } from "@ant-design/icons";
import MajorEmployerSelect from "./Select";
import SizeCheckboxGroup from "./SizeCheckbox";
import { Typography } from "antd";
const { Title } = Typography;
const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

const MajorEmployerLegend = () => {
  return (
    <div className="bg-white flex flex-col px-4 pb-6 w-[400px]">
      <Title level={5} className="text-center">
        Major Employers
      </Title>
      <MajorEmployerSelect />
      <SizeCheckboxGroup />
    </div>
  );
};

export { MajorEmployerLegend };
