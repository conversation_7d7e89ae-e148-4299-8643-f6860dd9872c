import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Select, Switch } from 'antd';
import moment from 'moment';

const { Option } = Select;

const IndustryNewsLegend = () => {
  const dispatch = useDispatch();
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const industryNewsDateFilter = useSelector((state) => state.Map.industryNewsDateFilter);

  const [showCustomRange, setShowCustomRange] = useState(
    industryNewsDateFilter?.selectedRange === 'custom'
  );

  useEffect(() => {
    setShowCustomRange(industryNewsDateFilter?.selectedRange === 'custom');
  }, [industryNewsDateFilter?.selectedRange]);

  const dateRangeOptions = [
    { label: 'All Time', value: 'all' },
    { label: 'Last 6 months', value: '6m' },
    { label: 'Last year', value: '1y' },
    { label: 'Custom Range', value: 'custom' }
  ];

  const getDateRangeFromOption = (option) => {
    const now = moment();
    switch (option) {
      case '6m':
        return { start: now.clone().subtract(6, 'months'), end: now };
      case '1y':
        return { start: now.clone().subtract(1, 'year'), end: now };
      case 'all':
      default:
        return { start: null, end: null };
    }
  };

  const handleDateRangeChange = (value) => {
    const dateRange = getDateRangeFromOption(value);
    
    dispatch({
      type: 'Map/saveState',
      payload: {
        industryNewsDateFilter: {
          ...industryNewsDateFilter,
          selectedRange: value,
          startDate: dateRange.start ? dateRange.start.format('YYYY-MM-DD') : null,
          endDate: dateRange.end ? dateRange.end.format('YYYY-MM-DD') : null,
        }
      }
    });

    setShowCustomRange(value === 'custom');
  };

  const handleCustomDateChange = (field, value) => {
    dispatch({
      type: 'Map/saveState',
      payload: {
        industryNewsDateFilter: {
          ...industryNewsDateFilter,
          [field]: value,
        }
      }
    });
  };

  const handleFilterToggle = (enabled) => {
    dispatch({
      type: 'Map/saveState',
      payload: {
        industryNewsDateFilter: {
          ...industryNewsDateFilter,
          enabled,
        }
      }
    });
  };

  if (!currentMapLayerOptions.includes('industry news') || !industryNewsDateFilter) {
    return null;
  }

  return (
    <div style={{
      backgroundColor: 'white',
      padding: '12px',
      minWidth: '280px',
      borderRadius: '4px',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '12px'
      }}>
        <h4 style={{ margin: 0, fontWeight: 'bold' }}>Industry News</h4>
        <Switch
          size="small"
          checked={industryNewsDateFilter.enabled}
          onChange={handleFilterToggle}
        />
      </div>

      <div style={{ marginBottom: '8px' }}>
        <label style={{ display: 'block', marginBottom: '4px', fontSize: '12px', color: '#666' }}>
          Date Filter
        </label>
        <Select
          value={industryNewsDateFilter.selectedRange}
          onChange={handleDateRangeChange}
          style={{ width: '100%' }}
          disabled={!industryNewsDateFilter.enabled}
        >
          {dateRangeOptions.map(option => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          ))}
        </Select>
      </div>

      {showCustomRange && industryNewsDateFilter.enabled && (
        <div style={{ marginTop: '12px' }}>
          <div style={{ marginBottom: '8px' }}>
            <label style={{ display: 'block', marginBottom: '4px', fontSize: '12px', color: '#666' }}>
              Start Date
            </label>
            <input
              type="date"
              value={industryNewsDateFilter.startDate || ''}
              onChange={(e) => handleCustomDateChange('startDate', e.target.value)}
              style={{
                width: '100%',
                padding: '4px 8px',
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
          </div>
          <div>
            <label style={{ display: 'block', marginBottom: '4px', fontSize: '12px', color: '#666' }}>
              End Date
            </label>
            <input
              type="date"
              value={industryNewsDateFilter.endDate || ''}
              onChange={(e) => handleCustomDateChange('endDate', e.target.value)}
              style={{
                width: '100%',
                padding: '4px 8px',
                border: '1px solid #d9d9d9',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
          </div>
        </div>
      )}

      <div style={{
        marginTop: '12px',
        padding: '8px',
        backgroundColor: '#f5f5f5',
        borderRadius: '4px',
        fontSize: '12px',
        color: '#666'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '4px' }}>
          <div style={{
            width: '12px',
            height: '12px',
            borderRadius: '50%',
            backgroundColor: '#1A7770',
            border: '2px solid #ffffff',
            boxShadow: '0 0 0 1px #ccc'
          }}></div>
          <span>Industry News Location</span>
        </div>
        <div style={{ fontSize: '11px', color: '#888', marginLeft: '20px' }}>
          Click markers to view news articles
        </div>
      </div>
    </div>
  );
};

export default IndustryNewsLegend; 