import { useState, useEffect, useRef } from "react";
import { renderToString } from "react-dom/server";
import { useSelector } from "react-redux";
import { getIndsutryNewsData } from "../../../services/data";
import { convertToGeoJSON } from "../../../utils/geography";
import { geojsonTemplate } from "../../../constants";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";
import mapboxgl from 'mapbox-gl';
import moment from 'moment';

const sourceId = "industry-news-source";
const pointStyle = {
  id: "industry-news-point",
  type: "circle",
  source: sourceId,
  paint: {
    "circle-radius": 6,
    "circle-color": "#1A7770",
    "circle-stroke-color": "#ffffff",
    "circle-stroke-width": 2,
    "circle-opacity": 1,
  },
  // Add hover state
  hover: {
    "circle-color": "#135c57",
    "circle-radius": 8,
  }
};

const popup = new mapboxgl.Popup({
  closeButton: false,
  closeOnClick: false,
  maxWidth: '400px',
  className: 'industry-news-popup'
});

const IndustryNewsLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const industryNewsDateFilter = useSelector((state) => state.Map.industryNewsDateFilter);
  const [geojson, setGeojson] = useState(geojsonTemplate);
  const mouseOnTopRef = useRef(false);
  const mouseOnPopupRef = useRef(false);
  const popupTimeoutRef = useRef(null);

  const getTooltipHTML = (news) => {
    return renderToString(
      <div
        style={{
          padding: "10px",
          display: "flex",
          flexDirection: "column",
          gap: "4px",
          minWidth: "200px"
        }}
      >
        {news.manufacture_name && (
          <span><strong>Manufacture Name:</strong> {news.manufacture_name}</span>
        )}
        {news.industry && (
          <span><strong>Industry:</strong> {news.industry}</span>
        )}
        {news.location && (
          <span><strong>Location:</strong> {news.location}</span>
        )}
        {(news.date || news.end_date) && (
          <span>
            <strong>Date:</strong> {news.date}{news.end_date && ' - ' + news.end_date}
          </span>
        )}
        {news.size && (
          <span><strong>Size:</strong> {news.size}</span>
        )}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', marginTop: '4px' }}>
          {news.url && (
            <a
              href={news.url}
              target="_blank"
              rel="noopener noreferrer"
              id="viewArticleButton"
              style={{
                color: "#1A7770",
                textDecoration: "none",
                padding: "6px 12px",
                backgroundColor: "#f0f0f0",
                borderRadius: "4px",
                display: "inline-block",
                cursor: "pointer",
                textAlign: "center",
                fontWeight: "500",
                border: "1px solid #1A7770",
              }}
            >
              View News Article →
            </a>
          )}
          <button
            id="closePopupButton"
            style={{
              color: "#666666",
              textDecoration: "none",
              padding: "6px 12px",
              backgroundColor: "#f0f0f0",
              borderRadius: "4px",
              display: "inline-block",
              cursor: "pointer",
              textAlign: "center",
              fontWeight: "500",
              border: "1px solid #666666",
            }}
          >
            Close
          </button>
        </div>
      </div>
    );
  };

  const clearPopupTimeout = () => {
    if (popupTimeoutRef.current) {
      clearTimeout(popupTimeoutRef.current);
      popupTimeoutRef.current = null;
    }
  };

  const filterDataByDate = (data) => {
    if (!industryNewsDateFilter || !industryNewsDateFilter.enabled) {
      return data;
    }

    const { startDate, endDate } = industryNewsDateFilter;
    
    if (!startDate && !endDate) {
      return data;
    }

    return data.filter(item => {
      // Try to parse the date field, handling various date formats
      let itemDate = null;
      
      if (item.date) {
        itemDate = moment(item.date);
      } else if (item.end_date) {
        itemDate = moment(item.end_date);
      }
      
      // If we can't parse the date, include the item
      if (!itemDate || !itemDate.isValid()) {
        return true;
      }

      // Check if the item date falls within the filter range
      const isAfterStart = !startDate || itemDate.isSameOrAfter(moment(startDate), 'day');
      const isBeforeEnd = !endDate || itemDate.isSameOrBefore(moment(endDate), 'day');
      
      return isAfterStart && isBeforeEnd;
    });
  };

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("industry news")) return;

    const moveEnd = async (e) => {
      const data = await getIndsutryNewsData();
      const filteredData = filterDataByDate(data);
      setGeojson(
        convertToGeoJSON({
          data: filteredData,
          geomAccessor: (item) => item.geom,
          propertiesAccessor: (item) => {
            const { geom, ...properties } = item;
            return {
              ...properties,
              id: `${properties.manufacture_name}-${properties.date}`
            };
          },
        })
      );
    };

    const handleClick = (e) => {
      const feature = e.features[0];
      if (feature && feature.properties) {
        // If there's a URL, open it in a new tab
        if (feature.properties.url) {
          window.open(feature.properties.url, '_blank');
        }
      }
    };

    const mouseEnter = (e) => {
      clearPopupTimeout();
      map.getCanvas().style.cursor = 'pointer';
      mouseOnTopRef.current = true;
      
      const feature = e.features[0];
      if (feature && feature.properties) {
        const coordinates = feature.geometry.coordinates.slice();
        
        while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
          coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
        }

        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);

        const popupElement = popup.getElement();
        const closeButton = popupElement.querySelector('#closePopupButton');
        const viewArticleButton = popupElement.querySelector('#viewArticleButton');

        popupElement.addEventListener('mouseenter', () => {
          clearPopupTimeout();
          mouseOnPopupRef.current = true;
        });

        popupElement.addEventListener('mouseleave', () => {
          mouseOnPopupRef.current = false;
          if (!mouseOnTopRef.current) {
            popupTimeoutRef.current = setTimeout(() => {
              if (!mouseOnTopRef.current && !mouseOnPopupRef.current) {
                popup.remove();
              }
            }, 100);
          }
        });

        if (closeButton) {
          closeButton.addEventListener('mouseenter', (e) => {
            e.target.style.backgroundColor = '#666666';
            e.target.style.color = '#ffffff';
          });

          closeButton.addEventListener('mouseleave', (e) => {
            e.target.style.backgroundColor = '#f0f0f0';
            e.target.style.color = '#666666';
          });

          closeButton.addEventListener('click', () => {
            clearPopupTimeout();
            popup.remove();
          });
        }

        if (viewArticleButton) {
          viewArticleButton.addEventListener('mouseenter', (e) => {
            e.target.style.backgroundColor = '#1A7770';
            e.target.style.color = '#ffffff';
          });

          viewArticleButton.addEventListener('mouseleave', (e) => {
            e.target.style.backgroundColor = '#f0f0f0';
            e.target.style.color = '#1A7770';
          });
        }
      }
    };

    const mouseLeave = (e) => {
      map.getCanvas().style.cursor = '';
      mouseOnTopRef.current = false;
      
      // Set a timeout before removing the popup
      if (!mouseOnPopupRef.current) {
        popupTimeoutRef.current = setTimeout(() => {
          if (!mouseOnTopRef.current && !mouseOnPopupRef.current) {
            popup.remove();
          }
        }, 100);
      }
    };

    // Close popup when clicking elsewhere on the map
    const mapClick = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: ['industry-news-point']
      });
      if (features.length === 0) {
        clearPopupTimeout();
        popup.remove();
      }
    };

    moveEnd();

    map.on("moveend", moveEnd);
    map.on("click", "industry-news-point", handleClick);
    map.on("mouseenter", "industry-news-point", mouseEnter);
    map.on("mouseleave", "industry-news-point", mouseLeave);
    map.on("click", mapClick);

    return () => {
      clearPopupTimeout();
      map.off("moveend", moveEnd);
      map.off("click", "industry-news-point", handleClick);
      map.off("mouseenter", "industry-news-point", mouseEnter);
      map.off("mouseleave", "industry-news-point", mouseLeave);
      map.off("click", mapClick);
      popup.remove();
    };
  }, [map, currentMapLayerOptions, industryNewsDateFilter]);

  if (!currentMapLayerOptions.includes("industry news")) return null;
  
  return (
    <Source id={sourceId} type="geojson" data={geojson}>
      <Layer {...pointStyle} />
    </Source>
  );
};

export default IndustryNewsLayer;