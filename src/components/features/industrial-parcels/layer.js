import React, { useEffect, useState } from "react";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";
import { initPopup } from "../../Map/MapUtility/general";
import { tileURLRoot } from "../../../services/data";
import { renderToString } from "react-dom/server";
import { useIndustrialParcel } from "./context";

const sourceId = "industrial_parcels";
const sourceLayer = sourceId;
const sourceIdClusters = "industrial_parcels_point_clusters";
const sourceLayerClusters = sourceIdClusters;

const CLUSTER_MAX_ZOOM = 9;

const getLayerStyle = (type) => {
  if (type === "point") {
    return {
      id: `${sourceId}-${type}`,
      type: "circle",
      minzoom: CLUSTER_MAX_ZOOM,
      source: sourceId,
      "source-layer": sourceLayer,
      paint: {
        "circle-radius": 6,
        "circle-color": "#5a3fc0",
      },
    };
  } else if (type === "polygon") {
    return {
      id: `${sourceId}-${type}`,
      type: "fill",
      minzoom: CLUSTER_MAX_ZOOM,
      source: sourceId,
      "source-layer": sourceLayer,
      paint: {
        "fill-color": "#5a3fc0",
        "fill-opacity": [
          "step",
          ["get", "parcel_count"],
          0.4,
          5,
          0.55,
          10,
          0.75,
          15,
          0.85,
          20,
          1,
        ],
      },
    };
  } else if (type === "cluster-circle") {
    return {
      id: `${sourceId}-${type}`,
      type: "circle",
      maxzoom: CLUSTER_MAX_ZOOM,
      source: sourceIdClusters,
      "source-layer": sourceLayerClusters,
      filter: ["has", "point_count"],
      paint: {
        "circle-color": [
          "step",
          ["get", "point_count"],
          "#f2f0f7",
          1000,
          "#cbc9e2",
          10000,
          "#9e9ac8",
          20000,
          "#6a51a3",
        ],
        "circle-radius": [
          "step",
          ["get", "point_count"],
          20,
          1000,
          30,
          10000,
          40,
          20000,
          50,
        ],
      },
    };
  } else if (type === "cluster-count") {
    return {
      id: `${sourceId}-${type}`,
      type: "symbol",
      source: sourceIdClusters,
      "source-layer": sourceLayerClusters,
      maxzoom: CLUSTER_MAX_ZOOM,
      layout: {
        "text-field": "{point_count}",
        "text-size": 20,
        "text-font": ["Arial Unicode MS Bold"],
        "text-allow-overlap": true,
      },
      paint: {
        "text-color": [
          "step",
          ["get", "point_count"],
          "black",
          1000,
          "black",
          10000,
          "white",
          20000,
          "white",
        ],
        // "text-halo-color": "black",
        "text-halo-width": 5,
        "text-halo-blur": 15,
      },
    };
  }
};

const getTileURL = (serverType, token, geom_type) =>
  `${tileURLRoot(
    "cma",
    serverType
  )}/map/tile/industrial_parcels/{z}/{x}/{y}.mvt?${
    geom_type ? `geom_type=${geom_type}&` : ""
  }access_token=${token}`;
const getTilePointClusterURL = (serverType, token) =>
  `${tileURLRoot(
    "cma",
    serverType
  )}/map/tile/clustered/industrial_parcels_point/{z}/{x}/{y}.mvt?access_token=${token}`;

const popup = initPopup();

popup.setMaxWidth("350px");

const popupHTML = (data, type) => {
  if (type === "polygon") {
    const landUseTypes = data.land_use_type.split(",");

    return renderToString(
      <div style={{ padding: "10px" }}>
        <strong>Land Use Types:</strong>
        <div style={{ display: "flex", flexDirection: "column" }}>
          {landUseTypes.map((type) => (
            <span key={type} style={{ paddingLeft: "0px" }}>
              {"- "}
              {type}
            </span>
          ))}
        </div>
        <strong>Parcel Count: </strong>
        <span>{data.parcel_count} parcels</span>
      </div>
    );
  }

  const address = `${data.formatted_street_address || "N/A"}, ${
    data.city || "N/A"
  }, ${data.state || "N/A"} ${data.zip_code || "N/A"}${
    data.zip_plus_four_code ? `-${data.zip_plus_four_code}` : ""
  }`;
  const jsx = (
    <div style={{ padding: "10px", display: "flex", flexDirection: "column" }}>
      <div style={{ display: "flex", flexDirection: "column" }}>
        <strong>Address</strong>
        <span>{address}</span>
      </div>

      <div style={{ display: "flex", flexDirection: "row", gap: "8px" }}>
        <div style={{ display: "flex", flexDirection: "column" }}>
          <strong>Category</strong>
          <span>{data.standardized_land_use_category}</span>
        </div>
        <div style={{ display: "flex", flexDirection: "column" }}>
          <strong>Type</strong>
          <span>{data.standardized_land_use_type}</span>
        </div>
      </div>

      <div style={{ display: "flex", flexDirection: "column" }}>
        <strong>Building Count</strong>
        <span>{data.building_count || "N/A"}</span>
      </div>
      <div style={{ display: "flex", flexDirection: "column" }}>
        <strong>Owner</strong>
        <span>{data.owner}</span>
      </div>
      <div style={{ display: "flex", flexDirection: "column" }}>
        <strong>Zoning</strong>
        <span>{data.zoning}</span>
      </div>
    </div>
  );

  return renderToString(jsx);
};

export const IndustrialParcelLayer = ({ map, serverType }) => {
  const [token, setToken] = useState(null);
  const { geomType } = useIndustrialParcel();

  useEffect(() => {
    if (!map) return;

    const layerId = `${sourceId}-${geomType}`;

    const mouseMove = async (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [layerId],
      });
      if (features.length > 0) {
        const feature = features[0];
        const data = feature.properties;
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        if (!data) return;

        popup
          .setLngLat(coordinates)
          .setHTML(popupHTML(data, geomType))
          .addTo(map);
      } else {
        popup.remove();
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    const checkForLatestToken = async () => {
      popup.isOpen() && popup.remove();
      const newToken = await window.spatiallaser.getUserToken("access");
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    checkForLatestToken();
    map.on("movestart", checkForLatestToken);
    map.on("mousemove", `${sourceId}-polygon`, mouseMove);
    map.on("mouseenter", layerId, mouseMove);
    map.on("mouseleave", layerId, mouseLeave);
    return () => {
      map.off("movestart", checkForLatestToken);
      map.off("mousemove", `${sourceId}-polygon`, mouseMove);
      map.off("mouseenter", layerId, mouseMove);
      map.off("mouseleave", layerId, mouseLeave);
    };
  }, [map, geomType]);

  return (
    <>
      <Source
        id={sourceId}
        type="vector"
        tiles={[getTileURL(serverType, token, geomType)]}
      >
        {geomType === "point" && <Layer {...getLayerStyle("point")} />}
        {geomType === "polygon" && <Layer {...getLayerStyle("polygon")} />}
      </Source>

      {/* <Source
        id={sourceIdClusters}
        type="vector"
        tiles={[getTilePointClusterURL(serverType, token)]}
      >
        <Layer {...getLayerStyle("cluster-circle")} />
        <Layer {...getLayerStyle("cluster-count")} />
      </Source> */}
    </>
  );
};
