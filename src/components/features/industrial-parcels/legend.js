import { useIndustrialParcel } from "./context";
import { Segmented } from "antd";

export const IndustrialParcelLegend = () => {
  const { geomType, setGeomType } = useIndustrialParcel();
  return (
    <div className="flex flex-col bg-white select-none">
      <div className="flex flex-col p-[20px]">
        <strong className="text-base">Industrial Parcels</strong>
        {geomType === "polygon" && (
          <span>
            Switch to points to more detailed view / tooltip information.
          </span>
        )}
        {geomType === "point" && (
          <span>Switch to polygons to see cleaner boundaries of parcel.</span>
        )}
      </div>
      <div>
        <Segmented
          block
          value={geomType}
          size="large"
          options={[
            {
              label: "Points",
              value: "point",
            },
            {
              label: "Polygons",
              value: "polygon",
            },
          ]}
          onChange={(value) => setGeomType(value)}
        />
      </div>
    </div>
  );
};
