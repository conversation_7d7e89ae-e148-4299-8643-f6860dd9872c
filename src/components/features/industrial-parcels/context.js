import { useState, useMemo, createContext, useContext } from "react";

const Context = createContext(undefined);

export const IndustrialParcelProvider = ({ children }) => {
  const [geomType, setGeomType] = useState("polygon");

  const context = useMemo(
    () => ({ geomType, setGeomType }),
    [geomType, setGeomType]
  );

  return <Context.Provider value={context}>{children}</Context.Provider>;
};

export const useIndustrialParcel = () => {
  const context = useContext(Context);
  if (context === undefined) {
    throw new Error(
      "useIndustrialParcel must be used within a IndustrialParcelProvider"
    );
  }
  return context;
};
