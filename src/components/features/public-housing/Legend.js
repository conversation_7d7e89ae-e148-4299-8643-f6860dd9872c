import { LoadingOutlined } from "@ant-design/icons";
import { Switch, Typography, Select } from "antd";
import { useDispatch, useSelector } from "react-redux";

const { Title } = Typography;

const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;

const PublicHousingLegend = () => {
  const dispatch = useDispatch();
  const voucherTractMode = useSelector((state) => state.Map.voucherTractMode);

  const onChange = (checked) => {
    console.log(`Switch to ${checked}`);
    dispatch({
      type: "Map/saveState",
      payload: { voucherTractMode: checked },
    });
  };
  const affordableDisplayMode = (value) => {
    console.log(`selected ${value}`);
    dispatch({
      type: "Map/saveState",
      payload: { affordableHousingMode: value },
    });
  };

  const legendItems = [
    { color: "#377eb8", label: "Assisted MultiFamily Properties" },
    { color: "#f781bf", label: "HUD Insured Multifamily Properties" },
    { color: "#4daf4a", label: "Public Housing Development" },
    { color: "#ed6b6d", label: "Public Housing Authorities" },
    { color: "#ff7f00", label: "Public Housing" },
  ];

  const affordableHousingItems = [
    { color: "#7fc97f", label: "Active" },
    { color: "#beaed4", label: "Closed Within 1 Month" },
    { color: "#fdc086", label: "Closed Within 2 Months" },
    { color: "#ffff99", label: "Closed Within 3 Months" },
  ];

  return (
    <div className="bg-white flex flex-col px-4 pb-6 w-[450px]">
      <Title level={5} className="text-center">
        Public Housing
      </Title>
      {legendItems.map((item, index) => (
        <div key={index} className="flex flex-row justify-between py-[2px] px-[10px]">
          <div
            className="w-[20px] h-[20px] rounded-full border border-solid border-black"
            style={{ backgroundColor: item.color }}
          ></div>
          <div>
            <span>{item.label}</span>
          </div>
        </div>
      ))}
      <p style={{ textAlign: "center", fontSize: "16px" }}>Layer Options</p>
      <div className="flex flex-row justify-between py-[2px] px-[10px]">
        Housing Choice Vouchers color display by
        <div>
          <Switch
            checkedChildren="PCT"
            unCheckedChildren="Count"
            checked={voucherTractMode}
            onChange={onChange}
          />
        </div>
      </div>
      {/* <div className="flex flex-row justify-between py-[6px] px-[10px]">
        Affordable housing Active Mode:
        <div>
          <Select
            defaultValue="all"
            size="small"
            style={{ width: 185 }}
            onChange={affordableDisplayMode}
            options={[
              {
                value: "all",
                label: "All",
              },
              {
                value: "active",
                label: "Active",
              },
              {
                value: "4",
                label: "Closed Within 1 Month",
              },
              {
                value: "8",
                label: "Closed Within 2 Months",
              },
              {
                value: "12",
                label: "Closed Within 3 Months",
              },
            ]}
          />
        </div>
      </div> */}
      {/* {affordableHousingItems.map((item, index) => (
        <div key={index} className="flex flex-row justify-between py-[2px] px-[10px]">
          <div
            className="w-[20px] h-[20px] rounded-full border border-solid border-black"
            style={{ backgroundColor: item.color }}
          ></div>
          <div>
            <span>{item.label}</span>
          </div>
        </div>
      ))} */}
    </div>
  );
};

export { PublicHousingLegend };
