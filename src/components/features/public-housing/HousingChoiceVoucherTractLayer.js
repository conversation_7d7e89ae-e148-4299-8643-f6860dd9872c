import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { renderToString } from "react-dom/server";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";
import { MAP_LAYER_NAME_BASE } from "../../../constants";
import { showVisibility } from "../../Map/MapUtility/layer";
import { initPopup } from "../../Map/MapUtility/general";
const sourceId = MAP_LAYER_NAME_BASE.voucherTract;
const zoomLevelToLayer = 8;
const zipCodeDefaultOpacity = 0.3;

const initialFillStyle = (mode) => ({
  id: `${sourceId}LayerFill`,
  type: "fill",
  minzoom: zoomLevelToLayer,
  paint: {
    "fill-color": [
      "interpolate",
      ["linear"],
      ["number", ["get", mode ? "HCV_PUBLIC_PCT" : "HCV_PUBLIC"]],
      0,
      "#f7fcfd",
      mode ? 1 : 20,
      "#e5f5f9",
      mode ? 3 : 50,
      "#ccece6",
      mode ? 7 : 70,
      "#99d8c9",
      mode ? 10 : 100,
      "#66c2a4",
      mode ? 12 : 200,
      "#41ae76",
      mode ? 15 : 350,
      "#238b45",
      mode ? 30 : 500,
      "#006d2c",
      mode ? 50 : 700,
      "#00441b",
    ],
    "fill-opacity": zipCodeDefaultOpacity,
  },
  filter: ["in", "$type", "Polygon"],
});

const outlineStyle = {
  id: `${sourceId}LayerOutline`,
  type: "line",
  minzoom: zoomLevelToLayer,
  layout: {
    visibility: "visible",
  },
  paint: {
    "line-color": "#000",
    "line-width": 2,
    "line-opacity": 0.2,
  },
  filter: ["in", "$type", "Polygon"],
};

let popup = initPopup();

export function VoucherTractLayer() {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const currentVoucherTractGeoJson = useSelector((state) => state.Map.currentVoucherTractGeoJson);
  const voucherTractMode = useSelector((state) => state.Map.voucherTractMode);


  const dispatch = useDispatch();

  const currentMapLayerOptionsRef = useRef(currentMapLayerOptions);
  currentMapLayerOptionsRef.current = currentMapLayerOptions;

  const [fillStyle, setFillStyle] = useState(initialFillStyle(voucherTractMode));
  const currentAffordableHousingGeoJson = useSelector(
    (state) => state.Map.currentAffordableHousingGeoJson
  );
  const getTooltipHTML = (property) => {
    const affordableNumber = JSON.parse(property.affordableNumber);

    return renderToString(
      <div style={{ padding: "10px", display: "flex", flexDirection: "column" }}>
        <span>HCV_PUBLIC: {property.HCV_PUBLIC ? property.HCV_PUBLIC : "N/A"}</span>
        <span>
          HCV_PUBLIC_PCT:{" "}
          {property.HCV_PUBLIC_PCT ? property.HCV_PUBLIC_PCT.toFixed(2) + "%" : "N/A"}
        </span>
        {/* <span>Total Number of Affordable Housing: </span>
        <span>Active: {affordableNumber.active}</span>
        <span>Closed Within 1 Month: {affordableNumber.oneMonth}</span>
        <span>Closed Within 2 Months: {affordableNumber.twoMonths}</span>
        <span>Closed Within 3 Months: {affordableNumber.threeMonths}</span> */}
      </div>
    );
  };

  useEffect(() => {
    setFillStyle(initialFillStyle(voucherTractMode));
  }, [voucherTractMode]);

  useEffect(() => {
    if (!map || !map.style || !map.style._loaded) return;
    if (!map.getLayer(`${sourceId}LayerFill`) || !map.getLayer(`${sourceId}LayerOutline`)) return;

    map.fire("map.setThemeOption", { payload: { currentMapThemeOption: "Monochrome" } });

    if (currentMapLayerOptions.includes("public housing voucher")) {
      const currentZoomLevel = map.getZoom();

      if (currentZoomLevel >= zoomLevelToLayer) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getVoucherTract",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });
        dispatch({ type: "Map/saveState", payload: { voucherTractLoading: true } });
      }
      showVisibility(map, `${sourceId}LayerFill`);
      showVisibility(map, `${sourceId}LayerOutline`);
    } else {
      map.fire("map.setThemeOption", { payload: { currentMapThemeOption: "Automatic" } });
    }
  }, [currentMapLayerOptions, map, dispatch]);

  useEffect(() => {
    if (!map) return;

    const moveEnd = () => {
      if (!currentMapLayerOptionsRef.current.includes("public housing voucher")) return;

      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToLayer) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getVoucherTract",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });
        dispatch({ type: "Map/saveState", payload: { voucherTractLoading: true } });
      }
    };

    const mouseMove = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [`${sourceId}LayerFill`],
      });
      if (features.length > 0 && currentAffordableHousingGeoJson) {
        const feature = features[features.length - 1];
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    map.on("moveend", moveEnd);
    map.on("mousemove", `${sourceId}LayerFill`, mouseMove);
    map.on("mouseleave", `${sourceId}LayerFill`, mouseLeave);

    return () => {
      map.off("moveend", moveEnd);
      map.off("mousemove", `${sourceId}LayerFill`, mouseMove);
      map.off("mouseleave", `${sourceId}LayerFill`, mouseLeave);
    };
  }, [map, dispatch, currentAffordableHousingGeoJson]);
  console.log("currentAffordableHousingGeoJson", currentAffordableHousingGeoJson);
  if (!currentMapLayerOptionsRef.current.includes("public housing voucher")) return null;

  return (
    <Source id={sourceId} type="geojson" data={currentVoucherTractGeoJson}>
      <Layer {...fillStyle} />
      <Layer {...outlineStyle} />
    </Source>
  );
}
