import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useSelector } from "react-redux";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";
import { getPublicHousingAuthoritiesData } from "../../../services/data";
import { geojsonTemplate } from "../../../constants";
import { convertToGeoJSON } from "../../../utils/geography";
import { initPopup } from "../../Map/MapUtility/general";
import { transformGeometry } from "./util";

const sourceId = "ph-source";

const pointStyle = {
  id: "ph-authorities-point",
  type: "circle",
  source: sourceId,
  paint: {
    "circle-radius": 7,
    "circle-color": "#ed6b6d",
    "circle-stroke-color": "#ffffff",
    "circle-stroke-width": 1,
  },
};

const getTooltipHTML = (property) => {
  const details = JSON.parse(property.attributes);
  return renderToString(
    <div style={{ padding: "10px", display: "flex", flexDirection: "column" }}>
      <span style={{ fontWeight: '600' }}>Type: Public Housing Authorities</span>
      <span>Participant Name: {details.FORMAL_PARTICIPANT_NAME}</span>
    </div>
  );
};

let popup = initPopup();

const moveBehindOwnedLayer = (map) => {
  const layers = map.getStyle().layers;
  let foundOwnedLayer = false;

  for (let i = layers.length - 1; i >= 0; i--) {
    if (layers[i].source && layers[i].source.includes("OwnedProperty")) {
      foundOwnedLayer = true;
    } else if (layers[i].source && !layers[i].source.includes("OwnedProperty") && foundOwnedLayer) {
      map.moveLayer(pointStyle.id, layers[i].id);
      break;
    }
  }
};

export const PublicHousingAuthoritiesLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const [geojson, setGeojson] = useState(geojsonTemplate);

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("public housing authorities")) return;

    const fetchAndSetData = async () => {
      const data = await getPublicHousingAuthoritiesData();
      const geoJson = convertToGeoJSON({
        data: data.features,
        geomAccessor: (item) => item.geometry,
        propertiesAccessor: (item) => {
          const { geometry, ...properties } = item;
          return properties;
        },
      });

      setGeojson(transformGeometry(geoJson));
    };

    const handleMouseMove = (e) => {
      const feature = e.features[0];
      if (feature && feature.properties) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const handleMouseLeave = () => {
      popup.remove();
    };

    fetchAndSetData();
    moveBehindOwnedLayer(map);

    map.on("moveend", fetchAndSetData);
    map.on("mousemove", "ph-authorities-point", handleMouseMove);
    map.on("mouseleave", "ph-authorities-point", handleMouseLeave);

    return () => {
      map.off("moveend", fetchAndSetData);
      map.off("mousemove", "ph-authorities-point", handleMouseMove);
      map.off("mouseleave", "ph-authorities-point", handleMouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("public housing authorities")) return null;

  return (
    <Source id={sourceId} type="geojson" data={geojson}>
      <Layer {...pointStyle} />
    </Source>
  );
};

