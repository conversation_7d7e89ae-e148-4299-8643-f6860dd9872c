import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useDispatch, useSelector } from "react-redux";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";
import { getAffordableHousingData } from "../../../services/data";
import { geojsonTemplate } from "../../../constants";
import { convertToGeoJSON } from "../../../utils/geography";
import { initPopup } from "../../Map/MapUtility/general";

const sourceId = "ph-source-ah";

const pointStyle = {
  id: "ph-ah-point",
  type: "circle",
  source: sourceId,
  paint: {
    "circle-radius": 7,
    "circle-color": [
      "match",
      ["get", "closing"],
      0,
      "#7fc97f",
      4,
      "#beaed4",
      8,
      "#fdc086",
      12,
      "#ffff99",
      "#000000",
    ],
    "circle-stroke-color": "#000000",
    "circle-stroke-width": 0.5,
  },
};

const getTooltipHTML = (property) => {
  let closed;
  switch (property.closing) {
    case 0:
      closed = "Active";
      break;
    case 4:
      closed = "Closed Within 1 Month";
      break;
    case 8:
      closed = "Closed Within 2 Months";
      break;
    case 12:
      closed = "Closed Within 3 Months";
      break;
    default:
      break;
  }
  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <span>Address: {property.address}</span>
      <span>Owner Name: {property.ownerName}</span>
      <span>Owner Address: {property.ownerAddress}</span>
      <span>
        Bd: {property.bed} | Ba: {property.bath} | Sqft: {property.sqft}
      </span>

      <span>Monthly Estimated Rent: {property.monthlyEstimateWRent}</span>
      <span>Status: {closed}</span>
    </div>
  );
};

let popup = initPopup();

export const AffordableHousingLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const affordableHousingMode = useSelector((state) => state.Map.affordableHousingMode);
  const currentAffordableHousingGeoJson = useSelector(
    (state) => state.Map.currentAffordableHousingGeoJson
  );
  const dispatch = useDispatch();
  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("public housing ah")) {
      return;
    }
    map.fire("map.setThemeOption", {
      payload: { currentMapThemeOption: "Monochrome" },
    });
    const moveEnd = async (e) => {
      let data = await getAffordableHousingData();
      switch (affordableHousingMode) {
        case "all":
          break;
        case "active":
          data = data.filter((item) => item.closing === 0);
          break;
        case "4":
          data = data.filter((item) => item.closing === 4);
          break;
        case "8":
          data = data.filter((item) => item.closing === 4 || item.closing === 8);
          break;
        case "12":
          data = data.filter((item) => item.closing != 0);
          break;
        default:
          break;
      }

      const geoJson = convertToGeoJSON({
        data,
        geomAccessor: (item) => item.geom,
        propertiesAccessor: (item) => {
          const { geom, ...properties } = item;
          return properties;
        },
      });
      console.log("geojson", geoJson);
      // setGeojson(geoJson);
      dispatch({
        type: "Map/saveState",
        payload: {
          currentAffordableHousingGeoJson: geoJson,
          affordableHousingLayerFetched: true
        },
      });
    };

    const mouseMove = (e) => {
      const feature = e.features[0];
      if (feature && feature.properties) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    const moveBehindOwnedLayer = () => {
      const layers = map.getStyle().layers;
      let foundOwnedLayer = false;
      console.log("moveBehindOwnedLayer", layers);
      for (let i = layers.length - 1; i >= 0; i--) {
        if (layers[i].source && layers[i].source.includes("OwnedProperty")) {
          foundOwnedLayer = true;
        } else if (
          layers[i].source &&
          !layers[i].source.includes("OwnedProperty") &&
          foundOwnedLayer
        ) {
          map.moveLayer(pointStyle.id, layers[i].id);
          console.log(`move ${pointStyle.id} behind ${layers[i].id}`);
          break;
        }
      }
    };

    moveEnd();
    moveBehindOwnedLayer();

    map.on("moveend", moveEnd);
    map.on("mousemove", "ph-ah-point", mouseMove);
    map.on("mouseleave", "ph-ah-point", mouseLeave);
    return () => {
      map.off("moveend", moveEnd);
      map.off("mousemove", "ph-ah-point", mouseMove);
      map.off("mouseleave", "ph-ah-point", mouseLeave);
    };
  }, [map, currentMapLayerOptions, affordableHousingMode]);

  if (!currentMapLayerOptions.includes("public housing ah")) return null;
  return (
    <>
      <Source id={sourceId} type="geojson" data={currentAffordableHousingGeoJson}>
        <Layer {...pointStyle} />
      </Source>
    </>
  );
};
