import React, { useRef, useState, useEffect } from "react";
import { Modal, Switch, Divider } from "antd";
import Draggable from "react-draggable";
import { useDispatch, useSelector } from "react-redux";
import { useMap } from "../../Map/MapProvider";
import { getAffordableHousingData } from "../../../services/data";
import { convertToGeoJSON } from "../../../utils/geography";

export const PHMenu = ({ onClose, openLayer, closeLayer }) => {
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const EHASALoading = useSelector((state) => state.Map.EHASALoading);
  const voucherTractLoading = useSelector((state) => state.Map.voucherTractLoading);
  const dispatch = useDispatch();
  const [open, setOpen] = useState(true);
  const [disabled, setDisabled] = useState(true);

  const [bounds, setBounds] = useState({
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  });
  const draggleRef = useRef(null);
  const onStart = (_event, uiData) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    if (!targetRect) {
      return;
    }
    setBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };

  // Switch
  const onChange = (checked) => {
    console.log(`switch to ${checked}`);
  };

  const [showPublicHousing, setShowPublicHousing] = useState(false);
  const [showPublicHousingAuthorities, setShowPublicHousingAuthorities] = useState(false);
  const [showAssistedMultiFamilyProperties, setShowAssistedMultiFamilyProperties] = useState(false);
  const [showEHASA, setShowEHASA] = useState(false); // Estimated Housing Authority Service Areas
  const [showHUDInsured, setShowHUDInsured] = useState(false); // HUD Insured Multifamily Properties
  const [showHousingChoice, setShowHousingChoice] = useState(false); // Housing Choice Vouchers by Tract
  const [showPublicHousingDevelopment, setShowPublicHousingDevelopment] = useState(false); // Public Housing Development
  const [showAffordableHousing, setShowAffordableHousing] = useState(false); // Affordable Housing


  const affordableHousingMode = useSelector((state) => state.Map.affordableHousingMode);

  useEffect(() => {
    console.log("currentMapLayerOptions", currentMapLayerOptions);
  }, [currentMapLayerOptions]);

  useEffect(() => {
    if (!showPublicHousing) {
      closeLayer('public housing')
      dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: currentMapLayerOptions.filter((l) => l !== "public housing"),
        },
      });
    } else {
      openLayer('public housing')
      dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: [...currentMapLayerOptions, "public housing"],
        },
      });
    }
  }, [showPublicHousing]);

  useEffect(() => {
    if (!showPublicHousingAuthorities) {
      closeLayer('public housing authorities')
      dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: currentMapLayerOptions.filter(
            (l) => l !== "public housing authorities"
          ),
        },
      });
    } else {
      openLayer('public housing authorities')
      dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: [...currentMapLayerOptions, "public housing authorities"],
        },
      });
    }
  }, [showPublicHousingAuthorities]);
  useEffect(() => {
    if (!showAssistedMultiFamilyProperties) {
      closeLayer('public housing assisted multifamily properties')
      dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: currentMapLayerOptions.filter(
            (l) => l !== "public housing assisted multifamily properties"
          ),
        },
      });
    } else {
      openLayer('public housing assisted multifamily properties')
      dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: [
            ...currentMapLayerOptions,
            "public housing assisted multifamily properties",
          ],
        },
      });
    }
  }, [showAssistedMultiFamilyProperties]);
  useEffect(() => {
    if (!showEHASA) {
      closeLayer('public housing ehasa')
      dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: currentMapLayerOptions.filter(
            (l) => l !== "public housing ehasa"
          ),
        },
      });
    } else {
      openLayer('public housing ehasa')
      dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: [...currentMapLayerOptions, "public housing ehasa"],
        },
      });
    }
  }, [showEHASA]);
  useEffect(() => {
    if (!showAffordableHousing) {
      closeLayer('public housing ah')
      dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: currentMapLayerOptions.filter((l) => l !== "public housing ah"),
        },
      });
    } else {
      openLayer('public housing ah')
      dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: [...currentMapLayerOptions, "public housing ah"],
        },
      });
    }
  }, [showAffordableHousing]);
  useEffect(() => {
    if (!showHUDInsured) {
      closeLayer('public housing insured')
      dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: currentMapLayerOptions.filter(
            (l) => l !== "public housing insured"
          ),
        },
      });
    } else {
      openLayer('public housing insured')
      dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: [...currentMapLayerOptions, "public housing insured"],
        },
      });
    }
  }, [showHUDInsured]);
  useEffect(() => {
    if (!showPublicHousingDevelopment) {
      closeLayer('public housing development')
      dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: currentMapLayerOptions.filter(
            (l) => l !== "public housing development"
          ),
        },
      });
    } else {
      openLayer('public housing development')
      dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: [...currentMapLayerOptions, "public housing development"],
        },
      });
    }
  }, [showPublicHousingDevelopment]);
  useEffect(() => {
    if (!showHousingChoice) {
      closeLayer('public housing voucher')
      dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: currentMapLayerOptions.filter(
            (l) => l !== "public housing voucher"
          ),
        },
      });
    } else {
      openLayer('public housing voucher')
      dispatch({
        type: "Map/saveState",
        payload: {
          currentMapLayerOptions: [...currentMapLayerOptions, "public housing voucher"],
        },
      });
    }
  }, [showHousingChoice]);
  useEffect(()=> {
    const fetchAffordable = async () => {
      let data = await getAffordableHousingData();
    switch (affordableHousingMode) {
      case "all":
        break;
      case "active":
        data = data.filter((item) => item.closing === 0);
        break;
      case "4":
        data = data.filter((item) => item.closing === 4);
        break;
      case "8":
        data = data.filter((item) => item.closing === 4 || item.closing === 8);
        break;
      case "12":
        data = data.filter((item) => item.closing != 0);
        break;
      default:
        break;
    }
    const geoJson = convertToGeoJSON({
      data,
      geomAccessor: (item) => item.geom,
      propertiesAccessor: (item) => {
        const { geom, ...properties } = item;
        return properties;
      },
    });
    console.log("geojson1", geoJson);
    // setGeojson(geoJson);
    dispatch({
      type: "Map/saveState",
      payload: {
        currentAffordableHousingGeoJson: geoJson,
        affordableHousingLayerFetched: true
      },
    });
    }
    fetchAffordable()
  }, [])
  return (
    <>
      <Modal
        title={
          <div
            style={{
              width: "100%",
              cursor: "move",
            }}
            onMouseOver={() => {
              if (disabled) {
                setDisabled(false);
              }
            }}
            onMouseOut={() => {
              setDisabled(true);
            }}
            onFocus={() => {}}
            onBlur={() => {}}
            // end
          >
            Public Housing Layers
          </div>
        }
        width={400}
        open={open}
        onCancel={() => {
          onClose();
        }}
        footer={false}
        modalRender={(modal) => (
          <Draggable
            disabled={disabled}
            bounds={bounds}
            nodeRef={draggleRef}
            onStart={(event, uiData) => onStart(event, uiData)}
          >
            <div ref={draggleRef}>{modal}</div>
          </Draggable>
        )}
        mask={false}
        wrapClassName="pointer-events-none"
      >
        <div className="flex flex-col gap-[6px]">
          {/* <div className="flex justify-between">
            Affordable housing (active advertising)
            <Switch value={showAffordableHousing} onClick={setShowAffordableHousing} />
          </div> */}
          <Divider style={{ marginTop: 3, marginBottom: 3 }} />
          <div className="flex justify-between">
            Housing Choice Vouchers by Tract
            <Switch value={showHousingChoice} onClick={setShowHousingChoice} loading={voucherTractLoading} />
          </div>
          <Divider style={{ marginTop: 3, marginBottom: 3 }} />
          <div className="flex justify-between">
            Assisted MultiFamily Properties
            <Switch
              value={showAssistedMultiFamilyProperties}
              onClick={setShowAssistedMultiFamilyProperties}
            />
          </div>

          <div className="flex justify-between">
            HUD Insured Multifamily Properties
            <Switch value={showHUDInsured} onClick={setShowHUDInsured} />
          </div>
          <div className="flex justify-between">
            Public Housing Development
            <Switch
              value={showPublicHousingDevelopment}
              onClick={setShowPublicHousingDevelopment}
            />
          </div>
          <Divider style={{ marginTop: 3, marginBottom: 3 }} />
          <div className="flex justify-between">
            Public Housing Authorities
            <Switch
              value={showPublicHousingAuthorities}
              onClick={setShowPublicHousingAuthorities}
            />
          </div>
          <div className="flex justify-between">
            Estimated Housing Authority Service Areas
            <Switch value={showEHASA} onClick={setShowEHASA}
            loading={EHASALoading}
             />
          </div>
          <Divider style={{ marginTop: 3, marginBottom: 3 }} />
          <div className="flex justify-between">
            Public Housing
            <Switch value={showPublicHousing} onClick={setShowPublicHousing} />
          </div>
        </div>
      </Modal>
    </>
  );
};
