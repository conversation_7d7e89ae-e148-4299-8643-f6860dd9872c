import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { renderToString } from "react-dom/server";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";
import { MAP_LAYER_NAME_BASE, zoomLevelToChangeBasemap } from "../../../constants";
import { hideVisibility, showVisibility } from "../../Map/MapUtility/layer";
import { initPopup } from "../../Map/MapUtility/general";

const sourceId = MAP_LAYER_NAME_BASE.ehasa;
const zoomLevelToLayer = 8;
const zipCodeDefaultOpacity = 0.15;

const fillStyle = {
  id: `${sourceId}LayerFill`,
  type: "fill",
  minzoom: zoomLevelToLayer,
  paint: {
    "fill-color": "#6699ff",
    "fill-opacity": zipCodeDefaultOpacity,
  },
  filter: ["in", "$type", "Polygon"],
};

const outlineStyle = {
  id: `${sourceId}LayerOutline`,
  type: "line",
  minzoom: zoomLevelToLayer,
  layout: {
    visibility: "visible",
  },
  paint: {
    "line-color": "#000",
    "line-width": 2,
    "line-opacity": 0.2,
  },
  filter: ["in", "$type", "Polygon"],
};

const getTooltipHTML = (property) => {
  return renderToString(
    <div style={{ padding: "10px", display: "flex", flexDirection: "column" }}>
      <span style={{ fontWeight: '600' }}>{property.FORMAL_PARTICIPANT_NAME}</span>
      <span>SHARE %: {property.PCT_SHARE ? property.PCT_SHARE : "N/A"}</span>
    </div>
  );
};

let popup = initPopup();

export function EHASALayer() {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const currentEHASAGeoJson = useSelector((state) => state.Map.currentEHASAGeoJson);
  const dispatch = useDispatch();
  const currentMapLayerOptionsRef = useRef(currentMapLayerOptions);
  currentMapLayerOptionsRef.current = currentMapLayerOptions;

  useEffect(() => {
    if (!map || !map.style || !map.style._loaded) return;
    if (!map.getLayer(`${sourceId}LayerFill`) || !map.getLayer(`${sourceId}LayerOutline`)) return;

    if (currentMapLayerOptions.includes("public housing ehasa")) {
      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToLayer) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getEHASA",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });
        dispatch({
          type: "Map/saveState",
          payload: {
            EHASALoading: true,
          },
        });
      }
      showVisibility(map, `${sourceId}LayerFill`);
      showVisibility(map, `${sourceId}LayerOutline`);
    }
  }, [currentMapLayerOptions, map, dispatch]);

  useEffect(() => {
    if (!map) return;

    const moveEnd = () => {
      if (!currentMapLayerOptionsRef.current.includes("public housing ehasa")) return;

      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToLayer) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getEHASA",
          payload: {
            lng1: mapScopeBBox[0][1],
            lat1: mapScopeBBox[0][0],
            lng2: mapScopeBBox[1][1],
            lat2: mapScopeBBox[1][0],
          },
        });
        dispatch({
          type: "Map/saveState",
          payload: {
            EHASALoading: true,
          },
        });
      }
    };

    const mouseMove = (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [`${sourceId}LayerFill`],
      });
      if (features.length > 0) {
        const feature = features[features.length - 1];
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    map.on("moveend", moveEnd);
    map.on("mousemove", `${sourceId}LayerFill`, mouseMove);
    map.on("mouseleave", `${sourceId}LayerFill`, mouseLeave);

    return () => {
      map.off("moveend", moveEnd);
      map.off("mousemove", `${sourceId}LayerFill`, mouseMove);
      map.off("mouseleave", `${sourceId}LayerFill`, mouseLeave);
    };
  }, [map, dispatch]);

  if (!currentMapLayerOptionsRef.current.includes("public housing ehasa")) return null;

  return (
    <Source id={sourceId} type="geojson" data={currentEHASAGeoJson}>
      <Layer {...fillStyle} />
      <Layer {...outlineStyle} />
    </Source>
  );
}
