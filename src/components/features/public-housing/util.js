export function transformGeometry(geoJson) {
    // Iterate over each feature in the GeoJSON
    geoJson.features = geoJson.features.map(feature => {
        // Check if geometry is defined and has x and y properties
        if (feature.geometry && feature.geometry.x !== undefined && feature.geometry.y !== undefined) {
            // Extract the x and y coordinates
            const { x, y } = feature.geometry;
            
            // Transform the geometry to the desired format
            feature.geometry = {
                type: "Point",
                coordinates: [x, y]
            };
        }
        
        return feature;
    });
    
    return geoJson;
}