import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useSelector } from "react-redux";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";
import { getInsuredData } from "../../../services/data";
import { geojsonTemplate } from "../../../constants";
import { convertToGeoJSON } from "../../../utils/geography";
import { initPopup } from "../../Map/MapUtility/general";
import { transformGeometry } from "./util";

const sourceId = "ph-source-hud";
const pointStyle = {
  id: "ph-hud-insured-point",
  type: "circle",
  source: sourceId,
  paint: {
    "circle-radius": 7,
    "circle-color": "#f781bf",
    "circle-stroke-color": "#000000",
    "circle-stroke-width": 1,
  },
};

const getTooltipHTML = (property) => {
  const details = JSON.parse(property.attributes);
  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <span style={{ fontWeight: "600" }}>Type: HUD Insured Multifamily Properties</span>
      <span>Property Name: {details.PROPERTY_NAME_TEXT}</span>
    </div>
  );
};

let popup = initPopup();

export const HUDInsuredLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const [geojson, setGeojson] = useState(geojsonTemplate);

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("public housing insured")) return;

    const moveEnd = async (e) => {
      const data = await getInsuredData();
      const geoJson = convertToGeoJSON({
        data: data.features,
        geomAccessor: (item) => item.geometry,
        propertiesAccessor: (item) => {
          const { geometry, ...properties } = item;
          return properties;
        },
      });
      setGeojson(transformGeometry(geoJson));
    };

    const mouseMove = (e) => {
      const feature = e.features[0];
      if (feature && feature.properties) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    const moveBehindOwnedLayer = () => {
      const layers = map.getStyle().layers;
      let foundOwnedLayer = false;
      console.log("moveBehindOwnedLayer", layers);
      for (let i = layers.length - 1; i >= 0; i--) {
        if (layers[i].source && layers[i].source.includes("OwnedProperty")) {
          foundOwnedLayer = true;
        } else if (
          layers[i].source &&
          !layers[i].source.includes("OwnedProperty") &&
          foundOwnedLayer
        ) {
          map.moveLayer(pointStyle.id, layers[i].id);
          console.log(`move ${pointStyle.id} behind ${layers[i].id}`);
          break;
        }
      }
    };

    moveEnd();
    moveBehindOwnedLayer();

    map.on("moveend", moveEnd);
    map.on("mousemove", "ph-hud-insured-point", mouseMove);
    map.on("mouseleave", "ph-hud-insured-point", mouseLeave);
    return () => {
      map.off("moveend", moveEnd);
      map.off("mousemove", "ph-hud-insured-point", mouseMove);
      map.off("mouseleave", "ph-hud-insured-point", mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("public housing insured")) return null;
  return (
    <>
      <Source id={sourceId} type="geojson" data={geojson}>
        <Layer {...pointStyle} />
      </Source>
    </>
  );
};
