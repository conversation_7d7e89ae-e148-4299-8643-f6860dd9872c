import { useEffect, useRef } from "react";
import { renderToString } from "react-dom/server";
import { useSelector, useDispatch } from "react-redux";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";
import { MAP_LAYER_NAME_BASE } from "../../../constants";
import { hideVisibility, showVisibility } from "../../Map/MapUtility/layer";
import { initPopup } from "../../Map/MapUtility/general";

const sourceId = MAP_LAYER_NAME_BASE.HUDPublicHousing;

const zoomLevelToHUD = 0;

const circleStyle = {
  id: `${sourceId}CircleLayer`,
  type: "circle",
  minzoom: zoomLevelToHUD,
  paint: {
    "circle-radius": 7,
    "circle-stroke-color": "#fff",
    "circle-stroke-width": 2,
    "circle-color": "#ff7f00",
  },
};

let HUDPublicHousingPopup;

const createHUDPopup = (map, feature, coordinates) => {
  if (feature && feature.length > 0) {
    const { STD_ADDR, BUILDING_NAME, FORMAL_PARTICIPANT_NAME } = feature[0].properties;

    const htmlRender = renderToString(
      <div style={{ padding: "10px", fontWeight: "500" }}>
        <h4 style={{ fontSize: "14px", fontWeight: "600", margin: 0 }}>{STD_ADDR}</h4>
        {BUILDING_NAME && BUILDING_NAME.length > 0 && (
          <p style={{ margin: 0 }}>Building Name: {BUILDING_NAME}</p>
        )}
        {FORMAL_PARTICIPANT_NAME && FORMAL_PARTICIPANT_NAME.length > 0 && (
          <p style={{ margin: 0 }}>Participant: {FORMAL_PARTICIPANT_NAME}</p>
        )}
      </div>
    );

    HUDPublicHousingPopup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
  } else {
    HUDPublicHousingPopup.remove();
  }
};

function HUDPublicHousingLayer() {
  const map = useSelector((state) => state.Map.map);
  const currentHUDPublicHousing = useSelector((state) => state.Map.currentHUDPublicHousing);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const dispatch = useDispatch();

  const currentMapLayerOptionsRef = useRef(currentMapLayerOptions);
  currentMapLayerOptionsRef.current = currentMapLayerOptions;

  useEffect(() => {
    if (!map || !map.style || !map.style._loaded) return;
    if (!map.getLayer(`${sourceId}CircleLayer`)) return;

    if (currentMapLayerOptions.includes("public housing")) {
      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToHUD) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getHUDPublicHousing",
          payload: {
            lng1: mapScopeBBox[0][0],
            lat1: mapScopeBBox[0][1],
            lng2: mapScopeBBox[1][0],
            lat2: mapScopeBBox[1][1],
          },
        });
      }
      showVisibility(map, `${sourceId}CircleLayer`);
    } else {
      if (map.getLayoutProperty(`${sourceId}CircleLayer`, "visibility") === "visible") {
        hideVisibility(map, `${sourceId}CircleLayer`);
      }
    }
  }, [currentMapLayerOptions]);

  useEffect(() => {
    if (!map) return;

    HUDPublicHousingPopup = initPopup();

    const moveEnd = () => {
      if (!currentMapLayerOptionsRef.current.includes("public housing")) return;

      const currentZoomLevel = map.getZoom();
      if (currentZoomLevel >= zoomLevelToHUD) {
        const mapScopeBBox = map.getBounds().toArray();
        dispatch({
          type: "Map/getHUDPublicHousing",
          payload: {
            lng1: mapScopeBBox[0][0],
            lat1: mapScopeBBox[0][1],
            lng2: mapScopeBBox[1][0],
            lat2: mapScopeBBox[1][1],
          },
        });
      }
    };

    const mouseEnter = (e) => {
      const feature = map.queryRenderedFeatures(e.point, {
        layers: [`${sourceId}CircleLayer`],
      });
      const coordinates = [e.lngLat.lng, e.lngLat.lat];
      createHUDPopup(map, feature, coordinates);
    };

    const mouseLeave = (e) => {
      HUDPublicHousingPopup.remove();
    };

    map.on("moveend", moveEnd);
    map.on("mouseenter", `${sourceId}CircleLayer`, mouseEnter);
    map.on("mouseleave", `${sourceId}CircleLayer`, mouseLeave);
    return () => {
      map.off("moveend", moveEnd);
      map.off("mouseenter", `${sourceId}CircleLayer`, mouseEnter);
      map.off("mouseleave", `${sourceId}CircleLayer`, mouseLeave);
    };
  }, [map]);

  return (
    <>
      <Source id={sourceId} type="geojson" data={currentHUDPublicHousing}>
        <Layer {...circleStyle} />
      </Source>
    </>
  );
}

export default HUDPublicHousingLayer;
