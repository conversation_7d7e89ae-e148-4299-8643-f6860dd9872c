import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useSelector } from "react-redux";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";
import { getInsuredData, getPublicHousingDevelopmentData } from "../../../services/data";
import { geojsonTemplate } from "../../../constants";
import { convertToGeoJSON } from "../../../utils/geography";
import { initPopup } from "../../Map/MapUtility/general";
import { transformGeometry } from "./util";

const sourceId = "ph-source-phd";
const pointStyle = {
  id: "ph-phd-point",
  type: "circle",
  source: sourceId,
  paint: {
    "circle-radius": 7,
    "circle-color": "#4daf4a",
    "circle-stroke-color": "#ffffff",
    "circle-stroke-width": 1,
  },
};

const getTooltipHTML = (property) => {
  const details = JSON.parse(property.attributes);
  return renderToString(
    <div
      style={{
        padding: "10px",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <span style={{ fontWeight: "600" }}>Type: Public Housing Development</span>
      <span>Property Name: {details.FORMAL_PARTICIPANT_NAME}</span>
      <span>Project Name: {details.PROJECT_NAME}</span>
      <span>Total Units: {details.TOTAL_UNITS}</span>
    </div>
  );
};

let popup = initPopup();

export const PHDLayer = () => {
  const map = useSelector((state) => state.Map.map);
  const currentMapLayerOptions = useSelector((state) => state.Map.currentMapLayerOptions);
  const [geojson, setGeojson] = useState(geojsonTemplate);

  useEffect(() => {
    if (!map || !currentMapLayerOptions.includes("public housing development")) return;

    const moveEnd = async (e) => {
      const data = await getPublicHousingDevelopmentData();
      const geoJson = convertToGeoJSON({
        data: data.features,
        geomAccessor: (item) => item.geometry,
        propertiesAccessor: (item) => {
          const { geometry, ...properties } = item;
          return properties;
        },
      });
      setGeojson(transformGeometry(geoJson));
    };

    const mouseMove = (e) => {
      const feature = e.features[0];
      if (feature && feature.properties) {
        const coordinates = [e.lngLat.lng, e.lngLat.lat];
        const htmlRender = getTooltipHTML(feature.properties);
        popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    const moveBehindOwnedLayer = () => {
      const layers = map.getStyle().layers;
      let foundOwnedLayer = false;
      console.log("moveBehindOwnedLayer", layers);
      for (let i = layers.length - 1; i >= 0; i--) {
        if (layers[i].source && layers[i].source.includes("OwnedProperty")) {
          foundOwnedLayer = true;
        } else if (
          layers[i].source &&
          !layers[i].source.includes("OwnedProperty") &&
          foundOwnedLayer
        ) {
          map.moveLayer(pointStyle.id, layers[i].id);
          console.log(`move ${pointStyle.id} behind ${layers[i].id}`);
          break;
        }
      }
    };

    moveEnd();
    moveBehindOwnedLayer();

    map.on("moveend", moveEnd);
    map.on("mousemove", "ph-phd-point", mouseMove);
    map.on("mouseleave", "ph-phd-point", mouseLeave);
    return () => {
      map.off("moveend", moveEnd);
      map.off("mousemove", "ph-phd-point", mouseMove);
      map.off("mouseleave", "ph-phd-point", mouseLeave);
    };
  }, [map, currentMapLayerOptions]);

  if (!currentMapLayerOptions.includes("public housing development")) return null;
  return (
    <>
      <Source id={sourceId} type="geojson" data={geojson}>
        <Layer {...pointStyle} />
      </Source>
    </>
  );
};
