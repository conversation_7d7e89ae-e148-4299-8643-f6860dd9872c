import { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { useQueries } from "react-query";
import { getPOIChainCategoryData, getPOIChainIDByCategoryData } from "../../../services/data";
import { Input, Select, Button, Table, Segmented, Slider, Result } from "antd";
import isEqual from "lodash.isequal";
import { useChainStores } from "./ChainStoresProvider";

import { useDispatch } from "react-redux";

import Draggable from "react-draggable";
import { Modal, Switch } from "antd";

// prettier-ignore
export const favorableGroups = [
  { chain_id: "4539", chain_name: "Starbucks US", category: "Restaurant - Coffee/Tea" },
  { chain_id: "1058", chain_name: "Chick-fil-A", category: "Restaurant - QSR/Fast Food" },
  { chain_id: "3937", chain_name: "QuikTrip", category: "Gas Stations" },
  { chain_id: "88", chain_name: "ALDI", category: "Grocery" },
  { chain_id: "4978", chain_name: "Trader Joe's", category: "Grocery" },
  { chain_id: "5295", chain_name: "Whole Foods Market", category: "Grocery" },
  { chain_id: "3900", chain_name: "Publix Supermarkets", category: "Grocery" },
  { chain_id: "2152", chain_name: "H-E-B", category: "Grocery" },
  { chain_id: "3535", chain_name: "Orangetheory Fitness", category: "Gyms + Fitness Facilities" },
  { chain_id: "4782", chain_name: "The Cheesecake Factory", category: "Restaurant - Casual" },
  { chain_id: "747", chain_name: "Buc-ee's", category: "Gas Stations" },
  { chain_id: "2026", chain_name:"Giant Food", category: "Grocery" },
  { chain_id: "2221", chain_name:"Harris Teeter", category: "Grocery" },
  { chain_id: "4202", chain_name:"Safeway", category: "Grocery" },
  { chain_id: "3979", chain_name:"Ralphs", category: "Grocery" },
  { chain_id: "5202", chain_name:"Vons", category: "Grocery" },
  { chain_id: "2506", chain_name:"Jewel-Osco", category: "Grocery" },
  { chain_id: "4508", chain_name:"Sprouts Farmers Market", category: "Grocery" },
  { chain_id: "5885", chain_name:"Lidi US", category: "Grocery" },
  { chain_id: "4808", chain_name:"The Fresh Market", category: "Grocery" },
  { chain_id: "1273", chain_name:"Costco Wholesale", category: "Grocery" },
  { chain_id: "2027", chain_name:"Giant Food Stores", category: "Grocery" },
  { chain_id: "3377", chain_name:"Natural Grocers", category: "Grocery" },
  { chain_id: "5256", chain_name:"Weis Markets", category: "Grocery" },
];
// prettier-ignore
export const unFavorableGroups = [
  { chain_id: "1560", chain_name: "EZPAWN", category: "Used Products" },
  { chain_id: "5127", chain_name: "Value Pawn and Jewelry", category: "Used Products" },
  { chain_id: "974", chain_name: "Cash America", category: "Used Products" },
  { chain_id: "4929", chain_name: "TitleMax", category: "Lending" },
  // { chain_id: "75", chain_name: "ACE Cash Express", category: "Lending" },
  { chain_id: "139", chain_name: "Advance America", category: "Lending" },
];

const DEFAULT_CHAINS = {
  favorable: favorableGroups,
  unFavorable: unFavorableGroups,
};

/**
 * Get chain stores menu modal.
 * Must be used within ChainStoresProvider.
 *
 * @param {function} onClose function called when modal closes
 * @returns
 */
export const ChainStoresMenu = ({ onClose }) => {
  const dispatch = useDispatch();
  const [disabled, setDisabled] = useState(true);
  const [bounds, setBounds] = useState({
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  });
  const draggleRef = useRef(null);

  const onStart = (_event, uiData) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    if (!targetRect) {
      return;
    }
    setBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };

  // Not a fan of this but quick solution for steven
  useEffect(() => {
    dispatch({
      type: "Map/saveState",
      payload: {
        currentMapThemeOption: "Monochrome",
      },
    });
  }, []);

  return (
    <Modal
      title={
        <div
          style={{
            width: "100%",
            cursor: "move",
          }}
          onMouseOver={() => {
            if (disabled) {
              setDisabled(false);
            }
          }}
          onMouseOut={() => {
            setDisabled(true);
          }}
          // fix eslintjsx-a11y/mouse-events-have-key-events
          // https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/master/docs/rules/mouse-events-have-key-events.md
          onFocus={() => {}}
          onBlur={() => {}}
          // end
        >
          Chain Stores
        </div>
      }
      width={700}
      open={true}
      onCancel={() => onClose()}
      footer={false}
      modalRender={(modal) => (
        <Draggable
          disabled={disabled}
          bounds={bounds}
          nodeRef={draggleRef}
          onStart={(event, uiData) => onStart(event, uiData)}
        >
          <div ref={draggleRef}>{modal}</div>
        </Draggable>
      )}
      mask={false}
      wrapClassName="pointer-events-none"
    >
      <div className="flex flex-col gap-[6px]">
        <ChainColorSwitch />
        <ChainSearch />
        <ChainOptionSelector />
        <ChainSelectedList />
      </div>
    </Modal>
    // <DraggableModalProvider>
    //   <DraggableModal
    //     open={true}
    //     onCancel={() => onClose()}
    //     footer={false}
    //     zIndex={1000}
    //     title="Chain Stores"
    //     mask={false}
    //     style={{ padding: "20px" }}
    //     className={"chainStoreMenu"}
    //     initialHeight={865}
    //   >
    //     <div className="flex flex-col gap-[10px]">
    //       <ChainSearch />
    //       <ChainOptionSelector />
    //       <ChainSelectedList />
    //     </div>
    //   </DraggableModal>
    // </DraggableModalProvider>
  );
};

const addIdsToData = (data) => {
  if (!data) return data;
  return data.map((item) => {
    return {
      ...item,
      id: crypto.randomUUID(),
    };
  });
};

const ChainSearch = () => {
  const { selectedChainIds, setSelectedChainIds } = useChainStores();

  const chainQueries = useQueries([
    {
      queryKey: ["getAllChainCategories"],
      queryFn: async () => await getPOIChainCategoryData(),
      initialData: [],
    },
    {
      queryKey: ["getAllChains"],
      queryFn: async () => await getPOIChainIDByCategoryData({ body: ["All"] }),
    },
  ]);
  const isLoading = chainQueries.some((query) => query.isLoading);
  const chainCategories = chainQueries[0].data;
  const { data, isError, refetch } = chainQueries[1];
  const modifiedData = useMemo(() => addIdsToData(data), [addIdsToData, data]);

  const [searchString, setSearchString] = useState("");
  const [selectedCategory, setSelectedCategory] = useState([]);

  const onChangeSearch = useCallback((e) => {
    setSearchString(e.target.value);
  }, []);

  const onChangeCategory = useCallback((value) => {
    setSelectedCategory(value);
  }, []);

  const addChainIdToGroup = useCallback((type, chainIds) => {
    console.log("addChainIdToGroup", type, chainIds);
    setSelectedChainIds((prevState) => {
      return {
        ...prevState,
        [type]: [...prevState[type], ...chainIds], // Spread the array of chainIds
      };
    });
  }, []);

  const filterData = useCallback((data, searchText, selectedCategory, selectedChainIds) => {
    if (!data) return [];
    return data.filter((item) => {
      const chainName = item.chain_name.toLowerCase();
      const category = item.category ? item.category.toLowerCase() : "";

      const isCategoryMatch =
        selectedCategory.length === 0 ||
        selectedCategory.some((item) => category.includes(item.toLowerCase()));

      const chainIds = selectedChainIds.favorable.concat(selectedChainIds.unFavorable);
      const isNotSelected = !chainIds.some((chain) => chain.chain_id === item.chain_id);

      return chainName.includes(searchText.toLowerCase()) && isCategoryMatch && isNotSelected;
    });
  }, []);

  return (
    <>
      <section className="flex flex-row gap-[10px] w-full">
        <div className="w-full">
          <Input value={searchString} onChange={onChangeSearch} placeholder="Search" />
        </div>
        <div className="w-full">
          <Select
            mode="multiple"
            style={{ width: "100%" }}
            value={selectedCategory}
            options={chainCategories.map((item) => ({
              label: item,
              value: item,
            }))}
            onChange={onChangeCategory}
            maxTagCount={"responsive"}
            allowClear={true}
            placeholder="Select a category"
          />
        </div>
      </section>
      <section>
        <section className="pb-2 gap-x-2 grid grid-cols-4">
          <Button
            type="default"
            onClick={() =>
              addChainIdToGroup(
                "favorable",
                filterData(modifiedData, searchString, selectedCategory, selectedChainIds)
              )
            }
          >
            Add All to Favorable
          </Button>
          <Button
            type="default"
            onClick={() =>
              addChainIdToGroup(
                "unFavorable",
                filterData(modifiedData, searchString, selectedCategory, selectedChainIds)
              )
            }
          >
            Add All to Unfavorable
          </Button>
        </section>
        {/* <div id="chainLocationTable" className="h-[344px]"> */}
        {!isError && (
          <div id="chainLocationTable">
            <Table
              rowKey={(record) => record.id}
              dataSource={filterData(
                modifiedData,
                searchString,
                selectedCategory,
                selectedChainIds
              )}
              columns={[
                {
                  title: "Chain Name",
                  dataIndex: "chain_name",
                  key: "chain_name",
                  sorter: (a, b) => a.chain_name.localeCompare(b.chain_name),
                  defaultSortOrder: "ascend",
                },
                {
                  title: "Category",
                  dataIndex: "category",
                  key: "category",
                  sorter: (a, b) => {
                    if (!a.category) return -1;
                    if (!b.category) return 1;
                    return a.category.localeCompare(b.category);
                  },
                },
                {
                  title: "Add to",
                  dataIndex: "chain_id",
                  key: "chain_id",
                  width: "195px",
                  render: (_, chain) => {
                    return (
                      <div className="flex flex-row gap-[10px]">
                        <Button
                          size="small"
                          onClick={() => addChainIdToGroup("favorable", [chain])}
                        >
                          Favorable
                        </Button>
                        <Button
                          size="small"
                          onClick={() => addChainIdToGroup("unFavorable", [chain])}
                        >
                          Unfavorable
                        </Button>
                      </div>
                    );
                  },
                },
              ]}
              scroll={{ y: 200 }}
              bordered={true}
              pagination={{ size: "small", position: ["bottomCenter"] }}
              loading={isLoading}
            />
          </div>
        )}
        {isError && <ErrorState refetch={refetch} />}
      </section>
    </>
  );
};

const ChainOptionSelector = () => {
  const {
    chainTimeFrameType,
    setChainTimeFrameType,
    chainOpenedMonthNumber,
    setChainOpenedMonthNumber,
    chainMapViewType,
    setChainMapViewType,
    chainSearchType,
    setChainSearchType,
  } = useChainStores();

  const monthSliderHandler = useCallback((value) => {
    setChainOpenedMonthNumber(value);
  }, []);

  const chainTimeframeSegmentHandler = useCallback((value) => {
    setChainTimeFrameType(value);
  }, []);

  const chainMapViewSegmentHandler = useCallback((value) => {
    setChainMapViewType(value);
  }, []);
  const chainSearchTypeHandler = useCallback((value) => {
    setChainSearchType(value);
  }, []);

  return (
    <section>
      <div className="flex flex-row gap-[20px] items-center w-full">
        <div
          className="min-w-[120px] text-center"
          style={{
            visibility: chainTimeFrameType === "All" ? "hidden" : "visible",
          }}
        >
          <Slider
            reverse
            min={0}
            max={24}
            value={chainOpenedMonthNumber}
            onChange={monthSliderHandler}
            className="m-0 mt-[20px] w-60"
            step={6}
          />
          <p className="m-0">{`${chainSearchType} ${chainOpenedMonthNumber} ${
            chainOpenedMonthNumber === 1 ? "month" : "months"
          } ago`}</p>
        </div>
        <div id="chain-menu-segmented" className="flex flex-row gap-[10px] w-[250px]">
          <div>
            <span>Status Type:</span>
            <Segmented
              value={chainSearchType}
              onChange={chainSearchTypeHandler}
              options={["Opened", "Closed"]}
              size="small"
            />
          </div>
          <div>
            <span>Timeframe:</span>
            <Segmented
              value={chainTimeFrameType}
              onChange={chainTimeframeSegmentHandler}
              options={["All", "Recent"]}
              size="small"
            />
          </div>

          <div>
            <span>View Type:</span>
            <Segmented
              value={chainMapViewType}
              onChange={chainMapViewSegmentHandler}
              options={["Points", "Heatmap"]}
              size="small"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

const DEFAULT_HIGHLIGHTED_CHAINS = {
  favorable: {
    chains: [],
    start: null,
    end: null,
  },
  unFavorable: {
    chains: [],
    start: null,
    end: null,
  },
};

const ChainSelectedList = () => {
  const {
    selectedChainIds,
    setSelectedChainIds,
    checkedFavorable,
    setCheckedFavorable,
    checkedUnfavorable,
    setCheckedUnfavorable,
    favorableColorBlind,
  } = useChainStores();

  const [highlightedChains, setHighlightedChains] = useState(DEFAULT_HIGHLIGHTED_CHAINS);

  const onReset = useCallback(() => {
    setSelectedChainIds(DEFAULT_CHAINS);
    setCheckedFavorable([]);
    setCheckedUnfavorable([]);
    setHighlightedChains(DEFAULT_HIGHLIGHTED_CHAINS);
  }, []);

  const onRemove = useCallback(() => {
    setSelectedChainIds((prevState) => {
      return {
        ...prevState,
        favorable: prevState.favorable.filter((chain) => !checkedFavorable.includes(chain)),
        unFavorable: prevState.unFavorable.filter((chain) => !checkedUnfavorable.includes(chain)),
      };
    });
    setCheckedFavorable([]);
    setCheckedUnfavorable([]);
    setHighlightedChains(DEFAULT_HIGHLIGHTED_CHAINS);
  }, [checkedFavorable, checkedUnfavorable]);

  const onTransferToUnfavorable = useCallback(() => {
    setSelectedChainIds((prevState) => {
      return {
        ...prevState,
        favorable: prevState.favorable.filter((chain) => !checkedFavorable.includes(chain)),
        unFavorable: [...prevState.unFavorable, ...checkedFavorable],
      };
    });
    setCheckedFavorable([]);
    setHighlightedChains((prevState) => ({
      ...prevState,
      favorable: DEFAULT_HIGHLIGHTED_CHAINS.favorable,
    }));
  }, [checkedFavorable]);

  const onTransferToFavorable = useCallback(() => {
    setSelectedChainIds((prevState) => {
      return {
        ...prevState,
        unFavorable: prevState.unFavorable.filter((chain) => !checkedUnfavorable.includes(chain)),
        favorable: [...prevState.favorable, ...checkedUnfavorable],
      };
    });
    setCheckedUnfavorable([]);
    setHighlightedChains((prevState) => ({
      ...prevState,
      unFavorable: DEFAULT_HIGHLIGHTED_CHAINS.unFavorable,
    }));
  }, [checkedUnfavorable]);

  const onRowClick = useCallback(
    (event, type, record) => {
      setHighlightedChains((prevState) => {
        const shiftKeyPressed = event.nativeEvent.shiftKey;
        if (
          (prevState[type].start || (!prevState[type].start && !prevState[type].end)) &&
          !shiftKeyPressed
        ) {
          const chains = [record];
          return {
            ...prevState,
            [type]: {
              ...prevState[type],
              chains: chains,
              start: record,
              end: null,
            },
          };
        } else if (prevState[type].chains.length >= 1 && shiftKeyPressed) {
          const firstHighlightedChain = prevState[type].start;
          const firstHighlightedChainIndex = selectedChainIds[type].findIndex(
            (chain) => chain.chain_id === firstHighlightedChain.chain_id
          );
          const currentChainIndex = selectedChainIds[type].findIndex(
            (chain) => chain.chain_id === record.chain_id
          );
          const newHighlightedChains = selectedChainIds[type].slice(
            Math.min(firstHighlightedChainIndex, currentChainIndex),
            Math.max(firstHighlightedChainIndex, currentChainIndex) + 1
          );

          return {
            ...prevState,
            [type]: {
              ...prevState[type],
              chains: newHighlightedChains,
              end: record,
            },
          };
        }
        return prevState;
      });
    },
    [selectedChainIds]
  );

  // prettier-ignore
  useEffect(() => {
    if (highlightedChains.favorable && highlightedChains.favorable.chains.length > 0) {
      setCheckedFavorable(highlightedChains.favorable.chains);
    }
    if (highlightedChains.unFavorable && highlightedChains.unFavorable.chains.length > 0) {
      setCheckedUnfavorable(highlightedChains.unFavorable.chains);
    }
  }, [highlightedChains]);

  const favorableColor = favorableColorBlind ? "#142fe0" : "#187f00";

  return (
    <section>
      <div className="flex flex-row gap-[10px] justify-between w-full">
        <section className="w-full">
          <h3 className="m-0 text-sm text-gray-600 mb-[5px] flex flex-row items-center gap-[10px]">
            <div style={{ backgroundColor: favorableColor }} className="w-3 h-3 rounded-full"></div>
            <span>Favorable</span>
          </h3>
          <div
            id="chain-favorable-list"
            className="w-full h-[200px] border-2 border-slate-200 border-solid overflow-y-auto"
          >
            {selectedChainIds.favorable.map((chain) => {
              return (
                <div
                  key={chain.chain_id}
                  className="hover:bg-blue-300 py-[2.5px] px-[10px] cursor-pointer"
                  onClick={(e) => onRowClick(e, "favorable", chain)}
                  style={{
                    backgroundColor: highlightedChains.favorable.chains.some(
                      (fav) => fav.chain_id === chain.chain_id
                    )
                      ? "#93c5fd"
                      : "transparent",
                  }}
                >
                  <span className="select-none" value={chain}>
                    {chain.chain_name}
                  </span>
                </div>
              );
            })}
          </div>
        </section>
        <section
          id="chain-menu-buttons"
          className="w-[200px] flex flex-col justify-center items-center gap-[10px]"
        >
          <Button
            id="chain-menu-reset-button"
            onClick={onReset}
            disabled={isEqual(selectedChainIds, DEFAULT_CHAINS)}
          >
            Reset
          </Button>
          <Button
            id="chain-menu-remove-button"
            onClick={onRemove}
            disabled={checkedFavorable.length === 0 && checkedUnfavorable.length === 0}
          >
            Remove
          </Button>
          <Button
            id="chain-menu-transfer-right-button"
            onClick={onTransferToUnfavorable}
            disabled={checkedFavorable.length === 0}
          >
            {">"}
          </Button>
          <Button
            id="chain-menu-transfer-left-button"
            onClick={onTransferToFavorable}
            disabled={checkedUnfavorable.length === 0}
          >
            {"<"}
          </Button>
        </section>
        <section className="w-full">
          <h3 className="m-0 text-sm text-gray-600 mb-[5px] flex flex-row items-center gap-[10px]">
            <div className="bg-[#f83d2a] w-[12px] h-[12px] rounded-full"></div>
            <span>Unfavorable</span>
          </h3>
          <div
            id="chain-unfavorable-list"
            className="w-full h-[200px] border-2 border-slate-200 border-solid overflow-y-auto py-[5px] px-[10px]"
          >
            {selectedChainIds.unFavorable.map((chain) => {
              return (
                <div
                  key={chain.chain_id}
                  className="hover:bg-blue-300 py-[2.5px] px-[10px] cursor-pointer"
                  onClick={(e) => onRowClick(e, "unFavorable", chain)}
                  style={{
                    backgroundColor: highlightedChains.unFavorable.chains.some(
                      (fav) => fav.chain_id === chain.chain_id
                    )
                      ? "#93c5fd"
                      : "transparent",
                  }}
                >
                  <span className="select-none" value={chain}>
                    {chain.chain_name}
                  </span>
                </div>
              );
            })}
          </div>
        </section>
      </div>
    </section>
  );
};

const ErrorState = ({ refetch }) => {
  return (
    <div className="flex flex-row justify-center items-center w-full h-full">
      <Result
        status="error"
        title="Something went wrong"
        extra={[<Button onClick={() => refetch()}>Try again?</Button>]}
      />
    </div>
  );
};

const ChainColorSwitch = () => {
  const { favorableColorBlind, setFavorableColorBlind } = useChainStores();

  return (
    <div className="flex justify-start">
      <p className="pr-2">Accessibility Options</p>
      <div className="w-8">
        <Switch
          value={favorableColorBlind}
          checkedChildren="Blue"
          unCheckedChildren="Green"
          onChange={() => setFavorableColorBlind(!favorableColorBlind)}
          style={{
            backgroundColor: favorableColorBlind ? "#142fe0" : "#187f00",
          }}
        />
      </div>
    </div>
  );
};
