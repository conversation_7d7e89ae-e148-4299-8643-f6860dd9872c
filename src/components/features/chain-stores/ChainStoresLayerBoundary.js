import { useState, useEffect } from "react";

import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";
import { geojsonTemplate } from "../../../constants";
import { initPopup } from "../../Map/MapUtility/general";
import { getPOIChainLocationBoundaryData } from "../../../services/data";
import { useChainStores } from "./ChainStoresProvider";
import { convertToGeoJSON } from "../../../utils/geography";
import moment from "moment";
import {
  getFavorableHeatStyle,
  getUnFavorableHeatStyle,
  getCirclePointStyle,
  createChainPopup,
} from "./utils";

const sourceId = "poiChainStoresBoundary";
const minZoom = 9;

const popup = initPopup();

/**
 * Get chain stores within map boundary.
 * Must be used within ChainStoresProvider.
 *
 * @param {object} map map state object
 * @returns
 */
export const ChainStoresLayerBoundary = ({ map }) => {
  const {
    selectedChainIds,
    chainMapViewType,
    chainOpenedMonthNumber,
    chainTimeFrameType,
    chainSearchType,
    favorableColorBlind
  } = useChainStores();
  const [favorableGeoJSON, setFavorableGeoJSON] = useState(geojsonTemplate);
  const [unFavorableGeoJSON, setUnFavorableGeoJSON] = useState(geojsonTemplate);

  useEffect(() => {
    if (!map) return;

    const abortController = new AbortController();
    const signal = abortController.signal;

    const fetchChainData = async () => {
      if (map.getZoom() < minZoom) return;
      try {
        const mapBBox = map.getBounds().toArray();

        const ids = [
          ...selectedChainIds.favorable.map(({ chain_id }) => chain_id),
          ...selectedChainIds.unFavorable.map(({ chain_id }) => chain_id),
        ];

        if (ids.length > 0) {
          const timeframe =
            chainTimeFrameType === "All"
              ? "1900-01-01"
              : moment()
                  .subtract(chainOpenedMonthNumber, "months")
                  .format("YYYY-MM-DD");

          const response = await getPOIChainLocationBoundaryData({
            lng1: mapBBox[0][1],
            lat1: mapBBox[0][0],
            lng2: mapBBox[1][1],
            lat2: mapBBox[1][0],
            firstappeared: timeframe,
            body: ids,
            signal: signal,
            status: chainSearchType
          });

          // prettier-ignore
          if (response) {
            const favorableChains = [];
            const unFavorableChains = [];
            for (let i = 0; i < response.length; i++) {
              const chain = response[i];
              if (selectedChainIds.favorable.some((fav) => fav.chain_id === chain.chain_id)) {
                favorableChains.push(chain);
              } else if (selectedChainIds.unFavorable.some((unfav) => unfav.chain_id === chain.chain_id)) {
                unFavorableChains.push(chain);
              }
            }

            setFavorableGeoJSON(convertToGeoJSON({
              data: favorableChains,
              geomAccessor: (d) => d.geometry,
              propertiesAccessor: (d) => {
                const {geometry, ...properties} = d;
                return properties;
              },
            }));
            setUnFavorableGeoJSON(convertToGeoJSON({
              data: unFavorableChains,
              geomAccessor: (d) => d.geometry,
              propertiesAccessor: (d) => {
                const {geometry, ...properties} = d;
                return properties;
              },
            }));
            return;
          }
        }
        if (
          favorableGeoJSON.features.length > 0 ||
          unFavorableGeoJSON.features.length > 0
        ) {
          setFavorableGeoJSON(geojsonTemplate);
          setUnFavorableGeoJSON(geojsonTemplate);
        }
      } catch (err) {
        if (err.name != "AbortError") {
          console.error(err);
        }
      }
    };

    const mouseEnter = (e) => {
      const feature = map.queryRenderedFeatures(e.point, {
        layers: [`${sourceId}FavourablePoint`, `${sourceId}UnFavourablePoint`],
      });
      const coordinates = [e.lngLat.lng, e.lngLat.lat];
      createChainPopup(map, popup, feature, coordinates, chainSearchType);
    };
    const mouseLeave = () => {
      popup.remove();
    };

    fetchChainData();
    map.on("moveend", fetchChainData);
    map.on("mouseenter", `${sourceId}FavourablePoint`, mouseEnter);
    map.on("mouseenter", `${sourceId}UnFavourablePoint`, mouseEnter);
    map.on("mouseleave", `${sourceId}FavourablePoint`, mouseLeave);
    map.on("mouseleave", `${sourceId}UnFavourablePoint`, mouseLeave);
    return () => {
      abortController.abort();
      map.off("moveend", fetchChainData);
      map.off("mouseenter", `${sourceId}FavourablePoint`, mouseEnter);
      map.off("mouseenter", `${sourceId}UnFavourablePoint`, mouseEnter);
      map.off("mouseleave", `${sourceId}FavourablePoint`, mouseLeave);
      map.off("mouseleave", `${sourceId}UnFavourablePoint`, mouseLeave);
    };
  }, [map, selectedChainIds, chainOpenedMonthNumber, chainTimeFrameType,chainSearchType ]);

  return (
    <>
      <Source
        id={`${sourceId}Favourable`}
        type="geojson"
        data={favorableGeoJSON}
      >
        {chainMapViewType === "Points" && (
          <Layer
            {...getCirclePointStyle(
              `${sourceId}FavourablePoint`,
              favorableColorBlind ? "#142fe0" :"#187f00",
              minZoom
            )}
          />
        )}
        {chainMapViewType === "Heatmap" && (
          <Layer {...getFavorableHeatStyle(sourceId, minZoom)} />
        )}
      </Source>
      <Source
        id={`${sourceId}UnFavourable`}
        type="geojson"
        data={unFavorableGeoJSON}
      >
        {chainMapViewType === "Points" && (
          <Layer
            {...getCirclePointStyle(
              `${sourceId}UnFavourablePoint`,
              "#f83d2a",
              minZoom
            )}
          />
        )}
        {chainMapViewType === "Heatmap" && (
          <Layer {...getUnFavorableHeatStyle(sourceId, minZoom)} />
        )}
      </Source>
    </>
  );
};

export default ChainStoresLayerBoundary;
