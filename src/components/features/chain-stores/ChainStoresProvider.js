import { useState, createContext, useContext, useMemo } from "react";

// prettier-ignore
export const favorableGroups = [
  { chain_id: "4539", chain_name: "Starbucks US", category: "Restaurant - Coffee/Tea"},
  { chain_id: "1058", chain_name: "Chick-fil-A", category: "Restaurant - QSR/Fast Food" },
  { chain_id: "3937", chain_name: "QuikTrip", category: "Gas Stations" },
  { chain_id: "88", chain_name: "ALDI", category: "Grocery" },
  { chain_id: "4978", chain_name: "Trader Joe's", category: "Grocery" },
  { chain_id: "5295", chain_name: "Whole Foods Market", category: "Grocery" },
  { chain_id: "3900", chain_name: "Publix Supermarkets", category: "Grocery" },
  { chain_id: "2152", chain_name: "H-E-B", category: "Grocery" },
  { chain_id: "3535", chain_name: "Orangetheory Fitness", category: "Gyms + Fitness Facilities" },
  { chain_id: "4782", chain_name: "The Cheesecake Factory", category: "Restaurant - Casual" },
  { chain_id: "747", chain_name: "Buc-ee's", category: "Gas Stations" },
  { chain_id: "2026", chain_name:"Giant Food", category: "Grocery" },
  { chain_id: "2221", chain_name:"Harris Teeter", category: "Grocery" },
  { chain_id: "4202", chain_name:"Safeway", category: "Grocery" },
  { chain_id: "3979", chain_name:"Ralphs", category: "Grocery" },
  { chain_id: "5202", chain_name:"Vons", category: "Grocery" },
  { chain_id: "2506", chain_name:"Jewel-Osco", category: "Grocery" },
  { chain_id: "4508", chain_name:"Sprouts Farmers Market", category: "Grocery" },
  { chain_id: "5885", chain_name:"Lidi US", category: "Grocery" },
  { chain_id: "4808", chain_name:"The Fresh Market", category: "Grocery" },
  { chain_id: "1273", chain_name:"Costco Wholesale", category: "Grocery" },
  { chain_id: "2027", chain_name:"Giant Food Stores", category: "Grocery" },
  { chain_id: "3377", chain_name:"Natural Grocers", category: "Grocery" },
  { chain_id: "5256", chain_name:"Weis Markets", category: "Grocery" },
];
// prettier-ignore
export const unFavorableGroups = [
  { chain_id: "1560", chain_name: "EZPAWN", category: "Used Products" },
  { chain_id: "5127", chain_name: "Value Pawn and Jewelry", category: "Used Products" },
  { chain_id: "974", chain_name: "Cash America", category: "Used Products" },
  { chain_id: "4929", chain_name: "TitleMax", category: "Lending" },
  // { chain_id: "75", chain_name: "ACE Cash Express", category: "Lending" },
  { chain_id: "139", chain_name: "Advance America", category: "Lending" },
];

const DEFAULT_CHAINS = {
  favorable: favorableGroups,
  unFavorable: unFavorableGroups,
};

const ChainStoresContext = createContext(undefined);

export const ChainStoresProvider = ({ children }) => {
  const [selectedChainIds, setSelectedChainIds] = useState(DEFAULT_CHAINS);

  const [chainTimeFrameType, setChainTimeFrameType] = useState("All");
  const [chainOpenedMonthNumber, setChainOpenedMonthNumber] = useState(24);
  const [chainMapViewType, setChainMapViewType] = useState("Points");
  const [chainSearchType, setChainSearchType] = useState("Opened");

  const [checkedFavorable, setCheckedFavorable] = useState([]);
  const [checkedUnfavorable, setCheckedUnfavorable] = useState([]);

  const [favorableColorBlind, setFavorableColorBlind] = useState(false)

  const context = useMemo(() => {
    return {
      selectedChainIds,
      setSelectedChainIds,
      chainTimeFrameType,
      setChainTimeFrameType,
      chainOpenedMonthNumber,
      setChainOpenedMonthNumber,
      chainMapViewType,
      setChainMapViewType,
      checkedFavorable,
      setCheckedFavorable,
      checkedUnfavorable,
      setCheckedUnfavorable,
      chainSearchType,
      setChainSearchType,
      favorableColorBlind,
      setFavorableColorBlind
    };
  }, [
    selectedChainIds,
    setSelectedChainIds,
    chainTimeFrameType,
    setChainTimeFrameType,
    chainOpenedMonthNumber,
    setChainOpenedMonthNumber,
    chainMapViewType,
    setChainMapViewType,
    checkedFavorable,
    setCheckedFavorable,
    checkedUnfavorable,
    setCheckedUnfavorable,
    chainSearchType,
    setChainSearchType,
    favorableColorBlind,
    setFavorableColorBlind
  ]);

  return <ChainStoresContext.Provider value={context}>{children}</ChainStoresContext.Provider>;
};

export const useChainStores = () => {
  const context = useContext(ChainStoresContext);
  if (context === undefined) {
    throw new Error("useChainStores must be used within a ChainStoresProvider");
  }
  return context;
};

// const [favorableList, setFavorableList] = useState([]);
// const [unFavorableList, setUnFavorableList] = useState([]);
// const [timeFrame, setTimeFrame] = useState("all"); // all | recent
// const [viewType, setViewType] = useState("points"); // points | heatmap
