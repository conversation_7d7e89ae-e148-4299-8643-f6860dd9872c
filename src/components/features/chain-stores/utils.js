import { renderToString } from "react-dom/server";

export const getFavorableHeatStyle = (sourceId, minZoom = undefined) => {
  const style = {
    id: `${sourceId}FavorableHeatLayer`,
    type: "heatmap",
    paint: {
      "heatmap-weight": ["interpolate", ["linear"], ["get", "mag"], 0, 0, 6, 1],
      "heatmap-intensity": ["interpolate", ["linear"], ["zoom"], 0, 1, 9, 3],
      "heatmap-color": [
        "interpolate",
        ["linear"],
        ["heatmap-density"],
        0,
        "rgba(33,102,172,0)",
        0.2,
        "rgb(209,229,240)",
        0.4,
        "rgb(130,242,214)",
        0.6,
        "rgb(110,233,114)",
        0.8,
        "rgb(53,208,70)",
        1,
        "rgb(59,187,26)",
      ],
      "heatmap-radius": ["interpolate", ["linear"], ["zoom"], 0, 2, 9, 20],
    },
  };
  if (minZoom) {
    style.minzoom = minZoom;
  }
  return style;
};

export const getUnFavorableHeatStyle = (sourceId, minZoom = undefined) => {
  const style = {
    id: `${sourceId}UnFavorableHeatLayer`,
    type: "heatmap",
    paint: {
      "heatmap-weight": ["interpolate", ["linear"], ["get", "mag"], 0, 0, 6, 1],
      "heatmap-intensity": ["interpolate", ["linear"], ["zoom"], 0, 1, 9, 3],
      "heatmap-color": [
        "interpolate",
        ["linear"],
        ["heatmap-density"],
        0,
        "rgba(33,102,172,0)",
        0.2,
        "rgb(209,229,240)",
        0.4,
        "rgb(209,229,240)",
        0.6,
        "rgb(253,219,199)",
        0.8,
        "rgb(239,138,98)",
        1,
        "rgb(225,24,43)",
      ],
      "heatmap-radius": ["interpolate", ["linear"], ["zoom"], 0, 2, 9, 20],
    },
  };
  if (minZoom) {
    style.minzoom = minZoom;
  }
  return style;
};

export const getCirclePointStyle = (id, color, minZoom = undefined) => {
  const style = {
    id,
    type: "circle",
    paint: {
      "circle-radius": 5,
      "circle-stroke-color": "#fff",
      "circle-stroke-width": 2,
      "circle-color": color,
    },
  };
  if (minZoom) {
    style.minzoom = minZoom;
  }
  return style;
};

export const createChainPopup = (map, popup, feature, coordinates, chainSearchType) => {
  if (feature && feature.length > 0) {
    const { address, chain_name, first_appeared, last_seen } = feature[0].properties;
    const date = chainSearchType === 'Closed' ? `Closed: ${last_seen}` : `Opened: ${first_appeared}`
    const htmlRender = renderToString(
      <div style={{ padding: "10px", fontWeight: "500" }}>
        <h4 style={{ fontSize: "14px", fontWeight: "600", margin: 0 }}>
          {chain_name}
        </h4>
        <p style={{ margin: 0 }}>{date}</p>
        <p style={{ margin: 0 }}>{address}</p>
      </div>
    );
 
    popup.setLngLat(coordinates).setHTML(htmlRender).addTo(map);
  } else {
    popup.remove();
  }
};
