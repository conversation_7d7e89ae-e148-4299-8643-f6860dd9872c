import { useState, useEffect } from "react";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";
import { geojsonTemplate } from "../../../constants";
import { initPopup } from "../../Map/MapUtility/general";
import { useMap } from "../../Map/MapProvider";
import { useChainStores } from "./ChainStoresProvider";
import { convertToGeoJSON } from "../../../utils/geography";
import moment from "moment";
import { getPOIChainLocationRadiusData } from "../../../services/data";
import distance from "@turf/distance";
import {
  getFavorableHeatStyle,
  getUnFavorableHeatStyle,
  getCirclePointStyle,
  createChainPopup,
} from "./utils";

const sourceId = "poiChainStoresRadius";

const popup = initPopup();

/**
 * Get chain stores within a radius.
 * Must be used within ChainStoresProvider.
 *
 * @param {object} map map state object
 * @param {number} lat latitude
 * @param {number} lng longitude
 * @param {number} radius radius in meters
 * @param {null | false | {favorable: boolean, unFavorable: boolean}} viewLayers object with favorable and un-favorable boolean properties
 * @returns
 */
export const ChainStoresLayerRadius = ({
  map,
  lat,
  lng,
  radius,
  viewLayers = {
    favorable: true,
    unFavorable: true,
  },
}) => {
  const { setNearbyChainStores } = useMap();
  const {
    selectedChainIds,
    chainMapViewType,
    chainOpenedMonthNumber,
    chainTimeFrameType,
    favorableColorBlind
  } = useChainStores();
  const [favorableGeoJSON, setFavorableGeoJSON] = useState(geojsonTemplate);
  const [unFavorableGeoJSON, setUnFavorableGeoJSON] = useState(geojsonTemplate);

  useEffect(() => {
    if (!map || !lat || !lng || !radius) return;

    const abortController = new AbortController();
    const signal = abortController.signal;

    const fetchChainData = async () => {
      try {
        setNearbyChainStores((prev) => ({ ...prev, isLoading: true }));

        const ids = [
          ...selectedChainIds.favorable.map(({ chain_id }) => chain_id),
          ...selectedChainIds.unFavorable.map(({ chain_id }) => chain_id),
        ];

        if (ids.length > 0) {
          const timeframe =
            chainTimeFrameType === "All"
              ? "1900-01-01"
              : moment()
                  .subtract(chainOpenedMonthNumber, "months")
                  .format("YYYY-MM-DD");

          const response = await getPOIChainLocationRadiusData({
            lat: lat,
            lng: lng,
            radius: radius,
            firstappeared: timeframe,
            body: ids,
            signal: signal,
          });

          // prettier-ignore
          if (response) {
            const favorableChains = [];
            const unFavorableChains = [];
            for (let i = 0; i < response.length; i++) {
              const chain = response[i];
              if (selectedChainIds.favorable.some((fav) => fav.chain_id === chain.chain_id)) {
                const miles = distance([lng, lat], chain.geometry.coordinates, { units: "miles" });
                favorableChains.push({...chain, distance: miles});
              } else if (selectedChainIds.unFavorable.some((unfav) => unfav.chain_id === chain.chain_id)) {
                const miles = distance([lng, lat], chain.geometry.coordinates, { units: "miles" });
                unFavorableChains.push({...chain, distance: miles});
              }
            }

            setFavorableGeoJSON(convertToGeoJSON({
              data: favorableChains,
              geomAccessor: (d) => d.geometry,
              propertiesAccessor: (d) => {
                const {geometry, ...properties} = d;
                return properties;
              },
            }));
            setUnFavorableGeoJSON(convertToGeoJSON({
              data: unFavorableChains,
              geomAccessor: (d) => d.geometry,
              propertiesAccessor: (d) => {
                const {geometry, ...properties} = d;
                return properties;
              },
            }));
            setNearbyChainStores({ 
              data: {favorable: favorableChains, unFavorable: unFavorableChains}, 
              isLoading: false, 
              isError: false 
            });
            return;
          }
        }
        if (
          favorableGeoJSON.features.length > 0 ||
          unFavorableGeoJSON.features.length > 0
        ) {
          setFavorableGeoJSON(geojsonTemplate);
          setUnFavorableGeoJSON(geojsonTemplate);
          setNearbyChainStores({
            data: null,
            isLoading: false,
            isError: false,
          });
        }
      } catch (err) {
        if (err.name != "AbortError") {
          console.error(err);
          setFavorableGeoJSON(geojsonTemplate);
          setUnFavorableGeoJSON(geojsonTemplate);
          setNearbyChainStores({
            data: null,
            isLoading: false,
            isError: true,
          });
        }
      }
    };

    fetchChainData();

    const mouseEnter = (e) => {
      const layers = [];
      if (map.getLayer(`${sourceId}FavourablePoint`)) {
        layers.push(`${sourceId}FavourablePoint`);
      }
      if (map.getLayer(`${sourceId}UnFavourablePoint`)) {
        layers.push(`${sourceId}UnFavourablePoint`);
      }

      const feature = map.queryRenderedFeatures(e.point, {
        layers,
      });
      const coordinates = [e.lngLat.lng, e.lngLat.lat];
      createChainPopup(map, popup, feature, coordinates);
    };
    const mouseLeave = () => {
      popup.remove();
    };

    map.on("mouseenter", `${sourceId}FavourablePoint`, mouseEnter);
    map.on("mouseleave", `${sourceId}FavourablePoint`, mouseLeave);
    map.on("mouseenter", `${sourceId}UnFavourablePoint`, mouseEnter);
    map.on("mouseleave", `${sourceId}UnFavourablePoint`, mouseLeave);

    return () => {
      abortController.abort();
      map.off("mouseenter", `${sourceId}FavourablePoint`, mouseEnter);
      map.off("mouseleave", `${sourceId}FavourablePoint`, mouseLeave);
      map.off("mouseenter", `${sourceId}UnFavourablePoint`, mouseEnter);
      map.off("mouseleave", `${sourceId}UnFavourablePoint`, mouseLeave);

      setFavorableGeoJSON(geojsonTemplate);
      setUnFavorableGeoJSON(geojsonTemplate);
      setNearbyChainStores({
        data: null,
        isLoading: false,
        isError: false,
      });
    };
  }, [
    map,
    lat,
    lng,
    radius,
    selectedChainIds,
    chainMapViewType,
    chainOpenedMonthNumber,
    chainTimeFrameType,
  ]);

  if (!viewLayers) return null;
  return (
    <>
      {viewLayers.favorable !== false && (
        <Source
          id={`${sourceId}Favourable`}
          type="geojson"
          data={favorableGeoJSON}
        >
          {chainMapViewType === "Points" && (
            <Layer
              {...getCirclePointStyle(`${sourceId}FavourablePoint`,   favorableColorBlind ? "#142fe0" :"#187f00",)}
            />
          )}
          {chainMapViewType === "Heatmap" && (
            <Layer {...getFavorableHeatStyle(sourceId)} />
          )}
        </Source>
      )}
      {viewLayers.unFavorable !== false && (
        <Source
          id={`${sourceId}UnFavourable`}
          type="geojson"
          data={unFavorableGeoJSON}
        >
          {chainMapViewType === "Points" && (
            <Layer
              {...getCirclePointStyle(
                `${sourceId}UnFavourablePoint`,
                "#f83d2a"
              )}
            />
          )}
          {chainMapViewType === "Heatmap" && (
            <Layer {...getUnFavorableHeatStyle(sourceId)} />
          )}
        </Source>
      )}
    </>
  );
};
