import { useState, useEffect, createContext, useContext, useMemo } from "react";
import { renderToString } from "react-dom/server";
import Source from "../../Map/MapLayers/Source";
import Layer from "../../Map/MapLayers/Layer";
import {
  tileURLRoot,
  getLandBankCountData,
  getLandBankData,
} from "../../../services/data";
import { Select, InputNumber, Switch } from "antd";
import { useQuery } from "react-query";
import "./styles.css";
import { initPopup } from "../../Map/MapUtility/general";

import AshtonWoodsHomesLogo from "./assets/Ashton_Woods_Homes.png";
import BeazerHomesLogo from "./assets/Beazer_Homes.png";
import CenturyCommunitiesLogo from "./assets/Century_Communities.png";
import ClaytonHomesLogo from "./assets/Clayton_Homes.png";
import <PERSON><PERSON><PERSON><PERSON>HomesLogo from "./assets/<PERSON>_<PERSON>_Homes.png";
import DreamFindersHomesLogo from "./assets/Dream_Finders_Homes.png";
import DRHortonLogo from "./assets/D_R_Horton.png";
import HeartlandHomesLogo from "./assets/Heartland_Homes-NVR.png";
import HovnanianEnterprisesLogo from "./assets/Hovnanian_Enterprises.png";
import KBHomeLogo from "./assets/KB_Home.png";
import LennarLogo from "./assets/Lennar.png";
import LGIHomes from "./assets/LGI_Homes.png";
import M_I_HomesLogo from "./assets/M_I_Homes.png";
import MattamyHomesLogo from "./assets/Mattamy_Homes.png";
import MeritageHomesLogo from "./assets/Meritage_Homes.png";
import NVRLogo from "./assets/NVR.png";
import PulteHomesLogo from "./assets/Pulte_Homes.png";
import RichmondAmericanHomes from "./assets/Richmond_American_Homes.png";
import RyanHomesNVR from "./assets/Ryan_Homes-NVR.png";
import TaylorMorrisonLogo from "./assets/Taylor_Morrison.png";
import TollBrothersLogo from "./assets/Toll_Brothers.png";
import TriPointeHomesLogo from "./assets/Tri_Pointe_Homes.png";
import redMarker from "../../../assets/images/mapbox/markers/mapbox-marker-icon-20px-red.png";

const builderLogos = {
  ashton_woods_homes_img: AshtonWoodsHomesLogo,
  beazer_homes_img: BeazerHomesLogo,
  century_communities_img: CenturyCommunitiesLogo,
  clayton_homes_img: ClaytonHomesLogo, // not used
  david_weekley_homes_img: DavidWeekleyHomesLogo,
  dream_finders_homes_img: DreamFindersHomesLogo,
  dr_horton_img: DRHortonLogo,
  heartland_homes_img: HeartlandHomesLogo,
  hovnanian_enterprises_img: HovnanianEnterprisesLogo,
  kb_home_img: KBHomeLogo,
  lennar_img: LennarLogo,
  lgi_home_img: LGIHomes,
  m_i_homes_img: M_I_HomesLogo,
  mattamy_homes_img: MattamyHomesLogo,
  meritage_home_img: MeritageHomesLogo,
  nvr_img: NVRLogo,
  pulte_homes_img: PulteHomesLogo,
  richmond_american_homes_img: RichmondAmericanHomes,
  ryan_homes_nvr_img: RyanHomesNVR,
  taylor_morrison_img: TaylorMorrisonLogo,
  toll_brothers_img: TollBrothersLogo,
  tri_pointe_homes_img: TriPointeHomesLogo,
};

const LandBankContext = createContext();

export const LandBankProvider = ({ children }) => {
  const [filter, setFilter] = useState({
    active: true,
    type: ">=",
    min: 10,
    max: 100,
  });

  const context = useMemo(
    () => ({
      filter,
      setFilter,
    }),
    [filter, setFilter]
  );

  return (
    <LandBankContext.Provider value={context}>
      {children}
    </LandBankContext.Provider>
  );
};

const useLandBank = () => {
  const context = useContext(LandBankContext);
  if (!context) {
    throw new Error("useLandBank must be used within a LandBankProvider");
  }
  return context;
};

const sourceId = "land_bank";
const sourceLayer = sourceId;
const sourceIdClusters = "land_bank_clusters";
const sourceLayerClusters = sourceIdClusters;

const getLayerStyle = (type) => {
  const iconImage = [
    "match",
    ["get", "builder"],
    "Ashton Woods Homes",
    "ashton_woods_homes_img",
    "Beazer Homes",
    "beazer_homes_img",
    "Century Communities",
    "century_communities_img",
    "David Weekley Homes",
    "david_weekley_homes_img",
    "Dream Finders Homes",
    "dream_finders_homes_img",
    "D.R. Horton",
    "dr_horton_img",
    "Express Homes",
    "dr_horton_img",
    "Heartland Homes-NVR",
    "heartland_homes_img",
    "Hovnanian Enterprises",
    "hovnanian_enterprises_img",
    "KB Home",
    "kb_home_img",
    "Lennar",
    "lennar_img",
    "LGI Homes",
    "lgi_home_img",
    "M/I Homes",
    "m_i_homes_img",
    "Mattamy Homes",
    "mattamy_homes_img",
    "Meritage Homes",
    "meritage_home_img",
    "NVHomes-NVR",
    "nvr_img",
    "Pulte Homes",
    "pulte_homes_img",
    "Richmond American Homes",
    "richmond_american_homes_img",
    "Ryan Homes-NVR",
    "ryan_homes_nvr_img",
    "Talor Morrison",
    "taylor_morrison_img",
    "Toll Brothers",
    "toll_brothers_img",
    "Tri Pointe Homes",
    "tri_pointe_homes_img",
    "red-marker",
  ];

  if (type === "default") {
    return {
      id: `${sourceId}-style`,
      source: sourceId,
      "source-layer": sourceLayer,
      minzoom: 12,
      type: "symbol",
      layout: {
        "icon-image": iconImage,
        "icon-allow-overlap": true,
        "icon-size": ["interpolate", ["linear"], ["zoom"], 8, 0.75, 16, 1.5],
      },
    };
  } else if (type === "cluster") {
    return {
      id: `${sourceIdClusters}-style`,
      source: sourceIdClusters,
      "source-layer": sourceLayerClusters,
      type: "symbol",
      maxzoom: 12,
      layout: {
        "icon-image": iconImage,
        "icon-allow-overlap": true,
        "icon-size": 1.25,
        "icon-size": [
          "interpolate",
          ["linear"],
          ["zoom"],
          0,
          1.5,
          5,
          1.25,
          10,
          1,
        ],
        "text-field": "{point_count}",
        "text-size": 20,
        "text-font": ["Arial Unicode MS Bold"],
        "text-allow-overlap": true,
        "text-offset": [0, 1.75],
      },
      paint: {
        "text-color": "white",
        "text-halo-color": "black",
        "text-halo-width": 5,
        "text-halo-blur": 15,
      },
    };
  }
};

const popup = initPopup();

const popupHTML = (data) => {
  const html = (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        gap: "1px",
        padding: "4px 8px",
      }}
    >
      <span>Builder: {data.builder}</span>
      <span>Owner name: {data.owner_name}</span>
      <span>
        Lot size {"(acres)"}: {data.area_acres}
      </span>
    </div>
  );
  return renderToString(html);
};

const getTileURL = (serverType, token) =>
  `${tileURLRoot(
    "cma",
    serverType
  )}/map/tile/land_bank/{z}/{x}/{y}.mvt?access_token=${token}`;
const getTileClusterURL = (serverType, token, filterQuery) =>
  `${tileURLRoot(
    "cma",
    serverType
  )}/map/tile/clustered/land_bank/{z}/{x}/{y}.mvt?${
    filterQuery ? `lot_size=${filterQuery}&` : ""
  }access_token=${token}`;

const LandBankLayer = ({ map, serverType }) => {
  const { filter } = useLandBank();
  const [token, setToken] = useState(null);
  const [layerStyle, setLayerStyle] = useState(getLayerStyle("default"));

  useEffect(() => {
    if (!map) return;

    const mouseMove = async (e) => {
      const features = map.queryRenderedFeatures(e.point, {
        layers: [layerStyle.id],
      });
      if (features.length > 0) {
        const feature = features[0];
        const { fips, apn } = feature.properties;
        const coordinates = [e.lngLat.lng, e.lngLat.lat];

        const data = await getLandBankData({ fips, apn });

        if (!data) return;

        popup.setLngLat(coordinates).setHTML(popupHTML(data)).addTo(map);
      } else {
        popup.remove();
      }
    };

    const mouseLeave = () => {
      popup.remove();
    };

    const checkForLatestToken = async () => {
      popup.isOpen() && popup.remove();
      const newToken = await window.spatiallaser.getUserToken("access");
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    const loadIconImage = () => {
      Object.keys(builderLogos).forEach((key) => {
        map.loadImage(builderLogos[key], (error, image) => {
          if (error) throw error;
          if (!map.hasImage(key)) {
            map.addImage(key, image);
          }
        });
      });

      map.loadImage(redMarker, (error, image) => {
        if (error) throw error;
        if (!map.hasImage("red-marker")) {
          map.addImage("red-marker", image);
        }
      });
    };

    loadIconImage();
    checkForLatestToken();
    map.on("style.load", loadIconImage);
    map.on("movestart", checkForLatestToken);
    map.on("mouseenter", layerStyle.id, mouseMove);
    map.on("mouseleave", layerStyle.id, mouseLeave);
    return () => {
      map.off("style.load", loadIconImage);
      map.off("movestart", checkForLatestToken);
      map.off("mouseenter", layerStyle.id, mouseMove);
      map.off("mouseleave", layerStyle.id, mouseLeave);
    };
  }, [map, filter]);

  useEffect(() => {
    if (filter.active) {
      const newFilter = ["all"];

      if (filter.type === ">=") {
        newFilter.push([filter.type, ["get", "area_acres"], filter.min]);
      } else if (filter.type === "<=") {
        newFilter.push([filter.type, ["get", "area_acres"], filter.max]);
      } else if (filter.type === "<->") {
        newFilter.push([">=", ["get", "area_acres"], filter.min]);
        newFilter.push(["<=", ["get", "area_acres"], filter.max]);
      }

      const newLayerStyle = {
        ...layerStyle,
        ...(newFilter.length > 1 ? { filter: newFilter } : {}),
      };

      setLayerStyle(newLayerStyle);
    } else {
      setLayerStyle(getLayerStyle("default"));
    }
  }, [filter]);

  if (!map || !token) return null;
  return (
    <>
      <Source
        id={sourceId}
        type="vector"
        tiles={[getTileURL(serverType, token)]}
      >
        <Layer {...layerStyle} />
      </Source>
      <Source
        id={sourceIdClusters}
        type="vector"
        tiles={[
          getTileClusterURL(serverType, token, generateURLFilterQuery(filter)),
        ]}
      >
        <Layer {...getLayerStyle("cluster")} />
      </Source>
    </>
  );
};

export default LandBankLayer;

const generateURLFilterQuery = (filter) => {
  if (!filter.active) return null;
  if (filter.type === ">=") {
    return `gte,${filter.min}`;
  } else if (filter.type === "<=") {
    return `lte,${filter.max}`;
  } else if (filter.type === "<->") {
    return `range,${filter.min},${filter.max}`;
  }
  return null;
};

export const LandBankLegend = ({ map }) => {
  const { filter, setFilter } = useLandBank();
  const [bounds, setBounds] = useState(null);

  useEffect(() => {
    if (!map) return;

    const moveEnd = () => {
      setBounds(map.getBounds().toArray().reverse().toString());
    };

    moveEnd();
    map.on("moveend", moveEnd);
    return () => {
      map.off("moveend", moveEnd);
    };
  }, [map]);

  if (!map) return;
  return (
    <div
      id="land_bank_legend"
      className="flex flex-col gap-2 bg-white min-w-[300px] max-w-[300px] p-4 select-none"
    >
      <div className="flex flex-row justify-center">
        <strong>Land Bank</strong>
      </div>
      <div className="flex flex-col gap-2">
        <div className="flex flex-row gap-2 items-center">
          <Switch
            checked={filter.active}
            onChange={(active) => setFilter({ ...filter, active })}
            size="small"
          />
          <span>Lot Size {"(acres)"}</span>
        </div>
        {filter.active && (
          <div className="flex flex-row gap-2">
            <Select
              value={filter.type}
              onChange={(type) => setFilter({ ...filter, type })}
              options={[
                { label: ">=", value: ">=" },
                { label: "<=", value: "<=" },
                { label: "<->", value: "<->" },
              ]}
              style={{ width: "80px" }}
            />
            {(filter.type === "<->" || filter.type === ">=") && (
              <InputNumber
                className="w-full"
                formatter={(value) => `${Number(value).toLocaleString()}`}
                value={filter.min}
                min={1}
                onChange={(value) => setFilter({ ...filter, min: value })}
              />
            )}
            {(filter.type === "<->" || filter.type === "<=") && (
              <InputNumber
                className="w-full"
                value={filter.max}
                min={1}
                formatter={(value) => `${Number(value).toLocaleString()}`}
                onChange={(value) => setFilter({ ...filter, max: value })}
              />
            )}
          </div>
        )}
      </div>
      <LandBankCounter
        bounds={bounds}
        filterQuery={generateURLFilterQuery(filter)}
      />
    </div>
  );
};

const LandBankCounter = ({ bounds, filterQuery }) => {
  const { data } = useQuery(
    ["land_bank_count", bounds, filterQuery],
    () => getLandBankCountData({ bounds, lot_size: filterQuery }),
    {
      enabled: !!bounds,
      keepPreviousData: true,
    }
  );

  return (
    <>
      <div className="flex flex-col select-text">
        {data &&
          data.map((d, i) => (
            <div
              key={i}
              className="flex flex-row justify-between pr-4 py-[1.5px]"
            >
              <span>{d.builder}</span>
              <span>{d.builder_count.toLocaleString()}</span>
            </div>
          ))}
      </div>
      <div>
        <span>
          Total:{" "}
          {data &&
            data
              .reduce((acc, curr) => acc + curr.builder_count, 0)
              .toLocaleString()}
        </span>
      </div>
    </>
  );
};
