import {
  useState,
  createContext,
  useContext,
  useMemo,
  useRef,
  useCallback,
} from "react";
import { useSelector } from "react-redux";
import { copyToClipboard, openInNewTab } from "../utils";

const ContextMenuContext = createContext(undefined);

export const ContextMenuProvider = ({ children, map }) => {
  const menuRef = useRef(null);
  const [coordinates, setCoordinates] = useState(null);
  const [geocodingResults, setGeocodingResults] = useState({});
  const parcelMode = useSelector((state) => state.Map.parcelMode);

  const onCopyToClipboard = useCallback((text, msg = undefined) => {
    copyToClipboard(text, msg);
    menuRef.current.style.display = "none";
  }, []);

  const onOpenInNewTab = useCallback((url) => {
    openInNewTab(url);
    menuRef.current.style.display = "none";
  }, []);

  const context = useMemo(() => {
    return {
      map,
      menuRef,
      coordinates,
      setCoordinates,
      geocodingResults,
      setGeocodingResults,
      onCopyToClipboard,
      onOpenInNewTab,
      parcelMode,
    };
  }, [map, menuRef, coordinates, geocodingResults, parcelMode]);

  return (
    <ContextMenuContext.Provider value={context}>
      {children}
    </ContextMenuContext.Provider>
  );
};

export const useContextMenu = () => {
  const context = useContext(ContextMenuContext);
  if (context === undefined) {
    throw new Error("useContextMenu must be used within a ContextMenuProvider");
  }
  return context;
};
