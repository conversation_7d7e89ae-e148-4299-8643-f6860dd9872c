import { Spin, message } from "antd";

message.config({
  maxCount: 1,
  duration: 1,
  getContainer: () => document.getElementById("Map"),
});

export const MenuItem = ({
  isLoading = false,
  title,
  icon,
  actions,
  style = undefined,
}) => {
  const multiOptions = Array.isArray(actions) && actions.length > 1;

  if (!isLoading && (!title || !actions || !actions.length)) return null;
  return (
    <div
      className={`flex flex-row items-center justify-between py-2 px-2 gap-1 bg-white select-none${
        !multiOptions ? " group hover:bg-[#efefef] cursor-pointer" : ""
      }`}
      style={{ ...style }}
      onClick={() => {
        if (multiOptions) return;
        actions && actions.length > 0 && actions[0].action();
      }}
      onContextMenu={(e) => e.preventDefault()}
    >
      <div className="flex flex-row items-center gap-1 min-w-0">
        {icon && (
          <div className="min-w-[24px] max-w-[24px] h-[24px] overflow-hidden">
            {icon}
          </div>
        )}
        {!isLoading && (
          <span className="text-base whitespace-nowrap min-w-0 overflow-hidden text-ellipsis">
            {title}
          </span>
        )}
        {isLoading && (
          <div className="ml-2">
            <Spin size="small" />
          </div>
        )}
      </div>
      {!isLoading && (
        <div
          className={`flex flex-row justify-center items-center ${
            !multiOptions ? "pr-2" : ""
          }`}
        >
          {!multiOptions && actions && actions.length > 0 && (
            <>
              {typeof actions[0].icon === "object" &&
                actions[0].icon["$$typeof"] === Symbol.for("react.element") &&
                actions[0].icon}
              {typeof actions[0].icon === "function" && actions[0].icon()}
            </>
          )}
          {multiOptions && (
            <>
              {actions.map((action, index) => (
                <button
                  key={index}
                  className="group bg-transparent rounded-full border-none bg-white hover:bg-[#eee] cursor-pointer w-[30px] h-[30px]"
                  onClick={() => {
                    action.action();
                  }}
                >
                  {typeof action.icon === "object" &&
                    action.icon["$$typeof"] === Symbol.for("react.element") &&
                    action.icon}
                  {typeof action.icon === "function" && action.icon()}
                </button>
              ))}
            </>
          )}
        </div>
      )}
    </div>
  );
};
