import React from "react";
import { Mo<PERSON>, <PERSON>, Button, InputNumber, Select } from "antd";
import { useQuery } from "react-query";
import {
  getSitePlanData,
  getParcelBoundaryOfCoordinateData,
} from "../../../../services/data";
import Source from "../../../Map/MapLayers/Source";
import Layer from "../../../Map/MapLayers/Layer";
import { default as turf_bbox } from "@turf/bbox";
import { useMap } from "../../../Map/MapProvider";
import Draggable from "react-draggable";

const DEFAULT_FORM_STATE = {
  width_bldg: 45,
  depth_bldg: 75,
  width_road: 50,
  exit_dir: "S",
  beds: 3,
  baths: 2,
  sqft: 1500,
  year_built: new Date().getFullYear(),
};

export const SitePlanGenerator = ({ latitude, longitude }) => {
  const [sitePlanInput, setSitePlanInput] = React.useState(null);
  const { data, isLoading } = useQuery(
    ["site-plan", JSON.stringify(sitePlanInput)],
    () => getSitePlanData({ ...sitePlanInput }),
    {
      enabled: sitePlanInput !== null,
    }
  );

  return (
    <>
      <SitePlanModal
        setSitePlanInput={setSitePlanInput}
        isLoading={isLoading}
        data={data}
        latitude={latitude}
        longitude={longitude}
      />
      <SitePlanLayer data={data} setSitePlanInput={setSitePlanInput} />
      <SitePlanBoundarySelector latitude={latitude} longitude={longitude} />
    </>
  );
};

const SitePlanModal = ({
  isLoading,
  data,
  setSitePlanInput,
  latitude,
  longitude,
}) => {
  const { map, openSitePlanModal, setOpenSitePlanModal } = useMap();
  const [disabled, setDisabled] = React.useState(true);
  const [bounds, setBounds] = React.useState({
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  });
  const draggleRef = React.useRef(null);
  const [form] = Form.useForm();

  React.useEffect(() => {
    if (!openSitePlanModal) return;
    form.resetFields();
  }, [openSitePlanModal]);

  const onStart = (_event, uiData) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    if (!targetRect) {
      return;
    }
    setBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };

  const handleGenerateSitePlan = React.useCallback(
    (values) => {
      if (!latitude || !longitude) return;
      setSitePlanInput({
        lat: latitude,
        lng: longitude,
        ...values,
      });
      // setOpenSitePlanModal(false);
    },
    [latitude, longitude]
  );

  const onClickView = React.useCallback(() => {
    if (!map || !data) return;
    map.fitBounds(turf_bbox(data), { padding: 32 });
  }, [map, data]);

  const onClickClear = React.useCallback(() => {
    setSitePlanInput(null);
  }, []);

  return (
    <Modal
      // title={"Generate a Site Plan"}
      title={
        <div
          style={{
            width: "100%",
            cursor: "move",
          }}
          onMouseOver={() => {
            if (disabled) {
              setDisabled(false);
            }
          }}
          onMouseOut={() => {
            setDisabled(true);
          }}
          // fix eslintjsx-a11y/mouse-events-have-key-events
          // https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/master/docs/rules/mouse-events-have-key-events.md
          onFocus={() => {}}
          onBlur={() => {}}
          // end
        >
          Generate a Site Plan
        </div>
      }
      open={openSitePlanModal}
      centered
      width={400}
      footer={null}
      mask={false}
      maskClosable={false}
      onCancel={() => {
        setOpenSitePlanModal(false);
      }}
      modalRender={(modal) => (
        <Draggable
          disabled={disabled}
          bounds={bounds}
          nodeRef={draggleRef}
          onStart={(event, uiData) => onStart(event, uiData)}
        >
          <div ref={draggleRef}>{modal}</div>
        </Draggable>
      )}
      wrapClassName={"pointer-events-none"}
    >
      <Form
        form={form}
        initialValues={DEFAULT_FORM_STATE}
        onFinish={handleGenerateSitePlan}
      >
        <span className="font-semibold">Average Parcel Dimension</span>
        <div className="flex flex-row gap-4 w-full">
          <div className="flex flex-col gap-1 w-full">
            <span>{"Width (ft):"}</span>
            <Form.Item
              name="width_bldg"
              rules={[{ required: true, message: "* Width required" }]}
              className="mb-2"
            >
              <InputNumber min={0} style={{ width: "100%" }} />
            </Form.Item>
          </div>
          <div className="flex flex-col gap-1 w-full">
            <span>{"Depth (ft):"}</span>
            <Form.Item
              name="depth_bldg"
              rules={[{ required: true, message: "* Depth required" }]}
              className="mb-2"
            >
              <InputNumber min={0} style={{ width: "100%" }} />
            </Form.Item>
          </div>
        </div>
        <span className="font-semibold">Average Road Dimension and Exit</span>
        <div className="flex flex-row gap-4 w-full">
          <div className="flex flex-col gap-1 w-full">
            <span>{"Width (ft):"}</span>
            <Form.Item
              name="width_road"
              rules={[{ required: true, message: "* Width required" }]}
              className="mb-4"
            >
              <InputNumber min={0} style={{ width: "100%" }} />
            </Form.Item>
          </div>
          <div className="flex flex-col gap-1 w-full">
            <span>{"Exit Direction:"}</span>
            <Form.Item name="exit_dir" className="mb-4">
              <Select
                options={[
                  { label: "North", value: "N" },
                  { label: "South", value: "S" },
                  { label: "East", value: "E" },
                  { label: "West", value: "W" },
                ]}
              />
            </Form.Item>
          </div>
        </div>
        <span className="font-semibold">Average Home Attributes</span>
        <div className="flex flex-row gap-4 w-full">
          <div className="flex flex-col gap-1 w-full">
            <span>{"Beds:"}</span>
            <Form.Item name="beds" className="mb-4">
              <InputNumber min={0} style={{ width: "100%" }} />
            </Form.Item>
          </div>
          <div className="flex flex-col gap-1 w-full">
            <span>{"Baths:"}</span>
            <Form.Item name="baths" className="mb-4">
              <InputNumber min={0} style={{ width: "100%" }} />
            </Form.Item>
          </div>
          <div className="flex flex-col gap-1 w-full">
            <span>{"Sqft:"}</span>
            <Form.Item name="sqft" className="mb-4">
              <InputNumber min={0} style={{ width: "100%" }} />
            </Form.Item>
          </div>
          <div className="flex flex-col gap-1 w-full">
            <span>{"Year Built:"}</span>
            <Form.Item name="year_built" className="mb-4">
              <InputNumber min={0} style={{ width: "100%" }} />
            </Form.Item>
          </div>
        </div>
        <div className="flex flex-row gap-4 justify-end">
          <Form.Item className="mb-0">
            <Button
              type="primary"
              htmlType="submit"
              loading={isLoading}
              disabled={!latitude || !longitude ? true : false}
            >
              Generate
            </Button>
          </Form.Item>
        </div>
      </Form>
      {data && data.properties && (
        <div>
          <div className="py-2">
            <span className="font-semibold">Site Plan Summary</span>
            <div className="flex flex-col gap-1">
              <span>Parcels: {data.properties.parcel_count}</span>
              <span>
                Estimated Annual Rental Income: ${" "}
                {data.properties.rent.toLocaleString() || "-"}
              </span>
              <span>
                Estimated Total Asset Value: ${" "}
                {data.properties.sales.toLocaleString() || "-"}
              </span>
            </div>
          </div>

          <div className="flex flex-row justify-end gap-4">
            <Button
              type="primary"
              onClick={() => onClickView()}
              disabled={
                !data || !data.features || data.features.length === 0
                  ? true
                  : false
              }
            >
              View
            </Button>
            <Button onClick={() => onClickClear()}>Clear</Button>
          </div>
        </div>
      )}
    </Modal>
  );
};

const SITE_PLAN_LINE_STYLE = {
  id: `site-plan`,
  type: "line",
  paint: {
    "line-color": "red",
    "line-width": 2,
  },
};

const EmptyGeoJSON = {
  type: "FeatureCollection",
  features: [],
};

const SitePlanLayer = ({ data }) => {
  return (
    <React.Fragment>
      <Source id={`site-plan`} type="geojson" data={data || EmptyGeoJSON}>
        <Layer {...SITE_PLAN_LINE_STYLE} />
      </Source>
    </React.Fragment>
  );
};

const SitePlanBoundarySelector = ({ latitude, longitude }) => {
  const { openSitePlanModal } = useMap();
  const { data } = useQuery(
    ["site-plan", JSON.stringify({ lat: latitude, lng: longitude })],
    () => getParcelBoundaryOfCoordinateData({ lat: latitude, lng: longitude }),
    {
      enabled: latitude && longitude && openSitePlanModal ? true : false,
    }
  );

  if (!latitude || !longitude || !openSitePlanModal) return null;
  return (
    <Source
      id={`site-plan-selected-boundary`}
      type="geojson"
      data={data || EmptyGeoJSON}
    >
      <Layer
        id="site-plan-selected-bound"
        type="line"
        paint={{
          "line-color": "red",
          "line-width": 5,
        }}
      />
    </Source>
  );
};
