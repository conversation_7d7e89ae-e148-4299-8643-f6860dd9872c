import { useEffect, useCallback, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useContextMenu } from "./ContextMenuProvider";
import { minZoom } from "../../../Map/MapLayers/Regrid";
import { useWhat3Words } from "../hooks/useWhat3Words";
import { useGoogleReverseGeocoding } from "../hooks/useGoogleReverseGeocoding";
import { MenuItem } from "./MenuItem";

import { MdLocationOn } from "@react-icons/all-files/md/MdLocationOn";
import { FaHome } from "@react-icons/all-files/fa/FaHome";
import { FaRegCopy } from "@react-icons/all-files/fa/FaRegCopy";
import { ImNewTab } from "@react-icons/all-files/im/ImNewTab";
import { IoMdDownload } from "@react-icons/all-files/io/IoMdDownload";
import { MdEmail } from "@react-icons/all-files/md/MdEmail";
import { IoIosSend } from "@react-icons/all-files/io/IoIosSend";
import { FcPicture } from "@react-icons/all-files/fc/FcPicture";
import { GrVirtualMachine } from "@react-icons/all-files/gr/GrVirtualMachine";
import { FaPlus } from "@react-icons/all-files/fa/FaPlus";
import { Expand } from "lucide-react";

import plusCode from "../assets/pluscode.png";
import what3words from "../assets/3words.png";

import { Modal } from "antd";
import { ExclamationCircleOutlined } from "@ant-design/icons";

import {
  getDirectionSchemas,
  getContextMenuPosition,
  takeMapScreenshot,
  sendToEmail,
  uploadToS3,
  toggleParcelMode,
} from "../utils";
import { useMap } from "../../../Map/MapProvider";

const Z_INDEX = 99999;

export const ContextMenu = () => {
  const {
    map,
    menuRef,
    coordinates,
    setCoordinates,
    onCopyToClipboard,
    parcelMode,
  } = useContextMenu();

  useEffect(() => {
    if (!map) return;

    const openContextMenu = (e) => {
      e.preventDefault();
      if (!menuRef.current) return;
      setCoordinates(e.lngLat);
      const mapBounds = document.getElementById("Map").getBoundingClientRect();
      const mouseX = e.point.x + mapBounds.left; // x relative to the window
      const mouseY = e.point.y + mapBounds.top; // y relative to the window

      const { left, right, top, bottom } = getContextMenuPosition(
        { x: mouseX, y: mouseY },
        mapBounds
      );

      menuRef.current.style.right = right;
      menuRef.current.style.left = left;
      menuRef.current.style.top = top;
      menuRef.current.style.bottom = bottom;
      menuRef.current.style.display = "block";
    };

    const closeContextMenu = (e) => {
      const ctxMenu = document
        .getElementById("locate-alpha-context-menu")
        .getBoundingClientRect();
      if (
        e.clientX >= ctxMenu.left &&
        e.clientX <= ctxMenu.right &&
        e.clientY >= ctxMenu.top &&
        e.clientY <= ctxMenu.bottom
      )
        return;

      menuRef.current.style.display = "none";
    };

    map.on("contextmenu", openContextMenu);
    window.addEventListener("click", closeContextMenu);
    window.addEventListener("contextmenu", closeContextMenu);
    window.addEventListener("wheel", closeContextMenu);
    window.addEventListener("mousedown", closeContextMenu);
    window.addEventListener("resize", closeContextMenu);
    return () => {
      map.off("contextmenu", openContextMenu);
      window.removeEventListener("click", closeContextMenu);
      window.removeEventListener("contextmenu", closeContextMenu);
      window.removeEventListener("wheel", closeContextMenu);
      window.removeEventListener("mousedown", closeContextMenu);
      window.removeEventListener("resize", closeContextMenu);
    };
  }, [map]);

  return (
    <div
      id={"locate-alpha-context-menu"}
      ref={menuRef}
      style={{
        display: "none",
        boxShadow:
          "0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)",
        zIndex: Z_INDEX,
      }}
      className="absolute bg-white border border-solid border-[#eee] rounded-xl overflow-hidden w-[250px]"
    >
      {coordinates && (
        <div className="flex flex-col">
          <ParcelModeItem />
          <MenuItem
            title={`${coordinates.lat.toFixed(5)}, ${coordinates.lng.toFixed(
              5
            )}`}
            icon={<MdLocationOn size={24} color={"#34c759"} />}
            style={{ borderBottom: "1px solid #ccc" }}
            actions={[
              {
                icon: (
                  <FaRegCopy
                    size={16}
                    className="text-[#777] group-hover:text-[#333]"
                  />
                ),
                action: () =>
                  onCopyToClipboard(
                    `${coordinates.lat}, ${coordinates.lng}`,
                    "Copied coordinates to clipboard"
                  ),
              },
            ]}
          />
          <GeocodedItems />
          <DirectionItems />
          {/* temp */}
          <ShareItems />
          {parcelMode && (
            <>
              <div
                className="text-center"
                style={{ borderBottom: "1px solid #ccc" }}
              >
                <strong>Other</strong>
              </div>
              <OtherItems />
            </>
          )}
        </div>
      )}
    </div>
  );
};

// prettier-ignore
const GeocodedItems = () => {
  return (
    <>
    <GoogleGeocodedItems />
    <What3WordsItem />
    </>
  )
}

// prettier-ignore
const GoogleGeocodedItems = () => {
  const { geocodingResults: { google: data }, onCopyToClipboard } = useContextMenu();
  const { isLoading, isError } = useGoogleReverseGeocoding();

  if ((!isLoading && !data) || isError) return null;
  return (
    <>
    <MenuItem
      isLoading={isLoading}
      title={data && data.address ? data.address.split(",")[0] : null}
      icon={<FaHome size={24} color={"#007aff"} />}
      style={{ borderBottom: "1px solid #ccc" }}
      actions={[
        {
          icon: <FaRegCopy size={16} className="text-[#777] group-hover:text-[#333]" />,
          action: () => onCopyToClipboard(data.address, "Copied address to clipboard"),
        },
      ]}
    />
    <MenuItem
      isLoading={isLoading}
      title={data && (data.compound_code || data.global_code) ? data.compound_code || data.global_code : null}
      icon={<img src={plusCode} alt="pluscode" width="100%" height="auto" draggable={false} />}
      style={{ borderBottom: "1px solid #ccc" }}
      actions={[
        {
          icon: <FaRegCopy size={16} className="text-[#777] group-hover:text-[#333]" />,
          action: () => onCopyToClipboard(data.compound_code || data.global_code, "Copied Plus Code to clipboard"),
        },
      ]}
    />
    </>
  )
}

// prettier-ignore
const What3WordsItem = () => {
  const { geocodingResults: { what3words: data }, onCopyToClipboard } = useContextMenu();
  const { isLoading, isError } = useWhat3Words();

  if ((!isLoading && !data) || isError) return null;
  return (
    <MenuItem
      isLoading={isLoading}
      title={data}
      icon={
        <img src={what3words} alt="what3words" width="100%" height="auto" draggable={false} />
      }
      style={{ borderBottom: "1px solid #ccc" }}
      actions={[
        {
          icon: <FaRegCopy size={16} className="text-[#777] group-hover:text-[#333]" />,
          action: () => onCopyToClipboard(data, "Copied what3words to clipboard"),
        },
      ]}
    />
  );
};

// prettier-ignore
const DirectionItems = () => {
  const { coordinates, onCopyToClipboard, onOpenInNewTab } = useContextMenu();

  if (!coordinates) return null;
  return (
    <>
      <div className="text-center" style={{ borderBottom: "1px solid #ccc" }}>
        <strong>Directions</strong>
      </div>
      {getDirectionSchemas(coordinates).map(({title, url, icon}) => (
        <MenuItem
          key={title}
          title={title}
          icon={<img src={icon} alt={`${title} icon`} width="100%" height="auto" draggable={false} />}
          style={{ borderBottom: "1px solid #ccc" }}
          actions={[
            {
              icon: <FaRegCopy size={16} className="text-[#777] group-hover:text-[#333]" />,
              action: () => onCopyToClipboard(url, `Copied ${title} URL direction to clipboard`),
            },
            {
              icon: <ImNewTab size={18} className="text-[#777] group-hover:text-[#333]" />,
              action: () => onOpenInNewTab(url),
            },
          ]}
        />
      ))}
    </>
  );
};

// prettier-ignore
const ShareItems = () => {
  const { map, menuRef, coordinates, geocodingResults: { google, what3words } } = useContextMenu();

  const onTakeScreenshot = useCallback(() => {
    if (!map) return;
    let title = `${google?.address ? `${google.address.split(",")[0].replace(/ /g, "_")}` : `${coordinates.lat.toFixed(5)}_${coordinates.lng.toFixed(5)}_${google.compound_code}`}-From_Locate_Alpha`;
    takeMapScreenshot(map, `${title}.png`, coordinates);
    menuRef.current.style.display = "none";
  }, [map, coordinates.lat, coordinates.lng, google]);

  const onSendtoEmail = useCallback(async () => {
    if (!map) return;

    const sendEmail = async (includeScreenshot) => {
      // NOTE: Apparently it is bad or security risk RFC2368 - https://www.ietf.org/rfc/rfc2368.txt
      // unicodes generated from https://yaytext.com/bold-italic/
      let content = "%0D%0A%0D%0A";
      content += "====== 📌 𝗟𝗼𝗰𝗮𝘁𝗶𝗼𝗻 𝗗𝗲𝘁𝗮𝗶𝗹𝘀 ======%0D%0A";
      content += `Coordinates: ${encodeURIComponent(coordinates.lat)}, ${encodeURIComponent(coordinates.lng)}%0D%0A`; 
      content += google?.address ? `Address: ${encodeURIComponent(google.address)}%0D%0A` : "";
      content += google?.compound_code ? `Plus Code: ${encodeURIComponent(google.compound_code)}%0D%0A` : "";
      content += what3words ? `What3Words: ${encodeURIComponent(what3words)}%0D%0A` : "";
      content += "%0D%0A";
      content += "====== 🚗 𝗗𝗶𝗿𝗲𝗰𝘁𝗶𝗼𝗻𝘀 ======%0D%0A";
      getDirectionSchemas(coordinates).forEach(({ title, url }) => {
        content += `${encodeURIComponent(title)}: ${encodeURIComponent(url)}%0D%0A`;
      });
      content += "%0D%0A";

      if (includeScreenshot) {
        try {
          const mapImgURL = await uploadToS3(map, coordinates);
          content += "====== 🏞️ 𝗦𝗰𝗿𝗲𝗲𝗻𝘀𝗵𝗼𝘁 (𝗘𝘅𝗽𝗶𝗿𝗲𝘀 𝗶𝗻 𝟭𝟰 𝗱𝗮𝘆𝘀) ======%0D%0A";
          content += `Screenshot: ${encodeURIComponent(mapImgURL)}%0D%0A`;
        } catch (error) {
          content += "====== 🏞️ 𝗦𝗰𝗿𝗲𝗲𝗻𝘀𝗵𝗼𝘁 ======%0D%0A";
          content += "Sorry, failed to include screenshot%0D%0A";
        }
      }

      content += "%0D%0A%0D%0ALocation shared from Locate Alpha%0D%0A%0D%0A";

      let title = `${google?.address ? `${google.address.split(",")[0]}` : `${coordinates.lat.toFixed(5)}, ${coordinates.lng.toFixed(5)}`} - From Locate Alpha`;
      sendToEmail(title, content);
      menuRef.current.style.display = "none";
    }

    const confirm = () => {
      Modal.confirm({
        title: "Send Email",
        icon: <ExclamationCircleOutlined />,
        content: "Would you like to send the map screenshot to your email?",
        okText: 'Yes',
        cancelText: 'No',
        centered: true,
        getContainer: () => document.getElementById("Map"),
        onOk: () => sendEmail(true),
        onCancel: () => sendEmail(false),
        zIndex: Z_INDEX + 1,
      });
    };
    confirm();
  }, [map, coordinates.lat, coordinates.lng, google, what3words]);

  return (
    <>
      <div className="text-center" style={{ borderBottom: "1px solid #ccc" }}>
        <strong>Share</strong>
      </div>
      <MenuItem
        title="Screenshot map"
        icon={<FcPicture size={24} color={"#007aff"} />}
        actions={[
          {
            icon: <IoMdDownload size={16} className="text-[#777] group-hover:text-[#333]" />,
            action: () => onTakeScreenshot(),
          },
        ]}
      />
      <MenuItem
        title="Email"
        icon={<MdEmail size={24} color={"#007aff"} />}
        actions={[
          {
            icon:<IoIosSend size={16} className="text-[#777] group-hover:text-[#333]" />,
            action: () => onSendtoEmail(),
          },
        ]}
      />
    </>
  );
};

const ParcelModeItem = () => {
  const { map, menuRef, parcelMode } = useContextMenu();
  const [parcelSwitch, setParcelSwitch] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    if (!map) return;

    const zoomEnd = () => {
      const zoom = map.getZoom();
      if (zoom >= minZoom) {
        if (!parcelSwitch) setParcelSwitch(true);
      } else {
        if (parcelSwitch) setParcelSwitch(false);
      }
    };

    map.on("zoomend", zoomEnd);
    // Check initial zoom level
    const initialZoom = map.getZoom();
    if (initialZoom >= minZoom) {
      setParcelSwitch(true);
    }

    return () => {
      map.off("zoomend", zoomEnd);
    };
  }, [map, parcelSwitch]);

  const handleToggleParcelMode = useCallback(() => {
    toggleParcelMode(map, dispatch, parcelMode);
    if (menuRef.current) {
      menuRef.current.style.display = "none";
    }
  }, [map, dispatch, parcelMode, menuRef]);

  if (!parcelSwitch) return null;

  const displayText = parcelMode ? "Turn off parcel Mode" : "Turn on parcel Mode";

  return (
    <div
      className="flex flex-row items-center justify-between py-2 px-2 gap-1 bg-white select-none group hover:bg-[#efefef] cursor-pointer"
      style={{ borderBottom: "1px solid #ccc" }}
      onClick={handleToggleParcelMode}
      onContextMenu={(e) => e.preventDefault()}
    >
      <div className="flex flex-row items-center gap-1 min-w-0">
        <div className="min-w-[24px] max-w-[24px] h-[24px] overflow-hidden flex items-center justify-center">
          <Expand size={20} color={parcelMode ? "#34c759" : "#999"} />
        </div>
        <span className="text-base whitespace-nowrap min-w-0 overflow-hidden text-ellipsis pl-2">
          {displayText}
        </span>
      </div>
    </div>
  );
};

const OtherItems = () => {
  const { menuRef } = useContextMenu();
  const { setOpenSitePlanModal } = useMap();

  const onGenerateSitePlan = useCallback(() => {
    setOpenSitePlanModal(true);
    if (menuRef.current) {
      menuRef.current.style.display = "none";
    }
  }, [menuRef.current]);

  return (
    <MenuItem
      title="Generate Site Plan"
      icon={<GrVirtualMachine size={24} color={"#007aff"} />}
      actions={[
        {
          icon: (
            <FaPlus size={16} className="text-[#777] group-hover:text-[#333]" />
          ),
          action: () => onGenerateSitePlan(),
        },
      ]}
    />
  );
};
