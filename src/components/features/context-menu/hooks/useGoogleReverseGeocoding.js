import { useState, useEffect } from "react";
import { useContextMenu } from "../components/ContextMenuProvider";

export const useGoogleReverseGeocoding = () => {
  const { coordinates, setGeocodingResults } = useContextMenu();
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    const abortController = new AbortController();
    const signal = abortController.signal;

    const fetchData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/geocode/json?latlng=${coordinates.lat},${coordinates.lng}&key=AIzaSyCjNoMR5ZTHEyXyqOdMrFWKWOCbE_JfynQ`,
          { signal: signal }
        );
        const json = await response.json();

        const result = {
          ...json.plus_code,
        };
        // the first item needs to be rooftop accuracy
        if (json.results[0].geometry.location_type === "ROOFTOP") {
          result["address"] = json.results[0].formatted_address;
        }

        setGeocodingResults((prev) => ({
          ...prev,
          google: result,
        }));
        if (isError) setIsError(false);
      } catch (error) {
        setIsError(true);
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();

    return () => {
      abortController.abort();
      setIsLoading(true);
      setIsError(false);
      setGeocodingResults((prev) => ({
        ...prev,
        google: null,
      }));
    };
  }, [coordinates.lat, coordinates.lng]);

  return { isLoading, isError };
};
