import { useState, useEffect } from "react";
import { useContextMenu } from "../components/ContextMenuProvider";

export const useWhat3Words = () => {
  const { coordinates, setGeocodingResults } = useContextMenu();
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    const abortController = new AbortController();
    const signal = abortController.signal;

    const fetchData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(
          `https://mapapi.what3words.com/api/convert-to-3wa?coordinates=${coordinates.lat},${coordinates.lng}&language=en&format=json`,
          { signal: signal }
        );
        const result = await response.json();
        setGeocodingResults((prev) => ({
          ...prev,
          what3words: result.words,
        }));
        if (isError) setIsError(false);
      } catch (error) {
        setIsError(true);
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();

    return () => {
      abortController.abort();
      setIsLoading(true);
      setIsError(false);
      setGeocodingResults((prev) => ({
        ...prev,
        what3words: null,
      }));
    };
  }, [coordinates.lat, coordinates.lng]);

  return { isLoading, isError };
};
