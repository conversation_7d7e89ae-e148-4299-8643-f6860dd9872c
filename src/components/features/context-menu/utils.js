import { toPng } from "html-to-image";
import { message } from "antd";
import AWS from "aws-sdk";
import googleMaps from "./assets/google.png";
import bingMaps from "./assets/bing.png";
import waze from "./assets/waze.png";
import citymapper from "./assets/citymapper.png";
import mapboxgl from "mapbox-gl"; // eslint-disable-line import/no-webpack-loader-syntax

message.config({
  maxCount: 1,
  duration: 1,
  getContainer: () => document.getElementById("Map"),
});

export const copyToClipboard = (text, msg = undefined) => {
  navigator.clipboard.writeText(text);
  message.success(msg ? msg : "Copied to clipboard");
};

export const openInNewTab = (url) => {
  window.open(url, "_blank", "noopener noreferrer");
};

export const toggleParcelMode = (map, dispatch, parcelMode) => {
  const newParcelMode = !parcelMode;
  dispatch({
    type: "Map/saveState",
    payload: {
      parcelMode: newParcelMode,
    },
  });
  map.fire("selectRadius.parcelMode", {
    payload: { parcelMode: newParcelMode },
  });
};

export const getDirectionSchemas = (coordinates) => [
  {
    title: "Google Maps",
    url: `https://www.google.com/maps/dir/?api=1&destination=${coordinates.lat},${coordinates.lng}`,
    icon: googleMaps,
  },
  {
    title: "Bing Maps",
    url: `http://bing.com/maps/default.aspx?rtp=~pos.${coordinates.lat}_${coordinates.lng}`,
    icon: bingMaps,
  },
  {
    title: "Waze",
    url: `https://www.waze.com/ul?ll=${coordinates.lat}%2C${coordinates.lng}&z=17`,
    icon: waze,
  },
  {
    title: "Citymapper",
    url: `https://citymapper.com/directions?startcoord=&endcoord=${coordinates.lat}%2C${coordinates.lng}&endname=From%20Locate%20Alpha`,
    icon: citymapper,
  },
];

// prettier-ignore
export const getContextMenuPosition = (mouseCoordinate, containerBounds) => {
  const { left, right, top, bottom } = containerBounds;
  const { x, y } = mouseCoordinate;
  const position = { left: "auto", right: "auto", top: "auto", bottom: "auto" };

  // No solution to dynamically calculate the position of the context menu as it dynamically changes size based on the content
  // The following code is a workaround to ensure the context menu is always visible on the screen
  const MAX_MENU_WIDTH = 250;
  const MAX_MENU_HEIGHT = 480;

  if (x > (left + right) / 2) {
    position.right = right - x + "px";
    if (x - MAX_MENU_WIDTH < left) {
      position.right = "auto";
      position.left = "0px";
    }
  } else {
    position.left = x - left + "px";    
    if (x + MAX_MENU_WIDTH > right) {
      position.left = "auto";
      position.right = "0px";
    }
  }

  if (y > (top + bottom) / 2) {
    position.bottom = bottom - y + "px";
    if (y - MAX_MENU_HEIGHT < top) {
      position.bottom = "auto";
      position.top = "0px";
    }
  } else {
    position.top = y - top + "px";
    if (y + MAX_MENU_HEIGHT > bottom) {
      position.top = "auto";
      position.bottom = "0px";
    }
  }

  return position;
};

const getScreenshotDimensions = (size, imgWidth, imgHeight) => {
  let newHeight, newWidth, xStart, yStart;
  if (imgHeight > imgWidth) {
    newHeight = imgHeight * (size / imgWidth);
    newWidth = size;
    xStart = 0;
    yStart = -(newHeight / 2 - size / 2);
  } else if (imgWidth > imgHeight) {
    newWidth = imgWidth * (size / imgHeight);
    newHeight = size;
    yStart = 0;
    xStart = -(newWidth / 2 - size / 2);
  } else {
    newWidth = 1000;
    newHeight = 1000;
    xStart = 0;
    yStart = 0;
  }
  return { xStart, yStart, width: newWidth, height: newHeight };
};

const compressImage = (imgData, size) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = imgData;

    img.onload = () => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      canvas.width = size;
      canvas.height = size;
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = "high";
      ctx.globalCompositeOperation = "multiply";

      const dim = getScreenshotDimensions(size, img.width, img.height);
      ctx.drawImage(img, dim.xStart, dim.yStart, dim.width, dim.height);

      resolve(canvas.toDataURL());
    };
  });
};

// prettier-ignore
const getMapScreenshot = async (map, { compress, coordinates }) => {
  // https://github.com/mapbox/mapbox-gl-js/issues/2766
  return new Promise(function (resolve, reject) {
    map.once("render", async () => {
      // take screenshot
      const zoomBtns = document.querySelector(`.mapboxgl-ctrl-top-right`);
      zoomBtns.style.visibility = "hidden";

      let subjectPropertyMarker;
      if (coordinates) {
        let el = document.createElement("div");
        el.innerHTML = `<svg viewBox="0 0 244 232" width="64" height="64" preserveAspectRatio="xMidYMid meet" xmlns="http://www.w3.org/2000/svg"><path d="m122 181.996-75.237 49.558 23.884-86.868L.265 88.446l89.997-4.129L122 0l31.738 84.317 89.997 4.129-70.382 56.24 23.884 86.868z" opacity="1" fill-rule="evenodd"/></svg>`;
        el.className = `z-[101] fill-[#e200ff] stroke-white stroke-[12px] drop-shadow-[0 0 10px #e200ff]`;
        subjectPropertyMarker = new mapboxgl.Marker(el);
        subjectPropertyMarker.setLngLat([coordinates.lng, coordinates.lat]).addTo(map);
      }

      const screenshotData = await toPng(document.querySelector(`.mapboxgl-map`));
      if (subjectPropertyMarker) subjectPropertyMarker.remove();

      zoomBtns.style.visibility = "visible";
      
      if (!compress) {
        resolve(screenshotData);
      } else { 
        resolve(await compressImage(screenshotData, compress.size || 500))
      }
    });
    /* trigger render */
    map.setBearing(map.getBearing());
  });
};

export const takeMapScreenshot = async (map, fileName, coordinates) => {
  map.once("render", async function () {
    const screenshotData = await getMapScreenshot(map, { coordinates });

    // download screenshot
    const download = document.createElement("a");
    download.href = screenshotData;
    download.download = fileName;
    download.click();
    download.remove();
  });

  /* trigger render */
  map.setBearing(map.getBearing());
};

export const sendToEmail = (subject, body) => {
  const mailto = `mailto:?subject=${subject}&body=${body}`;
  window.location.href = mailto;
};

const region =
  process.env.REACT_APP_AWS_COGNITO_REGION ||
  process.env.UMI_APP_AWS_COGNITO_REGION;
const IdentityPoolId =
  process.env.REACT_APP_AWS_COGNITO_IDENTITY_POOL ||
  process.env.UMI_APP_AWS_COGNITO_IDENTITY_POOL;
// temp
// const region = "";
// const IdentityPoolId = "";

AWS.config.update({
  region,
  credentials: new AWS.CognitoIdentityCredentials({
    IdentityPoolId,
  }),
});

const b64toBlob = (b64Data, contentType = "", sliceSize = 512) => {
  // https://stackoverflow.com/questions/16245767/creating-a-blob-from-a-base64-string-in-javascript
  const byteCharacters = atob(b64Data);
  const byteArrays = [];

  for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
    const slice = byteCharacters.slice(offset, offset + sliceSize);

    const byteNumbers = new Array(slice.length);
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }

  const blob = new Blob(byteArrays, { type: contentType });
  return blob;
};

export const uploadToS3 = async (map, coordinates) => {
  const s3 = new AWS.S3({
    apiVersion: "2006-03-01",
  });

  const fileName = `map-screenshot-${crypto.randomUUID()}.png`;
  const file = await getMapScreenshot(map, {
    compress: { size: 500 },
    coordinates,
  });

  const contentType = "image/png";

  const params = {
    Bucket: "sl-img-client/map-screenshots",
    Key: fileName,
    Body: b64toBlob(file.split(",")[1], contentType),
    ContentType: contentType,
  };

  return new Promise((resolve, reject) => {
    s3.upload(params, (err, data) => {
      if (err) {
        reject(err);
      }
      resolve(data.Location);
    });
  });
};
