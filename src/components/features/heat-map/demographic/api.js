import { customRequest, getServerType } from "../../../../services/data";

const tileURLRoot = (serverType) => {
  return serverType === "exp"
    ? "https://v72b1ngqw2.execute-api.us-east-1.amazonaws.com/expriment"
    : "https://wkgcer251f.execute-api.us-east-1.amazonaws.com/Prod";
};

/**
 * Get demographic data tile URL.
 * @param {String} benchmark - Benchmark type. Example: "national", "state", "metro"
 * @param {String} token - User token
 */
export const getDemographicDataTileURL = (serverType, benchmark, token) =>
  `${tileURLRoot(
    serverType
  )}/heatmap-demographics/data/${benchmark}/tile/{z}/{x}/{y}.mvt?token=${token}`;

/**
 * Get demographic data legend for whole country (USA).
 *
 */
export const getDemographicDataLegendNational = async () => {
  return await customRequest(
    `/api/cma/${getServerType()}/heatmap-demographics/data-legend/national`,
    { method: "GET" }
  );
};

/**
 * Get demographic data legend for state area.
 *
 * @param {String} coordinates - Coordinates of the metro area. Example: "33.753746,-84.386330"
 */
export const getDemographicDataLegendState = async ({ coordinates }) => {
  return await customRequest(
    `/api/cma/${getServerType()}/heatmap-demographics/data-legend/state/${coordinates}`,
    { method: "GET" }
  );
};

/**
 * Get demographic data legend for metro area.
 *
 * @param {String} coordinates - Coordinates of the metro area. Example: "33.753746,-84.386330"
 */
export const getDemographicDataLegendMetro = async ({ coordinates }) => {
  return await customRequest(
    `/api/cma/${getServerType()}/heatmap-demographics/data-legend/metro/${coordinates}`,
    { method: "GET" }
  );
};

export const getDemographicDataStatistics = async ({ mapBounds }) => {
  return await customRequest(
    `/api/cma/${getServerType()}/heatmap-demographics/data/stats/${mapBounds}`,
    { method: "GET" }
  );
};

export const getDemographicDataStatisticsFiltered = async ({
  mapBounds,
  body,
}) => {
  return await customRequest(
    `/api/cma/${getServerType()}/heatmap-demographics/data/stats/${mapBounds}`,
    {
      method: "POST",
      body: JSON.stringify(body),
    }
  );
};

export const getDemographicDataRange = async () => {
  return await customRequest(
    `/api/cma/${getServerType()}/heatmap-demographics/data/range`,
    { method: "GET" }
  );
};

export const getQualifiedZipCodes = async ({ mapBounds, body }) => {
  return await customRequest(
    `/api/cma/${getServerType()}/heatmap-demographics/data/csv/zip_codes/${
      mapBounds ? mapBounds : "all"
    }`,
    { method: "POST", body: JSON.stringify(body) }
  );
};
