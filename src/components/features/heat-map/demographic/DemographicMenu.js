import React, { useState, useEffect, useCallback, useRef } from "react";
import { useDemographicHeatmap } from "./DemographicProvider";
import {
  Dropdown,
  Button,
  Segmented,
  Tabs,
  Table,
  Slider,
  Select,
  InputNumber,
  Radio,
  Tooltip,
} from "antd";
import {
  DEFAULT_DEMOGRAPHIC_TYPE,
  DEMOGRAPHIC_TYPES,
  SUMMARY_TABLE_COLUMNS,
  valueFormatter,
  keyFormatter,
  getColorSchemeArray,
  processLegendAPIResponse,
  processRangesAPIResponse,
  getUserBasedDemographicTypes,
} from "./utils";
import { useMap } from "../../../Map/MapProvider";
import {
  getDemographicDataStatistics,
  getDemographicDataStatisticsFiltered,
  getDemographicDataLegendNational,
  getDemographicDataLegendState,
  getDemographicDataLegendMetro,
  getDemographicDataRange,
  getQualifiedZipCodes,
} from "./api";
import invert from "invert-color";
import { FaTrash } from "@react-icons/all-files/fa/FaTrash";
import { FaLock } from "@react-icons/all-files/fa/FaLock";
import { FaLockOpen } from "@react-icons/all-files/fa/FaLockOpen";

import Draggable from "react-draggable";
import { Modal } from "antd";

export const DemographicMenu = ({ onClose }) => {
  const { map } = useMap();
  const { benchmark, demographicFilters } = useDemographicHeatmap();
  const [activeTab, setActiveTab] = useState("summary");
  const [disabled, setDisabled] = useState(true);
  const [bounds, setBounds] = useState({
    left: 0,
    top: 0,
    bottom: 0,
    right: 0,
  });
  const draggleRef = useRef(null);

  const onStart = (_event, uiData) => {
    const { clientWidth, clientHeight } = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    if (!targetRect) {
      return;
    }
    setBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };

  const exportCSV = React.useCallback(
    (type) => {
      if (!map) return;
      const fetchData = async () => {
        const mapBounds =
          type === "export-zipcode-view"
            ? map.getBounds().toArray().flat().toString()
            : undefined;
        try {
          if (demographicFilters.some((f) => f.min === null || f.max === null))
            return;

          const data = await getQualifiedZipCodes({
            mapBounds,
            body: { benchmark, filters: demographicFilters },
          });

          const blob = new Blob([data], { type: "text/csv" });
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = `heatmap-qualifed-zip_codes.csv`;
          a.click();
          window.URL.revokeObjectURL(url);

          // console.log(data);
        } catch (error) {
          console.log(error);
        }
      };

      fetchData();
    },
    [map, benchmark, demographicFilters]
  );

  if (!map) return null;
  return (
    <Modal
      title={
        <div
          style={{
            width: "100%",
            cursor: "move",
          }}
          onMouseOver={() => {
            if (disabled) {
              setDisabled(false);
            }
          }}
          onMouseOut={() => {
            setDisabled(true);
          }}
          // fix eslintjsx-a11y/mouse-events-have-key-events
          // https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/master/docs/rules/mouse-events-have-key-events.md
          onFocus={() => {}}
          onBlur={() => {}}
          // end
        >
          Demographic Heatmap
        </div>
      }
      width={700}
      open={true}
      onCancel={() => onClose()}
      footer={false}
      modalRender={(modal) => (
        <Draggable
          disabled={disabled}
          bounds={bounds}
          nodeRef={draggleRef}
          onStart={(event, uiData) => onStart(event, uiData)}
        >
          <div ref={draggleRef}>{modal}</div>
        </Draggable>
      )}
      mask={false}
      wrapClassName="pointer-events-none"
    >
      <div className="flex flex-col gap-[6px] relative">
        <span>
          Demographic analytics using various benchmarks at a block group level
          across the country.
        </span>
        <div className="absolute right-2 top-8 z-10">
          <Dropdown
            placement="bottomRight"
            menu={{
              items: [
                {
                  label: "Qualified ZIP Codes (in view)",
                  key: "export-zipcode-view",
                  onClick: () => exportCSV("export-zipcode-view"),
                },
                // {
                //   label: "Qualified ZIP Codes (all)",
                //   key: "export-zipcode-all",
                //   onClick: () => exportCSV("export-zipcode-all"),
                // },
              ],
            }}
          >
            <Button>Export</Button>
          </Dropdown>
        </div>
        <Tabs
          value={activeTab}
          onChange={(value) => setActiveTab(value)}
          items={[
            {
              label: "Summary",
              key: "summary",
              children: <DemographicSummary activeTab={activeTab} />,
            },
            {
              label: "Filter",
              key: "filter",
              children: <DemographicFilter activeTab={activeTab} />,
            },
          ]}
        />
        <DemographicLegend />
      </div>
    </Modal>
  );
};

const DemographicSummary = ({ activeTab }) => {
  const { map } = useMap();
  const {
    demographicType,
    setDemographicType,
    summary,
    setSummary,
    benchmark,
    demographicFilterType,
    demographicFilters,
    userGroup,
  } = useDemographicHeatmap();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedType, setSelectedType] = useState(DEFAULT_DEMOGRAPHIC_TYPE);

  useEffect(() => {
    if (activeTab === "summary" && demographicType !== selectedType) {
      setDemographicType(selectedType);
    }
  }, [activeTab]);

  useEffect(() => {
    if (!map) return;

    const moveEnd = async () => {
      setIsLoading(true);
      const mapBounds = map.getBounds().toArray().flat().toString();
      try {
        if (demographicFilters.some((f) => f.min === null || f.max === null))
          return;

        const data = await getDemographicDataStatisticsFiltered({
          mapBounds,
          body: { benchmark, filters: demographicFilters },
        });

        const types =
          demographicFilterType === "combined"
            ? ["combined_score", ...DEMOGRAPHIC_TYPES]
            : DEMOGRAPHIC_TYPES;

        if (["Lennar", "demo-users"].includes(userGroup)) {
          types.push("purchase_price");
        }

        setSummary(
          types.reduce((acc, type) => {
            if (!data[type]) return acc;
            return [
              ...acc,
              {
                type,
                min: data[type].min,
                median: data[type].median,
                max: data[type].max,
                avg: data[type].average,
              },
            ];
          }, [])
        );
      } catch (error) {
        console.log(error);
        setSummary([]);
      }

      setIsLoading(false);
    };

    moveEnd();
    map.on("moveend", moveEnd);
    return () => {
      map.off("moveend", moveEnd);
    };
  }, [map, benchmark, demographicFilterType, demographicFilters, userGroup]);

  const onRow = useCallback(
    (record, rowIndex) => ({
      onClick: () => {
        setSelectedType(record.type);
        setDemographicType(record.type);
      },
    }),
    []
  );

  return (
    <div className="flex flex-col w-full">
      <div className="h-[20px] mb-[4px]">
        <span>
          Summarized demographic values of block groups within the map's view.
        </span>
      </div>
      <div className="h-[240px] w-full">
        <Table
          loading={isLoading}
          rowKey={(record) => record.type}
          dataSource={summary}
          columns={SUMMARY_TABLE_COLUMNS}
          pagination={false}
          size="small"
          scroll={{ y: 200 }}
          onRow={onRow}
          bordered={true}
          className="border border-solid border-[#ccc] h-full w-full"
          rowClassName={(record) => {
            return record.type === selectedType
              ? "bg-[#ddd] cursor-pointer"
              : "cursor-pointer";
          }}
        />
      </div>
    </div>
  );
};

const DemographicLegend = () => {
  const { map } = useMap();
  const {
    demographicType,
    benchmark,
    setBenchmark,
    layerOpacity,
    setLayerOpacity,
    colorScheme,
    setColorScheme,
    customColorScheme,
    setCustomColorScheme,
    setScoringFn,
    isColorReversed,
    setIsColorReversed
  } = useDemographicHeatmap();
  const [legendMeta, setLegendMeta] = useState({});
  const [legendData, setLegendData] = useState([]);

  useEffect(() => {
    if (!map) return;

    const fetchLegendData = async () => {
      try {
        let tempLegend = [];
        if (benchmark === "national") {
          const { meta, legend } = await getDemographicDataLegendNational();
          setLegendMeta(meta);
          setLegendData(processLegendAPIResponse(legend));
          tempLegend = legend;
        } else if (benchmark === "state") {
          const { meta, legend } = await getDemographicDataLegendState({
            coordinates: map.getCenter().toArray().reverse().toString(),
          });
          setLegendMeta(meta);
          setLegendData(processLegendAPIResponse(legend));
          tempLegend = legend;
        } else if (benchmark === "metro") {
          const { meta, legend } = await getDemographicDataLegendMetro({
            coordinates: map.getCenter().toArray().reverse().toString(),
          });
          setLegendMeta(meta);
          setLegendData(processLegendAPIResponse(legend));
          tempLegend = legend;
        }

        setScoringFn(() => (key, value) => {
          const sorted = tempLegend.sort((a, b) => a.rank - b.rank);
          let item = sorted[0];
          if (!item[key]) return null;
          for (let i = 1; i < sorted.length; i++) {
            if (value > item[key]) {
              item = sorted[i];
            }
          }
          return item.rank || null;
        });
      } catch (error) {
        console.log(error);
      }
    };

    fetchLegendData();
    if (["state", "metro"].includes(benchmark)) {
      map.on("moveend", fetchLegendData);
    }
    return () => {
      if (["state", "metro"].includes(benchmark)) {
        map.off("moveend", fetchLegendData);
      }
    };
  }, [map, benchmark]);

  const getLegendMeta = useCallback(() => {
    if (!legendMeta) return null;
    if (legendMeta.type === "national")
      return (
        <span>Scored nationally across all block groups in the country.</span>
      );
    if (legendMeta.type === "state")
      return (
        <span>
          Scored based on all block groups within{" "}
          <strong>{legendMeta.name}</strong> state{" "}
          {"(state nearest to map center)"}.
        </span>
      );
    if (legendMeta.type === "metro")
      return (
        <span>
          Scored based on all block groups within{" "}
          <strong>{legendMeta.name.split(",")[0]}</strong> metro area{" "}
          {"(metro nearest to map center)"}.
        </span>
      );
  }, [legendMeta, demographicType]);

  const getLegendData = useCallback(() => {
    if (!legendData || !legendData.length) return null;
  
    let colorArray = getColorSchemeArray(
      colorScheme === "default" ? colorScheme : customColorScheme,
      ["crime_score"].includes(demographicType) // reverse
    );
  
    // Apply color reversal for default scheme
    if (colorScheme === "default" && isColorReversed) {
      colorArray = [...colorArray].reverse();
    }
  
    // Sort legend data - reverse order if color is reversed
    const sortedLegendData = isColorReversed 
      ? legendData.sort((a, b) => b.rank - a.rank)
      : legendData.sort((a, b) => a.rank - b.rank);
  
    return (
      <div className="flex flex-row justify-center">
        <div className="flex flex-row w-[93%] ">
          {sortedLegendData
            .map((item, idx) => (
              <div key={idx} className="flex flex-col w-full gap-[4px]">
                <div
                  className="w-full h-[40px]"
                  style={{ backgroundColor: colorArray[isColorReversed ? (legendData.length - item.rank) : (item.rank - 1)] }}
                >
                  <span
                    className="font-bold text-lg flex flex-row justify-center items-center h-full w-full"
                    style={{ color: invert(colorArray[isColorReversed ? (legendData.length - item.rank) : (item.rank - 1)], true) }}
                  >
                    {isColorReversed ? (legendData.length + 1 - item.rank) : item.rank}
                  </span>
                </div>
                <div className="text-center text-xs translate-x-1/2">
                  <span>
                    {demographicType !== "combined_score"
                      ? valueFormatter(demographicType, item[demographicType])
                      : (isColorReversed ? (legendData.length - idx) : (idx + 1))}
                  </span>
                </div>
              </div>
            ))}
        </div>
      </div>
    );
  }, [legendData, demographicType, colorScheme, customColorScheme, isColorReversed]);
  return (
    <div className="flex flex-col gap-[6px] py-[6px]">
      <div>
        <p className="mb-1 text-sm font-[500] color-[#333]">Benchmark</p>
        <Segmented
          block
          value={benchmark}
          onChange={(value) => setBenchmark(value)}
          options={[
            { label: "Metro", value: "metro" },
            { label: "State", value: "state" },
            { label: "National", value: "national" },
          ]}
        />
      </div>
      <div>
        <p className="mb-1 text-sm font-[500] color-[#333]">
          Distribution Scores{" "}
          <strong>{`(${keyFormatter(demographicType)})`}</strong>
        </p>
        <div className="mb-[4px]">{getLegendMeta()}</div>
        {getLegendData()}
      </div>
      <div className="flex flex-row gap-[16px]">
      <div className="w-[250px]">
  <p className="mb-1 text-sm font-[500] color-[#333]">Color Scheme</p>
  <div className="flex flex-col gap-2">
    <Segmented
      block
      value={colorScheme}
      onChange={(value) => setColorScheme(value)}
      options={[
        { label: "Default", value: "default" },
        { label: "Custom", value: "custom" },
      ]}
    />
    {colorScheme === "default" && (
      <Button
        size="small"
        onClick={() => setIsColorReversed(!isColorReversed)}
        style={{ width: "100%" }}
      >
        {isColorReversed ? "Revert Colors" : "Reverse Colors"}
      </Button>
    )}
    {colorScheme === "custom" && (
      <Select
        value={customColorScheme}
        options={[
          { label: "Gray", value: "gray" },
          { label: "Red", value: "red" },
          { label: "Green", value: "green" },
          { label: "Blue", value: "blue" },
          { label: "Purple", value: "purple" },
          { label: "Orange", value: "orange" },
        ]}
        onChange={(value) => setCustomColorScheme(value)}
        style={{ width: "100%" }}
      />
    )}
  </div>
</div>

        <div className="w-full">
          <p className="mb-1 text-sm font-[500] color-[#333]">
            Opacity {`(${layerOpacity}%)`}
          </p>
          <Slider
            min={0}
            max={100}
            step={1}
            value={layerOpacity}
            onChange={(value) => setLayerOpacity(value)}
          />
        </div>
      </div>
    </div>
  );
};

const DemographicFilter = ({ activeTab }) => {
  const { map } = useMap();
  const {
    demographicType,
    setDemographicType,
    demographicFilters,
    setDemographicFilters,
    demographicFilterType,
    setDemographicFilterType,
    summary,
    userGroup,
  } = useDemographicHeatmap();
  const [ranges, setRanges] = useState(null);
  const [selectedType, setSelectedType] = useState(DEFAULT_DEMOGRAPHIC_TYPE);
  const [selectedFilters, setSelectedFilters] = useState([
    DEFAULT_DEMOGRAPHIC_TYPE,
  ]);
  const [filters, setFilters] = useState([
    // { type: DEFAULT_DEMOGRAPHIC_TYPE, min: 0, max: 100 },
  ]);
  const [lockedFilters, setLockedFilters] = useState(
    // [DEFAULT_DEMOGRAPHIC_TYPE]: false,
    getUserBasedDemographicTypes(userGroup).reduce((acc, type) => {
      acc[type] = true;
      return acc;
    }, {})
  );

  const rangesRef = useRef(ranges);
  rangesRef.current = ranges;
  const lockedFiltersRef = useRef(lockedFilters);
  lockedFiltersRef.current = lockedFilters;

  useEffect(() => {
    if (!map) return;

    // const fetchRanges = async () => {
    //   try {
    //     const rangesData = await getDemographicDataRange();
    //     const data = {
    //       ...rangesData,
    //       combined_score: { min: 1, max: 10 },
    //     };
    //     setRanges(data);
    //     setFilters((prev) => {
    //       const newState = prev.map((f) => ({
    //         type: f.type,
    //         min: data[f.type].min,
    //         max: data[f.type].max,
    //       }));
    //       return newState;
    //     });
    //   } catch (error) {
    //     console.log(error);
    //     setRanges([]);
    //   }
    // };

    const fetchRanges = async () => {
      try {
        const mapBounds = map.getBounds().toArray().flat().toString();
        const rangeData = await getDemographicDataStatistics({ mapBounds });
        const data = {
          ...processRangesAPIResponse(rangeData),
          combined_score: { min: 1, max: 10 },
        };

        setFilters((prev) => {
          const newState = prev.map((f) => {
            let newMin = f.min;
            let newMax = f.max;

            if (
              rangesRef.current[f.type] &&
              !lockedFiltersRef.current[f.type]
            ) {
              const oldRange =
                rangesRef.current[f.type].max - rangesRef.current[f.type].min;
              const newRange = data[f.type].max - data[f.type].min;
              newMin =
                data[f.type].min -
                ((rangesRef.current[f.type].min - f.min) / oldRange) * newRange;
              newMax =
                data[f.type].max -
                ((rangesRef.current[f.type].max - f.max) / oldRange) * newRange;
              if (newMin < data[f.type].min) newMin = data[f.type].min;
              if (newMax > data[f.type].max) newMax = data[f.type].max;
            }

            return {
              type: f.type,
              min: newMin,
              max: newMax,
            };
          });

          return newState;
        });
        setRanges(data);
      } catch (error) {
        console.log(error);
      }
    };

    fetchRanges();
    map.on("moveend", fetchRanges);
    return () => {
      map.off("moveend", fetchRanges);
      setRanges(null);
    };
  }, [map]);

  useEffect(() => {
    if (activeTab === "filter" && demographicType !== selectedType) {
      setDemographicType(selectedType);
    }
  }, [activeTab]);

  useEffect(() => {
    if (!ranges) return;

    if (selectedFilters.length === 0) {
      setSelectedFilters([DEFAULT_DEMOGRAPHIC_TYPE]);
      setFilters([
        {
          type: DEFAULT_DEMOGRAPHIC_TYPE,
          min: ranges[DEFAULT_DEMOGRAPHIC_TYPE].min,
          max: ranges[DEFAULT_DEMOGRAPHIC_TYPE].max,
        },
      ]);
    }

    setFilters((prev) => {
      if (prev.length === selectedFilters.length) return prev;
      const newState = prev.filter((f) => selectedFilters.includes(f.type));
      const newFilters = selectedFilters.filter((nf) =>
        prev.every((f) => f.type !== nf)
      );
      newFilters.forEach((f) => {
        newState.push({ type: f, min: ranges[f].min, max: ranges[f].max });
      });

      return newState;
    });
  }, [ranges, selectedFilters]);

  useEffect(() => {
    if (
      activeTab === "filter" &&
      filters.length > 0 &&
      filters.find((f) => f.type === demographicType) === undefined
    ) {
      onChangeSelectedType(filters[0].type);
    }

    const newDemographicFilters = filters.reduce((acc, f) => {
      if (demographicFilterType === "separate" && f.type === demographicType) {
        acc.push(f);
      } else if (demographicFilterType === "combined") {
        acc.push(f);
      }
      return acc;
    }, []);
    setDemographicFilters(newDemographicFilters);
  }, [filters, demographicType, demographicFilterType, activeTab]);

  const onFilterChange = useCallback((type, min, max) => {
    setFilters((prev) => {
      const idx = prev.findIndex((f) => f.type === type);
      if (idx === -1) return prev;
      const newFilters = [...prev];
      newFilters[idx] = {
        type,
        // min: max < min ? max : min,
        // max: min > max ? min : max,
        min: min,
        max: max,
      };
      return newFilters;
    });
  }, []);

  const onChangeSelectedType = useCallback((type) => {
    setSelectedType(type);
    setDemographicType(type);
  }, []);

  const getFilterOptions = useCallback(() => {
    const types = getUserBasedDemographicTypes(userGroup);
    if (demographicFilterType === "combined")
      return ["combined_score", ...types];
    return types;
  }, [demographicFilterType, userGroup]);

  const onChangeFilterType = useCallback(
    (type) => {
      if (type == "separate" && selectedFilters.includes("combined_score")) {
        setSelectedFilters((prev) => {
          const newFilters = prev.filter((f) => f !== "combined_score");
          return newFilters;
        });
      }
      setDemographicFilterType(type);
    },
    [selectedFilters]
  );

  console.log("lockedFilters: ", lockedFilters);

  return (
    <div className="flex flex-col gap-2" style={{ height: "300px" }}>
      <div className="flex flex-col">
        <div className="mb-[4px]">
          <span>
            Multiple filters produces a combined score based on selected
            filtered demographic in the hovered block group's popup tooltip.
          </span>
        </div>
        <div className="flex flex-row gap-4 justify-between">
          <div className="flex flex-col w-full">
            <div>
              <span className="font-[600]">Demographic Selector: </span>
            </div>
            <Select
              mode="multiple"
              maxTagCount="responsive"
              value={selectedFilters}
              options={getFilterOptions().map((d) => ({
                label: keyFormatter(d),
                value: d,
              }))}
              onChange={(value) => setSelectedFilters(value)}
              allowClear={true}
              style={{ width: "100%" }}
            />
          </div>
          {/* <div className="min-w-[210px] flex flex-col">
            <div>
              <span className="font-[600]">Filter Type:</span>
            </div>
            <div className="flex flex-col justify-center h-full">
              <Radio.Group
                value={demographicFilterType}
                onChange={(e) => onChangeFilterType(e.target.value)}
              >
                <Radio value="separate">Separate</Radio>
                <Radio value="combined">Combined</Radio>
              </Radio.Group>
            </div>
          </div> */}
        </div>
      </div>
      <div className="flex flex-col h-full overflow-y-auto border border-solid border-[#ccc] bg-[#fafafa]">
        {filters.map((filter, idx) => (
          <div
            key={idx}
            className={`flex flex-col gap-2 py-2 px-4 cursor-pointer ${
              filter.type === selectedType ? "bg-[#ddd]" : "bg-white"
            } hover:bg-[#eee]`}
            style={{
              borderBottom: "1px solid #ccc",
            }}
            onClick={() => onChangeSelectedType(filter.type)}
          >
            <div className="flex flex-row justify-between">
              <span
                className={`${
                  filter.type === selectedType ? "font-[600]" : ""
                }`}
              >
                {keyFormatter(filter.type)}
              </span>
              <div className="flex flex-row gap-1">
                {/* {filter.type === selectedType && summary.length > 0 && (
                  <span className="color-[#333]">
                    Map's viewed range:{" "}
                    {summary
                      .filter((s) => s.type === selectedType)
                      .map(
                        (s) =>
                          valueFormatter(selectedType, s.min) +
                          " - " +
                          valueFormatter(selectedType, s.max)
                      )}
                  </span>
                )} */}
                <button
                  className="bg-transparent border-none"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedFilters((prev) => {
                      const newFilters = prev.filter((f) => f != filter.type);
                      if (
                        newFilters.length === 1 &&
                        newFilters[0] === "combined_score"
                      ) {
                        return [];
                      }
                      return newFilters;
                    });
                  }}
                >
                  <FaTrash className="text-[#aaa] hover:text-[#777] cursor-pointer" />
                </button>
              </div>
            </div>
            <div className="flex flex-row gap-2">
              <div className="w-[350px] flex flex-row gap-1">
                <InputNumber
                  className="demographic-heatmap-filter-input"
                  min={ranges ? ranges[filter.type].min : 0}
                  max={ranges ? ranges[filter.type].max : 100}
                  value={filter.min}
                  style={{ width: "100%" }}
                  formatter={(value) => valueFormatter(filter.type, value)}
                  onChange={(value) => {
                    onFilterChange(filter.type, value, filter.max);
                  }}
                  status={filter.min > filter.max ? "error" : undefined}
                />
                <InputNumber
                  className="demographic-heatmap-filter-input"
                  min={ranges ? ranges[filter.type].min : 0}
                  max={ranges ? ranges[filter.type].max : 100}
                  value={filter.max}
                  style={{ width: "100%" }}
                  formatter={(value) => valueFormatter(filter.type, value)}
                  onChange={(value) => {
                    onFilterChange(filter.type, filter.min, value);
                  }}
                  status={filter.max < filter.min ? "error" : undefined}
                />
              </div>
              <Slider
                range={{ draggableTrack: true }}
                min={ranges ? ranges[filter.type].min : 0}
                max={ranges ? ranges[filter.type].max : 100}
                value={[filter.min, filter.max]}
                onChange={(value) => {
                  if (filter.type !== selectedType)
                    onChangeSelectedType(filter.type);
                  onFilterChange(filter.type, value[0], value[1]);
                }}
                tooltip={{
                  formatter: (value) => valueFormatter(filter.type, value),
                }}
                step={1}
                style={{ width: "100%" }}
              />

              <button
                className="bg-transparent border-none"
                onClick={() =>
                  setLockedFilters({
                    ...lockedFilters,
                    [filter.type]: !lockedFilters[filter.type] ? true : false,
                  })
                }
              >
                <Tooltip
                  title={
                    lockedFilters[filter.type] ? "Unlock filter" : "Lock filter"
                  }
                >
                  {lockedFilters[filter.type] ? (
                    <FaLock className="text-[#333] hover:text-[#777] cursor-pointer" />
                  ) : (
                    <FaLockOpen className="text-[#aaa] hover:text-[#777] cursor-pointer" />
                  )}
                </Tooltip>
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
