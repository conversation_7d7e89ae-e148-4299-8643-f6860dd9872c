import { useState, useEffect } from "react";
import { renderToString } from "react-dom/server";
import { useMap } from "../../../Map/MapProvider";
import { getDemographicDataTileURL } from "./api";
import { Source } from "../../../Map/MapLayers/index";
import { Layer } from "../../../Map/MapLayers/index";
import { initPopup } from "../../../Map/MapUtility/general";
import { useDemographicHeatmap } from "./DemographicProvider";
import { getColorSchemeArray, getUserBasedDemographicTypes } from "./utils";

const getLayerStyle = ({
  id,
  demographicType,
  opacity,
  colors,
  filters,
  filterType,
  isColorReversed, // Add this parameter
}) => {
  if (!demographicType) {
    return {
      id: `${id}-style`,
      type: "fill",
      "source-layer": `${id}`,
      paint: {
        "fill-color": "transparent",
        "fill-opacity": 0,
      },
    };
  } else {
    // Apply color reversal if needed
    const finalColors = isColorReversed ? [...colors].reverse() : colors;

    // prettier-ignore
    const fillColor = ["case"];
    if (demographicType != "combined_score") {
      fillColor.push(
        ["==", ["get", `${demographicType}_value`], null],
        "transparent"
      );
    }
    if (filters.length > 0) {
      if (filterType === "separate") {
        const f = filters.find((f) => f.type === demographicType);
        if (f) {
          fillColor.push(
            ["<", ["get", `${f.type}_value`], f.min],
            "transparent"
          );
          fillColor.push(
            [">", ["get", `${f.type}_value`], f.max],
            "transparent"
          );
        }
        // } else if (
        //   filterType === "combined" &&
        //   demographicType === "combined_score"
        // ) {
        //   const f = filters.find((f) => f.type === "combined_score");
        //   const types = filters
        //     .map((f) => f.type)
        //     .filter((t) => t !== "combined_score");
        //   const exp = ["any"];
        //   for (const type of types) {
        //     exp.push(
        //       ["<", ["get", `${type}_rank`], f.min],
        //       [">", ["get", `${type}_rank`], f.max]
        //     );
        //   }
        //   fillColor.push(exp, "transparent");
      } else if (filterType === "combined") {
        for (const f of filters) {
          if (f.type === "combined_score") {
            const types = filters
              .map((f) => f.type)
              .filter((t) => t !== "combined_score");
            const exp = ["any"];
            for (const type of types) {
              exp.push(
                ["<", ["get", `${type}_rank`], f.min],
                [">", ["get", `${type}_rank`], f.max]
              );
            }
            fillColor.push(exp, "transparent");
          } else {
            fillColor.push(
              ["==", ["get", `${f.type}_value`], null],
              "transparent"
            );
            fillColor.push(
              ["<", ["get", `${f.type}_value`], f.min],
              "transparent"
            );
            fillColor.push(
              [">", ["get", `${f.type}_value`], f.max],
              "transparent"
            );
          }
        }
      }
    }
    // ... rest of the existing filter logic stays the same ...

    if (filterType === "combined" && demographicType === "combined_score") {
      const avgExp = ["/"];
      const sumExp = ["+"];
      for (const f of filters) {
        if (f.type === "combined_score") continue;
        sumExp.push(["get", `${f.type}_rank`]);
      }
      let count = filters.length;
      if (filters.find((f) => f.type === "combined_score")) {
        count -= 1;
      }
      avgExp.push(sumExp, count === 0 ? 1 : count);
      
      if (isColorReversed) {
        fillColor.push(["<=", avgExp, 1], finalColors[9]);
        fillColor.push(["<=", avgExp, 2], finalColors[8]);
        fillColor.push(["<=", avgExp, 3], finalColors[7]);
        fillColor.push(["<=", avgExp, 4], finalColors[6]);
        fillColor.push(["<=", avgExp, 5], finalColors[5]);
        fillColor.push(["<=", avgExp, 6], finalColors[4]);
        fillColor.push(["<=", avgExp, 7], finalColors[3]);
        fillColor.push(["<=", avgExp, 8], finalColors[2]);
        fillColor.push(["<=", avgExp, 9], finalColors[1]);
        fillColor.push(["<=", avgExp, 10], finalColors[0]);
      } else {
        fillColor.push(["<=", avgExp, 1], finalColors[0]);
        fillColor.push(["<=", avgExp, 2], finalColors[1]);
        fillColor.push(["<=", avgExp, 3], finalColors[2]);
        fillColor.push(["<=", avgExp, 4], finalColors[3]);
        fillColor.push(["<=", avgExp, 5], finalColors[4]);
        fillColor.push(["<=", avgExp, 6], finalColors[5]);
        fillColor.push(["<=", avgExp, 7], finalColors[6]);
        fillColor.push(["<=", avgExp, 8], finalColors[7]);
        fillColor.push(["<=", avgExp, 9], finalColors[8]);
        fillColor.push(["<=", avgExp, 10], finalColors[9]);
      }
    }
  if (demographicType != "combined_score") {
      if (isColorReversed) {
        fillColor.push(["==", ["get", `${demographicType}_rank`], 1], finalColors[9]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 2], finalColors[8]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 3], finalColors[7]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 4], finalColors[6]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 5], finalColors[5]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 6], finalColors[4]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 7], finalColors[3]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 8], finalColors[2]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 9], finalColors[1]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 10], finalColors[0]);
      } else {
        fillColor.push(["==", ["get", `${demographicType}_rank`], 1], finalColors[0]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 2], finalColors[1]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 3], finalColors[2]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 4], finalColors[3]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 5], finalColors[4]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 6], finalColors[5]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 7], finalColors[6]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 8], finalColors[7]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 9], finalColors[8]);
        fillColor.push(["==", ["get", `${demographicType}_rank`], 10], finalColors[9]);
      }
    }
    fillColor.push("transparent");

    return {
      id: `${id}-style`,
      type: "fill",
      "source-layer": `demographic-heatmap-data`,
      paint: {
        "fill-color": fillColor,
        "fill-opacity": opacity / 100,
      },
    };
  }
};

const popup = initPopup();

// prettier-ignore
const keyFormatter = (key) => {
  if (key === "median_hh_income") return "Median HH Income";
  if (key === "five_year_pop_growth") return "5 Year Population Growth";
  if (key === "population_density") return "Population Density (per mile)";
  if (key === "fifty_five_plus") return "55+ Percentage";
  if (key === "rent_vs_owner_percentage") return "Rent vs Owner Percentage";
  if (key === "median_rent") return "Median Home Rent";
  if (key === "rent_vs_own") return "Mtg Payment - Rent";
  if (key === "rental_growth_5_years") return "Rent CAGR trailing 5 years";
  if (key === "avg_school") return "Avg. School Score";
  if (key === "pop3miles") return "Population within 3 miles";
  if (key === "pop10miles") return "Population within 10 miles";
  if (key === "purchase_price") return "Purchase Price";
  return key
    .split("_")
    .map((k) => k.charAt(0).toUpperCase() + k.slice(1))
    .join(" ");
};

// prettier-ignore
const valueFormatter = (key, value) => {
  if (!value) return "--";
  if (["median_hh_income", "median_rent", "median_home_value", "rent_vs_own", "purchase_price"].includes(key)) return `$${Math.round(value).toLocaleString()}`;
  if (["five_year_pop_growth", "bachelors_and_above", "household_growth", "rent_vs_owner_percentage", "rental_growth_5_years"].includes(key)) return `${(Math.round(value * 10) / 10)}%`;
  if (["median_age", "household_size", "population_density", "fifty_five_plus", "pop3miles", "pop10miles"].includes(key)) return Math.round(value).toLocaleString();
  return value;
}

// prettier-ignore
// const demographicTypes = ["median_hh_income", "five_year_pop_growth", "bachelors_and_above", "median_age", "household_size", "population_density", "fifty_five_plus", "household_growth", "rent_vs_owner_percentage", "median_rent", "median_home_value", "rent_vs_own", "rental_growth_5_years", "crime_score", "avg_school", "pop3miles", "pop10miles"];

// prettier-ignore
const getPopupHTML = (data, demographicType, demographicFilters, scoringFn, userGroup) => {
  if (!data) return null;

  const getCombinedScore = (properties) => {
    const scores = demographicFilters.map((f) => {
      const value = properties[`${f.type}_value`];
      return scoringFn(f.type, value);
    }).filter((s) => s !== null);
    return Math.round((scores.reduce((a, b) => a + b, 0) / scores.length) * 10) / 10;
  }

  const displayScore = (type) => {
    if (demographicFilters.length > 1 && scoringFn && demographicFilters.filter(f => f.type === type).length > 0) {
      return <span>{`[${scoringFn(type, data.properties[`${type}_value`])}]`}</span>
    }
    return null;
  }

  const types = getUserBasedDemographicTypes(userGroup);

  const content = (
    <div style={{ padding: "10px" }}>
      {/* <div><span>Id: </span><b>{data.properties.block_group_id}</b></div> */}
      {types.filter((k) => !["block_group_id"].includes(k)).map((k) => (
        <div key={k} className={k === demographicType ? 'font-bold': ''}>
          <strong>{displayScore(k)} </strong>
          <span>{keyFormatter(k)}: </span><b>{["crime_score"].includes(k) ? data.properties[`${k}_rank`] :  valueFormatter(k, data.properties[`${k}_value`])}</b>
        </div>
      ))}
      <div><span>Color Score: </span><b>{demographicType !== "combined_score" ? data.properties[`${demographicType}_rank`]: getCombinedScore(data.properties)}</b></div>
      {demographicFilters.length > 1 && scoringFn && (
        <div><span>Combined Score: </span><b>{getCombinedScore(data.properties)}</b></div>
      )}
    </div>
  );

  return renderToString(content);
};

export const DemographicLayer = ({ serverType }) => {
  const { map } = useMap();
  const [token, setToken] = useState(null);
  const {
    demographicType,
    benchmark,
    layerOpacity,
    colorScheme,
    customColorScheme,
    demographicFilters,
    demographicFilterType,
    scoringFn,
    userGroup,
    isColorReversed
  } = useDemographicHeatmap();

  useEffect(() => {
    if (!map) return;

    const mouseMove = (e) => {
      if (!map.getLayer(`demographic-heatmap-data-style`)) return;
      const features = map.queryRenderedFeatures(e.point, {
        layers: [`demographic-heatmap-data-style`],
      });
      if (!features.length) return;

      const data = features.find(
        (f) => f.sourceLayer === `demographic-heatmap-data`
      );

      const popupContent = getPopupHTML(
        data,
        demographicType,
        demographicFilters,
        scoringFn,
        userGroup
      );
      popup.setLngLat(e.lngLat).setHTML(popupContent).addTo(map);
    };

    const mouseLeave = () => {
      popup.remove();
    };

    const checkForLatestToken = async () => {
      const newToken = await window.spatiallaser.getUserToken("access");
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    // map.on("moveend", () => {
    //   console.log(
    //     map.queryRenderedFeatures({
    //       layers: ["demographic-heatmap-data-style"],
    //     })
    //   );
    // });

    checkForLatestToken();
    map.on("movestart", checkForLatestToken);
    map.on("mousemove", mouseMove);
    map.on("mouseleave", `demographic-heatmap-data-style`, mouseLeave);
    return () => {
      map.off("movestart", checkForLatestToken);
      map.off("mousemove", mouseMove);
      map.off("mouseleave", `demographic-heatmap-data-style`, mouseLeave);
    };
  }, [
    map,
    token,
    demographicType,
    benchmark,
    demographicFilters,
    scoringFn,
    userGroup,
    isColorReversed
  ]);

  if (!map || !token) return null;

  // console.log("demographicFilters:", demographicFilters);

  // Seems redundant to have the same Source and Layer components for each benchmark
  // This is because the Source and Layer component does not allow for dynamic changes
  // Possible refactor: add source and layer using map.addSource and map.addLayer
  // All layers might be possible to contain in one Source so that there isn't 2x tile requests
  return (
    <>
      <Source
        id={`demographic-heatmap-data`}
        type="vector"
        tiles={[getDemographicDataTileURL(serverType, benchmark, token)]}
      >
        <Layer
          {...getLayerStyle({
            id: `demographic-heatmap-data`,
          })}
        />
      </Source>
      {benchmark === "national" && (
  <Source
    id={`demographic-heatmap-data-${benchmark}`}
    type="vector"
    tiles={[getDemographicDataTileURL(serverType, benchmark, token)]}
  >
    <Layer
      {...getLayerStyle({
        id: `demographic-heatmap-data-${benchmark}`,
        demographicType,
        opacity: layerOpacity,
        colors: getColorSchemeArray(
          colorScheme === "default" ? colorScheme : customColorScheme,
          ["crime_score"].includes(demographicType)
        ),
        filters: demographicFilters,
        filterType: demographicFilterType,
        isColorReversed: colorScheme === "default" ? isColorReversed : false, // Add this line
      })}
    />
  </Source>
)}
 {benchmark === "state" && (
  <Source
    id={`demographic-heatmap-data-${benchmark}`}
    type="vector"
    tiles={[getDemographicDataTileURL(serverType, benchmark, token)]}
  >
    <Layer
      {...getLayerStyle({
        id: `demographic-heatmap-data-${benchmark}`,
        demographicType,
        opacity: layerOpacity,
        colors: getColorSchemeArray(
          colorScheme === "default" ? colorScheme : customColorScheme,
          ["crime_score"].includes(demographicType)
        ),
        filters: demographicFilters,
        filterType: demographicFilterType,
        isColorReversed: colorScheme === "default" ? isColorReversed : false, // Add this line
      })}
    />
  </Source>
)}
{benchmark === "metro" && (
  <Source
    id={`demographic-heatmap-data-${benchmark}`}
    type="vector"
    tiles={[getDemographicDataTileURL(serverType, benchmark, token)]}
  >
    <Layer
      {...getLayerStyle({
        id: `demographic-heatmap-data-${benchmark}`,
        demographicType,
        opacity: layerOpacity,
        colors: getColorSchemeArray(
          colorScheme === "default" ? colorScheme : customColorScheme,
          ["crime_score"].includes(demographicType)
        ),
        filters: demographicFilters,
        filterType: demographicFilterType,
        isColorReversed: colorScheme === "default" ? isColorReversed : false, // Add this line
      })}
    />
  </Source>
)}
    </>
  );
};
