import * as d3 from "d3";
import { rgbToHexString } from "../../../../utils/color";

export const DEMOGRAPHIC_TYPES = [
  "median_hh_income",
  "five_year_pop_growth",
  "bachelors_and_above",
  "median_age",
  "household_size",
  "population_density",
  "fifty_five_plus",
  "household_growth",
  "rent_vs_owner_percentage",
  "median_rent",
  "median_home_value",
  "rent_vs_own",
  "rental_growth_5_years",
  "crime_score",
  "avg_school",
  "pop3miles",
  "pop10miles",
];
export const DEFAULT_DEMOGRAPHIC_TYPE = DEMOGRAPHIC_TYPES[0];

// prettier-ignore
export const keyFormatter = (key) => {
  if (key === "median_hh_income") return "Median HH Income";
  if (key === "five_year_pop_growth") return "5 Year Population Growth";
  if (key === "population_density") return "Population Density (per mile)";
  if (key === "fifty_five_plus") return "55+ Percentage";
  if (key === "rent_vs_owner_percentage") return "Rent vs Owner Percentage";
  if (key === "median_rent") return "Median Home Rent";
  if (key === "rent_vs_own") return "Mtg Payment - Rent";
  if (key === "rental_growth_5_years") return "Rent CAGR trailing 5 years";
  if (key === "avg_school") return "Avg. School Score"
  if (key === "pop3miles") return "Population within 3 miles";
  if (key === "pop10miles") return "Population within 10 miles";
  return key
    .split("_")
    .map((k) => k.charAt(0).toUpperCase() + k.slice(1))
    .join(" ");
};

// prettier-ignore
export const valueFormatter = (key, value) => {
  if (value === null) return "--";
  if (["median_hh_income", "median_rent", "median_home_value", "rent_vs_own", "purchase_price"].includes(key)) return `$${Math.round(value).toLocaleString()}`;
  if (["five_year_pop_growth", "bachelors_and_above", "household_growth", "rent_vs_owner_percentage", "rental_growth_5_years"].includes(key)) return `${(Math.round(value * 10) / 10).toLocaleString()}%`;
  if (["median_age", "population_density", "fifty_five_plus", "pop3miles", "pop10miles"].includes(key)) return Math.round(value).toLocaleString();
  if (["crime_score", "avg_school","combined_score"].includes(key)) { 
    if (value % 1 !== 0) {
      return Math.round(value * 10) / 10;
    }
    return value;
  }
  if (['household_size'].includes(key)) {
    return Math.round(value * 10) / 10;
  }
  return value;
}

export const SUMMARY_TABLE_COLUMNS = [
  {
    title: "Type",
    dataIndex: "type",
    key: "type",
    render: (value) => keyFormatter(value),
    sorter: (a, b) => keyFormatter(a.type).localeCompare(keyFormatter(b.type)),
    filters: DEMOGRAPHIC_TYPES.map((type) => ({
      text: keyFormatter(type),
      value: type,
    })),
    onFilter: (value, record) => record.type === value,
  },
  {
    title: "Min",
    dataIndex: "min",
    key: "min",
    width: 100,
    render: (value, record) => valueFormatter(record.type, value),
    sorter: (a, b) => a.min - b.min,
  },
  {
    title: "Median",
    dataIndex: "median",
    key: "median",
    width: 100,
    render: (value, record) => valueFormatter(record.type, value),
    sorter: (a, b) => a.min - b.min,
  },
  {
    title: "Max",
    dataIndex: "max",
    key: "max",
    width: 100,
    render: (value, record) => valueFormatter(record.type, value),
    sorter: (a, b) => a.min - b.min,
  },
  {
    title: "Average",
    dataIndex: "avg",
    key: "avg",
    width: 100,
    render: (value, record) => valueFormatter(record.type, value),
    sorter: (a, b) => a.min - b.min,
  },
];

export const RANKED_COLORS = {
  1: "#d7191c",
  2: "#e85b3a",
  3: "#f99e59",
  4: "#fec981",
  5: "#ffedab",
  6: "#ecf7ad",
  7: "#c4e687",
  8: "#97d265",
  9: "#58b453",
  10: "#1a9641",
};

const generateClassRange = (num) => {
  const step = 1 / (num - 1);
  const range = Array.from({ length: num }, (_, i) => i * step);
  return range;
};

// prettier-ignore
export const getColorSchemeArray = (colorScheme, reverse = false) => {
  if ([ "blue", "green", "red", "purple", "orange", "gray" ].includes(colorScheme)) {
    let colorScale = null;
    if (colorScheme === "blue") {
      colorScale = d3.scaleSequential(d3.interpolateBlues);
    } else if (colorScheme === "green") {
      colorScale = d3.scaleSequential(d3.interpolateGreens);
    } else if (colorScheme === "red") {
      colorScale = d3.scaleSequential(d3.interpolateReds);
    } else if (colorScheme === "purple") {
      colorScale = d3.scaleSequential(d3.interpolatePurples);
    } else if (colorScheme === "orange") {
      colorScale = d3.scaleSequential(d3.interpolateOranges);
    } else if (colorScheme === "gray") {
      colorScale = d3.scaleSequential(d3.interpolateGreys);
    }
    const colorArray = generateClassRange(10).map((q) =>
      rgbToHexString(colorScale(q))
    );
    return reverse ? colorArray.reverse() : colorArray;
  }
  
  // default
  const defaultColorArray =  ["#d7191c", "#e85b3a", "#f99e59", "#fec981", "#ffedab", "#ecf7ad", "#c4e687", "#97d265", "#58b453", "#1a9641"];
  return reverse ? defaultColorArray.reverse() : defaultColorArray;
}

export const processLegendAPIResponse = (data) => {
  return data.map((d) => ({
    ...d,
    avg_school: d.rank,
  }));
};

export const processRangesAPIResponse = (data) => {
  return {
    ...data,
    avg_school: {
      ...data.avg_school,
      min: 1,
      max: 10,
    },
  };
};

export const getUserBasedDemographicTypes = (userGroup) => {
  if (["Lennar", "demo-users"].includes(userGroup)) {
    return [...DEMOGRAPHIC_TYPES, "purchase_price"];
  }
  return DEMOGRAPHIC_TYPES;
};
