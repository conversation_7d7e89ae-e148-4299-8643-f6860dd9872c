import { useState, createContext, useContext, useMemo } from "react";
import "./styles.css";
import { DEFAULT_DEMOGRAPHIC_TYPE } from "./utils";

const DemographicContext = createContext(undefined);

export const DemographicProvider = ({ children, ...props }) => {
  const [demographicType, setDemographicType] = useState(
    DEFAULT_DEMOGRAPHIC_TYPE
  );
  const [benchmark, setBenchmark] = useState("metro"); // national | state | metro
  const [layerOpacity, setLayerOpacity] = useState(50); // 0 - 100
  const [colorScheme, setColorScheme] = useState("default"); // default | custom
  const [customColorScheme, setCustomColorScheme] = useState("gray"); // gray | red | green | blue | purple | orange
  const [isColorReversed, setIsColorReversed] = useState(false);
  const [summary, setSummary] = useState([]);
  const [demographicFilters, setDemographicFilters] = useState([
    {
      type: DEFAULT_DEMOGRAPHIC_TYPE,
      min: 0,
      max: 9999999,
    },
  ]);
  const [demographicFilterType, setDemographicFilterType] =
    useState("combined"); // combined | separate
  const [scoringFn, setScoringFn] = useState(null);
  const [userGroup, setUserGroup] = useState(props?.userGroup);

  if (!userGroup) {
    throw new Error("User group not provided in DemographicProvider");
  }

  const context = useMemo(() => {
    return {
      demographicType,
      setDemographicType,
      benchmark,
      setBenchmark,
      layerOpacity,
      setLayerOpacity,
      colorScheme,
      setColorScheme,
      customColorScheme,
      setCustomColorScheme,
      summary,
      setSummary,
      demographicFilters,
      setDemographicFilters,
      scoringFn,
      setScoringFn,
      demographicFilterType,
      setDemographicFilterType,
      userGroup,
      isColorReversed,
      setIsColorReversed
    };
  }, [
    demographicType,
    setDemographicType,
    benchmark,
    setBenchmark,
    layerOpacity,
    setLayerOpacity,
    colorScheme,
    setColorScheme,
    customColorScheme,
    setCustomColorScheme,
    summary,
    setSummary,
    demographicFilters,
    setDemographicFilters,
    scoringFn,
    setScoringFn,
    demographicFilterType,
    setDemographicFilterType,
    userGroup,
    isColorReversed,
      setIsColorReversed
  ]);

  return (
    <DemographicContext.Provider value={context}>
      {children}
    </DemographicContext.Provider>
  );
};

export const useDemographicHeatmap = () => {
  const context = useContext(DemographicContext);
  if (context === undefined) {
    throw new Error(
      "useDemographicHeatmap must be used within a DemographicProvider"
    );
  }
  return context;
};
