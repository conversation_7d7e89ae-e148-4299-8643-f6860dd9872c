import React, { forwardRef } from "react";
import Map from "./Map/Map";
import "./MapWrapper.css";

import { Provider } from "react-redux";
import createSagaMiddleware from "redux-saga";
import { configureStore } from "@reduxjs/toolkit";

import mapReducer from "../model/store/mapStore";
import configureReducer from "../model/store/configureStore";
import mapSaga from "../model/saga/mapSaga";

import { QueryClient, QueryClientProvider } from "react-query";
import { ReactQueryDevtools } from "react-query/devtools";

import { LandBankProvider } from "./features/land-bank/LandBankLayer";
import { DriveTimeProvider } from "./features/drive-time/index";
import { IndustrialParcelProvider } from "./features/industrial-parcels/context";
import { HistoricalZoningProvider } from "./features/historical-zoning/context";

const saga = createSagaMiddleware();
const store = configureStore({
  reducer: {
    Map: mapReducer,
    Configure: configureReducer,
  },
  middleware: [saga],
});
saga.run(mapSaga);

const queryClient = new QueryClient();

const MapWrapper = forwardRef((props, ref) => {
  return (
    <QueryClientProvider client={queryClient}>
      <Provider store={store}>
        <LandBankProvider>
          <DriveTimeProvider>
            <IndustrialParcelProvider>
              <HistoricalZoningProvider>
                <Map
                  getMap={props.getMap}
                  token={props.token}
                  initProperties={props.initProperties}
                  configure={props.configure}
                  serverType={props.serverType}
                  user={props.user}
                  devMode={props.devMode}
                >
                  {props.children}
                </Map>
              </HistoricalZoningProvider>
            </IndustrialParcelProvider>
          </DriveTimeProvider>
        </LandBankProvider>
      </Provider>
      {/* <ReactQueryDevtools initialIsOpen={false} /> */}
    </QueryClientProvider>
  );
});

export default MapWrapper;
