import { postMapTrackingAPI } from "../../services/data";

// type Props = {
//   lat: number;
//   lng: number;
//   type: string;
//   app: string;
// };
export async function trackCoordinateActivity(props) {
  const body = {
    lat: props.lat,
    lng: props.lng,
    type: props.type,
    app: props.app,
  };

  try {
    const response = await postMapTrackingAPI(JSON.stringify(body));
    console.log('Success:', response);
    return response;
  } catch (error) {
    console.error('Error:', error);
    return { error: error };
  }
}
