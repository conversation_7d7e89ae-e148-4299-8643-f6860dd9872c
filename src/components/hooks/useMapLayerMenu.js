export const getMenuSchema = (userGroup, clientEmail) => {
  console.log("clientEmail", clientEmail);
  let schema = [
    {
      name: "School",
      icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWdyYWR1YXRpb24tY2FwLWljb24gbHVjaWRlLWdyYWR1YXRpb24tY2FwIj48cGF0aCBkPSJNMjEuNDIgMTAuOTIyYTEgMSAwIDAgMC0uMDE5LTEuODM4TDEyLjgzIDUuMThhMiAyIDAgMCAwLTEuNjYgMEwyLjYgOS4wOGExIDEgMCAwIDAgMCAxLjgzMmw4LjU3IDMuOTA4YTIgMiAwIDAgMCAxLjY2IDB6Ii8+PHBhdGggZD0iTTIyIDEwdjYiLz48cGF0aCBkPSJNNiAxMi41VjE2YTYgMyAwIDAgMCAxMiAwdi0zLjUiLz48L3N2Zz4=",
      children: [
        {
          id: "school districts",
          name: "Districts",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWFwcGxlLWljb24gbHVjaWRlLWFwcGxlIj48cGF0aCBkPSJNMTIgMjAuOTRjMS41IDAgMi43NSAxLjA2IDQgMS4wNiAzIDAgNi04IDYtMTIuMjJBNC45MSA0LjkxIDAgMCAwIDE3IDVjLTIuMjIgMC00IDEuNDQtNSAyLTEtLjU2LTIuNzgtMi01LTJhNC45IDQuOSAwIDAgMC01IDQuNzhDMiAxNCA1IDIyIDggMjJjMS4yNSAwIDIuNS0xLjA2IDQtMS4wNloiLz48cGF0aCBkPSJNMTAgMmMxIC41IDIgMiAyIDUiLz48L3N2Zz4=",
        },
        {
          id: "school zones",
          name: "Zones & Scores",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWZyYW1lLWljb24gbHVjaWRlLWZyYW1lIj48bGluZSB4MT0iMjIiIHgyPSIyIiB5MT0iNiIgeTI9IjYiLz48bGluZSB4MT0iMjIiIHgyPSIyIiB5MT0iMTgiIHkyPSIxOCIvPjxsaW5lIHgxPSI2IiB4Mj0iNiIgeTE9IjIiIHkyPSIyMiIvPjxsaW5lIHgxPSIxOCIgeDI9IjE4IiB5MT0iMiIgeTI9IjIyIi8+PC9zdmc+",
        },
        {
          id: "charter school",
          name: "Charter Schools",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXNjaG9vbC1pY29uIGx1Y2lkZS1zY2hvb2wiPjxwYXRoIGQ9Ik0xNCAyMnYtNGEyIDIgMCAxIDAtNCAwdjQiLz48cGF0aCBkPSJtMTggMTAgMy40NDcgMS43MjRhMSAxIDAgMCAxIC41NTMuODk0VjIwYTIgMiAwIDAgMS0yIDJINGEyIDIgMCAwIDEtMi0ydi03LjM4MmExIDEgMCAwIDEgLjU1My0uODk0TDYgMTAiLz48cGF0aCBkPSJNMTggNXYxNyIvPjxwYXRoIGQ9Im00IDYgNy4xMDYtMy41NTNhMiAyIDAgMCAxIDEuNzg4IDBMMjAgNiIvPjxwYXRoIGQ9Ik02IDV2MTciLz48Y2lyY2xlIGN4PSIxMiIgY3k9IjkiIHI9IjIiLz48L3N2Zz4=",
        },
      ],
    },
    {
      name: "Admin",
      icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWxhbmQtcGxvdC1pY29uIGx1Y2lkZS1sYW5kLXBsb3QiPjxwYXRoIGQ9Im0xMiA4IDYtMy02LTN2MTAiLz48cGF0aCBkPSJtOCAxMS45OS01LjUgMy4xNGExIDEgMCAwIDAgMCAxLjc0bDguNSA0Ljg2YTIgMiAwIDAgMCAyIDBsOC41LTQuODZhMSAxIDAgMCAwIDAtMS43NEwxNiAxMiIvPjxwYXRoIGQ9Im02LjQ5IDEyLjg1IDExLjAyIDYuMyIvPjxwYXRoIGQ9Ik0xNy41MSAxMi44NSA2LjUgMTkuMTUiLz48L3N2Zz4=",
      children: [
        {
          id: "cbsa",
          name: "Metro Area",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWZpbGUtY2hhcnQtbGluZS1pY29uIGx1Y2lkZS1maWxlLWNoYXJ0LWxpbmUiPjxwYXRoIGQ9Ik0xNSAySDZhMiAyIDAgMCAwLTIgMnYxNmEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWN1oiLz48cGF0aCBkPSJNMTQgMnY0YTIgMiAwIDAgMCAyIDJoNCIvPjxwYXRoIGQ9Im0xNiAxMy0zLjUgMy41LTItMkw4IDE3Ii8+PC9zdmc+",
        },
        {
          id: "county",
          name: "County",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLW1hcC1waW5uZWQtaWNvbiBsdWNpZGUtbWFwLXBpbm5lZCI+PHBhdGggZD0iTTE4IDhjMCAzLjYxMy0zLjg2OSA3LjQyOS01LjM5MyA4Ljc5NWExIDEgMCAwIDEtMS4yMTQgMEM5Ljg3IDE1LjQyOSA2IDExLjYxMyA2IDhhNiA2IDAgMCAxIDEyIDAiLz48Y2lyY2xlIGN4PSIxMiIgY3k9IjgiIHI9IjIiLz48cGF0aCBkPSJNOC43MTQgMTRoLTMuNzFhMSAxIDAgMCAwLS45NDguNjgzbC0yLjAwNCA2QTEgMSAwIDAgMCAzIDIyaDE4YTEgMSAwIDAgMCAuOTQ4LTEuMzE2bC0yLTZhMSAxIDAgMCAwLS45NDktLjY4NGgtMy43MTIiLz48L3N2Zz4=",
        },
        {
          id: "city",
          name: "City",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWJ1aWxkaW5nMi1pY29uIGx1Y2lkZS1idWlsZGluZy0yIj48cGF0aCBkPSJNNiAyMlY0YTIgMiAwIDAgMSAyLTJoOGEyIDIgMCAwIDEgMiAydjE4WiIvPjxwYXRoIGQ9Ik02IDEySDRhMiAyIDAgMCAwLTIgMnY2YTIgMiAwIDAgMCAyIDJoMiIvPjxwYXRoIGQ9Ik0xOCA5aDJhMiAyIDAgMCAxIDIgMnY5YTIgMiAwIDAgMS0yIDJoLTIiLz48cGF0aCBkPSJNMTAgNmg0Ii8+PHBhdGggZD0iTTEwIDEwaDQiLz48cGF0aCBkPSJNMTAgMTRoNCIvPjxwYXRoIGQ9Ik0xMCAxOGg0Ii8+PC9zdmc+",
        },
        {
          id: "ZIP code",
          name: "ZIP Code",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWZlbmNlLWljb24gbHVjaWRlLWZlbmNlIj48cGF0aCBkPSJNNCAzIDIgNXYxNWMwIC42LjQgMSAxIDFoMmMuNiAwIDEtLjQgMS0xVjVaIi8+PHBhdGggZD0iTTYgOGg0Ii8+PHBhdGggZD0iTTYgMThoNCIvPjxwYXRoIGQ9Im0xMiAzLTIgMnYxNWMwIC42LjQgMSAxIDFoMmMuNiAwIDEtLjQgMS0xVjVaIi8+PHBhdGggZD0iTTE0IDhoNCIvPjxwYXRoIGQ9Ik0xNCAxOGg0Ii8+PHBhdGggZD0ibTIwIDMtMiAydjE1YzAgLjYuNCAxIDEgMWgyYy42IDAgMS0uNCAxLTFWNVoiLz48L3N2Zz4=",
        },
        {
          id: "tax",
          name: "Tax Rate",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWJhZGdlLWRvbGxhci1zaWduLWljb24gbHVjaWRlLWJhZGdlLWRvbGxhci1zaWduIj48cGF0aCBkPSJNMy44NSA4LjYyYTQgNCAwIDAgMSA0Ljc4LTQuNzcgNCA0IDAgMCAxIDYuNzQgMCA0IDQgMCAwIDEgNC43OCA0Ljc4IDQgNCAwIDAgMSAwIDYuNzQgNCA0IDAgMCAxLTQuNzcgNC43OCA0IDQgMCAwIDEtNi43NSAwIDQgNCAwIDAgMS00Ljc4LTQuNzcgNCA0IDAgMCAxIDAtNi43NloiLz48cGF0aCBkPSJNMTYgOGgtNmEyIDIgMCAxIDAgMCA0aDRhMiAyIDAgMSAxIDAgNEg4Ii8+PHBhdGggZD0iTTEyIDE4VjYiLz48L3N2Zz4=",
        },
        {
          id: "subdivision",
          name: "Subdivision",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXVzZXJzLWljb24gbHVjaWRlLXVzZXJzIj48cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIvPjxwYXRoIGQ9Ik0xNiAzLjEyOGE0IDQgMCAwIDEgMCA3Ljc0NCIvPjxwYXRoIGQ9Ik0yMiAyMXYtMmE0IDQgMCAwIDAtMy0zLjg3Ii8+PGNpcmNsZSBjeD0iOSIgY3k9IjciIHI9IjQiLz48L3N2Zz4=",
        },
        {
          id: "neighborhood",
          name: "Neighborhood",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWJyaWNrLXdhbGwtaWNvbiBsdWNpZGUtYnJpY2std2FsbCI+PHJlY3Qgd2lkdGg9IjE4IiBoZWlnaHQ9IjE4IiB4PSIzIiB5PSIzIiByeD0iMiIvPjxwYXRoIGQ9Ik0xMiA5djYiLz48cGF0aCBkPSJNMTYgMTV2NiIvPjxwYXRoIGQ9Ik0xNiAzdjYiLz48cGF0aCBkPSJNMyAxNWgxOCIvPjxwYXRoIGQ9Ik0zIDloMTgiLz48cGF0aCBkPSJNOCAxNXY2Ii8+PHBhdGggZD0iTTggM3Y2Ii8+PC9zdmc+",
        },
        // {
        //   id: "qualified census tracts",
        //   name: "Qualified Census Tracts",
        //   icon: "https://sl-img-client.s3.amazonaws.com/menu-layer-icon/neighborhood.png",
        // },
      ],
    },
    {
      name: "Points of Interest",
      abbrev: "POI",
      icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLW1hcC1waW4taWNvbiBsdWNpZGUtbWFwLXBpbiI+PHBhdGggZD0iTTIwIDEwYzAgNC45OTMtNS41MzkgMTAuMTkzLTcuMzk5IDExLjc5OWExIDEgMCAwIDEtMS4yMDIgMEM5LjUzOSAyMC4xOTMgNCAxNC45OTMgNCAxMGE4IDggMCAwIDEgMTYgMCIvPjxjaXJjbGUgY3g9IjEyIiBjeT0iMTAiIHI9IjMiLz48L3N2Zz4=",
      children: [
        // {
        //   id: "bfr cluster",
        //   name: "BFR Communities (Divided Lots)",
        //   icon: "https://sl-img-client.s3.amazonaws.com/menu-layer-icon/neighborhood.png",
        // },
        {
          id: "airbnb",
          name: "AirBnB",
          icon: "https://sl-img-client.s3.amazonaws.com/menu-layer-icon/airbnb.png",
        },
        {
          id: "prologis",
          name: "Prologis",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXRydWNrLWljb24gbHVjaWRlLXRydWNrIj48cGF0aCBkPSJNMTQgMThWNmEyIDIgMCAwIDAtMi0ySDRhMiAyIDAgMCAwLTIgMnYxMWExIDEgMCAwIDAgMSAxaDIiLz48cGF0aCBkPSJNMTUgMThIOSIvPjxwYXRoIGQ9Ik0xOSAxOGgyYTEgMSAwIDAgMCAxLTF2LTMuNjVhMSAxIDAgMCAwLS4yMi0uNjI0bC0zLjQ4LTQuMzVBMSAxIDAgMCAwIDE3LjUyIDhIMTQiLz48Y2lyY2xlIGN4PSIxNyIgY3k9IjE4IiByPSIyIi8+PGNpcmNsZSBjeD0iNyIgY3k9IjE4IiByPSIyIi8+PC9zdmc+",
        },

        {
          id: "institutional owners",
          name: "Institutional Owners",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWxhbmRtYXJrLWljb24gbHVjaWRlLWxhbmRtYXJrIj48cGF0aCBkPSJNMTAgMTh2LTciLz48cGF0aCBkPSJNMTEuMTIgMi4xOThhMiAyIDAgMCAxIDEuNzYuMDA2bDcuODY2IDMuODQ3Yy40NzYuMjMzLjMxLjk0OS0uMjIuOTQ5SDMuNDc0Yy0uNTMgMC0uNjk1LS43MTYtLjIyLS45NDl6Ii8+PHBhdGggZD0iTTE0IDE4di03Ii8+PHBhdGggZD0iTTE4IDE4di03Ii8+PHBhdGggZD0iTTMgMjJoMTgiLz48cGF0aCBkPSJNNiAxOHYtNyIvPjwvc3ZnPg==",
        },
        {
          id: "multi family apartments",
          name: "Multi Family Community",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWJveGVzLWljb24gbHVjaWRlLWJveGVzIj48cGF0aCBkPSJNMi45NyAxMi45MkEyIDIgMCAwIDAgMiAxNC42M3YzLjI0YTIgMiAwIDAgMCAuOTcgMS43MWwzIDEuOGEyIDIgMCAwIDAgMi4wNiAwTDEyIDE5di01LjVsLTUtMy00LjAzIDIuNDJaIi8+PHBhdGggZD0ibTcgMTYuNS00Ljc0LTIuODUiLz48cGF0aCBkPSJtNyAxNi41IDUtMyIvPjxwYXRoIGQ9Ik03IDE2LjV2NS4xNyIvPjxwYXRoIGQ9Ik0xMiAxMy41VjE5bDMuOTcgMi4zOGEyIDIgMCAwIDAgMi4wNiAwbDMtMS44YTIgMiAwIDAgMCAuOTctMS43MXYtMy4yNGEyIDIgMCAwIDAtLjk3LTEuNzFMMTcgMTAuNWwtNSAzWiIvPjxwYXRoIGQ9Im0xNyAxNi41LTUtMyIvPjxwYXRoIGQ9Im0xNyAxNi41IDQuNzQtMi44NSIvPjxwYXRoIGQ9Ik0xNyAxNi41djUuMTciLz48cGF0aCBkPSJNNy45NyA0LjQyQTIgMiAwIDAgMCA3IDYuMTN2NC4zN2w1IDMgNS0zVjYuMTNhMiAyIDAgMCAwLS45Ny0xLjcxbC0zLTEuOGEyIDIgMCAwIDAtMi4wNiAwbC0zIDEuOFoiLz48cGF0aCBkPSJNMTIgOCA3LjI2IDUuMTUiLz48cGF0aCBkPSJtMTIgOCA0Ljc0LTIuODUiLz48cGF0aCBkPSJNMTIgMTMuNVY4Ii8+PC9zdmc+",
        },
        {
          id: "major employer",
          name: "Major Employers",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWJyaWVmY2FzZS1idXNpbmVzcy1pY29uIGx1Y2lkZS1icmllZmNhc2UtYnVzaW5lc3MiPjxwYXRoIGQ9Ik0xMiAxMmguMDEiLz48cGF0aCBkPSJNMTYgNlY0YTIgMiAwIDAgMC0yLTJoLTRhMiAyIDAgMCAwLTIgMnYyIi8+PHBhdGggZD0iTTIyIDEzYTE4LjE1IDE4LjE1IDAgMCAxLTIwIDAiLz48cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTQiIHg9IjIiIHk9IjYiIHJ4PSIyIi8+PC9zdmc+",
        },
        {
          id: "industrial parcels",
          name: "Industrial Parcels",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWJ1aWxkaW5nLWljb24gbHVjaWRlLWJ1aWxkaW5nIj48cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMjAiIHg9IjQiIHk9IjIiIHJ4PSIyIiByeT0iMiIvPjxwYXRoIGQ9Ik05IDIydi00aDZ2NCIvPjxwYXRoIGQ9Ik04IDZoLjAxIi8+PHBhdGggZD0iTTE2IDZoLjAxIi8+PHBhdGggZD0iTTEyIDZoLjAxIi8+PHBhdGggZD0iTTEyIDEwaC4wMSIvPjxwYXRoIGQ9Ik0xMiAxNGguMDEiLz48cGF0aCBkPSJNMTYgMTBoLjAxIi8+PHBhdGggZD0iTTE2IDE0aC4wMSIvPjxwYXRoIGQ9Ik04IDEwaC4wMSIvPjxwYXRoIGQ9Ik04IDE0aC4wMSIvPjwvc3ZnPg==",
        },
        {
          id: "mobile home park",
          name: "Mobile Home Park",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWhvdXNlLWljb24gbHVjaWRlLWhvdXNlIj48cGF0aCBkPSJNMTUgMjF2LThhMSAxIDAgMCAwLTEtMWgtNGExIDEgMCAwIDAtMSAxdjgiLz48cGF0aCBkPSJNMyAxMGEyIDIgMCAwIDEgLjcwOS0xLjUyOGw3LTUuOTk5YTIgMiAwIDAgMSAyLjU4MiAwbDcgNS45OTlBMiAyIDAgMCAxIDIxIDEwdjlhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJ6Ii8+PC9zdmc+",
        },
      ],
    },
    {
      name: "Zones",
      icon: "https://sl-img-client.s3.amazonaws.com/menu-layer-icon/drivetime2.png",
      children: [
        {
          id: "activity centers",
          name: "Activity Centers",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWJyaWVmY2FzZS1idXNpbmVzcy1pY29uIGx1Y2lkZS1icmllZmNhc2UtYnVzaW5lc3MiPjxwYXRoIGQ9Ik0xMiAxMmguMDEiLz48cGF0aCBkPSJNMTYgNlY0YTIgMiAwIDAgMC0yLTJoLTRhMiAyIDAgMCAwLTIgMnYyIi8+PHBhdGggZD0iTTIyIDEzYTE4LjE1IDE4LjE1IDAgMCAxLTIwIDAiLz48cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTQiIHg9IjIiIHk9IjYiIHJ4PSIyIi8+PC9zdmc+",
        },
        {
          id: "drive time",
          name: "Drive Time",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWNhci1mcm9udC1pY29uIGx1Y2lkZS1jYXItZnJvbnQiPjxwYXRoIGQ9Im0yMSA4LTIgMi0xLjUtMy43QTIgMiAwIDAgMCAxNS42NDYgNUg4LjRhMiAyIDAgMCAwLTEuOTAzIDEuMjU3TDUgMTAgMyA4Ii8+PHBhdGggZD0iTTcgMTRoLjAxIi8+PHBhdGggZD0iTTE3IDE0aC4wMSIvPjxyZWN0IHdpZHRoPSIxOCIgaGVpZ2h0PSI4IiB4PSIzIiB5PSIxMCIgcng9IjIiLz48cGF0aCBkPSJNNSAxOHYyIi8+PHBhdGggZD0iTTE5IDE4djIiLz48L3N2Zz4=",
        },
        {
          id: "opportunity zones",
          name: "Opportunity Zones",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXdyZW5jaC1pY29uIGx1Y2lkZS13cmVuY2giPjxwYXRoIGQ9Ik0xNC43IDYuM2ExIDEgMCAwIDAgMCAxLjRsMS42IDEuNmExIDEgMCAwIDAgMS40IDBsMy43Ny0zLjc3YTYgNiAwIDAgMS03Ljk0IDcuOTRsLTYuOTEgNi45MWEyLjEyIDIuMTIgMCAwIDEtMy0zbDYuOTEtNi45MWE2IDYgMCAwIDEgNy45NC03Ljk0bC0zLjc2IDMuNzZ6Ii8+PC9zdmc+",
        },
        {
          id: "zoning types",
          name: "Zoning Types",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXNwbGl0LWljb24gbHVjaWRlLXNwbGl0Ij48cGF0aCBkPSJNMTYgM2g1djUiLz48cGF0aCBkPSJNOCAzSDN2NSIvPjxwYXRoIGQ9Ik0xMiAyMnYtOC4zYTQgNCAwIDAgMC0xLjE3Mi0yLjg3MkwzIDMiLz48cGF0aCBkPSJtMTUgOSA2LTYiLz48L3N2Zz4=",
        },
      ],
    },
    {
      name: "Flood",
      icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXdhdmVzLWljb24gbHVjaWRlLXdhdmVzIj48cGF0aCBkPSJNMiA2Yy42LjUgMS4yIDEgMi41IDFDNyA3IDcgNSA5LjUgNWMyLjYgMCAyLjQgMiA1IDIgMi41IDAgMi41LTIgNS0yIDEuMyAwIDEuOS41IDIuNSAxIi8+PHBhdGggZD0iTTIgMTJjLjYuNSAxLjIgMSAyLjUgMSAyLjUgMCAyLjUtMiA1LTIgMi42IDAgMi40IDIgNSAyIDIuNSAwIDIuNS0yIDUtMiAxLjMgMCAxLjkuNSAyLjUgMSIvPjxwYXRoIGQ9Ik0yIDE4Yy42LjUgMS4yIDEgMi41IDEgMi41IDAgMi41LTIgNS0yIDIuNiAwIDIuNCAyIDUgMiAyLjUgMCAyLjUtMiA1LTIgMS4zIDAgMS45LjUgMi41IDEiLz48L3N2Zz4=",
      children: [
        {
          id: "flood zone",
          name: "Flood Zone",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWNsb3VkLXJhaW4td2luZC1pY29uIGx1Y2lkZS1jbG91ZC1yYWluLXdpbmQiPjxwYXRoIGQ9Ik00IDE0Ljg5OUE3IDcgMCAxIDEgMTUuNzEgOGgxLjc5YTQuNSA0LjUgMCAwIDEgMi41IDguMjQyIi8+PHBhdGggZD0ibTkuMiAyMiAzLTciLz48cGF0aCBkPSJtOSAxMy0zIDciLz48cGF0aCBkPSJtMTcgMTMtMyA3Ii8+PC9zdmc+",
        },
        {
          id: "wetlands",
          name: "Wetlands",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWZpc2gtaWNvbiBsdWNpZGUtZmlzaCI+PHBhdGggZD0iTTYuNSAxMmMuOTQtMy40NiA0Ljk0LTYgOC41LTYgMy41NiAwIDYuMDYgMi41NCA3IDYtLjk0IDMuNDctMy40NCA2LTcgNnMtNy41Ni0yLjUzLTguNS02WiIvPjxwYXRoIGQ9Ik0xOCAxMnYuNSIvPjxwYXRoIGQ9Ik0xNiAxNy45M2E5Ljc3IDkuNzcgMCAwIDEgMC0xMS44NiIvPjxwYXRoIGQ9Ik03IDEwLjY3QzcgOCA1LjU4IDUuOTcgMi43MyA1LjVjLTEgMS41LTEgNSAuMjMgNi41LTEuMjQgMS41LTEuMjQgNS0uMjMgNi41QzUuNTggMTguMDMgNyAxNiA3IDEzLjMzIi8+PHBhdGggZD0iTTEwLjQ2IDcuMjZDMTAuMiA1Ljg4IDkuMTcgNC4yNCA4IDNoNS44YTIgMiAwIDAgMSAxLjk4IDEuNjdsLjIzIDEuNCIvPjxwYXRoIGQ9Im0xNi4wMSAxNy45My0uMjMgMS40QTIgMiAwIDAgMSAxMy44IDIxSDkuNWE1Ljk2IDUuOTYgMCAwIDAgMS40OS0zLjk4Ii8+PC9zdmc+",
        },
        {
          id: "municipal water district",
          name: "Municipal Water District",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWRyb3BsZXQtaWNvbiBsdWNpZGUtZHJvcGxldCI+PHBhdGggZD0iTTEyIDIyYTcgNyAwIDAgMCA3LTdjMC0yLTEtMy45LTMtNS41cy0zLjUtNC00LTYuNWMtLjUgMi41LTIgNC45LTQgNi41QzYgMTEuMSA1IDEzIDUgMTVhNyA3IDAgMCAwIDcgN3oiLz48L3N2Zz4=",
        },
      ],
    },

    {
      name: "Infrastructure",
      abbrev: "INFRA",
      icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXphcC1pY29uIGx1Y2lkZS16YXAiPjxwYXRoIGQ9Ik00IDE0YTEgMSAwIDAgMS0uNzgtMS42M2w5LjktMTAuMmEuNS41IDAgMCAxIC44Ni40NmwtMS45MiA2LjAyQTEgMSAwIDAgMCAxMyAxMGg3YTEgMSAwIDAgMSAuNzggMS42M2wtOS45IDEwLjJhLjUuNSAwIDAgMS0uODYtLjQ2bDEuOTItNi4wMkExIDEgMCAwIDAgMTEgMTR6Ii8+PC9zdmc+",
      children: [
        {
          id: "power lines",
          name: "Power Lines",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXBsdWctaWNvbiBsdWNpZGUtcGx1ZyI+PHBhdGggZD0iTTEyIDIydi01Ii8+PHBhdGggZD0iTTkgOFYyIi8+PHBhdGggZD0iTTE1IDhWMiIvPjxwYXRoIGQ9Ik0xOCA4djVhNCA0IDAgMCAxLTQgNGgtNGE0IDQgMCAwIDEtNC00VjhaIi8+PC9zdmc+",
        },
        {
          id: "rail network",
          name: "Rail Network",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXRyYWluLXRyYWNrLWljb24gbHVjaWRlLXRyYWluLXRyYWNrIj48cGF0aCBkPSJNMiAxNyAxNyAyIi8+PHBhdGggZD0ibTIgMTQgOCA4Ii8+PHBhdGggZD0ibTUgMTEgOCA4Ii8+PHBhdGggZD0ibTggOCA4IDgiLz48cGF0aCBkPSJtMTEgNSA4IDgiLz48cGF0aCBkPSJtMTQgMiA4IDgiLz48cGF0aCBkPSJNNyAyMiAyMiA3Ii8+PC9zdmc+",
        },
        {
          id: "arcgis transit line",
          name: "Transit Line",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXRyYW0tZnJvbnQtaWNvbiBsdWNpZGUtdHJhbS1mcm9udCI+PHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiB4PSI0IiB5PSIzIiByeD0iMiIvPjxwYXRoIGQ9Ik00IDExaDE2Ii8+PHBhdGggZD0iTTEyIDN2OCIvPjxwYXRoIGQ9Im04IDE5LTIgMyIvPjxwYXRoIGQ9Im0xOCAyMi0yLTMiLz48cGF0aCBkPSJNOCAxNWguMDEiLz48cGF0aCBkPSJNMTYgMTVoLjAxIi8+PC9zdmc+",
        },
        {
          id: "arcgis bus stop",
          name: "Bus Stops",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWJ1cy1pY29uIGx1Y2lkZS1idXMiPjxwYXRoIGQ9Ik04IDZ2NiIvPjxwYXRoIGQ9Ik0xNSA2djYiLz48cGF0aCBkPSJNMiAxMmgxOS42Ii8+PHBhdGggZD0iTTE4IDE4aDNzLjUtMS43LjgtMi44Yy4xLS40LjItLjguMi0xLjIgMC0uNC0uMS0uOC0uMi0xLjJsLTEuNC01QzIwLjEgNi44IDE5LjEgNiAxOCA2SDRhMiAyIDAgMCAwLTIgMnYxMGgzIi8+PGNpcmNsZSBjeD0iNyIgY3k9IjE4IiByPSIyIi8+PHBhdGggZD0iTTkgMThoNSIvPjxjaXJjbGUgY3g9IjE2IiBjeT0iMTgiIHI9IjIiLz48L3N2Zz4=",
        },
        {
          id: "arcgis amtrak",
          name: "Amtrak Stations",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXRyYWluLWZyb250LWljb24gbHVjaWRlLXRyYWluLWZyb250Ij48cGF0aCBkPSJNOCAzLjFWN2E0IDQgMCAwIDAgOCAwVjMuMSIvPjxwYXRoIGQ9Im05IDE1LTEtMSIvPjxwYXRoIGQ9Im0xNSAxNSAxLTEiLz48cGF0aCBkPSJNOSAxOWMtMi44IDAtNS0yLjItNS01di00YTggOCAwIDAgMSAxNiAwdjRjMCAyLjgtMi4yIDUtNSA1WiIvPjxwYXRoIGQ9Im04IDE5LTIgMyIvPjxwYXRoIGQ9Im0xNiAxOSAyIDMiLz48L3N2Zz4=",
        },
        {
          id: "arcgis ca rail",
          name: "Rail Stations (CA)",
          icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLXRyYWluLWZyb250LXR1bm5lbC1pY29uIGx1Y2lkZS10cmFpbi1mcm9udC10dW5uZWwiPjxwYXRoIGQ9Ik0yIDIyVjEyYTEwIDEwIDAgMSAxIDIwIDB2MTAiLz48cGF0aCBkPSJNMTUgNi44djEuNGEzIDIuOCAwIDEgMS02IDBWNi44Ii8+PHBhdGggZD0iTTEwIDE1aC4wMSIvPjxwYXRoIGQ9Ik0xNCAxNWguMDEiLz48cGF0aCBkPSJNMTAgMTlhNCA0IDAgMCAxLTQtNHYtM2E2IDYgMCAxIDEgMTIgMHYzYTQgNCAwIDAgMS00IDRaIi8+PHBhdGggZD0ibTkgMTktMiAzIi8+PHBhdGggZD0ibTE1IDE5IDIgMyIvPjwvc3ZnPg==",
        },
      ],
    },
  ];

  if (userGroup) {
    if (typeof userGroup !== "string")
      throw new Error("userGroup for getMenuSchema must be a string");

    const ownedType = {};

    if (["HON"].includes(userGroup)) {
      ownedType.id = "hon owned";
      ownedType.name = "Owned";
      ownedType.icon = "https://sl-img-client.s3.amazonaws.com/menu-layer-icon/honlayer.png";
      ownedType.description = "HON Properties";

      schema.unshift(ownedType);
    } else if (["AlliedDev"].includes(userGroup)) {
      ownedType.id = "Allied Dev owned";
      ownedType.name = "Owned";
      ownedType.icon =
        "https://sl-img-client.s3.us-east-1.amazonaws.com/menu-layer-icon/AlliedDev-icon.png";
      ownedType.description = "Allied Dev Properties";
      ownedType.children = [
        // {
        //   id: "allied_mineola_future_land_use",
        //   name: "Mineola Future Land Use",
        // },
        // {
        //   id: "allied_sanitary_sewer",
        //   name: "Sanitary Sewer",
        // },
        {
          name: "Client Data",
          children: [
            {
              id: "allied_mineola_future_land_use",
              name: "Mineola Future Land Use",
            },
            {
              id: "allied_sanitary_sewer",
              name: "Roanoke Sanitary Sewer",
            },
          ],
        },
      ];
      schema.unshift(ownedType);
    } else if (["BridgeAdmin", "BridgeFull"].includes(userGroup)) {
      ownedType.id = "gorlick owned";
      ownedType.name = "Owned";
      ownedType.icon = "https://sl-img-client.s3.amazonaws.com/menu-layer-icon/gorlicklayer.png";
      ownedType.description = "Bridge Investment Group Properties";

      schema.unshift(ownedType);
    } else if (["Truehold"].includes(userGroup)) {
      ownedType.id = "truehold owned";
      ownedType.name = "Owned";
      ownedType.icon = "https://sl-img-client.s3.amazonaws.com/menu-layer-icon/truehold-icon.jpg";
      ownedType.description = "Truehold Properties";
      ownedType.children = [
        {
          id: "truehold owned",
          name: "Owned",
        },
        {
          id: "truehold underwritten",
          name: "Underwritten",
        },
        {
          id: "truehold target",
          name: "Target",
        },
      ];

      schema.unshift(ownedType);
    } else if (["BridgeTower"].includes(userGroup)) {
      ownedType.id = "Bridge Tower BTR";
      ownedType.name = "Owned";
      ownedType.icon =
        "https://sl-img-client.s3.amazonaws.com/menu-layer-icon/BridgeTower-icon.png";
      ownedType.description = "Bridge Tower BTR Properties";

      schema.unshift(ownedType);
    } else if (["BlueRiver"].includes(userGroup)) {
      const poiIdx = schema.findIndex((item) => item.abbrev === "POI");

      schema[poiIdx].children.push({
        id: "public record prospect",
        name: "Public Record Prospect",
        icon: "https://sl-img-client.s3.amazonaws.com/menu-layer-icon/prp.png",
      });

      ownedType.name = "Owned";
      ownedType.icon = "https://sl-img-client.s3.amazonaws.com/menu-layer-icon/blueriver.png";
      ownedType.description = "Blue River Development";
      ownedType.children = [
        {
          id: "blueriver active",
          name: "Active",
          description: "Active deals",
        },
        {
          id: "blueriver sold",
          name: "Sold",
          description: "Sold deals",
        },
      ];
      schema.unshift(ownedType);
    } else if (["demo-users"].includes(userGroup)) {
      // const AdminIdx = schema.findIndex((item) => item.name === "Admin");

      ownedType.name = "Owned";
      ownedType.icon =
        "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWJvb2stb3Blbi1pY29uIGx1Y2lkZS1ib29rLW9wZW4iPjxwYXRoIGQ9Ik0xMiA3djE0Ii8+PHBhdGggZD0iTTMgMThhMSAxIDAgMCAxLTEtMVY0YTEgMSAwIDAgMSAxLTFoNWE0IDQgMCAwIDEgNCA0IDQgNCAwIDAgMSA0LTRoNWExIDEgMCAwIDEgMSAxdjEzYTEgMSAwIDAgMS0xIDFoLTZhMyAzIDAgMCAwLTMgMyAzIDMgMCAwIDAtMy0zeiIvPjwvc3ZnPg==";
      ownedType.description = "demo owned Properties";
      ownedType.children = [
        {
          id: "demo owned",
          name: "Owned Homes",
        },
        {
          id: "demo underwritten",
          name: "Underwritten Homes",
        },
        {
          id: "demo land owned",
          name: "Owned Land",
        },
        {
          id: "demo land underwritten",
          name: "Underwritten Land",
        },
        {
          name: "Client Data",
          children: [
            {
              id: "allied_mineola_future_land_use",
              name: "Mineola Future Land Use",
            },
            {
              id: "allied_sanitary_sewer",
              name: "Roanoke Sanitary Sewer",
            },
            {
              id: "second_ave_public_housing_property",
              name: "Public Housing Property",
            },
          ],
        },
      ];

      schema.unshift(ownedType);
    } else if (["Platlabs"].includes(userGroup)) {
      const filteredSchema = schema.filter(
        (item) => item.name !== "Infrastructure" && item.name !== "Points of Interest"
      );
      // greystar-mudtax
      // Remove "Municipal Water District" from the "Flood" section
      filteredSchema.forEach((item) => {
        if (item.name === "Flood") {
          item.children = item.children.filter((child) => child.id !== "municipal water district");
        }
      });
      return filteredSchema;
    } else if (
      ["Greystar"].includes(userGroup) &&
      [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ].includes(clientEmail)
    ) {
      ownedType.name = "Owned";
      ownedType.icon = "https://sl-img-client.s3.amazonaws.com/menu-layer-icon/greystar.png";
      ownedType.description = "Greystar";
      ownedType.children = [
        {
          id: "greystar mudtax",
          name: "Greystar Mud Tax Rate",
          description: "",
        },
        {
          id: "greystar houston comps",
          name: "Greystar Houston Comps",
          description: "",
        },
      ];
      schema.unshift(ownedType);
    } else if (["demo-CMA-DFW-only"].includes(userGroup)) {
      ownedType.name = "Owned";
      ownedType.icon = "https://sl-img-client.s3.amazonaws.com/menu-layer-icon/locatealpha.png";
      ownedType.description = "demo owned Properties";
      ownedType.children = [
        {
          id: "demo owned",
          name: "Owned Homes",
        },
        {
          id: "demo underwritten",
          name: "Underwritten Homes",
        },
        {
          id: "demo land owned",
          name: "Owned Land",
        },
        {
          id: "demo land underwritten",
          name: "Underwritten Land",
        },
        {
          name: "Client Data",
          children: [
            {
              id: "allied_mineola_future_land_use",
              name: "Mineola Future Land Use",
            },
            {
              id: "allied_sanitary_sewer",
              name: "Roanoke Sanitary Sewer",
            },
            {
              id: "core_spaces_portfolio",
              name: "Test portfolio",
            },
          ],
        },
      ];

      schema.unshift(ownedType);
    } else if (["GreatGulf"].includes(userGroup)) {
      ownedType.name = "Owned";
      ownedType.icon = "https://sl-img-client.s3.amazonaws.com/menu-layer-icon/greatgulf.png";
      // ownedType.description = "Great Gulf Portfolio";
      ownedType.children = [
        {
          id: "greatgulf portfolio",
          name: "Portfolio",
          description: "Great Gulf Portfolio",
        },
      ];
      schema.unshift(ownedType);
    } else if (["Fundrise"].includes(userGroup)) {
      ownedType.name = "Owned";
      ownedType.icon = "https://sl-img-client.s3.amazonaws.com/menu-layer-icon/fundrise-icon.png";
      ownedType.description = "Fundrise Properties";
      ownedType.children = [
        // {
        //   id: "fundrise properties",
        //   name: "Fundrise Properties",
        // },
        {
          id: "fundrise owned community",
          name: "Fundrise Properties",
        },
      ];

      schema.unshift(ownedType);
    } else if (["InvitationHomes"].includes(userGroup)) {
      ownedType.name = "Owned";
      ownedType.icon = "https://sl-img-client.s3.us-east-1.amazonaws.com/menu-layer-icon/Invitation_Home_logo.png";
      ownedType.description = "Invitation Home BTR";
      ownedType.children = [
        // {
        //   id: "fundrise properties",
        //   name: "Fundrise Properties",
        // },
        {
          id: "invitationhomes btr",
          name: "Invitation Home BTR",
        },
      ];

      schema.unshift(ownedType);
    } else if (["SecondAvenue"].includes(userGroup)) {
      ownedType.id = "Second Avenue owned";
      ownedType.name = "Owned";
      ownedType.icon =
        "https://sl-img-client.s3.us-east-1.amazonaws.com/menu-layer-icon/second-avenue-logo.png";
      ownedType.description = "Second Avenue Properties";
      ownedType.children = [
        // {
        //   id: "allied_mineola_future_land_use",
        //   name: "Mineola Future Land Use",
        // },
        // {
        //   id: "allied_sanitary_sewer",
        //   name: "Sanitary Sewer",
        // },
        {
          name: "Client Data",
          children: [
            {
              id: "second_ave_public_housing_property",
              name: "Public Housing Property",
            },
          ],
        },
      ];
      schema.unshift(ownedType);
    } else if (["Avanta"].includes(userGroup)) {
      // Add Multi Family options for Avanta users
      const poiIdx = schema.findIndex(
        (item) => item.name === "Points of Interest" || item.abbrev === "POI"
      );

      if (poiIdx !== -1) {
        // Find where to insert Multi Family options (after institutional owners)
        const institutionalIdx = schema[poiIdx].children.findIndex(
          (child) => child.id === "institutional owners"
        );

        const multiFamilyOptions = [
          {
            id: "multi family",
            name: "Multi Family",
            icon: "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyNCIgaGVpZ2h0PSIyNCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9ImN1cnJlbnRDb2xvciIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJsdWNpZGUgbHVjaWRlLWFybWNoYWlyLWljb24gbHVjaWRlLWFybWNoYWlyIj48cGF0aCBkPSJNMTkgOVY2YTIgMiAwIDAgMC0yLTJIN2EyIDIgMCAwIDAtMiAydjMiLz48cGF0aCBkPSJNMyAxNmEyIDIgMCAwIDAgMiAyaDE0YTIgMiAwIDAgMCAyLTJ2LTVhMiAyIDAgMCAwLTQgMHYxLjVhLjUuNSAwIDAgMS0uNS41aC05YS41LjUgMCAwIDEtLjUtLjVWMTFhMiAyIDAgMCAwLTQgMHoiLz48cGF0aCBkPSJNNSAxOHYyIi8+PHBhdGggZD0iTTE5IDE4djIiLz48L3N2Zz4=",
          },
        ];

        if (institutionalIdx !== -1) {
          // Insert after institutional owners
          schema[poiIdx].children.splice(institutionalIdx + 1, 0, ...multiFamilyOptions);
        } else {
          // If institutional owners not found, add at the end
          schema[poiIdx].children.push(...multiFamilyOptions);
        }
      }
    }
  }

  return schema;
};
