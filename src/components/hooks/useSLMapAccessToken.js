import React from "react";

export const useSLMapAccessToken = ({ map }) => {
  const [token, setToken] = React.useState(null);

  React.useEffect(() => {
    if (!map) return;

    const checkForLatestToken = async () => {
      const newToken = await window.spatiallaser.getUserToken("access");
      if (token !== newToken) {
        setToken(newToken);
      }
    };

    checkForLatestToken();
    map.on("movestart", checkForLatestToken);

    return () => {
      map.off("movestart", checkForLatestToken);
    };
  }, [map, token]);

  return token;
};
