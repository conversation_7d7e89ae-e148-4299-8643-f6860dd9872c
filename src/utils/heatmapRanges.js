export const submarketRanges = {
  vacancy_rate: [
    { value: 0, color: "#1a9641" },
    { value: 3, color: "#58b453" },
    { value: 7, color: "#97d265" },
    { value: 11, color: "#c4e687" },
    { value: 16, color: "#ecf7ad" },
    { value: 21, color: "#ffedab" },
    { value: 31, color: "#fec981" },
    { value: 41, color: "#ffb987" },
    { value: 51, color: "#e85b3a" },
    { value: 71, color: "#d7191c" },
  ],
  market_effective_rent_growth_12_months: [
    { value: -50, color: "#d7191c" },
    { value: -9, color: "#e85b3a" },
    { value: -4, color: "#ffb987" },
    { value: 1, color: "#fec981" },
    { value: 4, color: "#ffedab" },
    { value: 6, color: "#ecf7ad" },
    { value: 8, color: "#c4e687" },
    { value: 11, color: "#97d265" },
    { value: 13, color: "#58b453" },
    { value: 25, color: "#1a9641" },
  ],
  market_effective_rent_sf: [
    { value: 0, color: "#1a9641" },
    { value: 1, color: "#58b453" },
    { value: 1.1, color: "#97d265" },
    { value: 1.2, color: "#c4e687" },
    { value: 1.3, color: "#ecf7ad" },
    { value: 1.4, color: "#ffedab" },
    { value: 1.5, color: "#fec981" },
    { value: 1.7, color: "#f99e59" },
    { value: 1.9, color: "#e85b3a" },
    { value: 2.2, color: "#d7191c" },
  ],
  market_effective_rent_unit: [
    { value: 0, color: "#1a9641" },
    { value: 1000, color: "#58b453" },
    { value: 1200, color: "#97d265" },
    { value: 1400, color: "#c4e687" },
    { value: 1600, color: "#ecf7ad" },
    { value: 1800, color: "#ffedab" },
    { value: 2000, color: "#fec981" },
    { value: 2200, color: "#f99e59" },
    { value: 2400, color: "#e85b3a" },
    { value: 2600, color: "#d7191c" },
  ],
  rental_growth_5_years: [
    { value: 1, color: "#d7191c" },
    { value: 2, color: "#e85b3a" },
    { value: 3, color: "#f99e59" },
    { value: 4, color: "#fec981" },
    { value: 5, color: "#ffedab" },
    { value: 6, color: "#ecf7ad" },
    { value: 7, color: "#c4e687" },
    { value: 8, color: "#97d265" },
    { value: 9, color: "#58b453" },
    { value: 10, color: "#1a9641" },
  ],
  combinedFilterScore: [
    { value: 1, color: "#d7191c" },
    { value: 2, color: "#e85b3a" },
    { value: 3, color: "#f99e59" },
    { value: 4, color: "#fec981" },
    { value: 5, color: "#ffedab" },
    { value: 6, color: "#ecf7ad" },
    { value: 7, color: "#c4e687" },
    { value: 8, color: "#97d265" },
    { value: 9, color: "#58b453" },
    { value: 10, color: "#1a9641" },
  ],
};

export const getSubmarketPaintLayout = (subtype) => {
  const validSubtypes = [
    "vacancy_rate",
    "market_effective_rent_growth_12_months",
    "market_effective_rent_sf",
    "market_effective_rent_unit",
  ];
  if (validSubtypes.includes(subtype)) {
    const colorRanges = submarketRanges[subtype].reduce((acc, range) => {
      acc.push(range.value, range.color);
      return acc;
    }, []);

    return {
      "fill-color": [
        "interpolate",
        ["linear"],
        ["get", subtype],
        ...colorRanges,
      ],
      "fill-opacity": [
        "case",
        ["boolean", ["feature-state", "hover"], false],
        0.9,
        0.6,
      ],
    };
  }
};

export const demographicsRanges = {
  median_hh_income: [
    { value: 30000, color: "#d7191c" },
    { value: 40000, color: "#e85b3a" },
    { value: 50000, color: "#f99e59" },
    { value: 60000, color: "#fec981" },
    { value: 70000, color: "#ffedab" },
    { value: 80000, color: "#ecf7ad" },
    { value: 90000, color: "#c4e687" },
    { value: 120000, color: "#97d265" },
    { value: 150000, color: "#58b453" },
    { value: 800000, color: "#1a9641" },
  ],
  five_year_pop_growth: [
    { value: -0.5, color: "#d7191c" },
    { value: 2.5, color: "#e85b3a" },
    { value: 5, color: "#f99e59" },
    { value: 7.5, color: "#fec981" },
    { value: 10, color: "#ffedab" },
    { value: 15, color: "#ecf7ad" },
    { value: 20, color: "#c4e687" },
    { value: 30, color: "#97d265" },
    { value: 40, color: "#58b453" },
    { value: 100, color: "#1a9641" },
  ],
  five_year_income_growth: [
    { value: -0.5, color: "#d7191c" },
    { value: 2.5, color: "#e85b3a" },
    { value: 5, color: "#f99e59" },
    { value: 7.5, color: "#fec981" },
    { value: 10, color: "#ffedab" },
    { value: 15, color: "#ecf7ad" },
    { value: 20, color: "#c4e687" },
    { value: 30, color: "#97d265" },
    { value: 40, color: "#58b453" },
    { value: 100, color: "#1a9641" },
  ],
  bachelors_and_above: [
    { value: 10, color: "#d7191c" },
    { value: 15, color: "#e85b3a" },
    { value: 20, color: "#f99e59" },
    { value: 25, color: "#fec981" },
    { value: 30, color: "#ffedab" },
    { value: 40, color: "#ecf7ad" },
    { value: 50, color: "#c4e687" },
    { value: 60, color: "#97d265" },
    { value: 70, color: "#58b453" },
    { value: 100, color: "#1a9641" },
  ],
  median_age: [
    { value: 10, color: "#d7191c" },
    { value: 15, color: "#e85b3a" },
    { value: 20, color: "#f99e59" },
    { value: 25, color: "#fec981" },
    { value: 30, color: "#ffedab" },
    { value: 40, color: "#ecf7ad" },
    { value: 50, color: "#c4e687" },
    { value: 60, color: "#97d265" },
    { value: 70, color: "#58b453" },
    { value: 100, color: "#1a9641" },
  ],
  household_size: [
    { value: 1, color: "#d7191c" },
    { value: 2, color: "#e85b3a" },
    { value: 3, color: "#f99e59" },
    { value: 4, color: "#fec981" },
    { value: 5, color: "#ffedab" },
    { value: 6, color: "#ecf7ad" },
    { value: 7, color: "#c4e687" },
    { value: 8, color: "#97d265" },
    { value: 9, color: "#58b453" },
    { value: 10, color: "#1a9641" },
  ],
  household_growth: [
    { value: -20, color: "#d7191c" },
    { value: -15, color: "#e85b3a" },
    { value: -10, color: "#f99e59" },
    { value: -5, color: "#fec981" },
    { value: 0, color: "#ffedab" },
    { value: 5, color: "#ecf7ad" },
    { value: 10, color: "#c4e687" },
    { value: 15, color: "#97d265" },
    { value: 20, color: "#58b453" },
    { value: 25, color: "#1a9641" },
  ],
  rental_growth_5_years: [
    { value: 0, color: "#d7191c" },
    { value: 2.5, color: "#e85b3a" },
    { value: 5, color: "#f99e59" },
    { value: 7.5, color: "#fec981" },
    { value: 10, color: "#ffedab" },
    { value: 12.5, color: "#ecf7ad" },
    { value: 15, color: "#c4e687" },
    { value: 17.5, color: "#97d265" },
    { value: 20, color: "#58b453" },
    { value: 25, color: "#1a9641" },
  ],
  rent_vs_owner_percentage: [
    { value: 10, color: "#d7191c" },
    { value: 20, color: "#e85b3a" },
    { value: 30, color: "#f99e59" },
    { value: 40, color: "#fec981" },
    { value: 50, color: "#ffedab" },
    { value: 60, color: "#ecf7ad" },
    { value: 70, color: "#c4e687" },
    { value: 80, color: "#97d265" },
    { value: 90, color: "#58b453" },
    { value: 100, color: "#1a9641" },
  ],
  population_density: [
    { value: 500, color: "#d7191c" },
    { value: 1000, color: "#e85b3a" },
    { value: 1500, color: "#f99e59" },
    { value: 2000, color: "#fec981" },
    { value: 2500, color: "#ffedab" },
    { value: 3000, color: "#ecf7ad" },
    { value: 3500, color: "#c4e687" },
    { value: 4000, color: "#97d265" },
    { value: 4500, color: "#58b453" },
    { value: 5000, color: "#1a9641" },
  ],
  fifty_five_plus: [
    { value: 10, color: "#d7191c" },
    { value: 15, color: "#e85b3a" },
    { value: 20, color: "#f99e59" },
    { value: 25, color: "#fec981" },
    { value: 30, color: "#ffedab" },
    { value: 40, color: "#ecf7ad" },
    { value: 50, color: "#c4e687" },
    { value: 60, color: "#97d265" },
    { value: 70, color: "#58b453" },
    { value: 100, color: "#1a9641" },
  ],
  median_home_value: [
    { value: 100000, color: "#d7191c" },
    { value: 200000, color: "#e85b3a" },
    { value: 300000, color: "#f99e59" },
    { value: 400000, color: "#fec981" },
    { value: 500000, color: "#ffedab" },
    { value: 600000, color: "#ecf7ad" },
    { value: 700000, color: "#c4e687" },
    { value: 800000, color: "#97d265" },
    { value: 900000, color: "#58b453" },
    { value: 1000000, color: "#1a9641" },
  ],
  median_rent: [
    { value: 1000, color: "#d7191c" },
    { value: 2000, color: "#e85b3a" },
    { value: 3000, color: "#f99e59" },
    { value: 4000, color: "#fec981" },
    { value: 5000, color: "#ffedab" },
    { value: 6000, color: "#ecf7ad" },
    { value: 7000, color: "#c4e687" },
    { value: 8000, color: "#97d265" },
    { value: 9000, color: "#58b453" },
    { value: 10000, color: "#1a9641" },
  ],
  rent_vs_own: [
    { value: -1500, color: "#d7191c" },
    { value: -1000, color: "#e85b3a" },
    { value: -500, color: "#f99e59" },
    { value: 0, color: "#fec981" },
    { value: 500, color: "#ffedab" },
    { value: 1000, color: "#ecf7ad" },
    { value: 1500, color: "#c4e687" },
    { value: 2000, color: "#97d265" },
    { value: 2500, color: "#58b453" },
    { value: 3000, color: "#1a9641" },
  ],

  crime_score: [
    { value: 1, color: "#1a9641" },
    { value: 2, color: "#58b453" },
    { value: 3, color: "#97d265" },
    { value: 4, color: "#c4e687" },
    { value: 5, color: "#ecf7ad" },
    { value: 6, color: "#ffedab" },
    { value: 7, color: "#fec981" },
    { value: 8, color: "#f99e59" },
    { value: 9, color: "#e85b3a" },
    { value: 10, color: "#d7191c" },
  ],

  combinedFilterScore: [
    { value: 1, color: "#d7191c" },
    { value: 2, color: "#e85b3a" },
    { value: 3, color: "#f99e59" },
    { value: 4, color: "#fec981" },
    { value: 5, color: "#ffedab" },
    { value: 6, color: "#ecf7ad" },
    { value: 7, color: "#c4e687" },
    { value: 8, color: "#97d265" },
    { value: 9, color: "#58b453" },
    { value: 10, color: "#1a9641" },
  ],

  // five_year_pop_growth: [
  //   { value: -0.5, color: "#b7410e" },
  //   { value: 2.5, color: "#ffffff" },
  //   { value: 5, color: "#d0edca" },
  //   { value: 7.5, color: "#b2e0ab" },
  //   { value: 10, color: "#8ed18c" },
  //   { value: 15, color: "#66bd6f" },
  //   { value: 20, color: "#3da75a" },
  //   { value: 30, color: "#238c45" },
  //   { value: 40, color: "#03702e" },
  //   { value: 100, color: "#00441b" },
  // ],
  // five_year_income_growth: [
  //   { value: -0.5, color: "#b7410e" },
  //   { value: 2.5, color: "#ffffff" },
  //   { value: 5, color: "#d0edca" },
  //   { value: 7.5, color: "#b2e0ab" },
  //   { value: 10, color: "#8ed18c" },
  //   { value: 15, color: "#66bd6f" },
  //   { value: 20, color: "#3da75a" },
  //   { value: 30, color: "#238c45" },
  //   { value: 40, color: "#03702e" },
  //   { value: 100, color: "#00441b" },
  // ],
  // bachelors_and_above: [
  //   { value: 10, color: "#fff5eb" },
  //   { value: 15, color: "#ffe9d3" },
  //   { value: 20, color: "#fed7af" },
  //   { value: 25, color: "#fdbd83" },
  //   { value: 30, color: "#fda057" },
  //   { value: 40, color: "#fa8330" },
  //   { value: 50, color: "#ee6510" },
  //   { value: 60, color: "#da4801" },
  //   { value: 70, color: "#ab3702" },
  //   { value: 100, color: "#7f2704" },
  // ],
};

// export const getDemographicRangeColor = (subtype, value) => {
//   const range = demographicsRanges[subtype];
//   if (range) {
//     for (let i = 0; i < range.length; i++) {
//       if (i + 1 < range.length) {
//         if (value >= range[i].value && value < range[i + 1].value)
//           return range[i].color;
//       } else {
//         if (value >= range[i].value) return range[i].color;
//       }
//     }
//     // if below range
//     return range[0].color;
//   }
// };

export const generateClassRange = (num) => {
  const step = 1 / (num - 1);
  const range = Array.from({ length: num }, (_, i) => i * step);
  return range;
};

export const getCountryDemographicColorArray = (subtype) => {
  const range = demographicsRanges[subtype];
  if (range) {
    return range.map((r) => r.color);
  }
};

export const getCountryDemographicScale = (subtype, color) => {
  const range = demographicsRanges[subtype];
  if (range) {
    function scale(value) {
      if (value === null || value === undefined) return null;
      for (let i = 0; i < range.length; i++) {
        if (i + 1 < range.length) {
          if (value >= range[i].value && value < range[i + 1].value)
            return color ? color[i] : range[i].color;
        } else {
          if (value >= range[i].value) return color ? color[i] : range[i].color;
        }
      }
      // if below range
      return color ? color[0] : range[0].color;
    }
    scale.quantiles = function () {
      const result = range.map((r) => r.value);
      // result.shift();
      result.pop();
      return result;
    };
    scale.range = function () {
      if (color) {
        return color;
      }
      return range.map((r) => r.color);
    };

    return scale;
  }
};

export const getCountrySubmarketColorArray = (subtype) => {
  const range = submarketRanges[subtype];
  if (range) {
    return range.map((r) => r.color);
  }
};

export const getCountrySubmarketScale = (subtype, color) => {
  const range = submarketRanges[subtype];
  if (range) {
    function scale(value) {
      if (value === null) return value;
      for (let i = 0; i < range.length; i++) {
        if (i + 1 < range.length) {
          if (value >= range[i].value && value < range[i + 1].value)
            return color ? color[i] : range[i].color;
        } else {
          if (value >= range[i].value) return color ? color[i] : range[i].color;
        }
      }
      // if below range
      return color ? color[0] : range[0].color;
    }
    scale.quantiles = function () {
      const result = range.map((r) => r.value);
      result.shift();
      // result.pop();
      return result;
    };
    scale.range = function () {
      if (color) {
        return color;
      }
      const result = range.map((r) => r.color);
      return result;
    };
    return scale;
  }
};
