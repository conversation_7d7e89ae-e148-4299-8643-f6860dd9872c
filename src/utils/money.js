// export const formatter = (num) => {
//   return Number(num)
//     .toFixed(0)
//     .replace(/\d{1,3}(?=(\d{3})+(\.|$))/g, '$&,');
// };

export const formatter = (num) => {
  if (["N/A", "N/a", "n/a", "-"].includes(num)) {
    // leave N/A alone
    return num;
  } else if (typeof Number(num) === "number" && typeof num !== "undefined") {
    // handle both number and string
    return Number(num)
      .toFixed(0)
      .replace(/\d{1,3}(?=(\d{3})+(\.|$))/g, "$&,");
  } else {
    return "";
  }
};

export const formatterKM = (num) => {
  const si = [
    { value: 1, symbol: "" },
    { value: 1e3, symbol: "k" },
    { value: 1e6, symbol: "M" },
  ];
  const rx = /\.0+$|(\.[0-9]*[1-9])0+$/;
  let i;
  for (i = si.length - 1; i > 0; i--) {
    if (num >= si[i].value) {
      break;
    }
  }
  let digits;
  if (num > 999999) {
    digits = 2;
  } else {
    digits = 0;
  }
  return (num / si[i].value).toFixed(digits).replace(rx, "$1") + si[i].symbol;
};
