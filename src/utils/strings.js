// re: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort
export const sortString = (a, b) => {
  // console.log('a,b', a, b);
  const nameA = a.toUpperCase(); // ignore upper and lowercase
  const nameB = b.toUpperCase(); // ignore upper and lowercase
  if (nameA < nameB) {
    return -1;
  }
  if (nameA > nameB) {
    return 1;
  }
  // names must be equal
  return 0;
};

const capitalizeSingleWord = (word) => {
  // don't capitalize state abbrev
  if (allStatesAbbrev.includes(word)) {
    return word;
  } else if (word && word[0] !== "") {
    return word[0].toUpperCase() + word.slice(1).toLowerCase();
  } else {
    return "";
  }
};

// capitalize first letters of imported addresses, cities, counties, etc.
export const capitalize = (text) => {
  if (text) {
    return text
      .split(" ")
      .map((word) => capitalizeSingleWord(word))
      .join(" ");
  } else {
    return "";
  }
};

const allStatesAbbrev = [
  "AL",
  "AK",
  "AZ",
  "AR",
  "CA",
  "CO",
  "CT",
  "DE",
  "DC",
  "FL",
  "GA",
  "HI",
  "ID",
  "IL",
  "IN",
  "IA",
  "KS",
  "KY",
  "LA",
  "ME",
  "MD",
  "MA",
  "MI",
  "MN",
  "MS",
  "MO",
  "MT",
  "NE",
  "NV",
  "NH",
  "NJ",
  "NM",
  "NY",
  "NC",
  "ND",
  "OH",
  "OK",
  "OR",
  "PA",
  "RI",
  "SC",
  "SD",
  "TN",
  "TX",
  "UT",
  "VT",
  "VA",
  "WA",
  "WV",
  "WI",
  "WY",
];
