import { useState, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import Source from "./Source";
import Layer from "./Layer";
import { default as turf_booleanOverlap } from "@turf/boolean-overlap";
import { default as turf_intersect } from "@turf/intersect";
import graphlib from "graphlib";
import RBush from "rbush";
import { default as turf_bbox } from "@turf/bbox";

// https://www.cs.cornell.edu/courses/cs3110/2012sp/recitations/rec21-graphs/rec21.html

const sourceId = "subdivision";
const sourceLayer = "residential_objects_usa";
const lineStyle = {
  id: `${sourceId}LineStyle`,
  "source-layer": sourceLayer,
  type: "line",
  // minzoom: zoomLevelToCBSA,
  paint: {
    "line-opacity": 1,
    "line-color": "#000",
  },
  // filter: ["in", "$type", "Polygon"],
};
const fillStyle = {
  id: `${sourceId}FillStyle`,
  "source-layer": sourceLayer,
  type: "fill",
  // minzoom: zoomLevelToCBSA,
  paint: {
    "fill-opacity": 1,
    "fill-color": "#000",
  },
  // filter: ["in", "$type", "Polygon"],
};

function findChromaticNumber(graph) {
  let chromaticNumber = 0;
  graph.nodes().forEach((node) => {
    // Get the colors of all adjacent nodes
    const adjacentColors = graph
      .neighbors(node)
      .map((neighbor) => graph.node(neighbor).color);
    // Find the lowest unused color
    let color = 0;
    while (adjacentColors.includes(color)) {
      color++;
    }
    // Assign the color to the current node
    graph.setNode(node, { color });
    // Update the chromatic number if necessary
    chromaticNumber = Math.max(chromaticNumber, color + 1);
  });
  return chromaticNumber;
}

// greedy algorithm
function colorGraph(graph) {
  let colors = {};
  let count = 0;
  graph.nodes().forEach((node) => {
    // Get the colors of all adjacent nodes
    const adjacentColors = graph
      .neighbors(node)
      .map((neighbor) => colors[neighbor]);
    // Find the lowest unused color
    let color = 0;
    while (adjacentColors.includes(color)) {
      color++;
    }
    // Assign the color to the current node
    colors[node] = color;
    count = Math.max(count, color);
  });
  return { colors: colors, count };
}

function Subdivision() {
  const map = useSelector((state) => state.Map.map);

  useEffect(() => {
    if (!map) return;
    // if (!map || !map.style || !map.style._loaded) return;

    const click = (e) => {
      console.log("HERE");
      const features = map.queryRenderedFeatures(e.point, {
        layers: [fillStyle.id],
      });
      // console.log(e);

      if (features.length > 0) {
        const { _x, _y, _z } = features[0];
        console.log(features[0]._vectorTileFeature.toGeoJSON(_x, _y, _z));
      }

      // const allFeatures = map.querySourceFeatures(sourceId, {
      //   sourceLayer: sourceLayer,
      // });
      // // Matlock Estates
      // // Quail Creek Arlington
      // const names = ["Matlock Estates", "Quail Creek Arlington"];

      // const geojson = allFeatures
      //   .filter((f) => names.includes(f.properties.OBJ_NAME))
      //   .map((f) => {
      //     const { _x, _y, _z } = f;
      //     return f._vectorTileFeature.toGeoJSON(_x, _y, _z);
      //   });

      // console.log(geojson);
      // console.log(turf_booleanOverlap(geojson[0], geojson[1]));
    };

    const allFeatures = map.querySourceFeatures(sourceId, {
      sourceLayer: sourceLayer,
    });
    // Matlock Estates
    // Quail Creek Arlington
    map.once("load", () => {
      console.log("HERE 1:");
      const features = map.querySourceFeatures(sourceId, {
        sourceLayer: sourceLayer,
      });

      if (features.length > 0) {
        const geojson = [];
        const tree = new RBush();
        const g = new graphlib.Graph();

        for (let i = 0; i < features.length; i++) {
          const { _x, _y, _z } = features[i];
          geojson.push(features[i]._vectorTileFeature.toGeoJSON(_x, _y, _z));
          const bbox = turf_bbox(geojson[i]);
          tree.insert({
            minX: bbox[0],
            minY: bbox[1],
            maxX: bbox[2],
            maxY: bbox[3],
            item: {
              OBJ_ID: features[i].properties.OBJ_ID,
              feature: features[i]._vectorTileFeature.toGeoJSON(_x, _y, _z),
            },
          });
          g.setNode(features[i].properties.OBJ_ID.toString());
        }

        console.log("geojson: ", geojson);

        // to improve performance for turf, we can use a spatial index
        // https://github.com/Turfjs/turf-line-slice-at-intersection/issues/1#issuecomment-290965561
        for (let i = 0; i < geojson.length; i++) {
          const bbox = turf_bbox(geojson[i]);
          const adjacent = tree.search({
            minX: bbox[0],
            minY: bbox[1],
            maxX: bbox[2],
            maxY: bbox[3],
          });
          // console.log("geojson[i]: ", geojson[i]);
          // console.log("adjacent: ", adjacent);
          for (let j = 0; j < adjacent.length; j++) {
            // console.log("geojson ID: ", geojson[i].properties.OBJ_ID);
            // console.log(
            //   "adjacent ID: ",
            //   adjacent[j].item.feature.properties.OBJ_ID
            // );
            if (
              geojson[i].properties.OBJ_ID ===
              adjacent[j].item.feature.properties.OBJ_ID
            )
              continue;
            // console.log(`${i}: `, adjacent[j]);
            // console.log("geojson[i]: ", geojson[i]);
            // console.log("adjacent[j].item.feature: ", adjacent[j].item.feature);
            // if (!turf_booleanOverlap(geojson[i], geojson[adjacent[j].item.index]))
            if (turf_booleanOverlap(geojson[i], adjacent[j].item.feature)) {
              g.setEdge(
                geojson[i].properties.OBJ_ID.toString(),
                adjacent[j].item.OBJ_ID.toString()
              );
            }
          }
        }
        console.log("GRAPH: ");
        console.log(g);

        // TODO: get color with d3
        const color = {
          0: "#d73027",
          1: "#fc8d59",
          2: "#fee08b",
          3: "#d9ef8b",
          4: "#91cf60",
          5: "#1a9850",
        };

        // console.log(findChromaticNumber(g));
        const graphColor = colorGraph(g).colors;
        console.log(graphColor);

        console.log("count: ", colorGraph(g).count);

        const expression = ["match", ["get", "OBJ_ID"]];

        for (let key in graphColor) {
          if (!graphColor.hasOwnProperty(key)) continue;

          expression.push(parseInt(key), color[graphColor[key]]);
        }
        expression.push("transparent");
        // fillStyle.paint["fill-color"] = expression;
        console.log(expression);

        map.setPaintProperty(fillStyle.id, "fill-color", expression);
        console.log(fillStyle);
        // const nodes = g.nodes();
        // for (let i = 0; i < nodes.length; i++) {
        //   console.log(g.outEdges(nodes[i]));
        // }
      }
      // const ids = [146199, 145878, 145088, 635564, 146158, 145625, 145444];
      // const polygons = [];
      // for (let i = 0; i < features.length; i++) {
      //   if (ids.includes(features[i].properties.OBJ_ID)) {
      //     const { _x, _y, _z } = features[i];
      //     polygons.push(features[i]._vectorTileFeature.toGeoJSON(_x, _y, _z));
      //   }
      // }

      // const index = new RBush();
      // for (let i = 0; i < polygons.length; i++) {
      //   const bbox = turf_bbox(polygons[i]);
      //   index.insert({
      //     minX: bbox[0],
      //     minY: bbox[1],
      //     maxX: bbox[2],
      //     maxY: bbox[3],
      //     feature: polygons[i].properties.OBJ_ID,
      //   });
      // }

      // const target = polygons.filter((p) => p.properties.OBJ_ID === 146199)[0];
      // const bbox = turf_bbox(target);
      // const nearby = index.collides({
      //   minX: bbox[0],
      //   minY: bbox[1],
      //   maxX: bbox[2],
      //   maxY: bbox[3],
      // });
      // console.log(nearby);

      // const geojson = [];
      // const size = 1000;
      // const index = new RBush();
      // for (let i = 0; i < size; i++) {
      //   const { _x, _y, _z } = features[i];
      //   geojson.push(features[i]._vectorTileFeature.toGeoJSON(_x, _y, _z));
      //   const bbox = turf_bbox(geojson[i]);
      //   index.insert({
      //     minX: bbox[0],
      //     minY: bbox[1],
      //     maxX: bbox[2],
      //     maxY: bbox[3],
      //     feature: features[i],
      //   });
      // }

      // console.log(geojson);
      // console.log(index);
      // if (geojson.length > 0) {
      //   // console.log("BEFORE GRAPH");
      //   // const g = new graphlib.Graph();
      //   // for (let i = 0; i < size; i++) {
      //   //   g.setNode(i.toString());
      //   // }
      //   // console.log("AFTER set node");
      //   // for (let i = 0; i < size; i++) {
      //   //   for (let j = i + 1; j < size; j++) {
      //   //     if (turf_intersect(geojson[i], geojson[j])) {
      //   //       g.setEdge(i.toString(), j.toString());
      //   //     }
      //   //   }
      //   // }
      //   // console.log("HERE 2:");
      //   // console.log(g);
      //   // console.log(g.nodeEdges("3"));
      //   for (let i = 0; i < size; i++) {
      //     const bbox = turf_bbox(geojson[i]);
      //     const nearby = index.search({
      //       minX: bbox[0],
      //       minY: bbox[1],
      //       maxX: bbox[2],
      //       maxY: bbox[3],
      //     });
      //     console.log("polygon: ", geojson[i]);
      //     console.log(nearby);
      //     nearby.forEach((polygon) => {
      //       if (
      //         polygon.feature.properties.OBJ_ID != geojson[i].properties.OBJ_ID
      //       ) {
      //         console.log(turf_booleanOverlap(geojson[i], polygon.feature));
      //       }
      //     });
      //   }
      // }
    });
    map.on("click", click);
    return () => {
      // map.off("click", click);
    };
  }, [map]);

  return (
    <Source id={sourceId} type="vector" url="mapbox://sxbxchen.30sgdha3">
      <Layer {...lineStyle} />
      <Layer {...fillStyle} />
    </Source>
  );
}

export default Subdivision;
