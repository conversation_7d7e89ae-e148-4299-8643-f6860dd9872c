export function organizeParcelData(opts) {
  // Predefine the keys that are organized in the new structure
  const parcel = opts.fields;
  const estated = opts.estated;

  const organizedKeys = [
    "ogc_fid",
    "geoid",
    "parcelnumb",
    "address",
    "scity",
    "state2",
    "szip",
    "owner",
    "mailadd",
    "mail_city",
    "mail_state2",
    "mail_zip",
    "parvaltype",
    "improvval",
    "landval",
    "parval",
    "zoning_type",
    "zoning_subtype",
    "zoning_id",
    "zoning_code_link",
    "usecode",
    "usedesc",
    "lbcs_activity",
    "lbcs_activity_desc",
    "lbcs_function",
    "lbcs_function_desc",
    "lbcs_structure",
    "lbcs_structure_desc",
    "lbcs_site",
    "lbcs_site_desc",
    "usps_vacancy",
    "usps_vacancy_date",
    "dpv_codes",
    "dpv_notes",
    "dpv_type",
    "cass_errorno",
    "numunits",
    "structstyle",
    "ll_gisacre",
    "ll_gissqft",
    "lat",
    "lon",
    "qoz",
    "qoz_tract",
    "census_block",
    "census_blockgroup",
    "census_tract",
    "legaldesc",
    "subdivision",
    "zoning",
    "zoning_description",
  ];

  const newParcel = {
    parcelAddress: {
      objectId: parcel.ogc_fid,
      fipsCode: parcel.geo_id,
      parcelNumber: parcel.parcelnumb,
      alternativeApn: estated?.owners?.apn,
      siteAddress: parcel.address,
      siteCity: parcel.scity,
      siteState: parcel.state2,
      siteZip: parcel.szip,
    },
    ownerInformation: {
      ownerName: estated?.owners?.name || parcel.owner,
      mailingInformation: {
        mailingAddress:
          estated?.owners?.formatted_street_address || parcel.mailadd,
        mailingCity: estated?.owners?.city || parcel.mail_city,
        mailingState: estated?.owners?.state || parcel.mail_state2,
        mailingZip: estated?.owners?.zip_code || parcel.mail_zip,
      },
      estated_owner: estated,
      possible_owner: parcel.owner,
      possible_owner_mailing_address: `${parcel.mailadd}, ${parcel.mail_city}, ${parcel.mail_state2} ${parcel.mail_zip}`,

    },
    propertySalesAndValue: {
      countyProvidedValues: {
        last_sale_date: estated?.lastsale?.assr_last_sale_date,
        last_sale_amount: estated?.lastsale?.assr_last_sale_amount,
        parcelValueType: parcel.parvaltype,
        improvementValue:
          estated?.market_assessment?.improvement_value || parcel.improvval,
        landValue: estated?.market_assessment?.land_value || parcel.landval,
        parcelValue: estated?.market_assessment?.total_value || parcel.parval,
        propertyTax: estated?.taxes?.amount,
      },
    },
    zoningLandUseVacancy: {
      zoningType: parcel.zoning_type,
      zoning: parcel.zoning,
      zoningDescription: parcel.zoning_description,
      zoningSubtype: parcel.zoning_subtype,
      zoningId: parcel.zoning_id,
      zoningCodeLink: parcel.zoning_code_link,
      parcelUseCode: parcel.usecode,
      parcelUseDescription: parcel.usedesc,
      standardizedLandUseCodes: {
        landUseCodeActivity: parcel.lbcs_activity,
        landUseCodeActivityDescription: parcel.lbcs_activity_desc,
        landUseCodeFunction: parcel.lbcs_function,
        landUseCodeFunctionDescription: parcel.lbcs_function_desc,
        landUseCodeStructure: parcel.lbcs_structure,
        landUseCodeStructureDescription: parcel.lbcs_structure_desc,
        landUseCodeSite: parcel.lbcs_site,
        landUseCodeSiteDescription: parcel.lbcs_site_desc,
      },
      residentialAndVacancyIndicators: {
        uspsVacancyIndicator: parcel.usps_vacancy,
        uspsVacancyIndicatorDate: parcel.usps_vacancy_date,
        deliveryPointValidationCodes: parcel.dpv_codes,
        deliveryPointValidationNotes: parcel.dpv_notes,
        deliveryPointMatchType: parcel.dpv_type,
        cassErrorCodes: parcel.cass_errorno,
      },
    },
    structureDetails: {
      numberOfLivingUnits: parcel.numunits,
      structureStyle: parcel.structstyle,
      regridCalculatedData: {
        regridCalculatedBuildingCount: parcel.ll_bldg_count,
        regridCalculatedBuildingFootprintSquareFeet:
          parcel?.ll_bldg_footprint_sqft?.toLocaleString("en-US") || "N/A",
      },
    },
    geographicInformation: {
      centroidCoordinates: `${parcel.lat}, ${parcel.lon}`,
      opportunityZone: {
        federalQualifiedOpportunityZone: parcel.qoz,
        qualifiedOpportunityZoneTractNumber: parcel.qoz_tract,
      },
      censusGeographies: {
        censusBlock: parcel.census_block,
        censusBlockgroup: parcel.census_blockgroup,
        censusTract: parcel.census_tract,
      },
      regridCalculatedData: {
        calculatedAcres: parcel.gisacre,
        calculatedParcelSqFt: parcel.sqft?.toLocaleString("en-US") || "N/A",
      },
      platBlockLotLegalData: {
        legalDescription: parcel.legaldesc,
        subdivision: parcel.subdivision,
      },
    },
    additionalItem: {},
  };

  // Iterate over all keys in the original parcel data
  Object.keys(parcel).forEach((key) => {
    // If the key is not in the list of organized keys, add it to additionalItem
    if (key === "original_address") {
      return;
    }
    if (!organizedKeys.includes(key)) {
      console.log("Additional", key, parcel[key]);
      let value;
      try {
        value = JSON.parse(parcel[key]);
      } catch (err) {
        value = parcel[key];
      }

      if (Array.isArray(value)) {
        console.log("Array", key, value);
        newParcel.additionalItem = {
          ...newParcel.additionalItem,
          ...value
            .map((item) => handleJSONItem(key, item))
            .reduce((a, b) => ({ ...a, ...b }), {}),
        };
      } else if (isNaN(value) && typeof value === "object") {
        newParcel.additionalItem = {
          ...newParcel.additionalItem,
          ...handleJSONItem(key, value),
        };
      } else {
        newParcel.additionalItem[key] = value;
      }
    }
  });

  // sort the additionalItem object by key
  const newAdditionalItem = Object.keys(newParcel.additionalItem)
    .sort()
    .reduce((obj, key) => {
      obj[key] = newParcel.additionalItem[key];
      return obj;
    }, {});

  newParcel.additionalItem = newAdditionalItem;

  return newParcel;
}

const handleJSONItem = (key, value) => {
  const obj = {};
  const keys = Object.keys(value);
  for (let i = 0; i < keys.length; i++) {
    obj[`${key}_${keys[i]}`] = value[keys[i]];
  }

  return obj;
};
