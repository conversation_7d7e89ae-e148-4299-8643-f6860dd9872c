/**
 * <PERSON>–<PERSON> Shuffle. Randomizes elements in an array.
 * Source: https://bost.ocks.org/mike/shuffle/
 *
 * @param {string} array - Array to randomize.
 */
export function shuffle(array) {
  let currentIndex = array.length,
    randomIndex;

  // While there remain elements to shuffle.
  while (currentIndex != 0) {
    // Pick a remaining element.
    randomIndex = Math.floor(Math.random() * currentIndex);
    currentIndex--;

    // And swap it with the current element.
    [array[currentIndex], array[randomIndex]] = [
      array[randomIndex],
      array[currentIndex],
    ];
  }

  return array;
}
