import {
  OWNER_COLOR,
  HOA_FEE_COLOR,
  dallasZipCodes,
  houstonZipCodes,
  sanantonioZipCodes,
  austinZipCodes,
  carolinaZipCodes,
  realtracZipCodes,
  atlantaZipCodes,
} from "../constants";
import { point } from "@turf/helpers";
import { default as turf_distance } from "@turf/distance";
import { geojsonTemplate } from "../constants";

export const cityBasedZipCode = (zipcode) => {
  const code = Number(zipcode.substring(0, 3));

  if (dallasZipCodes.includes(code)) {
    return "dallas";
  } else if (houstonZipCodes.includes(code)) {
    return "houston";
  } else if (sanantonioZipCodes.includes(code)) {
    return "sanantonio";
  } else if (austinZipCodes.includes(code)) {
    return "austin";
  } else if (carolinaZipCodes.includes(code)) {
    return "carolina";
  } else if (realtracZipCodes.includes(code)) {
    return "realtrac";
  } else if (atlantaZipCodes.includes(code)) {
    return "atlanta";
  } else {
    console.log("ZipCode not found in cityBasedZipCode()");
  }
};

export const setPropertyOwnerColor = (property) => {
  if (property["owner_occupied_sl"] === "Yes") {
    property.ownerColor = OWNER_COLOR.ownerOccupiedColor;
  } else {
    if (property.institution) {
      if (property.institution.includes("AH4R")) {
        property.ownerColor = OWNER_COLOR.AH4RColor;
      } else if (property.institution.includes("Amherst")) {
        property.ownerColor = OWNER_COLOR.amherstColor;
      } else if (property.institution.includes("Cerberus")) {
        property.ownerColor = OWNER_COLOR.cerberusColor;
      } else if (property.institution.includes("Invitation")) {
        property.ownerColor = OWNER_COLOR.invitationHomes;
      } else if (property.institution.includes("Progress")) {
        property.ownerColor = OWNER_COLOR.progressResColor;
      } else if (property.institution.includes("Tricon")) {
        property.ownerColor = OWNER_COLOR.triconColor;
      } else if (property.institution.includes("Other")) {
        property.ownerColor = OWNER_COLOR.othersColor;
      }
    } else {
      property.ownerColor = OWNER_COLOR.momAndPopColor;
    }
  }
  return property;
};

export const setHOAFeeColor = (property) => {
  switch (true) {
    case property.hoa_fees >= 1001:
      property.hoaColor = HOA_FEE_COLOR.haoFeeRange5Color;
      break;
    case property.hoa_fees >= 601:
      property.hoaColor = HOA_FEE_COLOR.haoFeeRange4Color;
      break;
    case property.hoa_fees >= 201:
      property.hoaColor = HOA_FEE_COLOR.haoFeeRange3Color;
      break;
    case property.hoa_fees > 0:
      property.hoaColor = HOA_FEE_COLOR.haoFeeRange2Color;
      break;
    default:
      property.hoaColor = HOA_FEE_COLOR.haoFeeRange1Color;
  }
  return property;
};

/**
 * Checks if a coordinate is within a specified distance
 * @param {Array}  coordinateA  Coordinates of a point
 * @param {Array}  coordinateB  Coordinates of a point
 * @param {number} distance     The distance to check for in meters
 * @return {boolean}  True if distance between two points are within distance
 */
export const isWithinDistance = ({ coordinateA, coordinateB, distance }) => {
  const pointA = point(coordinateA);
  const pointB = point(coordinateB);
  const options = { units: "kilometers" };

  const measurement = turf_distance(pointA, pointB, options) * 1000;

  return measurement < distance;
};

export const convertToGeoJSON = ({
  data,
  geomAccessor,
  propertiesAccessor,
}) => {
  const geojson = structuredClone(geojsonTemplate);

  geojson.features = data.map((item) => {
    return {
      type: "Feature",
      geometry: geomAccessor(item),
      properties: propertiesAccessor ? propertiesAccessor(item) : item,
    };
  });

  return geojson;
};
