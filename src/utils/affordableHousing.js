import * as turf from "@turf/turf";
import RTree from "rtree";

export function calculateNumberOfAffordableHousingInTract(TractCoordinate, affordableGeoJson) {
  let countMap = {
    active: 0,
    oneMonth: 0,
    twoMonths: 0,
    threeMonths: 0,
  };
  const polygon = turf.polygon([TractCoordinate]);

  const rtree = new RTree();

  affordableGeoJson.features.forEach((property) => {
    const coordinate = property.geometry.coordinates;
    rtree.insert(
      {
        x: coordinate[0],
        y: coordinate[1],
        w: 0,
        h: 0,
      },
      property
    );
  });

  const bbox = turf.bbox(polygon);

  const results = rtree.bbox(bbox[0], bbox[1], bbox[2], bbox[3]);

  const countedAddresses = new Set();

  results.forEach((property) => {
    const coordinate = property.geometry.coordinates;
    const isInside = turf.booleanPointInPolygon(turf.point(coordinate), polygon);
    if (isInside) {
      const address = property.properties.address;
      if (!countedAddresses.has(address)) {
        countedAddresses.add(address);
        const closing = property.properties.closing; // closing could be 0, 4, 8, 12
        if (closing === 0) {
          countMap.active += 1;
        } else if (closing === 4) {
          countMap.oneMonth += 1;
        } else if (closing === 8) {
          countMap.twoMonths += 1;
        } else if (closing === 12) {
          countMap.threeMonths += 1;
        }
      }
    }
  });

  return countMap;
}
