// type TokenType = 'id' | 'access' | 'refresh';

/**
 * Returns the JWT token from local storage
 *
 * Disclaimer: This is to be used after the user has been authenticated
 * Amplify stores user credentials in local storage
 *
 * @param type {@link TokenType} - The type of token to get
 * @returns {string | null} - The JWT token or null if not found
 */
export const getTokenFromLocalStorage = (type) => {
  if (!type || !["id", "access", "refresh"].includes(type))
    throw new Error("Invalid token type");
  for (const key in localStorage) {
    // This will return the first instance, if multiple pools used, also check for user pool id in key
    if (
      key.startsWith("CognitoIdentityServiceProvider") &&
      key.endsWith(`${type}Token`)
    ) {
      const token = localStorage.getItem(key);
      return token;
    }
  }
  return null;
};

// because this project not in typescript
const availableUserGroups = [
  "Avanta",
  "BlueRiver",
  "BridgeTower",
  "Truehold",
  "GEMR<PERSON>",
  "GreatGulf",
  "demo-users",
  "demo-CMA-DFW-only",
  "Platlabs",
  "Fundrise",
  "AlliedDev",
  "Lennar",
  'Evergreen',
  'ILE',
  'Greystar'
];

export const clientSelector = (userGroup) => {
  if (!availableUserGroups.includes(userGroup)) {
    throw new Error("User credentials not available for " + userGroup);
  }

  const emailBuilder = (user) => {
    if (user === "demo-CMA-DFW-only") {
      return `<EMAIL>`;
    }
    return `allcommonsenses+${user}@gmail.com`;
  };

  const CLIENT_USER_PASSWORD = "Narwhal-Scale-Nebula-Outburst-3000";
  const credentials = { CLIENT_USER_PASSWORD };
  if (
    [
      "BlueRiver",
      "GEMRC",
      "Truehold",
      "GreatGulf",
      "Fundrise",
      "Lennar",
      'Evergreen',
      'ILE',
      'Greystar'
    ].includes(userGroup)
  ) {
    credentials.CLIENT_USER_GROUP = userGroup;
    credentials.CLIENT_USER_EMAIL = emailBuilder(userGroup);
  } else if (userGroup === "Avanta") {
    credentials.CLIENT_USER_GROUP = userGroup;
    credentials.CLIENT_USER_EMAIL = emailBuilder("avanta");
  } else if (userGroup === "BridgeTower") {
    credentials.CLIENT_USER_GROUP = userGroup;
    credentials.CLIENT_USER_EMAIL = emailBuilder("BT");
  } else if (userGroup === "demo-users") {
    credentials.CLIENT_USER_GROUP = userGroup;
    credentials.CLIENT_USER_EMAIL = emailBuilder("demo");
  } else if (userGroup === "Platlabs") {
    credentials.CLIENT_USER_GROUP = userGroup;
    credentials.CLIENT_USER_EMAIL = emailBuilder("Platlabs");
  } else if (userGroup === "demo-CMA-DFW-only") {
    credentials.CLIENT_USER_GROUP = userGroup;
    credentials.CLIENT_USER_EMAIL = emailBuilder("demo-CMA-DFW-only");
  } else if (userGroup === "AlliedDev") {
    credentials.CLIENT_USER_GROUP = userGroup;
    credentials.CLIENT_USER_EMAIL = emailBuilder("AlliedDev");
  }
  return credentials;
};
