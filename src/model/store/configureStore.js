import { createSlice } from "@reduxjs/toolkit";

export const configureSlice = createSlice({
  name: "Configure",
  initialState: {
    sentinelHub: false,
    mapExpander: {
      enabled: false,
      init: {
        mapExpandedView: true,
      },
    },
    mapToImageDownload: false,
    mapDraw: false,
    mapRuler: false,
    mapNavigation: { enabled: false, init: { userGroup: [] } },
    mapControlsMapLayers: true,
    selectRadius: {
      enabled: false,
      init: {
        showClearButton: false,
        // defaultRadius will be set from App.js configuration
      },
    },
    streetview: {
      enabled: false,
    },
    serverType: "", // "exp" or "prod" or "canary"
    user: {
      userGroup: [],
      // userToken: "",
      metrosAllowedForCurrentTrialUser: [],
    },
  },
  reducers: {
    saveState: (state, action) => {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
});

export const { saveState } = configureSlice.actions;

export default configureSlice.reducer;
