import { createSlice } from "@reduxjs/toolkit";
import { geojsonTemplate } from "../../constants";
import HeatmapBlockGroup from "../../components/Map/MapLayers/Heatmap/heatmapBlockGroup";
import { DEFAULT_HEATMAP_TYPES } from "../../constants";

export const mapSlice = createSlice({
  name: "Map",
  initialState: {
    map: null,
    currentMapThemeOption: "Automatic",
    openedMapControllerOption: "",
    currentMapLayerOptions: [],
    parcelTypeShown: "subdivision",
    selectedOwnerParcel: [
      "Owner Occupied",
      "AH4R",
      "Amherst",
      "Cerberus",
      "Invitation Homes",
      "Progress Residential",
      "Tricon",
      "Other Funds",
      "Mom & Pop",
    ],
    selectedHOAFeeParcel: ["$0", "$1-200", "$201-600", "$601-1000", "$1001+"],
    sentinelLayerObjectURL: null,
    showSentinelControl: false,
    daysWithinSentinel: 30,
    selectedAttendanceCategory: "elementary",
    selectedChainIds: {
      favorable: [],
      unFavorable: [],
    },
    chainFirstAppearDate: "",
    chainMapViewType: "Points",
    mapExpandedView: false,
    drawingMode: false,
    parcelOutputMode: false,
    drawnCustomPolygons: [],
    currentRadiusMile: null, // Will be set from configuration
    eventCoordinates: [],
    CMALeaseMode: true,
    CMAscorecardModalOpen: false,
    measureMode: false,
    measureGeoJSON: geojsonTemplate,
    measureDistance: 0,
    mapViewMode: "Map", // or Street
    newBuildClusterType: "All",
    newBuildLegendCount: {},
    tiltMode: false,
    parcelMode: false,

    currentParcelMapScopeGeoJSON: geojsonTemplate,
    currentParcelBoundaryMapScopeGeoJSON: geojsonTemplate,
    currentDistrictMapScopeGeoJSON: geojsonTemplate,
    currentAttendanceZoneGeoJSON: geojsonTemplate,
    currentAttendancePointGeoJSON: geojsonTemplate,
    currentCBSAGeoJSON: geojsonTemplate,
    currentCountyGeoJSON: geojsonTemplate,
    currentZipCodeGeoJSON: geojsonTemplate,
    currentActivityCenterMapScopeGeoJSON: geojsonTemplate,
    currentOpportunityZoneGeoJSON: geojsonTemplate,
    currentFloodZoneGeoJSON: geojsonTemplate,
    currentFloodZoneLOMRGeoJSON: geojsonTemplate,
    chainLocationFavorableGeoJSON: geojsonTemplate,
    chainLocationUnFavorableGeoJSON: geojsonTemplate,
    currentHUDPublicHousing: geojsonTemplate,
    currentNewHomesGeoJSON: geojsonTemplate,
    currentOSMResidentialRoadGeoJSON: geojsonTemplate,
    currentOSMResidentialPolygonGeoJSON: geojsonTemplate,
    currentBuildingPermitSampleGeoJSON: geojsonTemplate,

    heatmapSubmarketData: [],
    heatmapDemographicsData: [],
    heatmapBlockGroupMap: {},
    heatmapType: null, // null || "submarket" || "demographics"
    submarketBGEnabled: false,
    heatmapSubmarketStudyType: "vacancy_rate",
    heatmapDemographicsStudyType: "median_hh_income",
    heatmapBGFilters: {},
    combineAllDemographicsFilters: false,
    // prettier-ignore
    heatmapRangeColorType: DEFAULT_HEATMAP_TYPES,
    heatmapColorScale: {},
    currentDemographicColorScale: null,
    currentSubmarketColorScale: null,
    neighborhoodType: "M",
    heatmapOpacity: 0.6,

    fiftyFivePlusSubdivisionEnabled: false,

    minimzedMapLegends: false,
    enableFloodZoneLOMRLayer: true,

    currentOwnedGeoJSON: geojsonTemplate,
    currentBridgeOwnedStatusCount: {},
    bridgeOwnedClusterType: "All",

    driveTime5MinGeoJSON: geojsonTemplate,
    driveTime10MinGeoJSON: geojsonTemplate,
    driveTime15MinGeoJSON: geojsonTemplate,
    driveTime20MinGeoJSON: geojsonTemplate,
    driveTime30MinGeoJSON: geojsonTemplate,
    driveTime60MinGeoJSON: geojsonTemplate,
    driveTimeOptions: {
      "5 Minutes": true,
      "10 Minutes": true,
      "15 Minutes": true,
      "20 Minutes": true,
      "30 Minutes": false,
      "60 Minutes": false,
    },
    driveTimeTraffic: true,
    currentAirBnBGeoJSON: geojsonTemplate,

    parcelOwnerHeatmapGeojson: geojsonTemplate,
    parcelHeatmapOptions: {
      ah4r: true,
      amherst: true,
      cerberus: true,
      invitationHomes: true,
      progressResidential: true,
      tricon: true,
    },
    parcelShowHeatmap: false,
    parcelHeatmapLoading: false,

    fundrisePropertyGeoJSON: geojsonTemplate,
    unCheckedFundrise: [],
    fundrisePropertiesLoading: false,
    tileSources: [],
    devMode: undefined,

    checkedVoltClass: {
      "Under 100": { checked: true },
      "100-161": { checked: true },
      "220-287": { checked: true },
      345: { checked: true },
      500: { checked: true },
      "735 and above": { checked: true },
      "Not available": { checked: true },
    },

    // Gentrifying
    gentrifyingFilter: [],

    // Major Employer
    majorEmployerFilter: {
      category: [],
      size: ['100 to 249', '250 to 499', '1000 to 4999', '10000+', '5000 to 9999', '500 to 999']
    },

    // Public Housing
    currentEHASAGeoJson: geojsonTemplate,
    currentAffordableHousingGeoJson: geojsonTemplate,
    affordableHousingLayerFetched: false,
    EHASALoading: false,
    currentVoucherTractGeoJson: geojsonTemplate,
    voucherTractLoading: false,
    voucherTractMode: true,
    affordableHousingMode: 'all',

    //Allied Dev
    FutureLandUseFly: null,
    SanitarySewer: null,

    //Transit Line
    transitLineGeoJSON: null,
    transitLineFilter: {showBus: false},

    // Mobile Home Park
    selectedMobileHomeParkType: 'all',
    selectedMobileHomeParkLoading: false,

    parcelLock: false,

    //fundrise community
    fundriseCommunities: [],

    greystarMudTaxLoading: false,
    houstonCompsSelectedField: "market_price_per_mo",

    // Industry News
    industryNewsDateFilter: {
      enabled: false,
      selectedRange: 'all',
      startDate: null,
      endDate: null,
    },
  
  },
  reducers: {
    saveState: (state, action) => {
      return {
        ...state,
        ...action.payload,
      };
    },
  },
});

export const { saveState } = mapSlice.actions;

export default mapSlice.reducer;
