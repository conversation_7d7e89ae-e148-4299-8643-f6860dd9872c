import { call, put, takeEvery, select, takeLatest, all, cancelled } from "redux-saga/effects";
import { saveState } from "../store/mapStore";
import {
  getParcelMapScopeData,
  getParcelBoundaryMapScopeData,
  getDistrictMapScopeData,
  getAttendanceZoneData,
  getCBSAData,
  getCountyData,
  getZipCodeData,
  getActivityCenterMapScopeData,
  getStrategyOpportunityZoneData,
  getFloodZoneData,
  getFloodZoneLOMRData,
  getHUDPublicHousingData,
  getNewHomesData,
  getHeatmapSubmarketData,
  getHeatmapSubmarketRentalGrowthData,
  getHeatmapSubmarketBGData,
  getHeatmapDemographicsData,
  getBlockGroupHomeValueData,
  getOwnedDataPropertyData,
  getHeatmapSFRGrowthData,
  getDriveTimeData,
  getAirBnBData,
  getOSMResidentialRoadsData,
  getOSMResidentialPolygonData,
  getBuildingPermitSampleData,
  getParcelOwnerHeatmapData,
  getBlockGroupCrimeValueData,
  getEHASAData,
  getVoucherData,
} from "../../services/data";
import { setPropertyOwnerColor, setHOAFeeColor } from "../../utils/geography";
import { capitalize } from "../../utils/strings";
import { shuffle } from "../../utils/sort";
import { geojsonTemplate, groupOneChainIds, groupTwoChainIds } from "../../constants";
import { default as turf_center_of_mass } from "@turf/center-of-mass";
import { calculateNumberOfAffordableHousingInTract } from "../../utils/affordableHousing";
// import { default as turf_bbox } from "@turf/bbox";

// function* workGetCatsFetch({ payload }) {
//   console.log(payload);
//   const cats = yield call(() => fetch("https://api.thecatapi.com/v1/breeds"));
//   const formattedCats = yield cats.json();
//   const formattedCatsShortened = formattedCats.slice(0, 10);
//   yield put(getCatsSuccess(formattedCatsShortened));
// }

// function* testingCat({ payload }) {
//   console.log("TestingCat: ", payload);
// }

// const tiltMode = select((state) => state.Map.tiltMode);
const tiltModeState = (state) => state.Map.tiltMode;
const currentAffordableHousingGeoJson = (state) => state.Map.currentAffordableHousingGeoJson;
const enableFloodZoneLOMRLayerState = (state) => state.Map.enableFloodZoneLOMRLayer;
const affordableHousingLayerFetched = (state) => state.Map.affordableHousingLayerFetched;
function* getParcelMapScope({ payload }) {
  const tiltMode = yield select(tiltModeState);

  if (!tiltMode) {
    const response = yield call(getParcelMapScopeData, payload);
    if (response) {
      let geojsonFeaturesParcelMapScope = [];
      for (let property of response) {
        // add a value with phase/ph/addition/addn/sec removed from subdivision names
        // there are subdivision names with more than one keywords, i.e. CHASE OAKS ADDITION PHASE TWO
        // get the smallest indexOfPhase
        if (property.subdivision) {
          let indexOfPhase = -1;
          for (const keyword of [
            " phase ",
            " ph ",
            " addition",
            " addn",
            " add ",
            " est ",
            " estates ",
            " estate ",
            " sec ",
            " section ",
          ]) {
            const currentIndex = property.subdivision.toLowerCase().indexOf(keyword);
            if (
              currentIndex > -1 &&
              (indexOfPhase === -1 || (indexOfPhase > -1 && currentIndex < indexOfPhase))
            ) {
              indexOfPhase = currentIndex;
            }
          }
          if (indexOfPhase > -1) {
            property.subdivisionWithoutPhase = property.subdivision.slice(0, indexOfPhase);
          } else {
            property.subdivisionWithoutPhase = property.subdivision;
          }
        } else {
          property.subdivisionWithoutPhase = "";
        }

        if (property.institution && property.institution.includes("Divvy")) {
          property.institution = "Other";
        }

        property = setPropertyOwnerColor(property);
        property = setHOAFeeColor(property);

        // generate geojson features
        // generate properties for each feature; copy object except geometry
        // re: https://stackoverflow.com/a/34710102/10039571
        let { geog, ...geojsonProperties } = property;
        geojsonFeaturesParcelMapScope.push({
          type: "Feature",
          geometry: property.geog,
          properties: geojsonProperties,
        });
      }

      yield put({
        type: "Map/saveState",
        payload: {
          currentParcelMapScopeGeoJSON: {
            type: "FeatureCollection",
            features: geojsonFeaturesParcelMapScope,
          },
        },
      });
    }
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentParcelMapScopeGeoJSON: geojsonTemplate,
      },
    });
  }
}

function* getParcelBoundaryMapScope({ payload }) {
  const tiltMode = yield select(tiltModeState);

  if (!tiltMode) {
    // expand map bounds to avoid clipping
    const change = 0.00008;
    payload.lat1 += -change;
    payload.lng1 += -change;
    payload.lat2 += change;
    payload.lng2 += change;

    const response = yield call(getParcelBoundaryMapScopeData, payload);
    if (response) {
      let geojsonFeaturesParcelBoundaryMapScope = [];
      for (const property of response) {
        // generate geojson features
        const { geom, ...properties } = property;

        // TODO: should probably use turf.js transformTranslate
        // move the parcel boundary a little bit to the left and up
        // to match with regrid boundaries
        const moveX = -7.5;
        const moveY = 6;
        const translationX = moveX / 1000000;
        const translationY = moveY / 1000000;
        geom.coordinates.forEach((ring) => {
          ring.forEach((point) => {
            point[0] += translationX; // Update longitude
            point[1] += translationY; // Update latitude
          });
        });

        geojsonFeaturesParcelBoundaryMapScope.push({
          type: "Feature",
          geometry: geom,
          properties: properties,
        });
      }
      yield put({
        type: "Map/saveState",
        payload: {
          currentParcelBoundaryMapScopeGeoJSON: {
            type: "FeatureCollection",
            features: geojsonFeaturesParcelBoundaryMapScope,
          },
        },
      });
    }
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentParcelBoundaryMapScopeGeoJSON: geojsonTemplate,
      },
    });
  }
}

function* getDistrictMapScope({ payload }) {
  const tiltMode = yield select(tiltModeState);

  if (!tiltMode) {
    const response = yield call(getDistrictMapScopeData, payload);
    if (response) {
      let geojsonFeaturesDistrictMapScope = [];

      for (const district of response) {
        let { geom, ...properties } = district;
        properties["obj_name"] = capitalize(properties["obj_name"]);

        geojsonFeaturesDistrictMapScope.push({
          type: "Feature",
          geometry: geom,
          properties: properties,
        });
      }

      yield put({
        type: "Map/saveState",
        payload: {
          currentDistrictMapScopeGeoJSON: {
            type: "FeatureCollection",
            features: geojsonFeaturesDistrictMapScope,
          },
        },
      });
      return true;
    }
    return false;
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentDistrictMapScopeGeoJSON: geojsonTemplate,
      },
    });
    return false;
  }
}

function* getAttendanceZone({ payload }) {
  const tiltMode = yield select(tiltModeState);

  if (!tiltMode) {
    const response = yield call(getAttendanceZoneData, payload);
    if (response) {
      let zoneFeatures = [];
      let pointFeatures = [];
      for (const attendance of response) {
        let { school_attendance_zone_geom, school_attendance_point, ...properties } = attendance;

        zoneFeatures.push({
          type: "Feature",
          geometry: school_attendance_zone_geom,
          properties: properties,
        });

        pointFeatures.push({
          type: "Feature",
          geometry: school_attendance_point,
          properties: properties,
        });
      }

      yield put({
        type: "Map/saveState",
        payload: {
          currentAttendanceZoneGeoJSON: {
            type: "FeatureCollection",
            features: zoneFeatures,
          },
          currentAttendancePointGeoJSON: {
            type: "FeatureCollection",
            features: pointFeatures,
          },
        },
      });
    }
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentAttendanceZoneGeoJSON: geojsonTemplate,
        currentAttendancePointGeoJSON: geojsonTemplate,
      },
    });
  }
}
function* getEHASA({ payload }) {
  const tiltMode = yield select(tiltModeState);

  if (!tiltMode) {
    const response = yield call(getEHASAData, payload);
    if (response) {
      console.log("response", response);
      let features = [];
      for (const area of response.features) {
        let { geometry, attributes } = area;

        const tempGeometry = {
          type: "MultiPolygon",
          coordinates: [[geometry.rings[0]]],
        };

        features.push({
          type: "Feature",
          geometry: tempGeometry,
          properties: attributes,
        });
      }
      yield put({
        type: "Map/saveState",
        payload: {
          currentEHASAGeoJson: {
            type: "FeatureCollection",
            features: features,
          },
          EHASALoading: false,
        },
      });
    }
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentEHASAGeoJson: geojsonTemplate,
        EHASALoading: false,
      },
    });
  }
}
function* getVoucherTract({ payload }) {
  const tiltMode = yield select(tiltModeState);
  const currentAffordableHousingData = yield select(currentAffordableHousingGeoJson);
  if (!tiltMode) {
    const response = yield call(getVoucherData, payload);
    if (response) {
      console.log("response", response);
      let features = [];
      console.log("currentAffordableHousingData", currentAffordableHousingData);
      for (const tract of response.features) {
        let { geometry, attributes } = tract;
        let tempAttributes = attributes;
        const coordinate = geometry.rings[0];
        const total = calculateNumberOfAffordableHousingInTract(coordinate, currentAffordableHousingData)
        tempAttributes.affordableNumber = total
        if (attributes.HCV_PUBLIC_PCT && attributes.HCV_PUBLIC) {
          const tempGeometry = {
            type: "MultiPolygon",
            coordinates: [[coordinate]],
          };

          features.push({
            type: "Feature",
            geometry: tempGeometry,
            properties: tempAttributes,
          });
        }
      }
      yield put({
        type: "Map/saveState",
        payload: {
          currentVoucherTractGeoJson: {
            type: "FeatureCollection",
            features: features,
          },
          voucherTractLoading: false,
        },
      });
    }
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentVoucherTractGeoJson: geojsonTemplate,
        voucherTractLoading: false,
      },
    });
  }
}
function* getCBSA({ payload }) {
  const tiltMode = yield select(tiltModeState);

  if (!tiltMode) {
    const response = yield call(getCBSAData, payload);
    if (response) {
      console.log("getCBSAData called");
      let features = [];
      for (const cbsa of response) {
        let { geom, ...properties } = cbsa;

        // console.log(properties.name, turf_bbox(geom));

        features.push({
          type: "Feature",
          geometry: geom,
          properties: properties,
        });

        features.push(turf_center_of_mass(geom, { properties: { name: properties.name } }));
      }

      console.log("features", features);
      yield put({
        type: "Map/saveState",
        payload: {
          currentCBSAGeoJSON: {
            type: "FeatureCollection",
            features: features,
          },
        },
      });
    }
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentCBSAGeoJSON: geojsonTemplate,
      },
    });
  }
}

function* getCounty({ payload }) {
  const tiltMode = yield select(tiltModeState);

  if (!tiltMode) {
    const response = yield call(getCountyData, payload);
    if (response) {
      let features = [];
      for (const county of response) {
        let { geom, ...properties } = county;

        features.push({
          type: "Feature",
          geometry: geom,
          properties: properties,
        });

        features.push(turf_center_of_mass(geom, { properties: { name: properties.name } }));
      }

      yield put({
        type: "Map/saveState",
        payload: {
          currentCountyGeoJSON: {
            type: "FeatureCollection",
            features: features,
          },
        },
      });
    }
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentCountyGeoJSON: geojsonTemplate,
      },
    });
  }
}

function* getZipCode({ payload }) {
  const tiltMode = yield select(tiltModeState);

  if (!tiltMode) {
    const response = yield call(getZipCodeData, payload);

    if (response) {
      let features = [];
      for (const zipcode of response) {
        let { geom, ...properties } = zipcode;

        features.push({
          type: "Feature",
          geometry: geom,
          properties: properties,
        });

        features.push(turf_center_of_mass(geom, { properties: { key: properties.key } }));
      }

      yield put({
        type: "Map/saveState",
        payload: {
          currentZipCodeGeoJSON: {
            type: "FeatureCollection",
            features: features,
          },
        },
      });
    }
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentZipCodeGeoJSON: geojsonTemplate,
      },
    });
  }
}

function* getActivityCenterMapScope({ payload }) {
  const tiltMode = yield select(tiltModeState);

  if (!tiltMode) {
    const response = yield call(getActivityCenterMapScopeData, payload);
    if (response) {
      let features = [];
      for (const activityCenter of response) {
        let { geom, ...properties } = activityCenter;

        features.push({
          type: "Feature",
          geometry: geom,
          properties: properties,
        });
      }

      yield put({
        type: "Map/saveState",
        payload: {
          currentActivityCenterMapScopeGeoJSON: {
            type: "FeatureCollection",
            features: features,
          },
        },
      });
    }
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentActivityCenterMapScopeGeoJSON: geojsonTemplate,
      },
    });
  }
}

function* getStrategyOpportunityZone({ payload }) {
  const tiltMode = yield select(tiltModeState);

  if (!tiltMode) {
    const response = yield call(getStrategyOpportunityZoneData, payload);
    if (response) {
      let features = [];
      for (const opportunity of response) {
        let { geom, ...properties } = opportunity;

        features.push({
          type: "Feature",
          geometry: geom,
          properties: properties,
        });
      }

      yield put({
        type: "Map/saveState",
        payload: {
          currentOpportunityZoneGeoJSON: {
            type: "FeatureCollection",
            features: features,
          },
        },
      });
    }
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentOpportunityZoneGeoJSON: geojsonTemplate,
      },
    });
  }
}

function* getFloodZone({ payload }) {
  const tiltMode = yield select(tiltModeState);

  if (!tiltMode) {
    const response = yield call(getFloodZoneData, payload);

    if (response) {
      let features = [];
      for (const zone of response) {
        let { shape, ...properties } = zone;

        features.push({
          type: "Feature",
          geometry: shape,
          properties: properties,
        });
      }

      yield put({
        type: "Map/saveState",
        payload: {
          currentFloodZoneGeoJSON: {
            type: "FeatureCollection",
            features: features,
          },
        },
      });
    }

    const enableFloodZoneLOMRLayer = yield select(enableFloodZoneLOMRLayerState);
    if (enableFloodZoneLOMRLayer) {
      yield put({
        type: "Map/getFloodZoneLOMR",
        payload: payload,
      });
    }
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentFloodZoneGeoJSON: geojsonTemplate,
      },
    });
  }
}

function* getFloodZoneLOMR({ payload }) {
  const tiltMode = yield select(tiltModeState);

  if (!tiltMode) {
    const response = yield call(getFloodZoneLOMRData, payload);
    if (response) {
      let features = [];
      for (const zone of response) {
        let { shape, ...properties } = zone;

        features.push({
          type: "Feature",
          geometry: shape,
          properties: properties,
        });
        features.push(
          turf_center_of_mass(shape, {
            properties: {
              eff_date: properties.eff_date ? properties.eff_date.split(" ")[0] : "N/A",
            },
          })
        );
      }

      yield put({
        type: "Map/saveState",
        payload: {
          currentFloodZoneLOMRGeoJSON: {
            type: "FeatureCollection",
            features: features,
          },
        },
      });
    }
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentFloodZoneLOMRGeoJSON: geojsonTemplate,
      },
    });
  }
}

function* getHUDPublicHousing({ payload }) {
  const tiltMode = yield select(tiltModeState);

  if (!tiltMode) {
    const response = yield call(getHUDPublicHousingData, payload);
    if (response && response.features && response.features.length > 0) {
      const features = [];
      for (let i = 0; i < response.features.length; i++) {
        const hudData = response.features[i];

        const { STD_ADDR, BUILDING_NAME } = hudData.attributes;
        if (STD_ADDR === null && BUILDING_NAME === null) continue;

        features.push({
          type: "Feature",
          geometry: {
            type: "Point",
            coordinates: [hudData.geometry.x, hudData.geometry.y],
          },
          properties: hudData.attributes,
        });
      }

      yield put({
        type: "Map/saveState",
        payload: {
          currentHUDPublicHousing: {
            type: "FeatureCollection",
            features: features,
          },
        },
      });
    }
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentHUDPublicHousing: geojsonTemplate,
      },
    });
  }
}

function* getNewHomes({ payload }) {
  const tiltMode = yield select(tiltModeState);

  if (!tiltMode) {
    const response = yield call(getNewHomesData, payload);

    if (response) {
      let features = [];
      for (const home of response) {
        let { geom, ...properties } = home;

        features.push({
          type: "Feature",
          geometry: geom,
          properties: properties,
        });
      }

      yield put({
        type: "Map/saveState",
        payload: {
          currentNewHomesGeoJSON: {
            type: "FeatureCollection",
            features: features,
          },
        },
      });
    } else {
      yield put({
        type: "Map/saveState",
        payload: {
          currentNewHomesGeoJSON: geojsonTemplate,
        },
      });
    }
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentNewHomesGeoJSON: geojsonTemplate,
      },
    });
  }
}

function* getHeatmapSubmarket({ payload }) {
  let response;
  if (payload.type === "default") {
    response = yield call(getHeatmapSubmarketData, payload);
    const rentalGrowthRes = yield call(getHeatmapSubmarketRentalGrowthData, payload);

    for (let i = 0; i < response.length; i++) {
      const geogname = response[i].geogname;
      const rentalGrowth = rentalGrowthRes.find((item) => item.geogname.includes(geogname));
      if (rentalGrowth) {
        response[i] = {
          ...response[i],
          rental_growth_5_years: rentalGrowth.rental_growth_5_years,
        };
      }
    }
  } else if (payload.type === "bg") {
    response = yield call(getHeatmapSubmarketBGData, payload);
  }

  if (response) {
    yield put({
      type: "Map/saveState",
      payload: {
        heatmapSubmarketData: response,
      },
    });
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        heatmapSubmarketData: [],
      },
    });
  }
}

function* getHeatmapDemographics({ payload }) {
  const response = yield call(getHeatmapDemographicsData, payload);
  const homeValuesRes = yield call(getBlockGroupHomeValueData, payload);
  const sfrGrowthRes = yield call(getHeatmapSFRGrowthData, payload);
  const crimeValuesRes = yield call(getBlockGroupCrimeValueData, payload);

  if (response) {
    if (homeValuesRes || sfrGrowthRes || crimeValuesRes) {
      for (let i = 0; i < response.length; i++) {
        const id = response[i].id;
        if (homeValuesRes) {
          const homeValue = homeValuesRes.find((item) => item.id === id);

          if (homeValue) {
            // calculate rent vs own
            const housePrice = homeValue.median_home_value;
            const houseRent = homeValue.median_rent;
            const mortgage = housePrice * 0.965 * 0.006157172 + 600;
            const rent_vs_own = mortgage - houseRent;

            response[i] = { ...response[i], ...homeValue, rent_vs_own };
          }
        }
        if (sfrGrowthRes) {
          const sfrGrowth = sfrGrowthRes.find((item) => item.block_group_id === id);
          if (sfrGrowth) {
            response[i] = {
              ...response[i],
              rental_growth_5_years: sfrGrowth.rental_growth_5_years,
            };
          }
        }
        if (crimeValuesRes) {
          const crimeValue = crimeValuesRes.find((item) => item.id === id);
          if (crimeValue) {
            response[i] = {
              ...response[i],
              crime_score: crimeValue.crime_score,
            };
          }
        }
      }
    }

    yield put({
      type: "Map/saveState",
      payload: {
        heatmapDemographicsData: response,
      },
    });
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        heatmapDemographicsData: [],
      },
    });
  }
}

function* getOwnedProperty({ payload }) {
  const response = yield call(getOwnedDataPropertyData, payload);

  if (response) {
    let features = [];
    for (const property of response) {
      let { geog, ...properties } = property;

      features.push({
        type: "Feature",
        geometry: geog,
        properties: properties,
      });
    }

    yield put({
      type: "Map/saveState",
      payload: {
        currentOwnedGeoJSON: {
          type: "FeatureCollection",
          features: features,
        },
      },
    });
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentOwnedGeoJSON: geojsonTemplate,
      },
    });
  }
}

function* getDemoOwnedProperty({ payload }) {
  const response = yield call(getOwnedDataPropertyData, payload);

  if (response) {
    let features = [];
    for (const property of response) {
      let { geom, ...properties } = property;

      features.push({
        type: "Feature",
        geometry: geom,
        properties: properties,
      });
    }

    yield put({
      type: "Map/saveState",
      payload: {
        currentOwnedGeoJSON: {
          type: "FeatureCollection",
          features: features,
        },
      },
    });
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentOwnedGeoJSON: geojsonTemplate,
      },
    });
  }
}

function* getDriveTime({ payload }) {
  const driveTimeOptions = yield select((state) => state.Map.driveTimeOptions);
  const driveTimeTraffic = yield select((state) => state.Map.driveTimeTraffic);

  const savePayload = {};
  if (driveTimeOptions["5 Minutes"]) {
    const response5Min = yield call(getDriveTimeData, {
      driveTimeTraffic,
      minutes: 5,
      ...payload,
    });
    if (response5Min) {
      savePayload.driveTime5MinGeoJSON = response5Min;
    }
  }

  if (driveTimeOptions["10 Minutes"]) {
    const response10Min = yield call(getDriveTimeData, {
      driveTimeTraffic,
      minutes: 10,
      ...payload,
    });

    if (response10Min) {
      savePayload.driveTime10MinGeoJSON = response10Min;
    }
  }

  if (driveTimeOptions["15 Minutes"]) {
    const response15Min = yield call(getDriveTimeData, {
      driveTimeTraffic,
      minutes: 15,
      ...payload,
    });
    if (response15Min) {
      savePayload.driveTime15MinGeoJSON = response15Min;
    }
  }

  if (driveTimeOptions["20 Minutes"]) {
    const response20Min = yield call(getDriveTimeData, {
      driveTimeTraffic,
      minutes: 20,
      ...payload,
    });
    if (response20Min) {
      savePayload.driveTime20MinGeoJSON = response20Min;
    }
  }

  if (driveTimeOptions["30 Minutes"]) {
    const response30Min = yield call(getDriveTimeData, {
      driveTimeTraffic,
      minutes: 30,
      ...payload,
    });
    if (response30Min) {
      savePayload.driveTime30MinGeoJSON = response30Min;
    }
  }

  if (driveTimeOptions["60 Minutes"]) {
    const response60Min = yield call(getDriveTimeData, {
      driveTimeTraffic,
      minutes: 60,
      ...payload,
    });
    if (response60Min) {
      savePayload.driveTime60MinGeoJSON = response60Min;
    }
  }

  yield put({
    type: "Map/saveState",
    payload: savePayload,
  });
}

function* getAirBnB({ payload }) {
  const tiltMode = yield select(tiltModeState);

  if (!tiltMode) {
    const response = yield call(getAirBnBData, payload);

    if (response) {
      let features = [];
      for (const home of response) {
        let { geom, ...properties } = home;

        features.push({
          type: "Feature",
          geometry: geom,
          properties: properties,
        });
      }

      yield put({
        type: "Map/saveState",
        payload: {
          currentAirBnBGeoJSON: {
            type: "FeatureCollection",
            features: features,
          },
        },
      });
    } else {
      yield put({
        type: "Map/saveState",
        payload: {
          currentAirBnBGeoJSON: geojsonTemplate,
        },
      });
    }
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentAirBnBGeoJSON: geojsonTemplate,
      },
    });
  }
}

function* getOSMResidential({ payload }) {
  const getFeatures = (response) => {
    let features = [];
    for (const property of response) {
      let { geom, ...properties } = property;

      features.push({
        type: "Feature",
        geometry: geom,
        properties: properties,
      });
    }
    return features;
  };

  const roadCall = call(getOSMResidentialRoadsData, payload);
  const polygonCall = call(getOSMResidentialPolygonData, payload);
  const buildingPermitSample = call(getBuildingPermitSampleData, {
    lat1: payload.lng1,
    lng1: payload.lat1,
    lat2: payload.lng2,
    lng2: payload.lat2,
    returnRawData: false,
    returnGeoJSON: true,
  });

  const response = yield all([roadCall, polygonCall, buildingPermitSample]);

  if (response[0]) {
    yield put({
      type: "Map/saveState",
      payload: {
        currentOSMResidentialRoadGeoJSON: {
          type: "FeatureCollection",
          features: getFeatures(response[0]),
        },
      },
    });
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentOSMResidentialRoadGeoJSON: geojsonTemplate,
      },
    });
  }

  if (response[1]) {
    yield put({
      type: "Map/saveState",
      payload: {
        currentOSMResidentialPolygonGeoJSON: {
          type: "FeatureCollection",
          features: getFeatures(response[1]),
        },
      },
    });
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentOSMResidentialPolygonGeoJSON: geojsonTemplate,
      },
    });
  }

  if (response[2]) {
    yield put({
      type: "Map/saveState",
      payload: {
        currentBuildingPermitSampleGeoJSON: response[2].geojson,
      },
    });
  } else {
    yield put({
      type: "Map/saveState",
      payload: {
        currentBuildingPermitSampleGeoJSON: geojsonTemplate,
      },
    });
  }
}

function* getParcelOwnerHeatmap({ payload }) {
  const controller = new AbortController();
  const signal = controller.signal;
  try {
    yield put({
      type: "Map/saveState",
      payload: { parcelHeatmapLoading: true },
    });

    const response = yield call(getParcelOwnerHeatmapData, {
      ...payload,
      signal,
    });
    if (response && response.length > 0) {
      const allFeatures = [];

      const createFeature = (data) => {
        const { geog, ...properties } = data;
        return {
          type: "Feature",
          geometry: geog,
          properties: properties,
        };
      };

      const createFeatureCollection = (features) => {
        return {
          type: "FeatureCollection",
          features: features,
        };
      };

      for (let i = 0; i < response.length; i++) {
        allFeatures.push(createFeature(response[i]));
      }

      const parcelOwnerFeatures = shuffle(allFeatures);

      yield put({
        type: "Map/saveState",
        payload: {
          parcelOwnerHeatmapGeojson: createFeatureCollection(parcelOwnerFeatures),
        },
      });
    } else {
      yield put({
        type: "Map/saveState",
        payload: {
          parcelOwnerHeatmapGeojson: geojsonTemplate,
        },
      });
    }
    // prettier-ignore
    yield put({ type: "Map/saveState", payload: { parcelHeatmapLoading: false } });
  } catch (error) {
    console.log(error);
  } finally {
    if (yield cancelled()) {
      // Check if the task was cancelled
      controller.abort();
    }
  }
}

function* mapSaga() {
  // yield takeEvery("Map/saveState", workGetCatsFetch);
  // yield takeEvery("cats/testingCat", testingCat);
  yield takeLatest("Map/getParcelMapScope", getParcelMapScope);
  yield takeLatest("Map/getParcelBoundaryMapScope", getParcelBoundaryMapScope);
  yield takeLatest("Map/getDistrictMapScope", getDistrictMapScope);
  yield takeLatest("Map/getAttendanceZone", getAttendanceZone);
  yield takeLatest("Map/getCBSA", getCBSA);
  yield takeLatest("Map/getEHASA", getEHASA);
  yield takeLatest("Map/getVoucherTract", getVoucherTract);
  yield takeLatest("Map/getCounty", getCounty);
  yield takeLatest("Map/getZipCode", getZipCode);
  yield takeLatest("Map/getActivityCenterMapScope", getActivityCenterMapScope);
  yield takeLatest("Map/getStrategyOpportunityZone", getStrategyOpportunityZone);
  yield takeLatest("Map/getFloodZone", getFloodZone);
  yield takeLatest("Map/getFloodZoneLOMR", getFloodZoneLOMR);
  yield takeLatest("Map/getHUDPublicHousing", getHUDPublicHousing);
  yield takeLatest("Map/getNewHomes", getNewHomes);
  yield takeLatest("Map/getHeatmapSubmarket", getHeatmapSubmarket);
  yield takeLatest("Map/getHeatmapDemographics", getHeatmapDemographics);
  yield takeLatest("Map/getOwnedProperty", getOwnedProperty);
  yield takeLatest("Map/getDemoOwnedProperty", getDemoOwnedProperty);
  yield takeLatest("Map/getDriveTime", getDriveTime);
  yield takeLatest("Map/getAirBnB", getAirBnB);
  yield takeLatest("Map/getOSMResidential", getOSMResidential);
  yield takeLatest("Map/getParcelOwnerHeatmap", getParcelOwnerHeatmap);
}

export default mapSaga;
