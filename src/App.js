import { useState, useEffect, useRef } from "react";
import "./App.css";
import { MAPBOX_TOKEN } from "./constants";
// import { MapProvider } from "./context/MapContext";
import MapWrapper from "./components/MapWrapper";
import moment from "moment";
import MyComponent from "./components/MyComponent";
import { clientSelector } from "./utils/auth";

import AWS from "aws-sdk";
import { jwtDecode } from "jwt-decode";

import { SideBar } from "@spatiallaser/menu-bar";
import { getMenuSchema, trackCoordinateActivity } from "./main";
import {
  ChainStoresProvider,
  ChainStoresMenu,
  ChainStoresLayerBoundary,
  ChainStoresLayerRadius,
} from "./main";
import { useMap, MapProvider, useNearbyChainStore } from "./main";
import { GentrifyingMenu } from "./main";
import { DemographicProvider, DemographicMenu, DemographicLayer } from "./main";
import { PHMenu } from "./components/features/public-housing";

// const DEFAULT_LNG = -97.040443;
const DEFAULT_LNG = -96.70343403421765
// const DEFAULT_LAT = 32.89748;
const DEFAULT_LAT = 33.11772479472964
const DEFAULT_ZOOM = 18.39787299330436;

// This is just for development purposes
const { CLIENT_USER_GROUP, CLIENT_USER_EMAIL, CLIENT_USER_PASSWORD } =
  // clientSelector("demo-CMA-DFW-only");
  clientSelector("Greystar");

const getUserAccessToken = async () => {
  return new Promise((resolve, reject) => {
    AWS.config.update({ region: "us-east-1" });
    const cognito = new AWS.CognitoIdentityServiceProvider();
    const params = {
      AuthFlow: "USER_PASSWORD_AUTH",
      ClientId: "2e304o8db57tg73pr4nbreiq75",
      AuthParameters: {
        USERNAME: CLIENT_USER_EMAIL,
        PASSWORD: CLIENT_USER_PASSWORD,
      },
    };
    cognito.initiateAuth(params, (err, data) => {
      if (err) {
        console.log(err, err.stack);
        reject(err);
      } else {
        resolve(data.AuthenticationResult);
      }
    });
  });
};

let token = null;
/**
 * Get the user token
 *
 * @param type {'id' | 'access'} - The type of token to get
 * @returns {string | null} - The JWT token or null if not found
 */
const getUserToken = async (type) => {
  // verify token
  if (token) {
    const decoded = jwtDecode(token.AccessToken);
    const now = Math.floor(Date.now() / 1000); // in seconds
    if (decoded.exp > now) {
      const { AccessToken, IdToken } = token;
      return type === "access" ? AccessToken : IdToken;
    }
  }

  // get new token
  token = await getUserAccessToken();
  const { AccessToken, IdToken } = token;
  return type === "access" ? AccessToken : IdToken;
};

if (!window.spatiallaser) {
  window.spatiallaser = {
    getUserToken,
  };
}

function App() {
  const { setMap: setProviderMap, activeLayers, setActiveLayers, setOpenSitePlanModal } = useMap();

  // const mapRef = useRef(null);
  const [map, setMap] = useState(null);
  const [currentMapLayerOptions, setCurrentMapLayerOptions] = useState([]);
  const [openChainStores, setOpenChainStores] = useState(false);
  const [openGentrifyingNeibourhoods, setOpenGentrifyingNeibourhoods] = useState(false);
  const [openPublicHousing, setOpenPublicHousing] = useState(false);
  const [openDemographics, setOpenDemographics] = useState(false);
  const [isLeaseMode, setIsLeaseMode] = useState(true);

  useEffect(() => {
    if (!map) return;
    // const track = async (trackData) => {
    //   const result = await trackCoordinateActivity(trackData);
    //   console.log("trackCoordinateActivity", result);
    // };
    map.on("mapExpandedView", (e) => {
      console.log(e);
    });
    map.on("click", (e) => {
      // console.log("map clicked", e);
      // track({
      //   lat: e.lngLat.lat,
      //   lng: e.lngLat.lng,
      //   type: "click",
      //   app: "map",
      // });
      map.fire("map.click", { ...e });
    });

    map.on("mapDraw.enter", (e) => {
      console.log(e);
    });
    map.on("mapDraw.exit", (e) => {
      console.log(e);
    });
    map.on("mapDraw.clear", (e) => {
      console.log(e);
    });
    map.on("mapDraw.apply", (e) => {
      console.log(e);
    });
    map.on("mapDraw.cancel", (e) => {
      console.log(e);
    });

    map.on("selectRadius.change", (e) => {
      console.log(e);
    });
    map.on("selectRadius.clear", (e) => {
      console.log(e);
    });

    map.on("map.themeOption", (e) => {
      console.log(e);
    });

    // map.once("load", () => {
    //   // console.log("setRadius call");
    //   // map.fire("selectRadius.setRadius", {
    //   //   payload: {
    //   //     currentRadiusMile: 2,
    //   //   },
    //   // });
    //   map.fire("selectRadius.setEventCoordinates", {
    //     payload: {
    //       eventCoordinates: [DEFAULT_LNG, DEFAULT_LAT],
    //     },
    //   });
    // });
    // map.once("load", () => {
    //   // map.fire("cma.leaseMode", { payload: { leaseMode: false } });
    //   map.fire("cma.scorecard", {
    //     payload: { scorecardModalOpen: false },
    //   });
    // });
  }, [map]);

  const outsideCircleHide = () => {
    map.fire("selectRadius.hideCircle");
  };
  const outsideCircleShow = () => {
    map.fire("selectRadius.showCircle");
  };
  const outsideCircleToggle = () => {
    map.fire("selectRadius.toggleCircle");
  };

  const setThemeHandler = () => {
    map.fire("map.setThemeOption", {
      payload: { currentMapThemeOption: "Satellite" },
    });
  };

  const triggerFilterChange = () => {
    map.fire("NewHomesLayer.updateNewHomeFilter", {
      payload: {
        filter: {
          status: "Available",
          builder: "Unspecified",
          minPrice: 0,
          maxPrice: 60000000,
          minBed: 0,
          maxBed: 6,
          minBath: 0,
          maxBath: 6,
          minSqft: 0,
          maxSqft: 2500,
          minFirstSeenDate: moment().subtract(30, "days").format("YYYY-MM-DD"),
          maxFirstSeenDate: moment().format("YYYY-MM-DD"),
        },
      },
    });
  };

  const changeHeatmapType = (type) => {
    console.log("heatmap change attempt to: ", type);
    map.fire("heatmap.updateType", {
      payload: {
        heatmapType: type,
      },
    });
  };

  const toggleLeaseMode = () => {
    setIsLeaseMode((prev) => !prev);
    map.fire("cma.leaseMode", { payload: { leaseMode: !isLeaseMode } });
  };

  console.log("currentMapLayerOptions", currentMapLayerOptions);

  return (
    <div className="outerMapContainer">
      {/* <button onClick={outsideCircleHide}>Outside circle hide</button>
      <button onClick={outsideCircleShow}>Outside circle show</button>
      <button onClick={outsideCircleToggle}>Outside circle toggle</button> */}
      {/* <button onClick={setThemeHandler}>set theme</button> */}
      {/* <button onClick={triggerFilterChange}>Change Filter</button> */}
      <button onClick={() => changeHeatmapType("demographics")}>Heatmap Demographics</button>
      <button onClick={() => changeHeatmapType("submarket")}>Heatmap SF Submarket1</button>
      <button
        onClick={() => {
          // console.log("open chain stores");
          setOpenChainStores(true);
          map.fire("mapLayers.currentMapLayerOptions", {
            payload: {
              currentMapLayerOptions: [...currentMapLayerOptions, "chain stores"],
            },
          });
        }}
      >
        Chain Stores
      </button>
      <button
        onClick={() => {
          // console.log("open chain stores");
          setOpenGentrifyingNeibourhoods(true);
          map.fire("mapLayers.currentMapLayerOptions", {
            payload: {
              currentMapLayerOptions: [...currentMapLayerOptions, "gn"],
            },
          });
          const newOptions = [...currentMapLayerOptions, "gn"];
          setCurrentMapLayerOptions(newOptions);
        }}
      >
        Gentrifying Neighborhoods
      </button>
      <button
        onClick={() => {
          if (!openDemographics == true) {
            setActiveLayers((prevState) => [...prevState, "heatmap-demographics"]);
          } else {
            setActiveLayers((prevState) =>
              prevState.filter((layer) => layer !== "heatmap-demographics")
            );
          }
          setOpenDemographics(!openDemographics);
        }}
      >
        New Demographics
      </button>
      <button onClick={() => toggleLeaseMode()}>Toggle Lease Mode</button>

      <button
        onClick={() => {
          const newOptions = [...currentMapLayerOptions, "land bank"];
          setCurrentMapLayerOptions(newOptions);
          if (!currentMapLayerOptions.includes("land bank")) {
            map.fire("mapLayers.currentMapLayerOptions", {
              payload: {
                currentMapLayerOptions: newOptions,
              },
            });
          } else {
            const newOptions = currentMapLayerOptions.filter((l) => l !== "land bank");
            setCurrentMapLayerOptions(newOptions);
            map.fire("mapLayers.currentMapLayerOptions", {
              payload: {
                currentMapLayerOptions: newOptions,
              },
            });
          }
        }}
      >
        Toggle Land Bank
      </button>

      <button
        onClick={() => {
          const newOptions = [...currentMapLayerOptions, "industry news"];
          setCurrentMapLayerOptions(newOptions);
          if (!currentMapLayerOptions.includes("industry news")) {
            map.fire("mapLayers.currentMapLayerOptions", {
              payload: {
                currentMapLayerOptions: newOptions,
              },
            });
          } else {
            const newOptions = currentMapLayerOptions.filter((l) => l !== "industry news");
            setCurrentMapLayerOptions(newOptions);
            map.fire("mapLayers.currentMapLayerOptions", {
              payload: {
                currentMapLayerOptions: newOptions,
              },
            });
          }
        }}
      >
        Industry News
      </button>
      <button
        onClick={() => {
          if (map) {
            map.fire("polygonExport.activate");
            // Update menu state if needed
            const newOptions = [...currentMapLayerOptions, "export parcels"];
            setCurrentMapLayerOptions(newOptions);
          }
        }}
      >
        Parcel Output
      </button>
      <button
        onClick={() => {
          // console.log("open chain stores");
          setOpenPublicHousing(true);
          map.fire("mapLayers.currentMapLayerOptions", {
            payload: {
              currentMapLayerOptions: [...currentMapLayerOptions, "public housing menu"],
            },
          });
          const newOptions = [...currentMapLayerOptions, "public housing menu"];
          setCurrentMapLayerOptions(newOptions);
        }}
      >
        Public Housing
      </button>

      <button onClick={() => setOpenSitePlanModal((prev) => !prev)}>Site Plan</button>
      <button
        onClick={() => {
          const newOptions = [...currentMapLayerOptions, "split parcels"];
          setCurrentMapLayerOptions(newOptions);
          if (!currentMapLayerOptions.includes("split parcels")) {
            map.fire("mapLayers.currentMapLayerOptions", {
              payload: {
                currentMapLayerOptions: newOptions,
              },
            });
          } else {
            const newOptions = currentMapLayerOptions.filter((l) => l !== "split parcels");
            setCurrentMapLayerOptions(newOptions);
            map.fire("mapLayers.currentMapLayerOptions", {
              payload: {
                currentMapLayerOptions: newOptions,
              },
            });
          }
        }}
      >
        Newly Subdivided Parcels
      </button>
      <div style={{ display: "flex", flexDirection: "row", height: "100%" }}>
        <SideBar
          values={currentMapLayerOptions}
          schema={getMenuSchema(CLIENT_USER_GROUP, CLIENT_USER_EMAIL)}
          options={{
            barWidth: 65,
            expanderWidth: 250,
          }}
          onClick={(id) => {
            console.log("test1 id", id);
            console.log("test1 currentMapLayerOptions", currentMapLayerOptions);
            if (id === 'drive time') {
              map.fire("map.setThemeOption", {
                payload: { currentMapThemeOption: "Monochrome" },
              });          
            }
            const selectedLayer = !currentMapLayerOptions.includes(id)
              ? [...currentMapLayerOptions, id]
              : currentMapLayerOptions.filter((layer) => layer !== id);
            console.log("test1 selectedLayer", selectedLayer);
            setCurrentMapLayerOptions(selectedLayer);

            map.fire("mapLayers.currentMapLayerOptions", {
              payload: {
                currentMapLayerOptions: selectedLayer,
              },
            });
          }}
          onRemove={(id) => {}}
        />
        <MapWrapper
          // ref={mapRef}
          getMap={(map) => {
            setMap(map);
            setProviderMap(map);
          }}
          token={MAPBOX_TOKEN}
          initProperties={{
            center: [DEFAULT_LNG, DEFAULT_LAT],
            zoom: DEFAULT_ZOOM,
            theme: "Street",
          }}
          configure={{
            showParcelBoundaryLayer: true,
            //For Platlabs to hide subdivision menu, parcel boundary and parcel dot
            platlabsOnly: false,
            marketplaceOnly: false,
            sentinelHub: true,
            mapExpander: {
              enabled: true,
              init: {
                mapExpandedView: true,
              },
            },
            mapToImageDownload: true,
            mapDraw: true,
            mapRuler: true,
            mapNavigation: { enabled: true },
            // mapNavigation: { enabled: true, init: { userGroup: ["demo-users"] } },
            // mapNavigation: { enabled: true, init: { userGroup: ["HON"] } },
            selectRadius: {
              enabled: true,
              init: {
                showClearButton: true,
              },
            },
            streetview: {
              enabled: true,
            },
            PropertyParcelLegend: true,
          }}
          serverType="exp" // "exp" or "prod" or "canary"
          // serverType="dev" // "exp" or "prod" or "canary"
          devMode={true}
          // token is generated with login of this usergroup above
          user={{ userGroup: [CLIENT_USER_GROUP] }}
        >
          <ChainStoresProvider>
            {openChainStores && (
              <>
                <ChainStoresMenu
                  onClose={() => {
                    setOpenChainStores(false);
                    map.fire("mapLayers.currentMapLayerOptions", {
                      payload: {
                        currentMapLayerOptions: currentMapLayerOptions.filter(
                          (l) => l !== "chain stores"
                        ),
                      },
                    });
                  }}
                />
                <ChainStoresLayerBoundary map={map} />
              </>
            )}
            {/* <ChainStoresLayerRadius
              map={map}
              lat={32.67177}
              lng={-97.15609}
              radius={4800}
              viewLayers={{
                favorable: true,
                unFavorable: false,
              }}
            /> */}
          </ChainStoresProvider>

          {openDemographics && (
            <DemographicProvider userGroup={CLIENT_USER_GROUP}>
              <DemographicLayer serverType={"exp"} />
              <DemographicMenu
                onClose={() => {
                  setActiveLayers((prevState) => {
                    if (prevState.includes("heatmap-demographics")) {
                      return prevState.filter((layer) => layer !== "heatmap-demographics");
                    }
                    return prevState;
                  });

                  setOpenDemographics(false);
                }}
              />
            </DemographicProvider>
          )}
          {openGentrifyingNeibourhoods && (
            <GentrifyingMenu
              onClose={() => {
                setOpenGentrifyingNeibourhoods(false);
                map.fire("map.setThemeOption", {
                  payload: { currentMapThemeOption: "Automatic" },
                });
                map.fire("mapLayers.currentMapLayerOptions", {
                  payload: {
                    currentMapLayerOptions: currentMapLayerOptions.filter((l) => l !== "gn"),
                  },
                });
              }}
            />
          )}
          {openPublicHousing && (
            <PHMenu
              onClose={() => {
                setOpenPublicHousing(false);
                map.fire("map.setThemeOption", {
                  payload: { currentMapThemeOption: "Automatic" },
                });
                map.fire("mapLayers.currentMapLayerOptions", {
                  payload: {
                    currentMapLayerOptions: currentMapLayerOptions.filter(
                      (l) => !l.startsWith("public housing")
                    ),
                  },
                });
              }}
              openLayer={(layerName) => {
                const payload = {
                  currentMapLayerOptions: [...currentMapLayerOptions, layerName],
                };
                map.fire("mapLayers.currentMapLayerOptions", {
                  payload: payload,
                });
                setCurrentMapLayerOptions(payload.currentMapLayerOptions);
              }}
              closeLayer={(layerName) => {
                const payload = {
                  currentMapLayerOptions: currentMapLayerOptions.filter((l) => l !== layerName),
                  // currentMapLayerOptions: [...currentMapLayerOptions, layerName],
                };
                map.fire("mapLayers.currentMapLayerOptions", {
                  payload: payload,
                });
                setCurrentMapLayerOptions(payload.currentMapLayerOptions);
              }}
            />
          )}
        </MapWrapper>
      </div>

      {/* <MyComponent /> */}
    </div>
  );
}

// export default App;
export default () => {
  return (
    <MapProvider>
      <App />
    </MapProvider>
  );
};

// {
//   avanta: {
//     userGroup: ["Avanta"],
//     userToken:
//       "eyJraWQiOiI5Q2E3Zm1mb01ud2EyTk9BMkFROUZMa2lkRmw5MG5YalN0K3RvQlRna0NVPSIsImFsZyI6IlJTMjU2In0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JMCZBsCCc-nflG42q11HLWPoYe4CFA5M-jj2iZY0OOYgEHuGAHWyMP9zRTyCLnZhNa11sSZG1ODhMNgkzxvYh9cRwg7F7_PoSa-oKaASEx5LiJlmKlgoHVYZBZ5aHdC1XMVuaQI-tBkFZN6MCLLwJikvsgVnErrGMJj9CddxL_Dp6LL_nFzMvsFZXjjZmNjOoEgvLF9d_0Sp1aU749hbJEQbxfik0XC1ZpUUt6hBsiaSi0qcUkP4jmULFiwRv4tm-nsUg_WO5w0JpjtI-dcnKq1KaNpziT6Zd4ZwQ3i5x2uMasK6-_0qBen2sIr6dkS0msGKdqRv9uHtLQx0FFAygg",
//   },
//   bridgeFull: {
//     userGroup: ["BridgeFull"],
//     userToken:
//       "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//   },
//   fundrise: {
//     userGroup: ["Fundrise"],
//     userToken:
//       "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//   },
//   hon: {
//     userGroup: ["HON"],
//     userToken:
//       "*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//   },
//   demoUsers: {
//     userGroup: ["demo-users"],
//     userToken:
//       // "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//       "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//   },
//   truehold: {
//     userGroup: ["Truehold"],
//     userToken:
//       "*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//   },
//   UrbanRowGroup: {
//     userGroup: ["UrbanRowGroup"],
//     userToken:
//       "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//   },
// }["Truehold"]
