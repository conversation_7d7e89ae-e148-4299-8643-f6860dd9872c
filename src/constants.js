export const dateFormat = "YYYY-MM-DD";

export const MAPBOX_TOKEN =
  "pk.**************************************************************************.-T2S1ZeAEBGxjC4rC0CZzA";

export const MAPBOX_STREET = "sxbxchen/clb6ws0po002x15qpswyiwpov";
export const MAPBOX_SATELLITE = "sxbxchen/clb6wti4f000014p25xhg3y2e";
export const MAPBOX_MONOCHROME = "sxbxchen/clb6wpbs4003g14nzv7b2x5cx";
export const MAPBOX_TERRAIN = "sxbxchen/clba2z24b001n14qr5h90kmxj";

export const MAP_LAYER_NAME_BASE = {
  parcel: "parcelMapScope",
  parcelBoundary: "parcelBoundaryMapScope",
  district: "districtMapScope",
  activityCenter: "activityCenterMapScope",
  county: "county",
  zipcode: "zipcode",
  opportunityZone: "opportunityZone",
  attendance: "attendance",
  cbsa: "cbsa",
  tax: "tax",
  floodZone: "floodZone",
  poiChainLocation: "poiChainLocation",
  circle: "circle",
  BTOwned: "BTOwned",
  multiFamily: "multiFamily",
  mls: "MLS",
  nationalOperator: "nationalOperators",
  hotPads: "HotPads",
  kml: "KML",
  iso: "iso",
  HUDPublicHousing: "HUDPublicHousing",
  heatmapDemographic: "heatmapDemographic",
  heatmapSubmarket: "heatmapSubmarket",
  subdivision: "subdivision",
  neighborhood: "neighborhood",
  ehasa: 'ehasa',
  voucherTract:'voucherTract'
};

export const OWNER_COLOR = {
  ownerOccupiedColor: "#8e8d8f",
  AH4RColor: "#f5222d",
  amherstColor: "#136170",
  cerberusColor: "#1890ff",
  invitationHomes: "#52c41a",
  progressResColor: "#08979c",
  triconColor: "#0749b5",
  momAndPopColor: "#8c8c8c",
  othersColor: "#faad14",
};

export const HOA_FEE_COLOR = {
  haoFeeRange1Color: "#4daf4a",
  haoFeeRange2Color: "#a8a802",
  haoFeeRange3Color: "#faad14",
  haoFeeRange4Color: "#ff7f00",
  haoFeeRange5Color: "#e41a1c",
};

export const ACTIVITY_CENTER_COLOR = {
  majorColor: "#FF9E1B",
  minorColor: "#F5CC00",
  monoColor: "#3182bd",
};

export const PARCEL_TYPE = {
  hoaFee: "hoa_fees",
  owner: "owners",
  subdivision: "subdivision",
};

// zipcodes
export const dallasZipCodes = [
  750, 751, 752, 753, 754, 756, 757, 760, 761, 762, 763,
];
export const houstonZipCodes = [770, 771, 772, 773, 774, 775, 776, 777];
export const sanantonioZipCodes = [780, 781, 782];
export const austinZipCodes = [786, 787];
export const carolinaZipCodes = [280, 281, 282, 297];
export const realtracZipCodes = [370, 371, 372];
export const atlantaZipCodes = [
  300, 301, 302, 303, 305, 306, 307, 310, 318, 362,
];

export const geojsonTemplate = {
  type: "FeatureCollection",
  features: [],
};

export const groupOneChainIds = [
  "4539",
  "1058",
  "3937",
  "88",
  "4978",
  "5295",
  "3535",
];
export const groupTwoChainIds = ["1463", "1709", "1465", "2078"];

export const zoomLevelToChangeBasemap = 14.5;

export const DEFAULT_HEATMAP_TYPES = {
  // demographic:
  median_hh_income: {
    rangeType: "metro",
    colorType: "default",
    metroDomain: [],
  },
  five_year_pop_growth: {
    rangeType: "metro",
    colorType: "default",
    metroDomain: [],
  },
  // five_year_income_growth: { rangeType: "default", colorType: "default" },
  bachelors_and_above: {
    rangeType: "metro",
    colorType: "default",
    metroDomain: [],
  },
  median_age: { rangeType: "metro", colorType: "default", metroDomain: [] },
  household_size: { rangeType: "metro", colorType: "default", metroDomain: [] },
  household_growth: {
    rangeType: "metro",
    colorType: "default",
    metroDomain: [],
  },
  rental_growth_5_years: {
    rangeType: "metro",
    colorType: "default",
    metroDomain: [],
  },
  rent_vs_owner_percentage: {
    rangeType: "metro",
    colorType: "default",
    metroDomain: [],
  },
  population_density: {
    rangeType: "metro",
    colorType: "default",
    metroDomain: [],
  },
  fifty_five_plus: {
    rangeType: "metro",
    colorType: "default",
    metroDomain: [],
  },
  median_home_value: {
    rangeType: "metro",
    colorType: "default",
    metroDomain: [],
  },
  rent_vs_own: { rangeType: "metro", colorType: "default", metroDomain: [] },
  median_rent: { rangeType: "metro", colorType: "default", metroDomain: [] },
  crime_score: { rangeType: "country", colorType: "default", metroDomain: [] },

  // submarket:
  vacancy_rate: { rangeType: "metro", colorType: "default", metroDomain: [] },
  market_effective_rent_growth_12_months: {
    rangeType: "metro",
    colorType: "default",
    metroDomain: [],
  },
  market_effective_rent_sf: {
    rangeType: "metro",
    colorType: "default",
    metroDomain: [],
  },
  market_effective_rent_unit: {
    rangeType: "metro",
    colorType: "default",
    metroDomain: [],
  },

  combinedFilterScore: {
    rangeType: "country",
    colorType: "default",
    metroDomain: [],
  },
};
