import { notification } from "antd";
import { MAPBOX_TOKEN } from "../constants";

let serverType = "prod";

export const setServerType = (type) => {
  serverType = type;
};

export const getServerType = () => serverType;

export const tileURLRoot = (serverId, serverType) => {
  if (serverId === "cma") {
    return serverType === "exp"
      ? "https://v72b1ngqw2.execute-api.us-east-1.amazonaws.com/expriment"
      : "https://wkgcer251f.execute-api.us-east-1.amazonaws.com/Prod";
  } else if (serverId === "sbs") {
    return serverType === "exp"
      ? "https://edo14g2g8a.execute-api.us-east-1.amazonaws.com/test"
      : "https://745tabhi4e.execute-api.us-east-1.amazonaws.com/prod";
  }
};

export const customRequest = async (url, parameter) => {
  const { url: newUrl, options } = await generateRequestPayload(url, parameter);

  return request(newUrl, options);
};

const generateRequestPayload = async (url, parameter) => {
  url = process.env.NODE_ENV === "development" ? pathRewrite(url) : url;

  // let accessToken;
  // for (const property in localStorage) {
  //   if (property.includes("accessToken")) {
  //     // console.log('property', property);
  //     accessToken = localStorage[property];
  //   }
  // }

  const token =
    window.spatiallaser && window.spatiallaser.getUserToken
      ? `${await window.spatiallaser.getUserToken("access")}`
      : "";

  const Authorization = `Bearer ${token}`;
  const headers = { Authorization };
  if (parameter.method === "POST") {
    headers["Content-Type"] = "application/json";
  }
  console.log("debug", url, headers, parameter);

  return { url, options: { headers, ...parameter } };
};

const request = async (url, options) => {
  try {
    const response = await fetch(url, options);

    if (!response.ok) {
      if (response.status >= 400 && response.status < 500) {
        throw new Error(`Bad Request!`);
      }
      if (response.status >= 500) {
        throw new Error(`Server error, please try again later!`);
      }
    }

    const contentType = response.headers.get("Content-Type");
    if (contentType && contentType.includes("application/json")) {
      const data = await response.json();
      return data;
    } else if (contentType && contentType.includes("image")) {
      return await response.blob();
    } else if (
      contentType &&
      contentType.includes("text") //"text/csv", "text/plain"
    ) {
      return await response.text();
    } else {
      return await response.json();
    }
  } catch (error) {
    if (error.name === "AbortError") return;
    console.error("Unable to call url", { error });

    if (!url.includes("/sources/tiles")) {
      // notification.error({
      //   // message: `request error: ${urlObj.pathname}`,
      //   message: `request error: ${url}`,
      //   description: error.message,
      // });
    }
  }
};

const pathRewrite = (path, useGateWay = false) => {
  if (path.includes("/api/cma/canary")) {
    return path.replace(
      "/api/cma/canary",
      "http://ec2-3-235-170-15.compute-1.amazonaws.com:8079"
      // "https://wkgcer251f.execute-api.us-east-1.amazonaws.com/Prod"
    );
  } else if (path.includes("/api/cma/prod")) {
    return path.replace(
      "/api/cma/prod",
      "http://ec2-54-146-231-140.compute-1.amazonaws.com:8080"
      // "https://wkgcer251f.execute-api.us-east-1.amazonaws.com/Prod"
    );
  } else if (path.includes("/api/cma/exp")) {
    return path.replace(
      "/api/cma/exp",
      "http://ec2-3-235-170-15.compute-1.amazonaws.com:8080"
      // "https://v72b1ngqw2.execute-api.us-east-1.amazonaws.com/expriment"
    );
  } else if (path.includes("/api/acq/exp")) {
    return path.replace(
      "/api/acq/exp",
      "http://ec2-3-235-170-15.compute-1.amazonaws.com:8090"
    );
  } else if (path.includes("/api/acq/prod")) {
    return path.replace(
      "/api/acq/prod",
      "http://ec2-54-146-231-140.compute-1.amazonaws.com:8090"
    );
  } else if (path.includes("/api/offMarket/exp")) {
    return path.replace(
      "/api/offMarket/exp",
      "http://ec2-3-235-170-15.compute-1.amazonaws.com:8055"
      // "https://e4h2dyqxah.execute-api.us-east-1.amazonaws.com/test"
    );
  } else if (path.includes("/api/offMarket/prod")) {
    return path.replace(
      "/api/offMarket/prod",
      "http://ec2-54-146-231-140.compute-1.amazonaws.com:8055"
    );
  } else if (path.includes("/api/acq/dev") || path.includes("/api/cma/dev")) {
    console.log("path", `http://localhost:8080${path.split("/dev")[1]}`);
    return `http://localhost:8080${path.split("/dev")[1]}`;
  } else if (path.includes("/api/sbs/prod")) {
    return path.replace(
      "/api/sbs/prod",
      useGateWay
        ? "https://745tabhi4e.execute-api.us-east-1.amazonaws.com/prod"
        : "http://ec2-54-146-231-140.compute-1.amazonaws.com:9003"
    );
  } else if (path.includes("/api/sbs/exp")) {
    return path.replace(
      "/api/sbs/exp",
      useGateWay
        ? "https://edo14g2g8a.execute-api.us-east-1.amazonaws.com/test"
        : "http://ec2-3-235-170-15.compute-1.amazonaws.com:9003"
    );
  } else if (path.includes("/api/bt-db/prod")) {
    return path.replace(
      "/api/bt-db/prod",
      " https://o6zrvp1n2j.execute-api.us-east-1.amazonaws.com/prod"
    );
  } else if (path.includes("/api/management/exp")) {
    return path.replace(
      "/api/management/exp",
      "http://ec2-3-235-170-15.compute-1.amazonaws.com:8070"
    );
  } else if (path.includes("/api/management/prod")) {
    return path.replace(
      "/api/management/prod",
      "http://ec2-44-222-3-252.compute-1.amazonaws.com:8070"
    );
  }

  return path;
};

export const getParcelMapScopeData = async (params) => {
  // ATTN: lng/lat switched
  return await customRequest(
    `/api/cma/${serverType}/parcel/square?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    {
      method: "GET",
    }
  );
};

export const getParcelBoundaryMapScopeData = async (params) => {
  // ATTN: lng/lat switched
  return await customRequest(
    `/api/cma/${serverType}/boundary/square?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    {
      method: "GET",
    }
  );
};

export const getDistrictMapScopeData = async (params) => {
  // ATTN: lng/lat switched
  return await customRequest(
    `/api/cma/${serverType}/district/square?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    {
      method: "GET",
    }
  );
};

export const getAttendanceZoneData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/attendance/square?category=${params.category}&lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: "GET" }
  );
};

export const getCBSAData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/cbsa?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: "GET" }
  );
};

export const getCountyData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/county?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    {
      method: "GET",
    }
  );
};

export const getZipCodeData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/zipcode?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    {
      method: "GET",
    }
  );
};
export const getTaxData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/admin-tract/square?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    {
      method: "GET",
    }
  );
};

export const getActivityCenterMapScopeData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/brookings?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    {
      method: "GET",
    }
  );
};

export const getStrategyOpportunityZoneData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/strategy-opportunity?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    {
      method: "GET",
    }
  );
};

export const getFloodZoneData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/flood-zone?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: "GET" }
  );
};

export const getFloodZoneLOMRData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/flood-zone-lomr?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: "GET" }
  );
};

export const getPOIChainLocationBoundaryData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/poi-chain-location?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}&first_appeared=${params.firstappeared}&status=${params.status}`,
    {
      method: "POST",
      body: JSON.stringify(params.body),
      ...(params.signal && { signal: params.signal }),
    }
  );
};
export const getPOIChainLocationRadiusData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/poi-chain-location?lat=${params.lat}&lng=${params.lng}&radius=${params.radius}&first_appeared=${params.firstappeared}`,
    {
      method: "POST",
      body: JSON.stringify(params.body),
      ...(params.signal && { signal: params.signal }),
    }
  );
};

export const getPOIChainIDByCategoryData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/poi-chain-location/get-chain-ids-by-category`,
    {
      method: "POST",
      body: JSON.stringify(params.body),
    }
  );
};

export const getPOIChainCategoryData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/poi-chain-location/get-all-categories`,
    {
      method: "GET",
    }
  );
};

export const getHUDPublicHousingData = async (params) => {
  return await request(
    `https://services.arcgis.com/VTyQ9soqVukalItT/arcgis/rest/services/Public_Housing_Buildings/FeatureServer/0/query?where=1%3D1&outFields=*&geometry=${params.lng1}%2C${params.lat1}%2C${params.lng2}%2C${params.lat2}&geometryType=esriGeometryEnvelope&inSR=4326&spatialRel=esriSpatialRelIntersects&outSR=4326&f=json`,
    { method: "GET" }
  );
};

export const getNewHomesData = async (params) => {
  return await customRequest(
    // `/api/cma/${serverType}/real-estate-house-new-home/square?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    `/api/cma/${serverType}/real-estate-house-new-home/square?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}&status=${params.status}&builder=${params.builder}&min_price=${params.minPrice}&max_price=${params.maxPrice}&min_bed_rooms=${params.minBed}&max_bed_rooms=${params.maxBed}&min_bath_rooms=${params.minBath}&max_bath_rooms=${params.maxBath}&min_square_feet=${params.minSqft}&max_square_feet=${params.maxSqft}&min_first_seen_date=${params.minFirstSeenDate}&max_first_seen_date=${params.maxFirstSeenDate}&source=${params.source}`,
    { method: "GET" }
  );
};

export const getHeatmapSubmarketData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/heatmap-submarket?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: "GET" }
  );
};

export const getHeatmapSubmarketRentalGrowthData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/heatmap-submarket/rental_growth?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: "GET" }
  );
};

export const getHeatmapSubmarketBGData = async (params) => {
  return await customRequest(`/api/cma/${serverType}/heatmap-submarket-bg`, {
    method: "POST",
    body: params.body,
  });
};

export const getHeatmapDemographicsData = async (params) => {
  return await customRequest(`/api/cma/${serverType}/heatmap-demographics`, {
    method: "POST",
    body: params.body,
  });
};

// get data layer of point
export const getMLSListingSummaryPointWithinLayerData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/mls-listing-summary/point-within-data-layer?lng=${params.lng}&lat=${params.lat}`,
    { method: "GET" }
  );
};

export let powerLineAPIVersion = null;
export const resetPowerLineAPIVersion = () => {
  powerLineAPIVersion = null;
};

export const getElectricPowerLinesData = async (params) => {
  const geometry = encodeURIComponent(
    `${params.lng1},${params.lat1},${params.lng2},${params.lat2}`
  );

  const url1 = `https://services2.arcgis.com/FiaPA4ga0iQKduv3/arcgis/rest/services/US_Electric_Power_Transmission_Lines/FeatureServer/0/query?where=1%3D1&outFields=*&geometry=${geometry}&geometryType=esriGeometryEnvelope&inSR=4326&spatialRel=esriSpatialRelIntersects&outSR=4326&f=geojson`;
  const url2 = `https://services1.arcgis.com/Hp6G80Pky0om7QvQ/arcgis/rest/services/Transmission_Lines/FeatureServer/0/query?where=1%3D1&outFields=*&geometry=${geometry}&geometryType=esriGeometryEnvelope&inSR=4326&spatialRel=esriSpatialRelIntersects&outSR=4326&f=geojson`;

  const timedFetch = (url, options, timeout = 3000) => {
    const controller = new AbortController();
    return Promise.race([
      fetch(url, { ...options, signal: controller.signal }),
      new Promise((_, reject) => {
        setTimeout(() => {
          reject("Cancelling Power Lines API 1 - too long");
          controller.abort();
        }, timeout);
      }),
    ]);
  };

  const getURL = (version) => {
    if (version === 1) {
      return url1;
    }
    return url2;
  };

  if (powerLineAPIVersion === null) {
    try {
      const response = await timedFetch(getURL(1), { method: "GET" }, 2000);
      if (response.ok) {
        powerLineAPIVersion = 1;
        return await response.json();
      }
      throw new Error("Cancelling Power Lines API 1 - not ok");
    } catch (error) {
      try {
        const response = await timedFetch(getURL(2), { method: "GET" }, 2000);
        if (response.ok) {
          powerLineAPIVersion = 2;
          return await response.json();
        } else {
          // notification.error({
          //   message: "Power Lines layer is not available at this time.",
          // });
        }
      } catch (error) {
        // notification.error({
        //   message: "Power Lines layer is not available at this time.",
        // });
      }
    }
  } else {
    return await request(getURL(powerLineAPIVersion), { method: "GET" });
  }
};

export const getSubdivision55PlusData = async (params) => {
  // api receives POST body for subdivison ids and returns ids that are 55+
  return await customRequest(`/api/cma/${serverType}/subdivision-55-plus`, {
    method: "POST",
    body: params.body,
  });
};

export const getBlockGroupHomeValueData = async (params) => {
  // return await customRequest(`/api/cma/${serverType}/heatmap-block-group`, {
  return await customRequest(`/api/cma/${serverType}/heatmap-block-group`, {
    method: "POST",
    body: params.body,
  });
};

export const getHeatmapSFRGrowthData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/heatmap-single-family/rental_growth`,
    {
      method: "POST",
      body: params.body,
    }
  );
};

export const getBlockGroupCrimeValueData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/heatmap-block-group/crime?lng=${params.lng}&lat=${params.lat}`,
    {
      method: "POST",
      body: params.body,
    }
  );
};

export const getHeatmapDomainData = async (params) => {
  params.type = params.type.replace(/_/g, "-");
  let url = `/api/cma/${serverType}/heatmap-demographics/metro-${params.type}?lng=${params.lng}&lat=${params.lat}`;

  if (
    ["median-rent", "median-home-value", "crime-score"].includes(params.type)
  ) {
    if (params.type === "crime-score") params.type = "crime";

    url = `/api/cma/${serverType}/heatmap-block-group/metro-${params.type}?lng=${params.lng}&lat=${params.lat}`;
  } else if (["rental-growth-5-years"].includes(params.type)) {
    params.type = "rental-growth";
    url = `/api/cma/${serverType}/heatmap-single-family/metro-${params.type}?lng=${params.lng}&lat=${params.lat}`;
  }

  return await customRequest(url, { method: "GET", signal: params.signal });
};

export const getSubmarketDomainData = async (params) => {
  params.type = params.type.replace(/_/g, "-");
  let url = `/api/cma/${serverType}/heatmap-submarket/metro-${params.type}?lng=${params.lng}&lat=${params.lat}`;

  if (["rental-growth-5-years"].includes(params.type)) {
    params.type = "rental_growth";
    url = `/api/cma/${serverType}/heatmap-submarket/metro-${params.type}?lng=${params.lng}&lat=${params.lat}`;
  }

  return await customRequest(url, { method: "GET" });
};

export const getSubmarketBGDomainData = async (params) => {
  params.type = params.type.replace(/_/g, "-");
  let url = `/api/cma/${serverType}/heatmap-submarket-bg/metro-${params.type}?lng=${params.lng}&lat=${params.lat}`;

  return await customRequest(url, { method: "GET" });
};

export const getOwnedDataPropertyData = async (params) => {
  let url = "";
  if (params.userGroup.includes("HON")) {
    url = `/api/acq/exp/hon/owned?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`;
  } else if (
    params.userGroup.includes("BridgeAdmin") ||
    params.userGroup.includes("BridgeFull")
  ) {
    url = `/api/acq/exp/gl/owned?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`;
  } else if (params.userGroup.includes("Truehold")) {
    url = `/api/offMarket/${serverType}/truehold/owned?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`;
  } else if (params.userGroup.includes("demo-users")) {
    url = `/api/cma/exp/demo/owned-property?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`;
  } else if (params.userGroup.includes("demo-CMA-DFW-only")) {
    url = `/api/cma/exp/demo/owned-property?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`;
  }

  return await customRequest(
    // `/api/acq/${serverType}/hon/owned?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    url,
    { method: "GET" }
  );
};

export const getTrueHoldUnderwrittenData = async (params) => {
  return await customRequest(
    `/api/offMarket/${serverType}/truehold/past-underwritten?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: "GET" }
  );
};

export const getTrueHoldTargetData = async (params) => {
  return await customRequest(
    `/api/offMarket/${serverType}/truehold/targets?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: "GET" }
  );
};

export const getDriveTimeData = async (params) => {
  return await request(
    `https://api.mapbox.com/isochrone/v1/mapbox/${
      params.driveTimeTraffic ? "driving-traffic" : "driving"
    }/${params.lng},${params.lat}?contours_minutes=${
      params.minutes
    }&polygons=true${
      params.departAt ? `&depart_at=${params.departAt}` : ""
    }&access_token=${MAPBOX_TOKEN}`,
    { method: "GET" }
  );
};

export const getRegridParcelDataProxy = async (params) => {
  return customRequest(`/api/cma/exp/regrid-query-proxy`, {
    method: "POST",
    body: JSON.stringify(params.body),
  });
};

export const getAirBnBData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/airbnb?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: "GET" }
  );
};

export const getOSMResidentialRoadsData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/poi-osm/residential-road?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: "GET" }
  );
};

export const getOSMResidentialPolygonData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/poi-osm/residential-polygon?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: "GET" }
  );
};

export const getBuildingPermitSampleData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/building-permit-sample?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}&return_raw_data=${params.returnRawData}&return_geojson=${params.returnGeoJSON}`,
    { method: "GET" }
  );
};

export const getParcelOwnerHeatmapData = async (params) => {
  const url = `https://api.locatealpha.com/cma/${serverType}/parcel/owners-map?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`;

  const token =
    window.spatiallaser && window.spatiallaser.getUserToken
      ? `${await window.spatiallaser.getUserToken("access")}`
      : "";
  const Authorization = `Bearer ${token}`;
  const headers = { Authorization };

  return await request(url, {
    headers,
    method: "GET",
    signal: params.signal,
  });
};

export const getMultiFamilyInBounds = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/multifamily/bounds?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}&return_raw_data=${params.returnRawData}&return_geojson=${params.returnGeoJSON}`,
    { method: "GET" }
  );
};

export const getFundriseOwnedData = async (params) => {
  return await customRequest(
    `/api/acq/${serverType}/fr/owned?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}&type=${params.type}&return_raw_data=${params.returnRawData}&return_geojson=${params.returnGeoJSON}`,
    { method: "GET" }
  );
};

export const getWaterDistrictRasterLink = async (params) => {
  return await request(
    `https://gisweb.tceq.texas.gov/arcgis/rest/services/iwud/WaterDistricts_PRD/MapServer/export`,
    { method: "POST", body: params.body }
  );
};

export const getWaterDistrictQueryData = async (params) => {
  return await request(
    `https://gisweb.tceq.texas.gov/arcgis/rest/services/iwud/WaterDistricts_PRD/MapServer/dynamicLayer/query?f=json&returnGeometry=true&spatialRel=esriSpatialRelIntersects&geometry=${encodeURIComponent(
      JSON.stringify(params.geometry)
    )}&geometryType=esriGeometryEnvelope&inSR=4326&outFields=NAME%2CDISTRICT_ID%2CTYPE%2CCOUNTY%2CCOGO_ACRES%2CPLAT_ACRES%2CDIGITIZED%2CSTATUS%2CCREATION_DATE%2CBNDRY_CHANGE%2CMETHOD%2CSOURCE%2CACCURACY%2CCOMMENTS%2CINITIALS%2CUPDATED%2CTX_CNTY%2CFIPS%2COBJECTID%2CSHAPE.AREA%2CSHAPE.LEN&outSR=4326&layer={%22source%22%3A{%22type%22%3A%22mapLayer%22%2C%22mapLayerId%22%3A6}}`,
    { method: "GET" }
  );
};

export const getMapTileSourcesData = async (params) => {
  return await customRequest(`/api/cma/${serverType}/sources/tiles`, {
    method: "GET",
  });
};

export const getAllDemographicsInSquareData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/heatmap-demographics/square?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: "GET" }
  );
};

export const getParcelDetailData = async (params) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/parcel/details/${params.ll_uuid}`,
    {
      method: "GET",
    }
  );
};

export const getParcelBoundaryTileSourceData = async (params) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/parcel/boundaries/tile/source`,
    {
      method: "GET",
    }
  );
};

export const getParcelBoundaryTileAPI = ({ path }) => {
  const url = `/api/sbs/${serverType}${path}`;
  return process.env.NODE_ENV === "development"
    ? pathRewrite(url)
    : pathRewrite(url, true);
};

export const getBT_btrPropertyData = async (params) => {
  const url = pathRewrite(
    `/api/bt-db/prod/bridgetower/bt_monday_underwriting_pending_contracts_b2r/data`
  );
  try {
    const res = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer A06qO5kJV9-PW4jc1hD025rX6WYPiYlEr5Eg_wtkAlY`,
      },
    });

    if (res.ok) {
      return await res.json();
    } else {
      throw new Error("Unable to fetch BTR data.");
    }
  } catch (error) {
    // notification.error({
    //   message: `request error: ${url}`,
    //   description: error.message,
    // });
  }
};

export const getCityLayerData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/city/square?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: "GET" }
  );
};

export const getBlueRiverPRPData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/br/prp?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    { method: "GET" }
  );
};

export const getAdminTaxNationalLegendData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/admin-tract/national-legend`,
    {
      method: "GET",
    }
  );
};

export const getPropertyTaxData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/parcel?placekey=${params.placekey}&lat=${params.lat}&lng=${params.lng}&streetnum=${params.streetnum}`,
    { method: "GET" }
  );
};

export const getPrologisData = async () => {
  return await customRequest(`/api/cma/${serverType}/prologis/all`, {
    method: "GET",
  });
};

export const getLandBankCountData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/map/bound/land_bank/${params.bounds}${
      params.lot_size ? `?lot_size=${params.lot_size}` : ""
    }`,
    { method: "GET" }
  );
};
export const getLandBankData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/map/land_bank/${params.fips}/${params.apn}`,
    {
      method: "GET",
    }
  );
};
export const getGentrifyingNeibourhoodsCategories = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/poi-chain-location/low-income/categories`,
    {
      method: "GET",
    }
  );
};

export const getMajorEmployerCategories = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/poi/major_employer/primary-categories`,
    {
      method: "GET",
    }
  );
};
export const getPublicHousingAuthoritiesData = async (params) => {
  return await request(
    `https://services.arcgis.com/VTyQ9soqVukalItT/arcgis/rest/services/Public_Housing_Authorities/FeatureServer/0/query?where=1%3D1&outFields=*&outSR=4326&f=json`,
    { method: "GET" }
  );
};
export const getAssistedMultiFamilyPropertiesData = async (params) => {
  return await request(
    `https://services.arcgis.com/VTyQ9soqVukalItT/arcgis/rest/services/MULTIFAMILY_PROPERTIES_ASSISTED/FeatureServer/0/query?where=1%3D1&outSR=4326&f=json&outFields=PROPERTY_NAME_TEXT`,
    { method: "GET" }
  );
};
export const getEHASAData = async (params) => {
  return await request(
    `https://services.arcgis.com/VTyQ9soqVukalItT/arcgis/rest/services/Proposed_Housing_Authority_Service_Areas/FeatureServer/15/query?where=1%3D1&outFields=*&geometry=${params.lat1}%2C${params.lng1}%2C${params.lat2}%2C${params.lng2}&geometryType=esriGeometryEnvelope&inSR=4326&spatialRel=esriSpatialRelIntersects&outSR=4326&f=json`,
    { method: "GET" }
  );
};
export const getVoucherData = async (params) => {
  return await request(
    `https://services.arcgis.com/VTyQ9soqVukalItT/arcgis/rest/services/Housing_Choice_Vouchers_by_Tract/FeatureServer/0/query?where=1%3D1&outFields=*&geometry=${params.lat1}%2C${params.lng1}%2C${params.lat2}%2C${params.lng2}&geometryType=esriGeometryEnvelope&inSR=4326&spatialRel=esriSpatialRelIntersects&outSR=4326&f=json`,
    { method: "GET" }
  );
};
export const getInsuredData = async (params) => {
  return await request(
    `https://services.arcgis.com/VTyQ9soqVukalItT/arcgis/rest/services/HUD_Insured_Multifamily_Properties/FeatureServer/0/query?where=1%3D1&outFields=PROPERTY_NAME_TEXT&outSR=4326&f=json`,
    { method: "GET" }
  );
};
export const getPublicHousingDevelopmentData = async (params) => {
  return await request(
    `https://services.arcgis.com/VTyQ9soqVukalItT/arcgis/rest/services/Public_Housing_Developments/FeatureServer/0/query?where=1%3D1&outFields=FORMAL_PARTICIPANT_NAME,PROJECT_NAME,TOTAL_UNITS&outSR=4326&f=json`,
    { method: "GET" }
  );
};

export const getAffordableHousingData = async () => {
  return await customRequest(`/api/cma/${serverType}/affordable-housing`, {
    method: "GET",
  });
};

export const getZoningDetailsData = async (params) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/zoneDetails?lat=${params.lat}&lng=${params.lng}`,
    {
      method: "GET",
    }
  );
};

export const getTaxProperDetailsData = async (params) => {
  const { url: newUrl, options } = await generateRequestPayload(
    `/api/sbs/${serverType}/api/v1/taxproper/tax-estimates`,
    {
      method: "POST",
      body: JSON.stringify(params.body),
    }
  );

  return await fetch(newUrl, options);
};

export const getParcelAVMData = async (params) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/parcel/avm/${params.lat},${params.lng}`,
    {
      method: "GET",
    }
  );
};

export const postMapTrackingAPI = async (body) => {
  return await customRequest(`/api/management/${serverType}/map/store`, {
    method: "POST",
    body: body,
  });
};

export const getDemoUnderwrittenData = async (params) => {
  const url = `/api/cma/exp/demo/underwritten?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`;
  return await customRequest(url, { method: "GET" });
};

export const getDemoLandData = async (params) => {
  const url = `/api/cma/exp/demo/land?type=${params.type}`;
  return await customRequest(url, { method: "GET" });
};

export const getSentinelAPIKeyData = async (params) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/metadata/sentinel`,
    {
      method: "GET",
    }
  );
};

// prettier-ignore
export const getSitePlanData = async (params) => {
  const { lat, lng, width_bldg, depth_bldg, width_road, exit_dir, beds, baths, sqft, year_built } = params;
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/site-plan/${lat},${lng}?width_bldg=${width_bldg}&depth_bldg=${depth_bldg}&width_road=${width_road}&exit_dir=${exit_dir}${beds ? `&beds=${beds}`: ''}${baths ? `&baths=${baths}`: ''}${sqft ? `&sqft=${sqft}`: ''}${year_built ? `&year_built=${year_built}`: ''}`,
    {
      method: "GET",
    }
  );
};

export const getParcelBoundaryOfCoordinateData = async (params) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/parcel/boundary/${params.lat},${params.lng}`,
    {
      method: "GET",
    }
  );
};

export const getCharterSchoolData = async () => {
  return await customRequest(`/api/cma/${serverType}/charter-school/all`, {
    method: "GET",
  });
};

export const getOwnerOtherParcels = async (params) => {
  const { lat, lng } = params;
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/parcel/owner_parcels/${lat},${lng}`,
    {
      method: "GET",
    }
  );
};

export const postSkipTrace = async (body) => {
  return await customRequest(
    `/api/sbs/${serverType}/api/v1/parcel/skip-trace`,
    // `http://localhost:9003/api/v1/parcel/skip-trace`,
    {
      method: "POST",
      body: JSON.stringify(body),
    }
  );
};

export const getSanitaySewer = async (params) => {
  return await customRequest(`/api/cma/${serverType}/admindev/sanitarysewer`, {
    method: "GET",
  });
};
export const getFutureLandUse = async (params) => {
  return await customRequest(`/api/cma/${serverType}/admindev/futurelanduse`, {
    method: "GET",
  });
};

export const getTransitLine = async (params) => {
  return await request(
    `https://services.arcgis.com/xOi1kZaI0eWDREZv/arcgis/rest/services/NTAD_National_Transit_Map_Routes/FeatureServer/0/query?where=1%3D1&outFields=*&geometry=${params.lng1}%2C${params.lat1}%2C${params.lng2}%2C${params.lat2}&geometryType=esriGeometryEnvelope&inSR=4326&spatialRel=esriSpatialRelContains&outSR=4326&f=json`,
    { method: "GET" }
  );
};
export const getBusStop = async (params) => {
  return await request(
    `https://services.arcgis.com/xOi1kZaI0eWDREZv/arcgis/rest/services/NTAD_National_Transit_Map_Stops/FeatureServer/0/query?where=1%3D1&outFields=*&geometry=${params.lng1}%2C${params.lat1}%2C${params.lng2}%2C${params.lat2}&geometryType=esriGeometryEnvelope&inSR=4326&spatialRel=esriSpatialRelIntersects&outSR=4326&f=json`,
    { method: "GET" }
  );
};
export const getAmtrakStations = async (params) => {
  return await request(
    `https://services.arcgis.com/xOi1kZaI0eWDREZv/arcgis/rest/services/NTAD_Amtrak_Stations/FeatureServer/0/query?where=1%3D1&outFields=*&geometry=${params.lng1}%2C${params.lat1}%2C${params.lng2}%2C${params.lat2}&geometryType=esriGeometryEnvelope&inSR=4326&spatialRel=esriSpatialRelIntersects&outSR=4326&f=json`,
    { method: "GET" }
  );
};
export const getCARailStations = async (params) => {
  return await request(
    `https://caltrans-gis.dot.ca.gov/arcgis/rest/services/CHrailroad/California_Rail_Stations/FeatureServer/0/query?where=1%3D1&outFields=*&geometry=${params.lng1}%2C${params.lat1}%2C${params.lng2}%2C${params.lat2}&geometryType=esriGeometryEnvelope&inSR=4326&spatialRel=esriSpatialRelIntersects&outSR=4326&f=json`,
    { method: "GET" }
  );
};

export const getQualifiedCensusTracts = async (params) => {
  return await request(
    `https://services.arcgis.com/VTyQ9soqVukalItT/arcgis/rest/services/QUALIFIED_CENSUS_TRACTS/FeatureServer/15/query?where=1%3D1&outFields=*&geometry=${params.lng1}%2C${params.lat1}%2C${params.lng2}%2C${params.lat2}&geometryType=esriGeometryEnvelope&inSR=4326&spatialRel=esriSpatialRelIntersects&outSR=4326&f=json`,
    { method: "GET" }
  );
};

export const getCoreSpacesPortfolio = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/admindev/core-spaces-portfolio`,
    {
      method: "GET",
    }
  );
};

export const getMobileHomeParkData = async () => {
  return await customRequest(`/api/cma/${serverType}/mobile-home-park/all`, {
    method: "GET",
  });
};
export const getIndsutryNewsData = async () => {
  return await customRequest(`/api/cma/${serverType}/industry-news/all`, {
    method: "GET",
  });
};

export const getBFRClusterData = async (params) => {
  const url = `/api/cma/exp/bfr-cluster/all`;
  return await customRequest(url, { method: "GET" });
};


export const getSAPublicHousingProperty = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/realestate/secondave`,
    {
      method: "GET",
    }
  );
};

export const getFundriseOwnedCommunities = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/fundrise/all`,
    {
      method: "GET",
    }
  );
};
export const getFundriseOwnedCommunitiesBoundary = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/fundrise/boundary?lat1=${params.lat1}&lng1=${params.lng1}&lat2=${params.lat2}&lng2=${params.lng2}`,
    {
      method: "GET",
    }
  );
};

export const getGreystarMudTaxData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/greystar/mud-tax/all`,
    // `http://localhost:8080/greystar/mud-tax/all`,
    {
      method: "GET",
    }
  );
};
export const getGreystarHoustonCompsData = async (params) => {
  return await customRequest(
    `/api/cma/${serverType}/greystar/houston-comps/all`,
    // `http://localhost:8080/greystar/houston-comps/all`,
    {
      method: "GET",
    }
  );
};
