export { default as Map } from "./components/MapWrapper";
export { default as Source } from "./components/Map/MapLayers/Source";
export { default as Layer } from "./components/Map/MapLayers/Layer";
export { default as HeatmapMenu } from "./components/Map/MapLayers/Heatmap2/menu/HeatmapMenu";
export { getMenuSchema } from "./components/hooks/useMapLayerMenu";
export { trackCoordinateActivity } from "./components/hooks/trackCoordinateActivity"
export {
  MapProvider,
  useMap,
  useNearbyChainStore,
} from "./components/Map/MapProvider";

export {
  ChainStoresProvider,
  useChainStores,
  ChainStoresMenu,
  ChainStoresLayerBoundary,
  ChainStoresLayerRadius,
} from "./components/features/chain-stores/index";

export {
  DemographicProvider,
  useDemographicHeatmap,
  DemographicMenu,
  DemographicLayer,
} from "./components/features/heat-map/demographic/index";

export {
  GentrifyingMenu
} from "./components/features/gentrifying-neibourhoods/index"

export {
  PHMenu
} from './components/features/public-housing/index'