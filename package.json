{"name": "@spatiallaser/map", "files": ["./dist"], "publishConfig": {"registry": "http://ec2-3-235-170-15.compute-1.amazonaws.com:4873/"}, "repository": "https://github.com/USDA-Prime-R<PERSON>ye-Tiramisu/map", "version": "1.0.257", "main": "dist/cjs/index.cjs.js", "module": "dist/esm/index.esm.js", "source": "src/main.js", "dependencies": {"@react-icons/all-files": "https://github.com/react-icons/react-icons/releases/download/v4.7.1/react-icons-all-files-4.7.1.tgz", "@reduxjs/toolkit": "^1.9.1", "@rollup/plugin-json": "^6.1.0", "@spatiallaser/menu-bar": "^1.0.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@turf/along": "^7.2.0", "@turf/area": "^6.5.0", "@turf/bbox": "^6.5.0", "@turf/bearing": "^7.2.0", "@turf/center-of-mass": "^6.5.0", "@turf/circle": "^6.5.0", "@turf/distance": "^6.5.0", "@turf/helpers": "^6.5.0", "@turf/length": "^7.2.0", "@turf/midpoint": "^7.2.0", "@turf/transform-rotate": "^6.5.0", "@turf/turf": "^7.2.0", "antd": "^5.16.1", "aws-sdk": "^2.1584.0", "d3": "^7.8.4", "d3-scale-chromatic": "^3.0.0", "geojson-apply-right-hand-rule": "^1.0.7", "geojson-validation": "^1.0.2", "html-to-image": "^1.11.3", "http-proxy-middleware": "^2.0.6", "invert-color": "^2.0.0", "jwt-decode": "^4.0.0", "lodash.debounce": "^4.0.8", "lodash.isempty": "^4.4.0", "lodash.isequal": "^4.5.0", "lucide-react": "^0.471.1", "mapbox-gl": "^2.11.1", "moment": "^2.29.4", "rc-util": "^5.27.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-error-boundary": "^4.1.2", "react-query": "^3.39.3", "react-redux": "^8.0.5", "react-scripts": "5.0.1", "redux-saga": "^1.2.2", "rtree": "^1.4.2", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "rollup": "rollup -c"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/cli": "^7.20.7", "@babel/core": "^7.20.7", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@rollup/plugin-babel": "^6.0.3", "@rollup/plugin-image": "^3.0.1", "@rollup/plugin-terser": "^0.2.1", "npm-run-all": "^4.1.5", "postcss-import": "^15.1.0", "postcss-url": "^10.1.3", "rollup": "^2.79.1", "rollup-plugin-delete": "^2.0.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "tailwindcss": "^3.3.2"}, "peerDependencies": {"@reduxjs/toolkit": "^1.9.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@turf/area": "^6.5.0", "@turf/bbox": "^6.5.0", "@turf/circle": "^6.5.0", "@turf/distance": "^6.5.0", "@turf/helpers": "^6.5.0", "html-to-image": "^1.11.3", "lodash.isempty": "^4.4.0", "lodash.isequal": "^4.5.0", "mapbox-gl": "^2.11.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.0.5", "react-scripts": "5.0.1", "redux-saga": "^1.2.2"}}